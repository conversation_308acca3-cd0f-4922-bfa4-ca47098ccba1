import * as React from 'react';
import { connect } from 'react-redux';
import { BlockLoader } from 'gces-ui';
import { UsersContainer } from './UsersContainer/index';
import { OrganizationTree } from './OrganizationTree';
import { MembersEditor } from './MembersEditor';
import { userActionCreators, UserState } from '../store';
import { Scrollbars } from 'gces-react-custom-scrollbars';
interface ConnectedProps {
	busy: boolean;
	dispatch: any;
	t: any;
}

class UserManagementInner extends React.Component<ConnectedProps> {
	componentWillMount() {
		this.props.dispatch(userActionCreators.init());
	}
	componentWillUnmount() {
		this.props.dispatch(userActionCreators.resetState());
	}
	render() {
		const { busy } = this.props;
		const scrollbarsProps = {
			style: { width: '100%', height: '100%' },
			renderThumbHorizontal: props => <div {...props} style={{ height: '4px' }} className='thumb-horizontal' />,
			autoHide: true
		};
		return (
			<Scrollbars {...scrollbarsProps}>
				<div className='user-management'>
					{busy && <BlockLoader />}
					<OrganizationTree />
					<UsersContainer />
					<MembersEditor />
				</div>
			</Scrollbars>
		);
	}
}
export const Management = connect(
	(state: { user: UserState }) => ({
		busy: state.user.busy,
	})
)(UserManagementInner) as React.ComponentClass<{}>;
