﻿const path = require('path');
const webpack = require('webpack');
const TerserPlugin = require("terser-webpack-plugin");
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require("css-minimizer-webpack-plugin");
const RemoveEmptyScriptsPlugin = require('webpack-remove-empty-scripts');
const glob = require("glob");
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');
const HookContextPlugin = require('./HookContextPlugin');
const { ThemeContentReplacePlugin } = require('./ThemeContentReplace');

function mapThemeFilesToEntries(entry, pattern) {
	return glob
		.sync(pattern)
		.reduce((entries, filename) => {
			const [, name] = filename.match(/([^/]+)\.scss$/);
			return { ...entries, [`${entry}.${name}`]: filename };
		}, {});
}

module.exports = (env) => {
	if (!env) env = {};

	const isDevBuild = !env.prod;
	process.env.NODE_ENV = isDevBuild ? 'development' : 'production';
	const plugins = [
		new HookContextPlugin({
			globalVar: 'AppContext',
			modules: [
				{
					filename: 'account-management.js',
					plugins: ['accountManagement'],
					component: 'account',
					type: 'register'
				}
			]
		}),
		new ForkTsCheckerWebpackPlugin(),
		new RemoveEmptyScriptsPlugin({ enabled: !isDevBuild }),
		new MiniCssExtractPlugin(),
		new ThemeContentReplacePlugin({ enabled: !isDevBuild })
	];

	const config = {
		entry: {
			'account-management': './boot.tsx',
		},

		mode: isDevBuild ? 'development' : 'production',

		performance: {
			maxEntrypointSize: 1024 * 1024 * 5, 	// Bytes
			maxAssetSize: 1024 * 1024 * 5, 			// Bytes
		},

		cache: {
			type: 'filesystem'
		},

		output: {
			path: path.resolve(__dirname, 'dist/'),
			environment: {				// https://webpack.js.org/configuration/output/#outputenvironment
				arrowFunction: true,
				const: true,
				destructuring: true,
				forOf: true,
				optionalChaining: true,
				templateLiteral: true,
			}
		},

		plugins: plugins,

		module: {
			rules: [
				{
					test: /\.json$/,
					loader: 'json5-loader',
					type: 'javascript/auto',
				},
				{
					test: /\.jsx?$/,
					exclude: /node_modules/,
					use: [
						{
							loader: 'babel-loader?cacheDirectory=true',
							options: {
								configFile: path.resolve('babel.config.js')
							}
						}
					]
				},
				{
					test: /\.tsx?$/,
					use: [
						'babel-loader?cacheDirectory=true',
						{
							loader: 'ts-loader',
							options: {
								// disable type checker - we will use it in fork plugin
								transpileOnly: true
							}
						},
						{ loader: path.resolve('./webpack-conditional-loader') }
					],
					exclude: /node_modules/
				},
				{
					test: /\.s?css$/,
					use: [
						isDevBuild ? 'style-loader' : MiniCssExtractPlugin.loader,
						{
							loader: 'css-loader',
							options: {
								sourceMap: isDevBuild
							}
						},
						{
							loader: 'sass-loader',
							options: {
								// Prefer `dart-sass`
								implementation: require('sass')
							},
						},
					].concat(!isDevBuild ? { loader: path.resolve('./ThemeContentReplace.js') } : []),
				}
			]
		},

		resolve: {
			extensions: ['.js', '.jsx', '.ts', '.tsx'],
			alias: {
				'@global': path.resolve(__dirname, '../../Gces.Dataset.Plugin/ClientApp/Global'),
			}
		},

		optimization: {
			minimize: !isDevBuild,
			minimizer: [
				new TerserPlugin({
					extractComments: false,
					terserOptions: {
						format: {
							comments: false,
						},
						compress: {
							drop_console: true,
						}
					}
				}),
				new CssMinimizerPlugin({
					minimizerOptions: {
						preset: [
						  'default',
						  {
							discardComments: { removeAll: true }
						  }
						]
					  }
				})
			]
		},

		externals: {
			'i18next': 'i18next',
			'i18next-browser-languagedetector': 'i18nextBrowserLanguageDetector',
			'moment': 'moment',
			'react-i18next': 'reactI18next',
			'react': 'React',
			'react-dom': 'ReactDOM',
			'react-redux': 'ReactRedux',
			'redux': 'Redux',
			'redux-thunk': 'ReduxThunk',
			'redux-saga': 'ReduxSaga'
		},

		stats: { modules: false, children: false, entrypoints: false },
	};

	if (isDevBuild) {
		plugins.push(new webpack.HotModuleReplacementPlugin());
		config.devtool = 'inline-source-map';
		config.devServer = {
			port: 7777,
			host: '0.0.0.0',
			hot: true,
			client: {
				progress: true,
			},
			static: {
				directory: path.join(__dirname, 'dist'),
			}
		};
	}
	else {
		Object.assign(config.entry, {
			...mapThemeFilesToEntries('account-management', './styles/themes/index.scss')
		})
	}

	return config;
};