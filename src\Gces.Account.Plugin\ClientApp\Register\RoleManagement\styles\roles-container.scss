.roles-container {
	border-right: 1px solid $ef-bg-dk;
	display: flex;
	flex-direction: column;
	padding: 10px;
	max-width: 20%;

	.search-box-container {
		flex: 0 0 50px;
		display: flex;
		align-items: center;

		.search-box-wp {
			flex: 1;
			margin-right: 10px;

			.search-box {
				width: 100%;
				margin: 10px 0;
				flex: 0 0 30px;
				background: $ef-bg-dk;
				border-radius: 2px;
				display: flex;
				align-items: center;

				.sc-icon {
					flex: 0 0 30px;
					color: $ef-accent;
					font-size: $ef-icon-18;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.sc-input {
					flex: 1;
					background: none !important;
					border: none !important;
				}
			}
		}

		button {
			>span {
				display: inline-block;
				max-width: 120px;

				@include gces-truncate;
			}
		}
	}

	.role-list {
		flex: 1;

		.role-item {
			width: 100%;
			display: flex;
			height: 40px;
			align-items: center;
			padding: 0 10px;

			>button {
				color: $accent1;
				transition: none;
			}

			.role-item-icon {
				font-size: $ef-font-size-sm;
				margin-right: 5px;
				color: $accent1;
			}

			.role-name {
				font-size: $ef-font-size-sm;
				flex: 1;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			&.odd {
				background: transparentize(#000, .95);
			}

			&:hover {
				background: $ef-accent-lt;

				.role-name {
					color: $text-contrast;
					cursor: default;
				}

				.role-item-icon,
				>button {
					color: $text-contrast;
				}
			}

			&.selected {
				background: $accent1;

				.role-item-icon,
				.role-name,
				>button {
					color: $text-contrast;
				}
			}
		}
	}

	.add-role-container {
		height: 100%;
		width: 100%;
		display: flex;
		flex-direction: column;

		.ar-title {
			flex: 0 0 40px;
			font-weight: bold;
			align-items: center;
			width: 150px;

			@include gces-truncate;
		}

		.ar-body {
			flex: 1;
		}

		.ar-footer {
			flex: 0 0 40px;
			display: flex;
			flex-direction: row-reverse;

			button {
				margin-left: 10px;

				>span {
					display: inline-block;
					max-width: 100px;

					@include gces-truncate;
				}
			}
		}
	}
}