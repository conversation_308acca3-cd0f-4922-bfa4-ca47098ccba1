import { Scrollbars } from 'gces-react-custom-scrollbars';
import { <PERSON><PERSON>oader, BooleanEditor, Button, DropdownEditor, InputEditor, NumberEditor, AriaIcon } from 'gces-ui';
import * as update from 'immutability-helper';
import * as React from 'react';
import { translate } from 'react-i18next';
import { connect } from 'react-redux';
import Actions from '../actions/actions';

interface SMSSettings {
	accessKeyId: string;
	accessKeySecret: string;
	signName: string;
	templateCode: string;
}
interface EmailSettings {
	subject: string;
	bodyTemplate: string;
}
interface TFASetings {
	enabled: boolean;
	lifetime: number;
	type: 'SMS' | 'EMAIL';
	settings: SMSSettings & EmailSettings;
}

interface TFAProps {
	tfaSettings: TFASetings;
	busy: boolean;
	validated: boolean;
	saved: boolean;
	hasEmailSettings: boolean;
}

interface ConnectProps {
	dispatch: any;
	t: any;
}

interface LocalState {
	settings: TFASetings;
	dirty: boolean;
	validated: boolean;
}

class TwoFactorAuthentication extends React.PureComponent<TFAProps & ConnectProps, LocalState> {
	constructor(props, context) {
		super(props, context);
		this.state = {
			settings: null,
			dirty: false,
			validated: false,
		};
	}

	_placeHolder = '{@code}';

	componentDidMount() {
		this.props.dispatch(Actions.GetTFASettings());
	}
	componentWillReceiveProps(nextProps: TFAProps & ConnectProps) {
		if (nextProps.tfaSettings && nextProps.tfaSettings.settings && nextProps.saved) {
			const settings = { ...nextProps.tfaSettings };
			const detail = { ...nextProps.tfaSettings.settings };
			let dirty = false;
			if (settings.type === 'EMAIL') {
				if (!detail.subject) {
					detail.subject = nextProps.t('defaultSubject');
					dirty = true;
				}
				if (!detail.bodyTemplate) {
					detail.bodyTemplate = nextProps.t('defaultBodyTemplate');
					dirty = true;
				}
			}
			settings.settings = detail;
			this.setState({ settings, dirty });
		}
		if (nextProps.validated !== null && nextProps.validated !== undefined) {
			this.setState({ validated: nextProps.validated });
		}
	}

	onTypeChanged = (value) => {
		if (value === 'EMAIL') {
			this.props.dispatch(Actions.GetEmailSettings());
		}
		const settings = update(this.state.settings, { type: { $set: value } });
		this.setState({ settings, dirty: true, validated: false });
	}

	invalidSMSSettings = (settings: SMSSettings): boolean => {
		return !settings.accessKeyId ||
			!settings.accessKeySecret ||
			!settings.signName ||
			!settings.templateCode;
	}
	invalidEmailSettings = (settings: EmailSettings): boolean => {
		return !settings.subject ||
			!settings.bodyTemplate ||
			(settings.bodyTemplate.indexOf(this._placeHolder) < 0);
	}

	testDisabled = (): boolean => {
		return !this.state.settings ||
			!this.state.settings.settings ||
			(this.state.settings.type === 'SMS' && this.invalidSMSSettings(this.state.settings.settings)) ||
			(this.state.settings.type === 'EMAIL' && this.invalidEmailSettings(this.state.settings.settings));
	}
	okDisabled = (): boolean => {
		return !this.state.dirty ||
			(this.state.settings.enabled && !this.state.validated) ||
			(this.state.settings.type === 'SMS' && this.invalidSMSSettings(this.state.settings.settings)) ||
			(this.state.settings.type === 'EMAIL' && this.invalidEmailSettings(this.state.settings.settings));
	}
	cancelDisabled = (): boolean => {
		return !this.state.dirty;
	}

	testSettings = () => {
		this.props.dispatch(Actions.TestTFASettings(this.state.settings));
	}

	saveSettings = () => {
		this.props.dispatch(Actions.SetTFASettingsSaga(this.state.settings));
	}
	resetSettings = () => {
		const settings = { ...this.props.tfaSettings };
		const detail = { ...this.props.tfaSettings.settings };
		let dirty = false;
		if (settings.type === 'EMAIL') {
			if (!detail.subject) {
				detail.subject = this.props.t('defaultSubject');
				dirty = true;
			}
			if (!detail.bodyTemplate) {
				detail.bodyTemplate = this.props.t('defaultBodyTemplate');
				dirty = true;
			}
		}
		settings.settings = detail;
		this.setState({ settings, dirty });
	}

	render() {
		const { busy, t, hasEmailSettings } = this.props;
		const scrollbarProps = {
			style: { width: '100%', height: '100%' },
			renderThumbVertical: props => <div {...props} style={{ width: '4px' }} className='thumb-vertical' />,
		};

		const dropdownItems = [
			{ value: 'SMS', text: t('SMS') },
			{ value: 'EMAIL', text: t('EMAIL') }
		];

		return <Scrollbars {...scrollbarProps}>
			<div className='tfa-settings'>
				<div className='setting-items'>
					<div className='setting-item'>
						<div className='setting-label'>
							<span className='label-text-wrapper'>
								<span className='label-text' title={t('EnableTFA')}>
									{t('EnableTFA')}
								</span>
								<AriaIcon
									className='ml-1 mdi mdi-help-circle-outline'
									title={t('EnableTFADescription')}
								/>
							</span>
						</div>
						<div className='setting-value'>
							<BooleanEditor
								value={(this.state.settings && this.state.settings.enabled) ? 'true' : 'false'}
								trueLabel={t('Yes')}
								falseLabel={t('No')}
								onChange={(value) => {
									const settings = update(this.state.settings, { enabled: { $set: value } });
									this.setState({ settings, dirty: true, validated: false });
								}}
							/>
						</div>
					</div>
					{this.state.settings && this.state.settings.enabled && (<>
						<div className='setting-item'>
							<div className='setting-label' title={t('Lifetime')}>{t('Lifetime')}</div>
							<div className='setting-value'>
								<NumberEditor
									minValue={1}
									maxValue={60}
									value={(this.state.settings && this.state.settings.lifetime) || 5}
									onChange={(value) => {
										const settings = update(this.state.settings, { lifetime: { $set: value } });
										this.setState({ settings, dirty: true });
									}}
								/>
							</div>
						</div>
						<div className='setting-item'>
							<div className='setting-label' title={t('TFAType')}>{t('TFAType')}</div>
							<div className='setting-value'>
								<DropdownEditor
									items={dropdownItems}
									text={(this.state.settings && this.state.settings.type === 'SMS') ? t('SMS') : t('EMAIL')}
									onChange={this.onTypeChanged}
								/>
							</div>
						</div>
						{this.state.settings.type === 'SMS' && (<>
							<div className='setting-item'>
								<div className='setting-label' title={t('accessKeyId')}>{t('accessKeyId')}</div>
								<div className='setting-value'>
									<InputEditor
										value={(this.state.settings && this.state.settings.settings && this.state.settings.settings.accessKeyId) || ''}
										invalid={!this.state.settings || !this.state.settings.settings || !this.state.settings.settings.accessKeyId}
										onEveryChange={(value) => {
											const sms = { ...this.state.settings.settings };
											sms.accessKeyId = value;
											const settings = update(this.state.settings, { settings: { $set: sms } });
											this.setState({ settings, dirty: true, validated: false });
										}}
									/>
								</div>
							</div>
							<div className='setting-item'>
								<div className='setting-label' title={t('accessKeySecret')}>{t('accessKeySecret')}</div>
								<div className='setting-value'>
									<InputEditor
										type='password'
										visibilityToggle
										showEyeTitle={t('ShowSecret')}
										hideEyeTitle={t('HideSecret')}
										value={(this.state.settings && this.state.settings.settings && this.state.settings.settings.accessKeySecret) || ''}
										invalid={!this.state.settings || !this.state.settings.settings || !this.state.settings.settings.accessKeySecret}
										onEveryChange={(value) => {
											const sms = { ...this.state.settings.settings };
											sms.accessKeySecret = value;
											const settings = update(this.state.settings, { settings: { $set: sms } });
											this.setState({ settings, dirty: true, validated: false });
										}}
									/>
								</div>
							</div>
							<div className='setting-item'>
								<div className='setting-label' title={t('signName')}>{t('signName')}</div>
								<div className='setting-value'>
									<InputEditor
										value={(this.state.settings && this.state.settings.settings && this.state.settings.settings.signName) || ''}
										invalid={!this.state.settings || !this.state.settings.settings || !this.state.settings.settings.signName}
										onEveryChange={(value) => {
											const sms = { ...this.state.settings.settings };
											sms.signName = value;
											const settings = update(this.state.settings, { settings: { $set: sms } });
											this.setState({ settings, dirty: true, validated: false });
										}}
									/>
								</div>
							</div>
							<div className='setting-item'>
								<div className='setting-label' title={t('templateCode')}>{t('templateCode')}</div>
								<div className='setting-value'>
									<InputEditor
										value={(this.state.settings && this.state.settings.settings && this.state.settings.settings.templateCode) || ''}
										invalid={!this.state.settings || !this.state.settings.settings || !this.state.settings.settings.templateCode}
										onEveryChange={(value) => {
											const sms = { ...this.state.settings.settings };
											sms.templateCode = value;
											const settings = update(this.state.settings, { settings: { $set: sms } });
											this.setState({ settings, dirty: true, validated: false });
										}}
									/>
								</div>
							</div>
						</>)}
						{this.state.settings.type === 'EMAIL' && (<>
							<div className='setting-item'>
								<div className='setting-label' title={t('subject')}>{t('subject')}</div>
								<div className='setting-value'>
									<InputEditor
										value={(this.state.settings && this.state.settings.settings && this.state.settings.settings.subject) || ''}
										invalid={!this.state.settings || !this.state.settings.settings || !this.state.settings.settings.subject}
										onEveryChange={(value) => {
											const email = { ...this.state.settings.settings };
											email.subject = value;
											const settings = update(this.state.settings, { settings: { $set: email } });
											this.setState({ settings, dirty: true, validated: false });
										}}
									/>
								</div>
							</div>
							<div className='setting-item'>
								<div className='setting-label'>
									<span className='label-text-wrapper'>
										<span className='label-text' title={t('bodyTemplate')}>
											{t('bodyTemplate')}
										</span>
										<AriaIcon
											className='ml-1 mdi mdi-help-circle-outline'
											title={t('BodyTemplateDescription')}
										/>
									</span>
								</div>
								<div className='setting-value'>
									<InputEditor
										multiline={true}
										value={(this.state.settings && this.state.settings.settings && this.state.settings.settings.bodyTemplate) || ''}
										invalid={!this.state.settings || !this.state.settings.settings || !this.state.settings.settings.bodyTemplate || this.state.settings.settings.bodyTemplate.indexOf(this._placeHolder) < 0}
										onEveryChange={(value) => {
											const email = { ...this.state.settings.settings };
											email.bodyTemplate = value;
											const settings = update(this.state.settings, { settings: { $set: email } });
											this.setState({ settings, dirty: true, validated: false });
										}}
									/>
									{(!this.state.settings || !this.state.settings.settings || !this.state.settings.settings.bodyTemplate || this.state.settings.settings.bodyTemplate.indexOf(this._placeHolder) < 0) && <div className='ef-error-message'>
										<AriaIcon className='mdi mdi-alert-circle-outline' />
										{t('BodyTemplateDescription')}
									</div>}
								</div>
							</div>
							{
								!hasEmailSettings && <div className='appsetting-configuration-tooltip'>
									<div className='appsetting-configuration-tooltip-text'>
										<i className='mdi mdi-alert' />
										<span title={t('NoEmailSettingsWarning')}>{t('NoEmailSettingsWarning')}</span>
									</div>
									<div className='link'><a href='/admin/configuration/notification/smtp' target='_blank' tabIndex={-1}>{t('Configure')}</a></div>
								</div>
							}
						</>)}
						<div className='setting-item'>
							<div className='setting-label' />
							<div className='setting-value'>
								<Button
									className='test-button'
									style='accent'
									inline={true}
									text={t('TestTFA')}
									title={t('TestTFA')}
									disabled={this.testDisabled()}
									onClick={this.testSettings}
								/>
							</div>
						</div>
					</>)}
				</div>
				<div className='settings-footer'>
					<Button
						style='accent'
						inline={true}
						text={t('Save')}
						title={t('Save')}
						disabled={this.okDisabled()}
						icon='mdi mdi-content-save'
						onClick={this.saveSettings}
					/>
					<Button
						inline={true}
						text={t('Cancel')}
						title={t('Cancel')}
						disabled={this.cancelDisabled()}
						icon='mdi mdi-cancel'
						onClick={this.resetSettings}
					/>
				</div>
			</div>
			{busy && <BlockLoader />}
		</Scrollbars >;
	}
}

export default translate('account', { withRef: true })(connect(state => state['account-management'].tfa)(TwoFactorAuthentication));
