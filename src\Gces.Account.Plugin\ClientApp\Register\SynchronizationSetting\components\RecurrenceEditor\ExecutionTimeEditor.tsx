import { DateTimeEditor } from 'gces-ui';
import * as moment from 'moment';
import * as React from 'react';
export type ExecutionTimeEditorProps = {
    time: moment.Moment | string;
    title: string;
    invalid: boolean;
    timeFormat: string;
    onTimeChange: (value: any) => void;
};
export class ExecutionTimeEditor extends React.PureComponent<ExecutionTimeEditorProps> {
    onTimeChange = (value: any) => {
        const { onTimeChange } = this.props;
        onTimeChange && onTimeChange(value);
    }
    public render() {
        const { time, title, invalid, timeFormat } = this.props;
        return (
            <div className='execution-time-editor'>
                <span className='execution-time-editor-title'>{title}</span>
                <DateTimeEditor
                    placeholder={timeFormat}
                    inverted={window.inverted}
                    invalid={invalid}
                    value={time}
                    onBlur={this.onTimeChange}
                    onChange={null}
                    mode='time'
                    timeFormat={timeFormat}
                />
            </div>
        );
    }
}
