﻿import ActionTypes from './../actions/action-types'
import update from 'immutability-helper'

const UserMappingReducer = (state = {}, action) => {
    switch (action.type) {
        case ActionTypes.SetUserMappings:
            {
                var mappings = { [action.providerName]: action.userMappings }
                let newState = update(state, { userMappings: { $set: mappings } })
                return newState
            }

        default:
            return state
    }
}

export default UserMappingReducer
