import * as moment from 'moment';

export const getTimeZoneDisplay = (timezoneId: string, t: any) => {
	const serverTimeZones = window.ServerTimeZones;
	const tz = timezoneId
		? serverTimeZones.find(s => s.id === timezoneId)
		: serverTimeZones.find(s => s.ianaId === Intl.DateTimeFormat().resolvedOptions().timeZone);

	if (!tz) {
		return timezoneId;
	}

	return getTimeZoneText(tz.ianaId, tz.baseOffset, t);
};
export const getTimeZoneText = (ianaId: string, baseOffset: string, t: any) => {
	const resourceKey = `timezone_${ianaId}`;
	const display = t(resourceKey);

	return display !== resourceKey ? display : `(UTC${baseOffset.startsWith('-') ? '' : '+'}${baseOffset}) ${ianaId}`;
};
export const changeToTimezoneUTCStr = (time: moment.Moment, timezoneId: string): string => {
	const selectTZOffset = window.ServerTimeZones.find(p => p.id === timezoneId)?.baseOffset;
	if (!selectTZOffset) {
		return time.second(0).toISOString();
	}
	const localTZOffset = new Date().getTimezoneOffset();
	const [hours, minutes] = selectTZOffset.split(':').map(Number);
	const totalMinutes = hours * 60 + minutes;
	return time.clone().subtract(localTZOffset + totalMinutes, 'minutes').second(0).toISOString();
};
export const changeToTimezoneTime = (timeStr: string, timezoneId: string): moment.Moment => {
	const time = moment(timeStr);
	const selectTZOffset = window.ServerTimeZones.find(p => p.id === timezoneId)?.baseOffset;
	if (!selectTZOffset) {
		return time;
	}
	const localTZOffset = new Date().getTimezoneOffset();
	const [hours, minutes] = selectTZOffset.split(':').map(Number);
	const totalMinutes = hours * 60 + minutes;
	const timezoneTime = time.clone().add(localTZOffset + totalMinutes, 'minutes').second(0);
	return timezoneTime;
};

export const getDefaultTimeZoneId = (): string => {
	const serverTimeZones = window.ServerTimeZones;
	if (!serverTimeZones) return '';

	let timeZoneInfo = serverTimeZones.find(s => s.ianaId === Intl.DateTimeFormat().resolvedOptions().timeZone);
	if (!timeZoneInfo) {
		let currentOffset = moment().format('Z');
		currentOffset = currentOffset.startsWith('+') ? currentOffset.substring(1) : currentOffset;
		timeZoneInfo = serverTimeZones.find(s => s.baseOffset === currentOffset);
	}
	if (!timeZoneInfo) {
		if (!serverTimeZones.length) {
			return '';
		}
		timeZoneInfo = serverTimeZones[0];
	}

	return timeZoneInfo.id;
};