﻿import { Button, InputEditor } from 'gces-ui';
import * as React from 'react';
import * as classnames from 'classnames';
import { isFloat, isInteger } from '../util';

export interface CNumberPickerProps {
	value: string;
	onChange?: (value: string) => void;
	onEveryChange?: (value: string) => void;
	className?: string;
	placeholder?: string;
	disabled?: boolean;
	maxLength?: number;
	invalid?: boolean;
	visibilityToggle?: boolean;
	noVisibilityToggle?: boolean;
	showEyeTitle?: string;
	hideEyeTitle?: string;
	type?: 'integer' | 'float';
}

interface LocalState {
	value: string;
	showEncryptedText: boolean;
	invalid: boolean;
}

export class CNumberPicker extends React.PureComponent<CNumberPickerProps, LocalState> {
	constructor(props, context) {
		super(props, context);
		this.state = { value: props.value, showEncryptedText: false, invalid: false };
	}

	componentWillReceiveProps(newProps: CNumberPickerProps) {
		const value = newProps.value;
		this.setState({ value, invalid: !this.validateValue(value) });
	}

	validateValue = (value: string) => {
		return !value || (this.props.type === 'float' ? isFloat(value) : isInteger(value));
	}

	onValueChange = (newValue: string) => {
		const { onChange } = this.props;
		if (onChange) onChange(newValue);
	}
	onEveryValueChange = (newValue: string) => {
		const { value } = this.state;
		if (value !== newValue) {
			this.setState({ value: newValue, invalid: !this.validateValue(value) });
			const { onEveryChange } = this.props;
			if (onEveryChange) onEveryChange(newValue);
		}
	}

	onBlur = () => {
		const { onChange } = this.props;
		let { value } = this.state;
		if (value === '') value = null;

		if (onChange) onChange(value);
	}

	toggleShowEncryptedText = () => {
		this.setState({ showEncryptedText: !this.state.showEncryptedText });
	}

	render() {
		const { className, placeholder, disabled, maxLength, invalid, visibilityToggle, noVisibilityToggle, hideEyeTitle, showEyeTitle } = this.props;
		let { value } = this.state;
		if (value === null) value = '';

		return (
			<div className={classnames(className, 'efc-sensitive-wrapper', { 'efc-input-password': visibilityToggle })}>
				<InputEditor
					className={classnames('efc-textbox', 'efc-number-textbox', { 'efc-invalid': invalid || this.state.invalid }, { 'efc-disabled': this.props.disabled }, { 'efc-text-security': visibilityToggle && !this.state.showEncryptedText })}
					maxLength={maxLength}
					invalid={invalid}
					placeholder={placeholder}
					value={value}
					onEveryChange={v => this.onEveryValueChange(v)}
					onChange={v => this.onValueChange(v)}
					type='text'
					disabled={disabled}
				/>
				<Button
					className={classnames('efc-input-visible-toggle', { 'efc-input-visible-hidden': !visibilityToggle }, { 'efc-input-visible-none': noVisibilityToggle })}
					icon={this.state.showEncryptedText ? 'mdi mdi-eye' : 'mdi mdi-eye-off'}
					size='small'
					style='transparent'
					rounded
					onClick={this.toggleShowEncryptedText}
					title={this.state.showEncryptedText ? hideEyeTitle : showEyeTitle}
				/>
			</div>
		);
	}
}