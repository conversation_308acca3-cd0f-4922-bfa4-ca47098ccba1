import { delay } from 'redux-saga';
import { sendRequestV2 } from '.';
import { syncSettingActionCreator } from '../store/action';
import { TaskStatusViewModel } from '../store/viewModel';

const execute = async () => {
	const { result } = await sendRequestV2('/api/v2/admin/account/sync/status');
	return result;
};

let heartbeatSwitch: boolean = false;
let isRunning: boolean = true;
let isScheduled: boolean = false;
const start = async () => {
	if (heartbeatSwitch) return;
	heartbeatSwitch = true;
	while (heartbeatSwitch) {
		if (isRunning || isScheduled) {
			const status: TaskStatusViewModel = await execute();
			window.AdminPortal.dispatch(syncSettingActionCreator.updateTaskStatus(status));
		}
		await delay(2000);
	}
};

const getIsRunning = () => {
	return isRunning;
};

const setIsRunning = (status: boolean) => {
	isRunning = status;
};

const setIsScheduled = (status: boolean) => {
	isScheduled = status;
};

const stop = () => {
	heartbeatSwitch = false;
};

export const heartbeat = {
	start,
	stop,
	setIsRunning,
	setIsScheduled,
	getIsRunning
};