import { SearchBox, Button } from 'gces-ui';
import * as React from 'react';
import * as classnames from 'classnames';

interface ColumnHeaderWithInputSearchProps {
	title: string;
	searchValue: string;
	onSearchChanged: (searchText: string) => void;
	placeholder?: string;
}

interface OwnState {
	searching: boolean;
}

export class ColumnHeaderWithInputSearch extends React.PureComponent<ColumnHeaderWithInputSearchProps, OwnState> {
	state: OwnState = {
		searching: false
	};

	onSearch = (val: string) => {
		const { onSearchChanged } = this.props;
		onSearchChanged && onSearchChanged(val);
	}

	render() {
		const { searching } = this.state;
		const { title, searchValue } = this.props;

		return (
			<div className='column-header-with-input-search'>
				<div className='column-header-title' title={title}>
					{title}
				</div>
				<div className={classnames('column-header-filter', { 'un-filtering': !searching }, { 'filtering': searching })}>
					<SearchBox
						className='header-filter-input-box'
						size='small'
						value={searchValue}
						onChange={this.onSearch}
						onClear={() => this.setState({ searching: false })}
						displayClearButton
						withClearButton
					/>
					{!searching && <Button
						className='start-search-btn'
						size='small'
						style='transparent'
						icon='mdi mdi-magnify'
						onClick={() => this.setState({ searching: true })}
						rounded
					/>}
				</div>
			</div>
		);
	}
}