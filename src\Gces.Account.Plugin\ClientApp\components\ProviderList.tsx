﻿import * as React from 'react';
import { translate } from 'react-i18next';
import { Dropdown, Button } from 'gces-ui';
import { Provider } from '../interfaces';

interface ProviderListProps {
	providers: Provider[];
	titleProp: string;
	nameProp: string;
	selectedProvider: Provider;
	onSelect: (value) => void;
	onAdd?: () => void;
	dropdownBuilder?: any;
	t?: any;
}
class ProviderList extends React.Component<ProviderListProps> {
	constructor(props, context) {
		super(props, context);
	}

	_builtInProviders = [
		'local',
		'AD Security Provider',
		'WeChat4Work',
		'DingTalk'
	];

	isBuiltInProvider = (providerName: string) => this._builtInProviders.indexOf(providerName) > -1;

	renderProviderItem = (provider: Provider) => {
		const { titleProp, nameProp, dropdownBuilder, onSelect, selectedProvider, t } = this.props;
		const dropdownProps = dropdownBuilder ? dropdownBuilder(provider) : null;
		const selected = provider[nameProp] === selectedProvider[nameProp] ? 'selected' : '';
		const providerName = provider[nameProp];
		const translatedProviderName = this.isBuiltInProvider(providerName) ? t(`account_provider_${providerName}`) : providerName;
		const translatedProviderTitle = this.isBuiltInProvider(providerName) ? t(`account_provider_${providerName}_description`) : (provider[titleProp] || providerName);

		return <div
			key={provider[nameProp]}
			className={`provider-row ${selected}`}
			onClick={() => onSelect(provider)}
		>
			<div className='td-provider-name' title={translatedProviderTitle}>
				<span className='provider-name'> {translatedProviderName} </span>
			</div>

			{dropdownProps && dropdownProps.items.length !== 0 && <div className='td-provider-actions'>  <Dropdown {...dropdownProps} /> </div>}
		</div>;
	}

	render() {
		const { providers, onAdd, t } = this.props;

		return <div className='provider-list'>
			{onAdd && <div className='provider-list-header'> <Button title={t('AddProvider')} text={t('AddProvider')} style='accent' icon='mdi mdi-plus' onClick={onAdd} /></div>}

			<div className='provider-list-body'> {providers && providers.map(p => this.renderProviderItem(p))} </div>
		</div>;
	}
}

export default translate('account', { withRef: true })(ProviderList);
