.claim-mappings-container {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;

	.claim-mappings-list {
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100%;

		.btn-add-claim {
			display: flex;
			justify-content: flex-end;
			margin-bottom: 17px;
		}
	}

	.new-claim-mapping-container {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;

		.setting-items {
			.setting-item {
				font-size: 13px;
				margin-bottom: 5px;
				width: 100%;

				>label {
					width: 100%;

					@include gces-truncate;
				}
			}
		}

		.footer {
			display: flex;
			flex-direction: row;
			justify-content: flex-end;

			button {
				margin-right: 4px;
			}

			.btn {
				>span {
					display: block;
					max-width: 100px;

					@include gces-truncate;
				}
			}
		}

		padding: 20px 2px;
	}
}