import { RoleConsts } from '../../../Register/Common';
import { Role } from '../store/interfaces';
import * as util from '../../../util';

export function* sendRequestV2(url: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET', data: any = null) {
	return yield util.sendRequestV2(url, method, data, 'role');
}

export const isAdministrator = (roles: string[] = []) => {
	return roles.some(role => RoleConsts.admin.name === role.toLowerCase());
};

export const getRole = (selectedOrganizationId: string, roles: Role[], selectedRoleId: string) => {
	let role = selectedOrganizationId + '$' + roles.filter(item => item.id === selectedRoleId)[0].name;
	if (selectedOrganizationId === 'root') {
		role = roles.filter(item => item.id === selectedRoleId)[0].name;
	}
	return role;
};