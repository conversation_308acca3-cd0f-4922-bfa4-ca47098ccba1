.license-dialog {
	&.cn-license-dialog {
		.modal-content {
			width: 600px !important;
		}
	}

	.modal-header {
		.header-inner {
			height: 100%;
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;

			label {
				margin: 0;
			}

			.close-btn {
				color: $text-contrast;
			}
		}
	}

	.modal-body {
		transition: none;
		width: 100%;

		&,
		* {
			animation: none;
		}

		.label-top {
			font-size: $ef-font-size-sm;
			display: block;
			margin: 0 0 5px 0;
		}

		.activation-row {
			display: flex;
			align-items: center;
			width: 100%;
		}

		.license-key-container {
			margin: 0 0 18px 0;

			.key-input {
				font-weight: bold;
			}
		}

		.message-tip {
			background-color: rgba(246, 229, 25, 0.1);
			display: flex;
			align-items: center;
			padding: 8px;

			i {
				font-size: $ef-icon-18;
				color: $ef-accent;
				margin: 0 5px 0 0;
			}

			span {
				color: $ef-text;
				font-size: $ef-font-size-sm;
				white-space: unset;

				@include gces-truncate-2;
			}
		}

		.license-data-container {
			margin: 18px 0 0 0;

			.license-data-input {
				margin: 0 10px 0 0;
			}

			.get-offline-license-data-btn {
				min-width: 140px;
				max-width: 140px;
			}

			.offline-activation-code-container {
				margin: 10px 0 0 0;
				padding: 10px;
				background-color: var(--gces-content-bg-lt-5);
				border-radius: 5px;
				position: relative;

				.offline-activation-code {
					margin: 0 0 10px 0;

					.activation-input {
						background-color: transparent;
					}

					.copy-text,
					.show-qr {
						min-width: 100px;
						margin: 0 0 0 10px;
					}
				}

				.arrow-place-holder {
					background-color: var(--gces-content-bg-lt-5);
					position: absolute;
					top: -5px;
					right: 70px;
					width: 10px;
					height: 10px;
					transform: rotate(45deg);
				}
			}
		}

		.license-item-detail-content {
			display: flex;
			align-items: center;
			width: 100%;

			.detail-inner {
				width: 100%;

				.detail-item {
					display: flex;
					line-height: normal;

					.item-title {
						margin: 0 15px 0 0;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}

					.item-content {
						flex: 1;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}
				}
			}
		}
	}
}