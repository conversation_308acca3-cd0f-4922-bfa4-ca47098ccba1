import { Management } from './components/Management';
import { userLocaleData } from './localization';
import { userReducer, watchUser } from './store';

window.AdminPortal.Register.Locale(userLocaleData);
window.AdminPortal.Register.StoreExtension('user', userReducer, watchUser);
window.AdminPortal.Register.SectionItem('account-management-user', 'account-management', 'user', 'mdi mdi-account', 'User', 'User management.', true, Management, true, undefined, undefined, 3);