import { Organization, Role, UserInfo } from './interfaces';
import { Permission } from '../../Common/interfaces';

// reducer actions
export interface ResetStateAction { type: 'Portal/Role/ResetState'; }
export interface SetBusyAction { type: 'Portal/Role/SetBusy'; payload: { busy: boolean }; }
export interface SetAllUsersAction { type: 'Portal/Role/SetAllUsers'; payload: { allUsers: UserInfo[] }; }
export interface SetOrganizationsAction { type: 'Portal/Role/SetOrganizations'; payload: { organizations: Organization[] }; }
export interface SetSelectedOrganizationIdAction { type: 'Portal/Role/SetSelectedOrganizationId'; payload: { selectedOrganizationId: string }; }
export interface SetRolesAction { type: 'Portal/Role/SetRoles'; payload: { roles: Role[] }; }
export interface SetIsAddingRoleAction { type: 'Portal/Role/SetIsAddingRole'; payload: { isAddingRole: boolean }; }
export interface SetSelectedRoleIdAction { type: 'Portal/Role/SetSelectedRoleId'; payload: { selectedRoleId: string }; }
export interface SetUsersAction { type: 'Portal/Role/SetUsers'; payload: { users: UserInfo[] }; }
export interface SetPermissionsAction { type: 'Portal/Role/SetPermissions'; payload: { permissions: Permission[] }; }
export interface SetAvailablePermissionsAction { type: 'Portal/Role/SetAvailablePermissions'; payload: { permissions: Permission[] }; }
export interface SetIsAddingMembersAction { type: 'Portal/Role/SetIsAddingMembers'; payload: { isAddingMembers: boolean }; }
export interface AlertMessageAction { type: 'Portal/Role/AlertMessages'; payload: { messages: string[] }; }
export interface SetDocumentColumnsAction { type: 'Portal/Role/RoleDocumentColumn'; payload: { documentColumns: string[] }; }
export interface SetOriginalDocumentColumnsAction { type: 'Portal/Role/OriginalRoleDocumentColumn'; payload: { originalDocumentColumns: string[] }; }
export interface SetEnableStrictPermissionManagementAction { type: 'Portal/Role/EnableStrictPermissionManagement'; payload: { enableStrictPermissionManagement: boolean }; }

const resetState = (): ResetStateAction => ({ type: 'Portal/Role/ResetState' });
const setBusy = (busy: boolean): SetBusyAction => ({ type: 'Portal/Role/SetBusy', payload: { busy } });
const setAllUsers = (allUsers: UserInfo[]): SetAllUsersAction => ({ type: 'Portal/Role/SetAllUsers', payload: { allUsers } });
const setOrganizations = (organizations: Organization[]): SetOrganizationsAction => ({ type: 'Portal/Role/SetOrganizations', payload: { organizations } });
const setSelectedOrganizationId = (selectedOrganizationId: string): SetSelectedOrganizationIdAction => ({ type: 'Portal/Role/SetSelectedOrganizationId', payload: { selectedOrganizationId } });
const setRoles = (roles: Role[]): SetRolesAction => ({ type: 'Portal/Role/SetRoles', payload: { roles } });
const setIsAddingRole = (isAddingRole: boolean): SetIsAddingRoleAction => ({ type: 'Portal/Role/SetIsAddingRole', payload: { isAddingRole } });
const setSelectedRoleId = (selectedRoleId: string): SetSelectedRoleIdAction => ({ type: 'Portal/Role/SetSelectedRoleId', payload: { selectedRoleId } });
const setUsers = (users: UserInfo[]): SetUsersAction => ({ type: 'Portal/Role/SetUsers', payload: { users } });
const setPermissions = (permissions: Permission[]): SetPermissionsAction => ({ type: 'Portal/Role/SetPermissions', payload: { permissions } });
const setAvailablePermissions = (permissions: Permission[]): SetAvailablePermissionsAction => ({ type: 'Portal/Role/SetAvailablePermissions', payload: { permissions } });
const setIsAddingMembers = (isAddingMembers: boolean): SetIsAddingMembersAction => ({ type: 'Portal/Role/SetIsAddingMembers', payload: { isAddingMembers } });
const alertMessage = (messages: string[]): AlertMessageAction => ({ type: 'Portal/Role/AlertMessages', payload: { messages } });
const setDocumentColumns = (documentColumns: string[]): SetDocumentColumnsAction => ({ type: 'Portal/Role/RoleDocumentColumn', payload: { documentColumns } });
const setOriginalDocumentColumns = (originalDocumentColumns: string[]): SetOriginalDocumentColumnsAction => ({ type: 'Portal/Role/OriginalRoleDocumentColumn', payload: { originalDocumentColumns } });
const setEnableStrictPermissionManagement = (enableStrictPermissionManagement: boolean): SetEnableStrictPermissionManagementAction => ({ type: 'Portal/Role/EnableStrictPermissionManagement', payload: { enableStrictPermissionManagement } });

export type RoleReducerActions = ResetStateAction | SetBusyAction | SetAllUsersAction | SetOrganizationsAction | SetSelectedOrganizationIdAction
	| SetRolesAction | SetIsAddingRoleAction | SetSelectedRoleIdAction | SetUsersAction | SetPermissionsAction | SetAvailablePermissionsAction
	| SetIsAddingMembersAction | AlertMessageAction | SetDocumentColumnsAction | SetOriginalDocumentColumnsAction | SetEnableStrictPermissionManagementAction;

// saga actions
export interface InitAction { type: 'Portal/Role/Init'; }
export interface SelectOrgAction { type: 'Portal/Role/SelectOrg'; payload: { selectedOrganizationId: string, enableStrictPermissionManagement: boolean }; }
export interface AddRoleAction { type: 'Portal/Role/AddRole'; payload: { name: string, orgId: string, enableStrictPermissionManagement: boolean }; }
export interface DeleteRoleAction { type: 'Portal/Role/DeleteRole'; payload: { orgId: string, roleId: string, roleName: string, enableStrictPermissionManagement: boolean }; }
export interface SelectRoleAction { type: 'Portal/Role/SelectRole'; payload: { orgId: string, roleId: string, enableStrictPermissionManagement: boolean }; }
export interface UpdateRolePermissionsAction { type: 'Portal/Role/UpdateRolePermissions'; payload: { orgId: string, roleId: string, permissions: string[] }; }
export interface UpdateRoleUsersAction { type: 'Portal/Role/UpdateRoleUsers'; payload: { orgId: string, roleId: string, userIds: string[], enableStrictPermissionManagement: boolean }; }
export interface DeleteRoleUserAction { type: 'Portal/Role/DeleteRoleUser'; payload: { orgId: string, roleId: string, userId: string, enableStrictPermissionManagement: boolean }; }
export interface GetDocumentColumnsAction { type: 'Portal/Role/GetRoleDocumentColumn'; payload: { role: string }; }
export interface UpdateDocumentColumnsAction { type: 'Portal/Role/UpdateRoleDocumentColumn'; payload: { role: string, documentColumns: string[] }; }
export interface UpdateRoleNameAction { type: 'Portal/Role/UpdateRoleName'; payload: { orgId: string, roleId: string, displayName: string, enableStrictPermissionManagement: boolean, callback: (success: boolean) => void }; }

const init = (): InitAction => ({ type: 'Portal/Role/Init' });
const selectOrg = (selectedOrganizationId: string, enableStrictPermissionManagement: boolean): SelectOrgAction => ({ type: 'Portal/Role/SelectOrg', payload: { selectedOrganizationId, enableStrictPermissionManagement } });
const addRole = (name: string, orgId: string, enableStrictPermissionManagement: boolean): AddRoleAction => ({ type: 'Portal/Role/AddRole', payload: { name, orgId, enableStrictPermissionManagement } });
const deleteRole = (orgId: string, roleId: string, roleName: string, enableStrictPermissionManagement: boolean): DeleteRoleAction => ({ type: 'Portal/Role/DeleteRole', payload: { orgId, roleId, roleName, enableStrictPermissionManagement } });
const selectRole = (orgId: string, roleId: string, enableStrictPermissionManagement: boolean): SelectRoleAction => ({ type: 'Portal/Role/SelectRole', payload: { orgId, roleId, enableStrictPermissionManagement } });
const updateRolePermissions = (orgId: string, roleId: string, permissions: string[]): UpdateRolePermissionsAction => ({ type: 'Portal/Role/UpdateRolePermissions', payload: { orgId, roleId, permissions } });
const updateRoleUsers = (orgId: string, roleId: string, userIds: string[], enableStrictPermissionManagement: boolean): UpdateRoleUsersAction => ({ type: 'Portal/Role/UpdateRoleUsers', payload: { orgId, roleId, userIds, enableStrictPermissionManagement } });
const deleteRoleUser = (orgId: string, roleId: string, userId: string, enableStrictPermissionManagement: boolean): DeleteRoleUserAction => ({ type: 'Portal/Role/DeleteRoleUser', payload: { orgId, roleId, userId, enableStrictPermissionManagement } });
const getDocumentColumns = (role: string): GetDocumentColumnsAction => ({ type: 'Portal/Role/GetRoleDocumentColumn', payload: { role } });
const updateDocumentColumns = (role: string, documentColumns: string[]): UpdateDocumentColumnsAction => ({ type: 'Portal/Role/UpdateRoleDocumentColumn', payload: { role, documentColumns } });
const updateRoleName = (orgId: string, roleId: string, displayName: string, enableStrictPermissionManagement: boolean, callback: (success: boolean) => void): UpdateRoleNameAction => ({ type: 'Portal/Role/UpdateRoleName', payload: { orgId, roleId, displayName, enableStrictPermissionManagement, callback } });

export const SagaActionTypes = {
	Init: 'Portal/Role/Init',
	SelectOrg: 'Portal/Role/SelectOrg',
	AddRole: 'Portal/Role/AddRole',
	DeleteRole: 'Portal/Role/DeleteRole',
	SelectRole: 'Portal/Role/SelectRole',
	UpdateRolePermissions: 'Portal/Role/UpdateRolePermissions',
	UpdateRoleUsers: 'Portal/Role/UpdateRoleUsers',
	DeleteRoleUser: 'Portal/Role/DeleteRoleUser',
	GetRoleDocumentColumn: 'Portal/Role/GetRoleDocumentColumn',
	UpdateRoleDocumentColumn: 'Portal/Role/UpdateRoleDocumentColumn',
	GetRoleControlColumnConfig: 'Portal/Role/GetRoleControlColumnConfig',
	GetAvailablePermissions: 'Portal/Role/GetAvailablePermissions',
	UpdateRoleName: 'Portal/Role/UpdateRoleName',
};

// actions creators
export const roleActionCreators = {
	// saga actions
	init,
	selectOrg,
	addRole,
	deleteRole,
	selectRole,
	updateRolePermissions,
	updateRoleUsers,
	deleteRoleUser,
	getDocumentColumns,
	updateDocumentColumns,
	updateRoleName,

	// reducer actions
	resetState,
	setBusy,
	setAllUsers,
	setOrganizations,
	setSelectedOrganizationId,
	setRoles,
	setIsAddingRole,
	setSelectedRoleId,
	setUsers,
	setPermissions,
	setAvailablePermissions,
	setIsAddingMembers,
	alertMessage,
	setDocumentColumns,
	setOriginalDocumentColumns,
	setEnableStrictPermissionManagement,
};