﻿import ActionTypes from './../actions/action-types'
import update from 'immutability-helper'

export default (state = { users: [], roles: [], properties: [], securitySettings: {} }, action) => {
	switch (action.type) {
		case ActionTypes.SetUsers:
			return update(state, { users: { $set: action.users } })

		case ActionTypes.SetRoles:
			return update(state, { roles: { $set: action.roles } })

		case ActionTypes.SetProperties:
			return update(state, { properties: { $set: action.properties } })

		default:
			return state
	}
}
