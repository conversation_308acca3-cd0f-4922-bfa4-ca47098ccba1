import * as React from 'react';
import * as classnames from 'classnames';
import { Button, Dropdown, DropdownItemProps } from 'gces-ui';

export interface CDropdownEditorProps {
	items: DropdownItemProps[];
	text: string;
	onChange: (values: any) => void;
	aid?: string;
	invalid?: boolean;
	className?: string;
	visibilityToggle?: boolean;
	noVisibilityToggle?: boolean;
	noSelectedItem?: boolean;
	showEyeTitle?: string;
	hideEyeTitle?: string;
}

interface LocalState {
	showEncryptedText: boolean;
}

export class CDropdownEditor extends React.PureComponent<CDropdownEditorProps, LocalState> {

	constructor(props: CDropdownEditorProps, context) {
		super(props, context);

		this.state = {
			showEncryptedText: false,
		};
	}

	toggleShowEncryptedText = () => {
		this.setState({ showEncryptedText: !this.state.showEncryptedText });
	}

	render() {
		const { items, invalid, text, className, visibilityToggle, noVisibilityToggle, noSelectedItem, hideEyeTitle, showEyeTitle, onChange } = this.props;

		const disabled = visibilityToggle && !this.state.showEncryptedText;
		const enableTextSecurity = !noSelectedItem && disabled;

		return (
			<div className={classnames(className, 'efc-sensitive-wrapper', { 'efc-input-password': visibilityToggle })}>
				<Dropdown
					className={classnames('efc-dropdown', { 'efc-text-security': enableTextSecurity })}
					items={items}
					disabled={disabled}
					onSelect={onChange}
					offset={true}
					width='100%'
					menuWidth='100%'
					style='default'
					size='small'
					text={text}
					title={disabled ? ' ' : text}
					textAlign='left'
					invalid={invalid}
				/>
				<Button
					className={classnames('efc-input-visible-toggle', { 'efc-input-visible-hidden': !visibilityToggle }, { 'efc-input-visible-none': noVisibilityToggle })}
					icon={this.state.showEncryptedText ? 'mdi mdi-eye' : 'mdi mdi-eye-off'}
					size='small'
					style='transparent'
					rounded
					onClick={this.toggleShowEncryptedText}
					title={this.state.showEncryptedText ? hideEyeTitle : showEyeTitle}
				/>
			</div>
		);
	}
}