import CGrid, { CGridProps } from 'gces-react-grid';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>oader, Divider } from 'gces-ui';
import * as React from 'react';
import { translate } from 'react-i18next';
import { connect } from 'react-redux';
import { noDocument } from '../resources/svgIcon';
import { HistoryTaskState, SynchronizationView, UserSyncProcess } from '../store/interface';
import { ColumnHeaderWithDropdownFilter } from './GridHeaderComps/ColumnHeaderWithDropdownFilter';
import { ColumnHeaderWithInputSearch } from './GridHeaderComps/ColumnHeaderWithInputSearch';
import { heartbeat } from '../utils/heartbeat';

export interface ConnectedProps {
	view: SynchronizationView;
	dispatch?: any;
	t?: any;
}

interface HistorySearch {
	name: string;
	unSelectedTypes: string[];
	unSelectedErrorLevels: string[];
}

interface ViewState {
	recordsFolded: boolean;
	detailsFolded: boolean;
	search: HistorySearch;
}

@translate('synchronization', { wait: true })
class ViewBase extends React.PureComponent<ConnectedProps, ViewState> {

	state: ViewState = {
		recordsFolded: false,
		detailsFolded: false,
		search: {
			name: '',
			unSelectedTypes: [],
			unSelectedErrorLevels: [],
		},
	};

	foldRecords = () => {
		this.setState({ recordsFolded: !this.state.recordsFolded });
	}

	foldDetails = () => {
		this.setState({ detailsFolded: !this.state.detailsFolded });
	}

	renderRunning = (process: UserSyncProcess) => {
		const { t } = this.props;
		const calculateClass = (process: UserSyncProcess, current: number) => {
			if (current < process) return 'done';
			else if (current === process) return 'active';
			else return 'inactive';
		};
		return (
			<React.Fragment>
				<div className='progress-bar'>
					<div className={`progress-item ${calculateClass(process, 0)}`} />
					<div className={`progress-item ${calculateClass(process, 2)}`} />
					<div className={`progress-item ${calculateClass(process, 2.5)}`} />
					<div className={`progress-item ${calculateClass(process, 3)}`} />
					<div className={`progress-item ${calculateClass(process, 3.5)}`} />
					<div className={`progress-item ${calculateClass(process, 4)}`} />
				</div>
				<div className='progress-tip'>
					<span>{t('synchronizing')}</span>
				</div>
			</React.Fragment>
		);
	}

	renderLastTaskResult = () => {
		const { view: { history: { info: { state, runTime }, errors } }, t } = this.props;
		let syncedMessage = '', iconClass = '';
		switch (state) {
			case HistoryTaskState.Success:
				syncedMessage = t('syncedMessage!success');
				iconClass = 'mdi-check-circle';
				break;
			case HistoryTaskState.NotAllSuccess:
				syncedMessage = t('syncedMessage!notAllSuccess');
				iconClass = 'mdi-alert-circle';
				break;
			case HistoryTaskState.Canceled:
				syncedMessage = errors.join(';') || t('syncedMessage!canceled');
				iconClass = 'mdi-alert';
				break;
			case HistoryTaskState.Failed:
				syncedMessage = errors.join(';') || t('syncedMessage!syncedMessage');
				iconClass = 'mdi-alert';
				break;
		}
		return (
			<React.Fragment>
				<AriaIcon className={`last-task-result-icon mdi ${iconClass}`} />
				<div className='synced-time'>{`${t('syncedTime')}: ${runTime.format('LLL')}`}</div>
				<div className='synced-message'>{syncedMessage}</div>
			</React.Fragment>
		);
	}

	renderSyncResults = () => {
		const { view: { taskStatus: { process, nextRunTime }, history }, t } = this.props;
		const noHistory = !history;
		const running = process === UserSyncProcess.Validating || process === UserSyncProcess.FetchingData || process === UserSyncProcess.Synchronizing;
		const showEmptyIcon = noHistory && !running;
		const showLastTaskInfo = !noHistory && !running;
		return (
			<div className={`synchronization-results  ${noHistory ? 'full' : ''}`}>
				<div className='synchronize-settings-header'>
					<span className='synchronize-settings-title'>{t('syncResults')}</span>
					<Divider className='synchronize-settings-header-divider' />
					<span className='synchronize-settings-header-tip'>{t('nextRunTime')}: {nextRunTime ? nextRunTime.format('LLL') : t('NoAutoSyncTask')}</span>
				</div>
				<div className={'view-results-content'}>
					{
						showEmptyIcon &&
						<div className='no-record-tip'>
							{noDocument}
							<span>{t('noRecordTip')}</span>
						</div>
					}
					{running && this.renderRunning(process)}
					{showLastTaskInfo && this.renderLastTaskResult()}
				</div>
			</div >
		);
	}

	renderSyncRecords = () => {
		const { view: { history }, t } = this.props;
		const { recordsFolded } = this.state;
		if (!history) return null;
		const { records } = history;
		const gridProps: CGridProps = {
			columns: [
				{ key: 'entity', label: t('entity') },
				{ key: 'success', label: t('successfulQuantity') },
				{ key: 'failure', label: t('failedQuantity') },
				{ key: 'warning', label: t('warningQuantity') },
			],
			rows: [
				{ entity: t('user'), success: records.user.success, failure: records.user.failure, warning: records.user.warning },
				{ entity: t('organization'), success: records.organization.success, failure: records.organization.failure, warning: records.organization.warning },
				{ entity: t('role'), success: records.role.success, failure: records.role.failure, warning: records.role.warning },
				{ entity: t('userRoleRelation'), success: records.userRoleRelation.success, failure: records.userRoleRelation.failure, warning: records.userRoleRelation.warning },
				{ entity: t('userOrganization'), success: records.userOrganization.success, failure: records.userOrganization.failure, warning: records.userOrganization.warning },
			],
			columnResizing: true,
			rowHeight: 40,
			hideGridLine: true,
			useInterlacedColor: true,
			bodyClassName: 'dl-body',
			colClassName: 'dl-header',
			autoHide: true,
			useSmallScrollbars: false,
		};
		return (
			<div className='synchronization-records'>
				<div className='synchronize-settings-header'>
					<span className='synchronize-settings-title'>{t('syncRecords')}</span>
					<Divider className='synchronize-settings-header-divider' />
					<div className='synchronize-settings-header-operation'>
						<span className={`mdi ${recordsFolded ? 'mdi-chevron-right' : 'mdi-chevron-down'}`} onClick={this.foldRecords} />
					</div>
				</div>
				{!recordsFolded &&
					<div className='view-records-content'>
						<CGrid {...gridProps} />
					</div>
				}
			</div>
		);
	}

	onRenderCell = (key: string, row: any) => {
		const { t } = this.props;
		if (key === 'reason') {
			const icon = row.level === 'Warning' ? 'mdi-alert-circle' : 'mdi-alert';
			return (
				<div className='not-sync-reason'>
					<AriaIcon className={`mdi ${icon}`} />
					<span title={row[key]}>{row[key]}</span>
				</div>
			);
		} else if (key === 'type') {
			return <span title={t(row[key])}>{t(row[key])}</span>;
		} else {
			return <span title={row[key]}>{row[key]}</span>;
		}
	}

	setFilterName = (searchValue: string) => {
		this.setState({ search: { ...this.state.search, name: searchValue } });
	}
	setFilterEntityTypes = (types: string[]) => {
		this.setState({ search: { ...this.state.search, unSelectedTypes: types } });
	}

	setFilterErrorLevels = (errLevels: string[]) => {
		this.setState({ search: { ...this.state.search, unSelectedErrorLevels: errLevels } });
	}

	renderInformationNotSynced = () => {
		const { view: { history }, t } = this.props;
		const { detailsFolded, search } = this.state;
		if (!history) return null;
		const { info: { state }, errorDetails } = history;

		if (state !== HistoryTaskState.NotAllSuccess) return null;
		let filteredErrors = errorDetails.filter(e => (search.unSelectedTypes.length === 0 || !search.unSelectedTypes.includes(e.type))
			&& (search.unSelectedErrorLevels.length === 0 || !search.unSelectedErrorLevels.includes(e.level)));
		if (!!search.name) {
			filteredErrors = filteredErrors.filter(e => e.name.toLowerCase().includes(search.name.toLowerCase()));
		}

		const entityTypes = [...new Set(errorDetails.map(e => e.type))];
		const entityTypeFilters = entityTypes.map(type => ({ name: t(type) as string, value: type, selected: !search.unSelectedTypes.includes(type) }));

		const errorLevels = [...new Set(errorDetails.map(e => e.level))];
		const errorLevelFilters = errorLevels.map(level => ({ name: t(level) as string, value: level, selected: !search.unSelectedErrorLevels.includes(level) }));

		const gridProps: CGridProps = {
			columns: [
				{
					key: 'type',
					minWidth: 120,
					component: <ColumnHeaderWithDropdownFilter title={t('type')} types={entityTypeFilters} onUnSelect={this.setFilterEntityTypes} />
				},
				{
					key: 'name',
					minWidth: 330,
					component: <ColumnHeaderWithInputSearch title={t('name')} searchValue={search.name} onSearchChanged={this.setFilterName} />
				},
				{
					key: 'reason',
					minWidth: 300,
					component: <ColumnHeaderWithDropdownFilter title={t('reason')} types={errorLevelFilters} onUnSelect={this.setFilterErrorLevels} />
				},
			],
			rows: filteredErrors,
			columnResizing: true,
			rowHeight: 40,
			hideGridLine: true,
			useInterlacedColor: true,
			bodyClassName: 'dl-body',
			colClassName: 'dl-header',
			autoHide: true,
			useSmallScrollbars: false,
			minHorizontalScrollWidth: 5,
			minVerticalScrollHeight: 5,
			autoFit: true,
			onRenderCell: this.onRenderCell,
		};
		return (
			<div className='synchronization-information-not-synced'>
				<div className='synchronize-settings-header'>
					<span className='synchronize-settings-title'>{t('informationNotSynced')}</span>
					<Divider className='synchronize-settings-header-divider' />
					<div className='synchronize-settings-header-operation'>
						<span className={`mdi ${detailsFolded ? 'mdi-chevron-right' : 'mdi-chevron-down'}`} onClick={this.foldDetails} />
					</div>
				</div>
				{!detailsFolded &&
					<div className='view-error-content'>
						<CGrid {...gridProps} />
					</div>
				}

			</div>
		);
	}

	render() {
		const isRunning = heartbeat.getIsRunning();
		return (
			<div className='synchronization-view'>
				{this.renderSyncResults()}
				{this.renderSyncRecords()}
				{this.renderInformationNotSynced()}
				{
					!isRunning && this.props.view.gettingHistory && <BlockLoader />
				}
			</div>
		);
	}
}

export const View = connect((state: any) => ({
	view: state.synchronization.syncView,
}))(ViewBase) as React.ComponentClass<{}>;