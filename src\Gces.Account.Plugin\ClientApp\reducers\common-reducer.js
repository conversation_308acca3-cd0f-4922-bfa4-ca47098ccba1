﻿import ActionTypes from './../actions/action-types'
import update from 'immutability-helper'

const defaultState = {
	isBusy: false,
}

export default (state = defaultState, action) => {
	switch (action.type) {
		case ActionTypes.StartLoading:
			return update(state, { isBusy: { $set: true } })
		case ActionTypes.EndLoading:
			return update(state, { isBusy: { $set: false } })
		default:
			return state
	}
}
