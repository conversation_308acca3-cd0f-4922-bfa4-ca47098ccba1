import * as classnames from 'classnames';
import cronstrue from 'gces-cronstrue/i18n';
import * as update from 'immutability-helper';
import * as moment from 'moment';
import * as React from 'react';
import { translate } from 'react-i18next';

import { AriaIcon, DropdownEditor } from 'gces-ui';
import { Checkbox } from 'gces-ui/lib/components/Checkbox';
import { Dropdown, DropdownItemProps } from 'gces-ui/lib/components/Dropdown';
import { Label } from 'gces-ui/lib/components/Label';
import { DateTimeEditor } from 'gces-ui/lib/components/editors/DateTime';
import { DailyEditor } from './DailyEditor';
import { MonthlyEditor } from './MonthlyEditor';
import { WeeklyEditor } from './WeeklyEditor';

import { DailyRepeatDetail, IntervalWeeklyRepeatDetail, MonthlyRepeatDetail, ScheduledInfo, WeeklyRepeatDetail } from '../../store/interface';
import { beautifyMinutes, convertToMomentValue, findIntervalWeeklyStartTime, generateCronExpression, getCronstrueLanguage, getValidDailyExecutionTimeRange, isNeedToRemoveExecutionTimeRange, parseCronAndHoursCronAndDailyExecutionTimeRange, startTimeMatchDaysOfWeek } from '../../utils';
import * as timeZoneUtils from '../../utils/timeZoneUtils';
import { IntervalWeeklyEditor } from './IntervalWeeklyEditor';

interface RecurrenceEditorProps {
	scheduleInfo: ScheduledInfo;
	updateScheduleInfo: (scheduleInfo: ScheduledInfo) => void;
	t?: any;
}

@translate('synchronization', { wait: true })
export class RecurrenceEditor extends React.Component<RecurrenceEditorProps> {
	_dataTimePlaceholder = window.AdminPortal.i18n.language === 'en' ? 'MM/DD/YYYY hh:mm A' : 'YYYY/MM/DD HH:mm';

	onEditorSelect = (value: string) => {
		const { scheduleInfo, updateScheduleInfo: updateScheduleInfo } = this.props;
		let { startDate, endDate } = scheduleInfo;

		let detail: DailyRepeatDetail | WeeklyRepeatDetail | MonthlyRepeatDetail | IntervalWeeklyRepeatDetail = null;
		if (value === 'Daily') {
			const dailyRepeatDetail: DailyRepeatDetail = {
				repeatType: 'Once',
				hoursRepeatInterval: 1,
				minutesRepeatInterval: 5,
				secondsRepeatInterval: 5,
				days: [2, 3, 4, 5, 6],
				dailyExecutionTimeRange: null,
			};

			detail = dailyRepeatDetail;
		}
		else if (value === 'Weekly') {
			const weeklyRepeatDetail: WeeklyRepeatDetail = { repeatInterval: 1 };

			detail = weeklyRepeatDetail;
		}
		else if (value === 'Monthly') {
			const monthlyRepeatDetail: MonthlyRepeatDetail = {
				repeatInterval: 1,
				type: 'OnDay',
				onDay: moment().date(),
				onTheLast: 'L',
				onTheFirst: '1',
			};

			detail = monthlyRepeatDetail;
		}
		else if (value === 'IntervalWeekly') {
			const days: number[] = [2, 3, 4, 5, 6];
			const intervalWeeklyRepeatDetail: IntervalWeeklyRepeatDetail = {
				weeksRepeatInterval: 1,
				days,
			};
			detail = intervalWeeklyRepeatDetail;
			startDate = findIntervalWeeklyStartTime(days);
			startDate.minute(beautifyMinutes(startDate.minute()));
			startDate.second(0);
			if (endDate?.isBefore(startDate, 'minute')) {
				endDate = startDate.clone().add(1, 'day');
			}
		}
		const newSchedule = update(scheduleInfo, {
			repeatType: { $set: value },
			detail: { $set: detail },
			startDate: { $set: startDate },
			endDate: { $set: endDate }
		});
		updateScheduleInfo(newSchedule);
	}

	onStartDateChange = (value: moment.Moment) => {
		const { scheduleInfo, scheduleInfo: { endDate }, updateScheduleInfo: updateScheduleInfo, t } = this.props;
		if (moment.isMoment(value) && startTimeMatchDaysOfWeek(value, scheduleInfo) && (!endDate || value.isSameOrBefore(endDate, 'minute'))) {
			let newSchedule = update(scheduleInfo, { startDate: { $set: value } });
			if (isNeedToRemoveExecutionTimeRange(newSchedule)) {
				window.AdminPortal.Notifications.Send(1, t('dailyExecutionTimeRangeNeedToBeRemove'), t('dailyTaskLessThan24HoursRemoveExecutionTimeRange'));
				newSchedule = update(newSchedule, { detail: { dailyExecutionTimeRange: { $set: null } } });
			}
			updateScheduleInfo(newSchedule);
		} else {
			updateScheduleInfo({ ...scheduleInfo });
		}
	}

	onEndDateChange = (value: moment.Moment) => {
		const { scheduleInfo, scheduleInfo: { startDate }, updateScheduleInfo: updateScheduleInfo, t } = this.props;
		if (moment.isMoment(value) && value.isSameOrAfter(startDate, 'minute')) {
			let newSchedule = update(scheduleInfo, { endDate: { $set: value } });
			if (isNeedToRemoveExecutionTimeRange(newSchedule)) {
				window.AdminPortal.Notifications.Send(1, t('dailyExecutionTimeRangeNeedToBeRemove'), t('dailyTaskLessThan24HoursRemoveExecutionTimeRange'));
				newSchedule = update(newSchedule, { detail: { dailyExecutionTimeRange: { $set: null } } });
			}
			updateScheduleInfo(newSchedule);
		} else {
			updateScheduleInfo({ ...scheduleInfo });
		}
	}

	onEndDateClick = () => {
		const { scheduleInfo, scheduleInfo: { endDate: oldEndDate, startDate }, updateScheduleInfo: updateScheduleInfo } = this.props;
		const newEndDate = oldEndDate ? null : startDate.clone().add(1, 'day');
		const newSchedule = update(scheduleInfo, { endDate: { $set: newEndDate } });
		updateScheduleInfo(newSchedule);
	}

	onTimeZoneChange = (value: string) => {
		const { scheduleInfo, updateScheduleInfo: updateScheduleInfo } = this.props;
		const newSchedule = update(scheduleInfo, { timeZoneId: { $set: value } });
		updateScheduleInfo(newSchedule);
	}

	parseIntervalWeeklyRepeatDescription(cronExpression: string) {
		const { t } = this.props;
		const parts = cronExpression.trim().split(' ');
		const minutes = parts[1], hours = parts[2], weekPart = parts[5], yearPart = parts[6];
		// startTime
		const startTime = this.formatTime(hours, minutes);

		// repeatWeekDes
		const weekNumber = weekPart.split('/')[1];
		let repeatWeekDes;
		if (parseInt(weekNumber) === 1) {
			repeatWeekDes = t('repeatWeekDes', {
				weekUnitStr: (parseInt(weekNumber) === 1) ? t('repeatWeek') : t('repeatWeeks'),
			});
		} else {
			repeatWeekDes = t('repeatWeekDes', {
				weekNumber,
				weekUnitStr: (parseInt(weekNumber) === 1) ? t('repeatWeek') : t('repeatWeeks'),
			});
		}

		// daysOfWeekDes
		const dayNumbers = weekPart.split('/')[0];
		const dayNames = dayNumbers.split(',').map(d => t(`dayOfWeek_${d}`));
		let daysOfWeekDes;
		if (dayNames.length > 1) {
			const lastDay = dayNames.splice(dayNames.length - 1, 1)[0];
			daysOfWeekDes = `${dayNames.join(t('comma'))}${t('comma')} ${t('and')} ${lastDay}`;
		} else {
			daysOfWeekDes = dayNames[0];
		}
		const desContext = {
			startTime,
			repeatWeekDes,
			daysOfWeekDes,
		};

		// years
		if (yearPart !== '*') {
			let years;
			if (yearPart.indexOf('-') > -1) {
				const yearArray = yearPart.split('-');
				years = t('throughYears', { startYear: yearArray[0], endYear: yearArray[1] });
			} else {
				years = t('onlyInYear', { year: yearPart });
			}
			const yearsKey = 'years';
			desContext[yearsKey] = `${t('comma')} ${years}`;
		}
		return t('intervalWeeklyRepeatDescription', desContext);
	}
	parseDailyExecutionTimeRangeDescription(startMoment: moment.Moment, endMoment: moment.Moment) {
		const { t } = this.props;
		const desContext = {
			startTime: this.formatTimeByHourMinute(startMoment.hour(), startMoment.minute()),
			endTime: this.formatTimeByHourMinute(endMoment.hour(), endMoment.minute()),
		};
		return t('dailyExecutionTimeRangeDescription', desContext);
	}
	formatTime(hourExpression: string, minuteExpression: string) {
		const hour: number = parseInt(hourExpression);
		const minute = parseInt(minuteExpression);
		return this.formatTimeByHourMinute(hour, minute);
	}
	formatTimeByHourMinute(hour: number, minute: number) {
		const period = hour >= 12 ? ' PM' : ' AM';
		if (hour > 12) {
			hour -= 12;
		}
		if (hour === 0) {
			hour = 12;
		}
		return `${('00' + hour.toString()).substring(hour.toString().length)}:${('00' + minute.toString()).substring(
			minute.toString().length
		)}${period}`;
	}

	parseRepeatDescription() {
		const { scheduleInfo } = this.props;
		const locale = getCronstrueLanguage();
		const cronExpression = generateCronExpression(scheduleInfo);
		if (scheduleInfo.repeatType === 'IntervalWeekly') {
			return this.parseIntervalWeeklyRepeatDescription(cronExpression);
		}
		if (scheduleInfo.repeatType === 'Daily') {
			const { realCronExpression, dailyExecutionTimeRange } = parseCronAndHoursCronAndDailyExecutionTimeRange(cronExpression);
			if (dailyExecutionTimeRange) {
				const { valid, value } = getValidDailyExecutionTimeRange(dailyExecutionTimeRange);
				if (valid) {
					const cronDescription = cronstrue.toString(realCronExpression, { locale, dayOfWeekStartIndexZero: false });
					const dailyExecutionTimeRangeDescription = this.parseDailyExecutionTimeRangeDescription(value.startTime as moment.Moment, value.endTime as moment.Moment);
					return `${cronDescription}. ${dailyExecutionTimeRangeDescription}`;
				} else {
					return cronstrue.toString(realCronExpression, { locale, dayOfWeekStartIndexZero: false });
				}
			}
		}
		return cronstrue.toString(cronExpression, { locale, dayOfWeekStartIndexZero: false });
	}

	render() {
		const { scheduleInfo, updateScheduleInfo: updateScheduleInfo, t } = this.props;
		if (!scheduleInfo) return null;
		const repeatTypes: DropdownItemProps[] = ['Daily', /*'Weekly', */ 'IntervalWeekly', 'Monthly'].map(rt => ({ text: t(`recEditor${rt}Editor`), value: rt, selected: scheduleInfo.repeatType === rt }));
		const timezoneDropdownItemProps: DropdownItemProps[] = window.ServerTimeZones.map(s => ({ text: timeZoneUtils.getTimeZoneText(s.ianaId, s.baseOffset, t), value: s.id, selected: scheduleInfo.timeZoneId === s.id }));
		const props = { scheduleInfo, updateScheduleInfo };
		return (
			<fieldset className='sc-recurrence'>
				<Label inverted={window.inverted} labelOnTop text={t('recEditorHeaderRepeat')}>
					<Dropdown
						items={repeatTypes}
						text={t(`recEditor${scheduleInfo.repeatType}Editor`)}
						menuWidth='100%'
						offset
						size='small'
						textAlign='left'
						position='center'
						onSelect={this.onEditorSelect}
						inverted={window.inverted}
					/>
				</Label>

				{scheduleInfo.repeatType === 'Daily' && <DailyEditor {...props} />}
				{scheduleInfo.repeatType === 'Weekly' && <WeeklyEditor {...props} />}
				{scheduleInfo.repeatType === 'IntervalWeekly' && <IntervalWeeklyEditor {...props} />}
				{scheduleInfo.repeatType === 'Monthly' && <MonthlyEditor {...props} />}

				<Label inverted={window.inverted} text={t('recEditorLabelStart')} labelOnTop>
					<DateTimeEditor
						placeholder={this._dataTimePlaceholder}
						inverted={window.inverted}
						value={convertToMomentValue(scheduleInfo.startDate)}
						onBlur={this.onStartDateChange}
						onChange={null}
					/>
				</Label>
				<Label inverted={window.inverted} text={t('recEditorLabelEnd')} labelOnTop>
					{scheduleInfo.endDate &&
						<DateTimeEditor
							inverted={window.inverted}
							value={convertToMomentValue(scheduleInfo.endDate)}
							placeholder={t('recEditorPlaceholderNoEndDate')}
							onBlur={this.onEndDateChange}
							onChange={null}
						/>
					}
					<div className={classnames('sc-rec-checkbox', { 'sc-rec-checkbox--offset': scheduleInfo.endDate })}>
						<Checkbox
							inverted={window.inverted}
							inline
							value='no-end'
							text={t('recEditorPlaceholderNoEndDate')}
							checked={!scheduleInfo.endDate}
							onChange={this.onEndDateClick}
						/>
					</div>
				</Label>

				<Label inverted={window.inverted} text={t('taskExecutingTimezone')} labelOnTop>
					<DropdownEditor
						items={timezoneDropdownItemProps}
						text={timeZoneUtils.getTimeZoneDisplay(scheduleInfo.timeZoneId, t)}
						onChange={this.onTimeZoneChange}
						inverted={window.inverted}
					/>
				</Label>

				<p className='sc-rec-descr'><AriaIcon className='mdi mdi-repeat' />{this.parseRepeatDescription()}</p>
			</fieldset>
		);
	}
}
