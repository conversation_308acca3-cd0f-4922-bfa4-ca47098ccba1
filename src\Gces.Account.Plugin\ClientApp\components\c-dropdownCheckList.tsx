import * as React from 'react';
import * as classnames from 'classnames';
import { Dropdown, DropdownItemProps, Checkbox, SearchBox, Button } from 'gces-ui';

export interface CDropdownCheckListProps {
	items: DropdownItemProps[];
	onItemSelect: (value: any) => void;
	handleSelectAll: (allSelected: boolean, values: any[]) => void;
	text?: string;
	placeholder?: string;
	searchPlaceholder?: string;
	className?: string;
	disabled?: boolean;
	aid?: string;
	visibilityToggle?: boolean;
	noVisibilityToggle?: boolean;
	showEyeTitle?: string;
	hideEyeTitle?: string;
	selectAllText: string;
}

interface LocalState {
	isOpen: boolean;
	searchText: string;
	filteredItems: DropdownItemProps[];
	showEncryptedText: boolean;
}

export class CDropdownCheckList extends React.PureComponent<CDropdownCheckListProps, LocalState> {
	constructor(props: CDropdownCheckListProps, context: any) {
		super(props, context);

		this.state = {
			isOpen: false,
			searchText: '',
			filteredItems: [...props.items],
			showEncryptedText: false
		};
	}

	componentDidUpdate(prevProps: CDropdownCheckListProps) {
		if (prevProps.items !== this.props.items) {
			this.setState({
				filteredItems: this.filterItems(this.props.items, this.state.searchText)
			});
		}
	}

	filterItems = (items: DropdownItemProps[], searchText: string): DropdownItemProps[] => {
		if (!searchText) {
			return items;
		}
		const lowerSearchText = searchText.toLowerCase();
		return items.filter(item =>
			item.text && item.text.toString().toLowerCase().includes(lowerSearchText)
		);
	}

	handleSearchChange = (value: string) => {
		const filteredItems = this.filterItems(this.props.items, value);
		this.setState({ searchText: value, filteredItems });
	}

	handleToggle = (isOpen: boolean) => {
		this.setState({
			isOpen,
			searchText: isOpen ? this.state.searchText : '',
			filteredItems: isOpen ? this.state.filteredItems : this.props.items
		});
	}

	areAllFilteredItemsSelected = (): boolean => {
		const { filteredItems } = this.state;
		if (filteredItems.length === 0) {
			return false;
		}
		return filteredItems.every(item => item.selected);
	}

	renderDropdownItem = (item: DropdownItemProps) => {
		return (
			<div className='c-dropdown-check-list-item'>
				<Checkbox
					text={item.text}
					checked={!!item.selected}
					onChange={() => this.props.onItemSelect(item.value)}
					value={item.value}
					inline
				/>
			</div>
		);
	}

	getDisplayText = (): string => {
		const { placeholder, items } = this.props;
		const selectedItems = items.filter(item => item.selected);
		if (selectedItems.length === 0) {
			return placeholder || '';
		}
		const selectedTexts = selectedItems.map(item => item.text);
		return selectedTexts.join(', ');
	}

	renderToolbarItem = () => {
		const { searchPlaceholder } = this.props;
		const { searchText } = this.state;
		return (
			<div className='c-dropdown-check-list-toolbar-container'>
				<div className='c-dropdown-check-list-toolbar'>
					<div className='c-dropdown-check-list-search'>
						<SearchBox
							value={searchText}
							onChange={this.handleSearchChange}
							placeholder={searchPlaceholder}
						/>
					</div>
				</div>
			</div>
		);
	}
	renderSelectAllItem = () => {
		const { selectAllText, handleSelectAll } = this.props;
		const allSelected = this.areAllFilteredItemsSelected();
		return (
			<div className='c-dropdown-check-list-item'>
				<Checkbox
					text={selectAllText}
					checked={allSelected}
					onChange={() => handleSelectAll(!allSelected, this.state.filteredItems.map(i => i.value))}
					value={null}
					inline
				/>
			</div>
		);
	}

	toggleShowEncryptedText = () => {
		this.setState({ showEncryptedText: !this.state.showEncryptedText });
	}

	render() {
		const { className, disabled, aid, visibilityToggle, noVisibilityToggle, hideEyeTitle, showEyeTitle, onItemSelect, selectAllText } = this.props;
		const { filteredItems, showEncryptedText } = this.state;

		const isDisabled = disabled || (visibilityToggle && !showEncryptedText);
		const enableTextSecurity = visibilityToggle && !showEncryptedText;

		const toolbarItem: DropdownItemProps = {
			text: '',
			value: '',
			divider: true,
			classNames: 'toolbar-btn',
			onRenderCustomDropdownItem: () => this.renderToolbarItem()
		};
		const selectAllItem: DropdownItemProps = {
			text: selectAllText,
			value: '',
			divider: false,
			classNames: 'select-all-btn',
			onRenderCustomDropdownItem: () => this.renderSelectAllItem()
		}

		const dropdownItems = [
			toolbarItem,
			selectAllItem,
			...filteredItems.map(item => ({
				...item,
				onRenderCustomDropdownItem: () => this.renderDropdownItem(item)
			}))
		];

		return (
			<div className={classnames('c-dropdown-check-list-wrapper', className, 'efc-sensitive-wrapper', { 'efc-input-password': visibilityToggle })}>
				<Dropdown
					className={classnames('c-dropdown-check-list', { 'efc-text-security': enableTextSecurity })}
					text={this.getDisplayText()}
					disabled={isDisabled}
					aid={aid}
					offset={false}
					width='100%'
					menuWidth='100%'
					style='default'
					size='small'
					textAlign='left'
					onToggle={this.handleToggle}
					items={dropdownItems}
					noCloseOnSelect={true}
					onSelect={onItemSelect}
				/>
				<Button
					className={classnames('efc-input-visible-toggle', { 'efc-input-visible-hidden': !visibilityToggle }, { 'efc-input-visible-none': noVisibilityToggle })}
					icon={showEncryptedText ? 'mdi mdi-eye' : 'mdi mdi-eye-off'}
					size='small'
					style='transparent'
					rounded
					onClick={this.toggleShowEncryptedText}
					title={showEncryptedText ? hideEyeTitle : showEyeTitle}
				/>
			</div>
		);
	}
}