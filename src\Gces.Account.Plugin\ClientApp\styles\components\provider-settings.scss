.provider-settings {
	width: 100%;
	height: 100%;
	min-width: 650px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;

	.provider-settings-header,
	.provider-settings-footer {
		width: 100%;
		height: 60px;
		padding: 10px 20px;
	}

	.explain-text {
		display: flex;
		flex-direction: column;
		margin: 14px;
		font-size: 14px;

		span {
			margin: 4px 0;
			display: block;
			max-width: 100%;

			@include gces-truncate;
		}
	}

	.provider-settings-body {
		.provider-settings {
			width: auto;
			height: auto;
			padding: 10px;

			.provider-setting-item {
				height: 36px;
				vertical-align: middle;
				margin: 5px;

				.provider-setting-item-label,
				.provider-setting-item-value {
					display: inline-block;
					height: 100%;
					vertical-align: middle;
					margin-left: 5px;

					div {
						display: inline-block;
					}
				}

				.provider-setting-item-label {
					width: 150px;

					label {
						font-size: 12px;
						width: 100%;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						line-height: 36px;
					}

					.provider-setting-item-required {
						display: inline-block;
						vertical-align: bottom;
						color: red;
						font-size: 6px;
						margin-left: 3px;
					}
				}

				.provider-setting-item-value {
					width: 450px;
					height: 36px;

					.inputPassword-group {
						display: flex;
					}
				}
			}
		}

		.btn-sync-data {
			margin: 15px;
		}

		.security-provider-test-area {
			padding: 10px;
			display: flex;
			flex-direction: column;
			width: 650px;

			.test-config-area {
				width: 620px;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				margin: 10px 0;

				.config-item {
					display: flex;
					align-items: center;
					padding: 6px;
					height: 40px;

					&.multi-line {
						height: 120px;
					}

					label {
						width: 150px;
						height: 30px;
						font-size: 12px;
						line-height: 40px;

						@include gces-truncate;
					}

					input {
						width: 300px;
					}

					.custom-param-text-area {
						width: 460px;
						resize: none;
					}

					button {
						margin-left: 60px;
						width: 100px;
						height: 32px;
						line-height: 32px;
					}

					.test-password-group {
						display: flex;
						justify-content: flex-start;
						width: 300px;

						button {
							width: 30px;
							height: 30px;
							margin: 0;
						}
					}
				}

				.btn-sp-test {
					width: 100px;
					height: 36px;
					line-height: 36px;
				}
			}

			.test-result-area {
				margin: 10px 0;
				padding: 10px;
				min-height: 40px;
				border: 1px solid lightgray;

				.test-result-summary {
					display: flex;
					justify-content: space-between;

					span {
						font-size: 14px;
						font-weight: bold;

						&.success-result {
							color: $ef-accent;
						}

						&.fail-result {
							color: red;
						}

						&:before {
							margin-right: 4px;
						}
					}

					button {
						max-width: 200px;
						font-size: 12px;
						font-weight: bold;
						color: $ef-accent;
						background-color: lightgray;
					}
				}

				.test-result-item {
					display: flex;
					justify-content: space-between;

					.test-result-item-key {
						width: 120px;
						line-height: 30px;
						font-size: 12px;
						font-weight: bold;
					}

					.test-result-item-value {
						font-size: 12px;
						background-color: transparent;
					}
				}
			}
		}

		.title-lg {
			display: flex;
			width: 650px;
			margin: 10px 0;
			color: $ef-accent;
			font-size: 14px;
			font-weight: bold;

			&:after {
				flex: 1;
				content: "";
				border-top: 1px solid $ef-accent;
				margin: 10px 0 0 20px;
			}
		}
	}

	.provider-settings-footer {
		text-align: right;

		.btn-group {
			button {
				margin: 0 5px;
				width: 100px;
			}
		}
	}
}
