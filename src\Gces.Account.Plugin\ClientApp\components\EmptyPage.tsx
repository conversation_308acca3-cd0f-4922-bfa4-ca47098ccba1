﻿import * as React from 'react';
import { Button } from 'gces-ui';

interface EmptyPageProps {
	imageName: string;
	tip: string;
	buttonText?: string;
	onclick?: () => void;
	tipSuffix?: string;
	showTip?: boolean;
}

export default class EmptyPage extends React.Component<EmptyPageProps> {
	render() {
		const { imageName, tip, buttonText, onclick, tipSuffix, showTip } = this.props;
		return <div className='empty-page-container' >
			<div className='empty-page-bg has-bg-image' style={{ backgroundImage: `url('images/admin/${imageName}.png')` }} />
			{
				showTip && <div className='empty-page-tip'>
					<div className='main-tip' title={tip}>{tip}</div>
					{
						onclick && <Button
							className='empty-page-select-btn'
							text={buttonText}
							title={buttonText}
							onClick={onclick}
						/>
					}
					{tipSuffix && <div className='main-tip'>{tipSuffix}</div>}
				</div>
			}
		</div>;
	}
}