import ExternalProvider from './components/ExternalProvider';
import ConcurrenceManagement from './components/ConcurrenceManagement';
import TwoFactorAuthentication from './components/TwoFactorAuthentication';
// import InactiveSessionSettings from './components/InactiveSession';
// #if process.env.NODE_ENV === 'development'
import './styles/themes/index.scss';
// #endif
import './app';
import { DropdownMenu } from 'gces-ui/lib/components/Dropdown';
const app = document.querySelector('.wyn-portal-app');
if (app) {
	DropdownMenu.setRootContainer(app as HTMLElement);
}
if (window.AdminPortal.Edition !== 'en') {
	window.AdminPortal.Register.SectionItem('sysconfig-external-provider', 'security-settings', 'external-provider', 'mdi mdi-ticket-account', 'External Providers', 'External provider management.', true, ExternalProvider);
}

if (!window.AdminPortal.EnableMultipleServers) {
	window.AdminPortal.Register.SectionItem('account-management-concurrence', 'security-settings', 'concurrence', 'mdi mdi-account-network', 'Concurrence', 'Concurrence user manage', true, ConcurrenceManagement);
}

if (window.AdminPortal.Edition === 'zh') {
	window.AdminPortal.Register.SectionItem('sysconfig-tfa', 'security-settings', 'two-factor-authentication', 'mdi mdi-two-factor-authentication', 'Two-Factor Authentication', 'Two factor authentication', false, TwoFactorAuthentication);
}

// Disable feature GEF-15880
// window.AdminPortal.Register.SectionItem('sysconfig-iss', 'security-settings', 'inactive-session-settings', 'mdi mdi-account-remove', 'Inactive Session Settings', 'Inactive Session Settings', false, InactiveSessionSettings);
