﻿.users-container {
	position: relative;
	display: flex;
	flex-direction: column;
	min-width: 0;

	.toolbar-div {
		flex: 0 0 50px;
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}

	.user-grid-container {
		flex: 1;
		margin-top: 8px;

		.user-item-actions {
			align-self: center;
		}

		.cg-body {
			.cg-row {
				.row-action-box {
					display: flex;
					align-items: center;

					.edit-user-button {
						margin-right: 5px;
					}
				}

				.user-status {
					@include gces-truncate;
				}

				.efc-sensitive-wrapper {
					display: flex;
					align-items: center;
					width: 100%;

					&.efc-input-password-show {
						.efc-textbox {
							@include gces-truncate;
						}
					}

					.efc-textbox {
						background-color: transparent;
						padding: 0;
						cursor: default;
					}

					.efc-input-visible-toggle {
						>i {
							font-size: $ef-font-size-lg;
						}

						&.efc-input-visible-hidden {
							visibility: hidden;
						}
					}
				}

				&:hover {
					background-color: $accent1;
					color: $text-contrast;
					opacity: 0.8;
					cursor: default;

					.user-item-actions {
						i {
							color: $text-contrast;
						}
					}

					.efc-sensitive-wrapper {
						.efc-textbox {
							color: $text-contrast;
						}

						.efc-input-visible-toggle {
							>i {
								color: $text-contrast;
							}
						}
					}
				}

				&.selected {
					background-color: $accent1;
					color: $text-contrast;
					opacity: 1;

					.user-item-actions {
						i {
							color: $text-contrast;
						}
					}

					.efc-sensitive-wrapper {
						.efc-textbox {
							color: $text-contrast;
						}

						.efc-input-visible-toggle {
							>i {
								color: $text-contrast;
							}
						}
					}
				}
			}
		}
	}

	.user-editor {
		.account-management-panel {
			.user-input-property {
				input::-webkit-outer-spin-button,
				input::-webkit-inner-spin-button {
					-webkit-appearance: none;
					margin: 0;
				}

				input[type="number"] {
					-moz-appearance: textfield;
					appearance: none;
				}

				input::-ms-clear {
					display: none;
				}
			}

			.efc-sensitive-wrapper {
				display: flex;
				align-items: center;

				&.efc-checklist-wrapper {
					align-items: normal;
				}

				.efc-checklist,
				.efc-datetime {
					width: 100%;
				}
			}

			.efc-input-visible-toggle {
				margin-left: 5px;

				>i {
					font-size: $ef-font-size-lg;
				}

				&.efc-input-visible-hidden {
					visibility: hidden;
				}

				&.efc-input-visible-none {
					display: none;
				}
			}

			.multi-valued {
				.efc-textbox {
					height: 85px;
				}
			}

			.efc-input-password {
				&.multi-valued {
					.efc-text-security {
						-webkit-text-security: disc;
					}
				}

				.efc-text-security {
					&.efc-dropdown {
						.ef-btn {
							-webkit-text-security: disc;
						}
					}

					&.efc-datetime {
						-webkit-text-security: disc;
					}

					&.efc-number-textbox {
						-webkit-text-security: disc;
					}

					&.efc-checklist {
						>.btn {
							-webkit-text-security: disc;

							i {
								-webkit-text-security: none;
							}
						}
					}

					&.c-dropdown-check-list {
						.ef-btn {
							-webkit-text-security: disc;
						}
					}
				}
			}
		}
	}

	.password-policy-checkbox {
		margin-left: 2px;
	}
}