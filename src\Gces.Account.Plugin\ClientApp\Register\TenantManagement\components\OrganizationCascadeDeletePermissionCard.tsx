import CGrid, { CGridProps } from 'gces-react-grid';
import { Button } from 'gces-ui';
import React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';

interface OrganizationCascadeDeletePermissionCardProps {
	type: 'tenant' | 'role';
	rows: Row[];
}

interface Row {
	path: string;
	permissions: string;
}

interface ConnectedProps {
	t: any;
}

interface OrganizationCascadeDeletePermissionCardState {
	isExpanded: boolean;
}

@translate('organization', { wait: true })
class OrganizationCascadeDeletePermissionCardInner extends React.PureComponent<OrganizationCascadeDeletePermissionCardProps & ConnectedProps, OrganizationCascadeDeletePermissionCardState> {
	state: OrganizationCascadeDeletePermissionCardState = {
		isExpanded: true,
	};

	onExpandOneClick = () => {
		const { isExpanded } = this.state;
		this.setState({ isExpanded: !isExpanded });
	}

	renderCardHeader = () => {
		const { type, t } = this.props;
		const { isExpanded } = this.state;
		const expandedClassName = isExpanded ? 'mdi mdi-chevron-down expand-size' : 'mdi mdi-chevron-right expand-size';
		const title = type === 'tenant' ? t('tntModifyPermissionScopeMessageWithTenant') : t('tntModifyPermissionScopeMessageWithRole');
		return (
			<div className='cascade-delete-permission-card-header'>
				<div className='card-name-container'>
					<div className='card-expand'>
						<Button
							aid='card-expand-btn'
							icon={expandedClassName}
							style='transparent'
							size='small'
							title={isExpanded ? t('tntCollapse') : t('tntExpand')}
							onClick={() => this.onExpandOneClick()}
							rounded
						/>
					</div>
					<span title={title}>{title}</span>
				</div>
			</div>
		);
	}

	buildCardColumns = () => {
		const { type, t } = this.props;
		return [
			{ id: 'path', key: 'path', label: type === 'tenant' ? t('tntOrganizationName') : t('tntRoleName'), width: 200 },
			{ id: 'permissions', key: 'permissions', label: t('tntTenantPermissions') },
		];
	}

	buildCardRows = () => {
		const { rows } = this.props;
		return rows?.map(row => {
			return {
				path: row.path,
				permissions: row.permissions
			};
		});
	}

	renderGrid = () => {
		const gridProps: CGridProps = {
			columns: this.buildCardColumns(),
			rows: this.buildCardRows(),
			hideGridLines: true,
			columnResizing: false,
			rowHeight: 32,
			useSmallScrollbars: true,
		};
		return <CGrid {...gridProps} />;
	}

	render() {
		const { isExpanded } = this.state;
		return (
			<div className='cascade-delete-permission-card'>
				{this.renderCardHeader()}
				{isExpanded && <div className='cascade-delete-permission-card-body'>{this.renderGrid()}</div>}
			</div>
		);
	}
}

export const OrganizationCascadeDeletePermissionCard = connect(
)(OrganizationCascadeDeletePermissionCardInner) as React.ComponentClass<OrganizationCascadeDeletePermissionCardProps>;