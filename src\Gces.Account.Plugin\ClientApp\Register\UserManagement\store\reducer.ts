import { Reducer } from 'redux';
import * as update from 'immutability-helper';
import { UserState } from './interfaces';
import { UserReducerActions } from './actions';
import { nameSorter } from '../../../utils';

const defaultState: UserState = {
    busy: false,
    allUsers: [],
    properties: [],
    roles: [],
    passwordPolicy: null,
    editingUser: null,
    isAddingUser: false,
    isImportingUser: false,
    isSelectingMembers: false,
    importResult: null,
    searchText: '',
    organizations: [],
    selectedOrganizationId: '',
    detailUserId: '',
    showMembersOfSubOrg: false,
};

export const userReducer: Reducer<UserState> = (state: UserState = defaultState, action: UserReducerActions) => {
    switch (action.type) {
        case 'Portal/User/SetBusy':
            return update(state, { busy: { $set: action.payload.busy } });
        case 'Portal/User/SetAllUsers':
            return update(state, { allUsers: { $set: action.payload.allUsers } });
        case 'Portal/User/SetRoles':
            return update(state, { roles: { $set: action.payload.roles } });
        case 'Portal/User/SetProperties':
            return update(state, { properties: { $set: action.payload.Properties } });
        case 'Portal/User/SetPasswordPolicy':
            return update(state, { passwordPolicy: { $set: action.payload.passwordPolicy } });
        case 'Portal/User/SetOrganizations':
            const sortedOrganizations = [...action.payload.organizations];
            sortedOrganizations.sort(nameSorter);
            return update(state, { organizations: { $set: sortedOrganizations } });
        case 'Portal/User/SetEditingUser':
            return update(state, { editingUser: { $set: action.payload.editingUser } });
        case 'Portal/User/SetIsAddingUser':
            return update(state, { isAddingUser: { $set: action.payload.isAddingUser } });
        case 'Portal/User/SetIsImportingUser':
            return update(state, { isImportingUser: { $set: action.payload.isImportingUser } });
        case 'Portal/User/SetSearchText':
            return update(state, { searchText: { $set: action.payload.searchText } });
        case 'Portal/User/SetImportResult':
            return update(state, { importResult: { $set: action.payload.importResult } });
        case 'Portal/User/SetSelectedOrganizationId':
            return update(state, { selectedOrganizationId: { $set: action.payload.selectedOrganizationId } });
        case 'Portal/User/SetIsSelectingMembers':
            return update(state, { isSelectingMembers: { $set: action.payload.isSelectingMembers } });
        case 'Portal/User/SetDetailUserId':
            return update(state, { detailUserId: { $set: action.payload.detailUserId } });
        case 'Portal/User/SetShowMembersOfSubOrg':
            return update(state, { showMembersOfSubOrg: { $set: action.payload.showMembersOfSubOrg } });
        case 'Portal/user/ResetState':
            return update(state, {
                editingUser: { $set: null },
                isAddingUser: { $set: false },
                isImportingUser: { $set: false },
                isSelectingMembers: { $set: false },
                detailUserId: { $set: '' },
                searchText: { $set: ''}
            });
        default: return state;
    }
};