
(
	() => {
		let __namespace, __product, __plugin, __entries, __lazy, __globalVar;
		try {
			const url = new URL(document.currentScript.src);
			__namespace = url.searchParams.get('namespace');
			__product = url.searchParams.get('product');
			__plugin = url.searchParams.get('plugin');
			__entries = url.searchParams.get('entries');
			__lazy = url.searchParams.get('lazy');
			__globalVar = url.searchParams.get('globalVar');
		}
		catch { }
		function __WrapClass() {
			this.__init = function (window, ___globalVar___) {
				const fetch = window.fetch;
				// code split marker
			}
		}
		if (/^\s*(true|1|on)\s*$/i.test(__lazy)) {
			let namespace = window[__namespace || 'grapecity'] = window[__namespace || 'grapecity'] || {};
			let product = namespace[__product || 'wyn'] = namespace[__product || 'wyn'] || {};
			let plugin = product[__plugin || 'plugin'] = product[__plugin || 'plugin'] || {};
			let entries = plugin[__entries || 'registerEntries'] = plugin[__entries || 'registerEntries'] || [];
			entries.push({
				id: '___id___',
				component: '___component___',
				plugins: ___plugins___,
				regEntry: function (__globalVar) {
					(function (globalVar) {
						new __WrapClass().__init(globalVar, {});
					})(__globalVar || window);
				}
			});
		} else {
			new __WrapClass().__init(__globalVar || window, {});
		}
	}
)();