import { TFunction } from 'i18next';
import * as moment from 'moment';
import { Organization } from '../Register/UserManagement/store/interfaces';
import { safeFetchV2 } from '../utils/safeFetchV2';
const strongPasswordCheckReg = /(?=^.{8,150}$)(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.*\s).*$/;
const normalPasswordCheckReg = /^.{4,150}$/;
const weakPasswordCheckReg = /^.{1,150}$/;
const emailCheckReg = /^(([^<>()\[\]\.,;:\s@\"]+(\.[^<>()\[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{1,})$/; // same as task tab

export const SettingItemValueType = {
	Text: 0,
	Boolean: 1,
	Number: 2,
	Password: 3
};

export const SettingItemRestriction = {
	Mandatory: 0,
	Optional: 1
};

export const PasswordPolicy = {
	WeakPasswordPolicy: 0,
	NormalPasswordPolicy: 10,
	StrongPasswordPolicy: 20
};

const needReloadResState = [401, 404, 302];
const checkStatus = (res) => {
	const beenRedirectedToLogin = res.url && res.url.toLowerCase().indexOf('/account/login') !== -1;
	if (!beenRedirectedToLogin && res.status >= 200 && res.status < 300) {
		return Promise.resolve(res);
	} else if (beenRedirectedToLogin) {
		return Promise.reject({ message: res.statusText, status: 401 });
	} else {
		return Promise.reject(res);
	}
};

function getErrorMsgFromErrorCode(code: string, msg: string, context, ln): string {
	if (code && code.startsWith('V2_007_000_')) {
		const subCode = code.substr(11, 4);
		switch (subCode) {
			case '0001':
				return window.AdminPortal.i18n.t('account:error_V2_007_000_0001', { resourceType: window.AdminPortal.i18n.t(`${ln}:rt_${context.resourceType}`), resourceIdentifier: context.resourceIdentifier });
			case '0002':
				return window.AdminPortal.i18n.t('account:error_V2_007_000_0002', { resourceType: window.AdminPortal.i18n.t(`${ln}:rt_${context.resourceType}`), resourceIdentifier: context.resourceIdentifier });
			case '0003':
				return window.AdminPortal.i18n.t('account:error_V2_007_000_0003', { resourceType: window.AdminPortal.i18n.t(`${ln}:rt_${context.resourceType}`), resourceIdentifier: context.resourceIdentifier });
			case '0006':
				return window.AdminPortal.i18n.t('account:error_V2_007_000_0006', { operation: window.AdminPortal.i18n.t(`${ln}:op_${context.operation}`), resourceIdentifier: context.resourceIdentifier });
			case '0010':
				return window.AdminPortal.i18n.t('account:error_V2_007_000_0010', { message: msg });
			default:
				return msg;
		}
	} else if (code === 'V2_000_019_0004') {
		const messages = [window.AdminPortal.i18n.t(`${ln}:error_${code}`)];
		const generateMessage = (type: string, tKey: string) => {
			if (context[type]) {
				const datasetName = window.AdminPortal.i18n.t(`${ln}:${`synchronizationDatasets!${tKey}`}`);
				messages.push(`${datasetName}: ${context[type]}`);
			}
		};
		generateMessage('User', 'user');
		generateMessage('Organization', 'organization');
		generateMessage('Role', 'role');
		generateMessage('UserRole', 'userRoleRelation');
		generateMessage('UserOrg', 'userOrganization');
		return messages.join('\n');
	}
	else {
		return window.AdminPortal.i18n.t([`error_${code}`, 'err_V2_000_000_0001'], { ...context, ns: [ln, 'common', 'portal'] });
	}
}
function extractErrorMsg(data, ln): string {
	if (data.errors) {
		if (data.errors.length === 1) {
			const error = data.errors[0];
			return getErrorMsgFromErrorCode(error.code, error.message, error.context, ln);
		} else {
			let errorMessage = '';
			data.errors.forEach(error => {
				errorMessage += `${getErrorMsgFromErrorCode(error.code, error.message, error.context, ln)} \r\n`;
			});
			return errorMessage;
		}
	} else {
		return getErrorMsgFromErrorCode(data.code, data.message, data.context, ln);
	}
}

export async function sendRequestV2(url: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET', data: any = null, ln = 'account', ignoreError = false) {
	const init: RequestInit = {
		credentials: 'same-origin',
		method
	};
	if (data !== null) {
		init.headers = { 'Content-Type': 'application/json' };
		init.body = JSON.stringify(data);
	}
	const { result, error } = await safeFetchV2(url, init);
	if (result) {
		return { result };
	} else if (error) {
		if (!ignoreError) {
			const msg = extractErrorMsg(error, ln);
			if (msg) {
				window.AdminPortal.Notifications.Send(0, window.AdminPortal.i18n.t('account:Error'), msg);
			}
		}
		return { error };
	}
}

export const ajax = (url, callback, method = 'GET', data = null, failCallback = null) => {
	const init: RequestInit = {
		credentials: 'same-origin',
		method
	};
	if (data !== null) {
		init.headers = { 'Content-Type': 'application/json' };
		init.body = JSON.stringify(data);
	}
	fetch(url, init)
		.then(checkStatus)
		.then(res => res.status === 204 ? {} : res.json())
		.then(data => {
			if (callback) callback(data);
		})
		.catch(err => {
			if (failCallback) failCallback();
			if (needReloadResState.indexOf(err.status) !== -1) window.location.href = 'logout';
			else {
				err.json().then(json => {
					const error = json;
					const msg = extractErrorMsg(error, 'account');
					window.AdminPortal.Notifications.Send(0, window.AdminPortal.i18n.t('account:Error'), msg);
				});
			}
		});
};

export const isValidFieldName = (name) => {
	const pattern = /[<>\\\/]/g;
	return !pattern.test(name);
};

export function format(str) {
	for (let i = 1; i < arguments.length; i++) {
		str = str.replace(new RegExp('{[' + (i - 1) + ']}', 'g'), arguments[i]);
	}
	return str;
}

export function isValidEmail(email) {
	return emailCheckReg.test(email);
}

export function isValidUserPassword(password, passwordPolicy) {
	if (!password) return false;
	switch (passwordPolicy) {
		case PasswordPolicy.WeakPasswordPolicy:
			return weakPasswordCheckReg.test(password);
		case PasswordPolicy.NormalPasswordPolicy:
			return normalPasswordCheckReg.test(password);
		case PasswordPolicy.StrongPasswordPolicy:
			return strongPasswordCheckReg.test(password);
		default:
			return false;
	}
}

export function toStr(value) {
	return value === null ? '' : value;
}

export const usersUrl = '/api/v2/identity/users';
export const claimMappingsUrl = '/api/v2/identity/claim-mappings';
export const customizePropertiesUrl = '/api/v2/identity/custom-properties';
export const securitySettingsUrl = '/api/v2/identity/sys-config/security-settings';
export const externalLoginProvidersUrl = '/api/v2/identity/external-providers';
export const tfaSettingsUrl = '/api/v2/identity/sys-config/tfa';
export const emailSettingsUrl = '/api/v2/identity/sys-config/email-settings';
export const inactiveSessionSettingsUrl = '/api/v2/identity/sys-config/inactive-session-settings';

export const portalAppId = '#portal-app';

export function isLocalUser(user) {
	return user && user.provider && user.provider.trim().toLowerCase() === 'local';
}
export function isLocalOrSyncedUser(user) {
	return user && user.provider && (user.provider.trim().toLowerCase() === 'local' || user.provider.trim().toLowerCase() === 'synchronizer');
}

export const getNamePathByIdPath = (orgs: Organization[], idPath: string): string => {
	const idpathArr = idPath.split('/');
	const namePath = idpathArr.map(n => orgs.find(o => o.id === n)?.name).join('/');
	return namePath;
};

export const GlobalOrganization = {
	Id: 'global',
	Name: 'Global',
	Path: '/'
};
export const isGlobalOrg = (orgId: string) => {
	return orgId === null || orgId === GlobalOrganization.Id;
};

export const accountIntegrationTokenV2Url = 'api/v2/account/integration/token';
export const accountRolesV2Url = 'api/v2/account/roles';

export interface ApiV2Error {
	code: string;
	message: string;
	context: {
		[K: string]: string
	};
}

export const notificationApiV2Errors = (errors: ApiV2Error[], summary: string, t: TFunction, namespace: string, prefix: string = ''): void => {
	const errorDetails = errors.map(e => t(`${namespace}:${prefix}${e.code}`, e.message)).join('\n');
	window.AdminPortal.Notifications.Send(0, summary, errorDetails);
};

export const ContextValueTypes = ['String', 'Boolean', 'Integer', 'Float', 'Date', 'DateTime'];

export function isValidValue(value, cSharpType, operator?: string) {
	if (Array.isArray(value)) {
		if (operator === 'Between') {
			if (cSharpType === 'Number') {
				if (parseFloat(value[0]) > parseFloat(value[1])) {
					return false;
				}
			}
			if (cSharpType === 'Date' || cSharpType === 'DateTime') {
				if (moment(value[0]).isAfter(moment(value[1]))) {
					return false;
				}
			}
		}
		return value.length > 0 && value.every(v => isValidValueCore(v, cSharpType, operator));
	} else {
		return isValidValueCore(value, cSharpType, operator);
	}
}

export function isValidValueCore(value, cSharpType, operator) {
	if (!cSharpType) console.error('cSharpType is required param.');
	if (operator === 'NotIs' || operator === 'Is') {
		return true;
	}

	switch (toJSDetailType(cSharpType)) {
		case DataType.Integer: return isInteger(value);
		case DataType.Float: return isFloat(value);
		case DataType.DateTime: return isDateTime(value);
		case DataType.Date: return isDate(value);
		case DataType.Number: return isNumeric(value);
		case DataType.String: return isString(value);
		case DataType.Boolean: return isBoolean(value);
		case DataType.Guid: return isGuid(value);

		default: return false;
	}
}

export function toJSDetailType(dataType) {
	if (alreadyIsJsType(dataType)) return dataType;

	const mapping = createCSharpTypeToJsDetailTypeMapping();
	switch (mapping[dataType]) {
		case 0: return DataType.String;
		case 1: return DataType.Boolean;
		case 2: return DataType.Integer;
		case 3: return DataType.Float;
		case 4: return DataType.DateTime;
		case 5: return DataType.Guid;
		case 6: return DataType.Binary;

		default: return DataType.Unknown;
	}
}

export const alreadyIsJsType = (dataType: string) => ['String', 'Number', 'Integer', 'Float', 'DateTime', 'Date', 'Boolean', 'Guid', 'Binary', 'Unknown'].indexOf(dataType) !== -1;

export function createCSharpTypeToJsDetailTypeMapping() {
	const mappings: Dictionary<any> = {};

	// String
	mappings.String = 0;

	// Boolean
	mappings.Boolean = 1;

	// Integer
	mappings.SByte = 2;
	mappings.Byte = 2;
	mappings.Char = 2;
	mappings.Int16 = 2;
	mappings.UInt16 = 2;
	mappings.Int32 = 2;
	mappings.UInt32 = 2;
	mappings.Int64 = 2;
	mappings.UInt64 = 2;

	// Float
	mappings.Decimal = 3;
	mappings.Single = 3;
	mappings.Double = 3;

	// DateTime
	mappings.DateTime = 4;
	mappings.TimeSpan = 4;
	mappings.DateTimeOffset = 4;

	// Guid
	mappings.Guid = 5;

	// binary
	mappings['Byte[]'] = 6;

	// Other
	mappings.Object = 7;

	return mappings;
}

export enum DataType {
	String = 'String',
	// Number is used for field
	Number = 'Number',
	// Integer and Float only be used for parameter
	Integer = 'Integer',
	Float = 'Float',
	DateTime = 'DateTime',
	Date = 'Date',
	Boolean = 'Boolean',
	Guid = 'Guid',
	Binary = 'Binary',
	Unknown = 'Unknown',
}

const integerRegex = /^-?\d+$/;
export function isInteger(i) {
	return integerRegex.test('' + i) && parseInt(i) === Number(i);
}

export function isFloat(f) {
	return !isNaN(parseFloat(f)) && isFinite(f);
}

export function isNumeric(n) {
	return !isNaN(parseFloat(n)) && isFinite(n);
}

export function isDate(n) {
	if (typeof n === 'string') {
		const momentObj = moment(n);
		if (momentObj.isValid()) {
			return !!Date.parse(momentObj.toString());
		}
		return false;
	}
	return !!Date.parse(n);
}

export function fixDateFormat(date) {
	const isISODateString = (str: string) => /^\d{4}-\d{2}-\d{2}T/.test(str);
	if (typeof date === 'string' && !isISODateString(date)) {
		return date.replace(/-/g, '/');
	}
	return date;
}
export function isDateTime(date) {
	date = fixDateFormat(date);
	return (date instanceof Date && !isNaN(date.valueOf())) || (date && date._isAMomentObject && date.isValid()) || (!isNaN(new Date(date).valueOf())) || (!isNaN(moment(date).valueOf()));
}

export function isString(s) {
	return typeof s === 'string';
}

export function isBoolean(s) {
	return typeof s === 'boolean' || (typeof s === 'string' && /^(true|false)$/i.test((s || '').trim()));
}

export function isGuid(s) {
	const pattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
	return pattern.test(s);
}
export const DateTimeFormat = 'YYYY-MM-DD HH:mm:ss';
export const DateFormat = 'YYYY-MM-DD';
