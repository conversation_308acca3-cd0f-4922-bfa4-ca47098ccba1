import { Permission } from '../../Common/interfaces';
export interface RoleState {
	busy: boolean;
	allUsers: UserInfo[];
	organizations: Organization[];
	selectedOrganizationId: string;

	roles: Role[];
	isAddingRole: boolean;
	selectedRoleId: string;

	users: UserInfo[];
	permissions: Permission[];
	availablePermissions: Permission[];

	isAddingMembers: boolean;

	messages: string[];
	documentColumns: string[];
	originalDocumentColumns: string[];

	enableStrictPermissionManagement: boolean;
}

export interface Organization {
	description: string;
	enabled: boolean;
	fromEmail: string;
	id: string;
	name: string;
	order: number;
	parentTenantId: string;
	path: string;
}

export interface Role {
	id: string;
	name: string;
	allowEditPermissions: boolean;
	isBuiltin: boolean;
	tenantId: string;
	displayName: string;
}

export interface UserInfo {
	id: string;
	username: string;
	email: string;
	firstName: string;
	fullName: string;
	organizations: string[];
}