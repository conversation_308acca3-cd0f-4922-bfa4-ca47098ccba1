import * as React from 'react';
import { translate } from 'react-i18next';
import { connect } from 'react-redux';
import { OrganizationItem, tenantActionCreators, UsersDic, OrganizationState, PermissionsDic } from '../store';
import { TabContainer, TabContainerProps } from '../../../components/TabContainer';
import { OrganizationPropEditor } from './OrganizationPropEditor';
import { PermissionList } from '../../Common/PermissionList';
import { Permission } from '../../Common/interfaces';
import { AriaIcon, Button, ConfirmDialog, ConfirmDialogProps } from 'gces-ui';
import { safeFetchV2 } from '../../../utils/safeFetchV2';
import * as util from '../../../util';
import { getRoleDisplayName, hasManagePermissionForOrgsAndRoles } from '../../../utils';
import { isAdministrator } from 'wyn-utils';
import { OrganizationCascadeDeletePermissionCard } from './OrganizationCascadeDeletePermissionCard';

interface ConnectedProps {
	items: OrganizationItem[];
	selected: string;
	showMembersEditor: boolean;
	showTenantEditor: boolean;
	showSchemaEditor: boolean;
	showTenantPropEditor: boolean;
	usersDic: UsersDic;
	permissionsDic: PermissionsDic;
	availablePermissions: Permission[];
	isAdding?: boolean;
	isEditing?: boolean;
	curUser: User;
	sections: any[];
	enableStrictPermissionManagement: boolean;
	dispatch: any;
	t: any;
}

interface OrganizationDetailEditorState {
	selectedTabIndex: number;
	permissionsChanged: boolean;
	newPermissions: string[];
	showCascadeDeletePermissionDialog: boolean;
	cascadeDeletePermissionMessage: CascadeDeletePermissionMessage;
	portalConfigurationDefaultState: { activeSection: any; activeItem: any };
}

interface CascadeDeletePermissionMessage {
	messageType: 'tenant' | 'role' | 'tenantAndRole';
	tenantMessageDetail?: CascadeDeletePermissionMessageDetail[];
	roleMessageDetail?: CascadeDeletePermissionMessageDetail[];
}

interface CascadeDeletePermissionMessageDetail {
	path: string;
	permissions: string;
}

@translate('organization', { wait: true })
class OrganizationDetailEditorInner extends React.PureComponent<ConnectedProps, OrganizationDetailEditorState> {
	state: OrganizationDetailEditorState = {
		selectedTabIndex: 0,
		permissionsChanged: false,
		newPermissions: [],
		showCascadeDeletePermissionDialog: false,
		cascadeDeletePermissionMessage: {
			messageType: 'tenant',
		},
		portalConfigurationDefaultState: null,
	};

	private _registerMiddleware = false;

	UNSAFE_componentWillMount(): void {
		if ((window.AdminPortal as any).NavAppReducer) {
			AppContext.registerMiddleware(this.hackPortalConfigurationState);
			this._registerMiddleware = true;
			let defaultState = (window.AdminPortal as any).NavAppReducer(undefined, { type: '' });
			const { activeItem, activeSection } = this.organizeActive(this.props);
			defaultState = { ...defaultState, activeItem, activeSection };
			this.setState({ portalConfigurationDefaultState: defaultState });
		}
	}

	componentWillUnmount(): void {
		if (this._registerMiddleware) {
			AppContext.unregisterMiddleware(this.hackPortalConfigurationState);
		}
	}

	_stash: { api: any, next: any, action: any }[] = [];
	_previousChanging: boolean = false;

	organizeActive(props: ConnectedProps) {
		const { sections } = props;
		let portalSection = sections.find(p => p.id === 'configuration');
		portalSection = portalSection && { ...portalSection, items: portalSection.items?.filter(p => p.showInOrgAdmin === true) || [] };
		const portalSectionItem = portalSection?.items.find(p => p.pluginKey === 'portal' && p.id === 'portal');
		return {
			activeItem: portalSectionItem,
			activeSection: portalSection,
		};
	}

	hackPortalConfigurationState: InternalMiddleware = (api, next, action) => {
		if (this._previousChanging) {
			this._stash.push({ api, next, action });
			return;
		}
		const { portalConfigurationDefaultState } = this.state;
		if (action.type !== 'Portal/NA/UpdatePath') {
			const newState = (window.AdminPortal as any).NavAppReducer(portalConfigurationDefaultState, action);
			if (newState.activeItem !== portalConfigurationDefaultState.activeItem ||
				newState.activeSection !== portalConfigurationDefaultState.activeSection) {
				this._previousChanging = true;
				const promise = new Promise<void>((resolve) => {
					this.setState({ portalConfigurationDefaultState: newState }, resolve);
				});
				promise.then(() => {
					this._previousChanging = false;
					while (this._stash.length > 0) {
						const s = this._stash.shift();
						this.hackPortalConfigurationState(s.api, s.next, s.action);
					}
				});
				return;
			}
		}
		else if (action.payload?.section?.id === 'configuration' && action.payload?.item?.pluginKey === 'portal') {
			return;
		}

		return next(action);
	}

	UNSAFE_componentWillReceiveProps(nextProps: ConnectedProps) {
		if (nextProps.isAdding) {
			this.setState({ selectedTabIndex: 0 });
		}
		if (nextProps.selected && this.props.selected !== nextProps.selected) {
			this.props.dispatch(tenantActionCreators.getTenantPermissions(nextProps.selected));
			if (nextProps.enableStrictPermissionManagement) {
				const tenant = nextProps.items.find(t => t.id === nextProps.selected);
				const availablePermissionsTenantId = nextProps.items.find(t => t.id === tenant.parentTenantId) ? tenant.parentTenantId : tenant.id;
				this.props.dispatch(tenantActionCreators.getAvailablePermissions(availablePermissionsTenantId));
			}
			if (nextProps.selected !== 'global') {
				this.props.dispatch({ type: 'Portal/NA/SetPath', payload: { sectionUrl: 'configuration', itemUrl: 'portal', childItemUrl: undefined, onlyGetData: true, organizationId: nextProps.selected } });
			}
		}
	}

	buildBasicInformation = () => {
		return (<OrganizationPropEditor />);
	}

	onOrganizationPermissionsTabChange = (permissionNames: string[]) => {
		const { permissionsDic, selected } = this.props;
		const originPermissionNames = (permissionsDic[selected] || []).map(p => p.name).sort();
		const permissionsChanged = JSON.stringify(permissionNames.sort()) !== JSON.stringify(originPermissionNames);

		this.setState({ permissionsChanged, newPermissions: permissionNames });
	}

	buildOrganizationPermissions = () => {
		const { availablePermissions, permissionsDic, curUser, enableStrictPermissionManagement, selected, t } = this.props;
		const allOrgPermissions = enableStrictPermissionManagement ? availablePermissions : availablePermissions.filter(p => p.name !== 'assign-manage-user' && p.name !== 'manage-user' && p.name !== 'manage-permission');
		const permissions = [...(permissionsDic[selected] || [])];
		let tip = '';
		if (enableStrictPermissionManagement) {
			tip = selected === util.GlobalOrganization.Id ? t('globalPermissionTip') : t('strictPermissionOrgTip');
		} else {
			if (selected !== util.GlobalOrganization.Id) { tip = t('loosePermissionOrgTip'); }
		}

		return <div className='org-permission-list-container'>
			{!!tip && <div className='org-permission-tip'>
				<AriaIcon className='mdi mdi-help-circle-outline' />
				<span title={tip}> {tip}</span>
			</div>}
			<div className='permission-list-wrap'>
				<PermissionList
					allPermissions={allOrgPermissions}
					permissions={permissions}
					orgId={selected}
					disabledCheckBox={(enableStrictPermissionManagement && selected === curUser.orgId) || !hasManagePermissionForOrgsAndRoles(curUser.roles)}
					onChange={this.onOrganizationPermissionsTabChange}
					curUser={curUser}
					enableStrictPermissionManagement={enableStrictPermissionManagement}
				/>
			</div>
		</div >;
	}

	getTenantPath = (tenantId: string) => {
		const { items, t } = this.props;
		const tenant = items.find(t => t.id === tenantId);
		let tenantPath = tenant.name === 'Global' ? t('tntGlobal') : tenant.name;
		let parentItem = items.find(t => t.id === tenant.parentTenantId);
		while (parentItem) {
			tenantPath = `${parentItem.name === 'Global' ? t('tntGlobal') : parentItem.name}/${tenantPath}`;
			parentItem = items.find(t => t.id === parentItem.parentTenantId);
		}
		return tenantPath;
	}

	onPermissionSaveClick = async () => {
		const { newPermissions, permissionsChanged } = this.state;
		if (permissionsChanged) {
			const { selected, dispatch } = this.props;
			const cascadeDeletePermissions = await safeFetchV2(`/api/v2/identity/organizations/${selected}/permissions/cascade`, {
				credentials: 'same-origin',
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ permissions: newPermissions }),
			});
			if (cascadeDeletePermissions.result?.tenantCascadePermissions?.length > 0 || cascadeDeletePermissions.result?.roleCascadePermissions?.length > 0) {
				const tenantCascadeDeletePermissionDetail = cascadeDeletePermissions.result?.tenantCascadePermissions?.map(t => {
					return {
						path: this.getTenantPath(t.tenantId),
						permissions: t.permissions.map(p => window.AdminPortal.i18n.t(`common:${p}`)).join(', ')
					};
				}).sort((a, b) => a.path.localeCompare(b.path));
				const roleCascadeDeletePermissionDetail = cascadeDeletePermissions.result?.roleCascadePermissions?.map(r => {
					return {
						tenantPath: this.getTenantPath(r.tenantId),
						roleDisplayName: getRoleDisplayName(r.roleName),
						permissions: r.permissions
					};
				}).sort((a, b) => {
					const tenantPathComparison = a.tenantPath.localeCompare(b.tenantPath);
					if (tenantPathComparison !== 0) {
						return tenantPathComparison;
					} else {
						return a.roleDisplayName.localeCompare(b.roleDisplayName);
					}
				}).map(r => {
					return {
						path: `${r.tenantPath}/${r.roleDisplayName}`,
						permissions: r.permissions.map(p => window.AdminPortal.i18n.t(`common:${p}`)).join(', ')
					};
				});
				if (cascadeDeletePermissions.result?.tenantCascadePermissions?.length > 0 && cascadeDeletePermissions.result?.roleCascadePermissions?.length > 0) {
					this.setState({
						showCascadeDeletePermissionDialog: true,
						cascadeDeletePermissionMessage: {
							messageType: 'tenantAndRole',
							tenantMessageDetail: tenantCascadeDeletePermissionDetail,
							roleMessageDetail: roleCascadeDeletePermissionDetail,
						}
					});
				} else if (cascadeDeletePermissions.result?.tenantCascadePermissions?.length > 0) {
					this.setState({
						showCascadeDeletePermissionDialog: true,
						cascadeDeletePermissionMessage: {
							messageType: 'tenant',
							tenantMessageDetail: tenantCascadeDeletePermissionDetail,
						}
					});
				} else {
					this.setState({
						showCascadeDeletePermissionDialog: true,
						cascadeDeletePermissionMessage: {
							messageType: 'role',
							roleMessageDetail: roleCascadeDeletePermissionDetail,
						}
					});
				}
			} else {
				dispatch(tenantActionCreators.updatePermissionsInTenant(selected, newPermissions));
			}
		}
	}

	buildCascadeDeletePermissionDialog = () => {
		const { items, selected, dispatch, t } = this.props;
		const { newPermissions, showCascadeDeletePermissionDialog, cascadeDeletePermissionMessage } = this.state;
		if (!showCascadeDeletePermissionDialog) return null;
		const tenant = items.find(t => t.id === selected);
		const title = t('tntModifyPermissionScopeMessage', { tenantName: tenant.name });
		const dlgProps: ConfirmDialogProps = {
			parentSelector: (): HTMLElement => document.querySelector(util.portalAppId),
			title: t('tntModifyPermissionScope'),
			yesText: t('Yes'),
			noText: t('Close'),
			portalClassName: 'cascade-delete-permission-dialog',
			yesCallback: () => {
				dispatch(tenantActionCreators.updatePermissionsInTenant(selected, newPermissions));
				this.setState({ showCascadeDeletePermissionDialog: false });
			},
			noCallback: () => { this.setState({ showCascadeDeletePermissionDialog: false }); },
		};
		let cgrid: any;
		if (cascadeDeletePermissionMessage.messageType === 'tenantAndRole') {
			cgrid = <div>
				<OrganizationCascadeDeletePermissionCard type={'tenant'} rows={cascadeDeletePermissionMessage.tenantMessageDetail} />
				<OrganizationCascadeDeletePermissionCard type={'role'} rows={cascadeDeletePermissionMessage.roleMessageDetail} />
			</div>;
		} else if (cascadeDeletePermissionMessage.messageType === 'tenant') {
			cgrid = <OrganizationCascadeDeletePermissionCard type={'tenant'} rows={cascadeDeletePermissionMessage.tenantMessageDetail} />;
		} else {
			cgrid = <OrganizationCascadeDeletePermissionCard type={'role'} rows={cascadeDeletePermissionMessage.roleMessageDetail} />;
		}

		return <ConfirmDialog {...dlgProps} >
			<div className='cascade-delete-permission-title'>{title}</div>
			{cgrid}
		</ConfirmDialog>;
	}

	render() {
		const { items, curUser, selected, isAdding, isEditing, showTenantEditor, showSchemaEditor, showTenantPropEditor, enableStrictPermissionManagement, t } = this.props;
		const leftPartBusy = showTenantEditor || showSchemaEditor || showTenantPropEditor;
		const { selectedTabIndex, permissionsChanged, portalConfigurationDefaultState } = this.state;
		const item = items.find(it => it.id === selected);
		if (!item && !isAdding) return null;

		const tabHeaderItems = [
			{ name: t('tntTenantBasicInformation'), content: this.buildBasicInformation() },
		];
		if (selected) {
			tabHeaderItems.push({ name: enableStrictPermissionManagement ? t('tntTenantPermissionScope') : t('tntTenantPermissions'), content: this.buildOrganizationPermissions() });
			const portalConfiguration = (window.AdminPortal as any).PortalConfiguration as React.ComponentClass<any>;
			if (selected !== 'global' && !isAdding && isAdministrator(curUser.roles) && portalConfiguration && portalConfigurationDefaultState) {
				const component = React.createElement(portalConfiguration, {
					activeItem: portalConfigurationDefaultState.activeItem,
					activeSection: portalConfigurationDefaultState.activeSection,
					simulateOrgId: selected,
					selectedIndex: selectedTabIndex - 2
				});
				tabHeaderItems.push({ name: window.AdminPortal.i18n.t('portal:pcPortalSetting'), content: component });
				for (const portalItem of portalConfigurationDefaultState.activeSection.items) {
					if (!portalItem.parentId || portalItem.parentId !== 'portal' || portalItem.hidden) {
						continue;
					}
					tabHeaderItems.push({ name: window.AdminPortal.i18n.t(`portal:${portalItem.id}!title`), content: component });
				}
			}
		}
		const onSelectedTabChanged = (idx: number) => this.setState({ selectedTabIndex: idx });
		const tabContainerProps: TabContainerProps = {
			items: tabHeaderItems,
			onSelectedTabChanged,
			disabled: !!isAdding || !!isEditing,
			selectedIndex: selectedTabIndex,
		};

		let displayName = isAdding ? t('tntNewOrganization') : item && item.name;
		if (displayName === 'Global') {
			displayName = t('tntGlobal');
		}
		const detailEditorContent = (
			<React.Fragment>
				<div className='tnt-name' title={displayName}><span>{displayName}</span></div>
				<div className='organization-tab'>
					<TabContainer {...tabContainerProps} />
				</div>
				{selectedTabIndex === 1 &&
					<div className='ec-footer'>
						<Button
							style='accent'
							className='save-btn'
							text={t('common:cmSaveChanges')}
							title={t('common:cmSaveChanges')}
							disabled={!permissionsChanged}
							onClick={this.onPermissionSaveClick}
							width='100px'
						/>
					</div>
				}
			</React.Fragment>
		);

		return (
			<div className='tnt-detail-editor'>
				{leftPartBusy ? null : detailEditorContent}
				{this.buildCascadeDeletePermissionDialog()}
			</div>
		);
	}
}

export const OrganizationDetailEditor = connect(
	(state: { tenant: OrganizationState, navApp: { user: User, sections: any[] } }) => ({
		items: state.tenant.items,
		selected: state.tenant.selected,
		showMembersEditor: state.tenant.showMembersEditor,
		showTenantEditor: state.tenant.showTenantEditor,
		showSchemaEditor: state.tenant.showSchemaEditor,
		showTenantPropEditor: state.tenant.showTenantPropEditor,
		isAdding: state.tenant.isAdding,
		isEditing: state.tenant.isEditing,
		usersDic: state.tenant.usersDic,
		permissionsDic: state.tenant.permissionsDic,
		availablePermissions: state.tenant.availablePermissions,
		curUser: state.navApp.user,
		sections: state.navApp.sections,
		enableStrictPermissionManagement: state.tenant.enableStrictPermissionManagement,
	})
)(OrganizationDetailEditorInner) as React.ComponentClass<{}>;