export function copyTextToClipboard(text: string) {
	const textArea = document.createElement('textarea');
	textArea.style.position = 'fixed';
	textArea.style.top = '-500px';
	textArea.style.left = '-500px';
	textArea.style.width = '32px';
	textArea.style.height = '32px';
	textArea.style.padding = '0';
	textArea.style.border = 'none';
	textArea.style.outline = 'none';
	textArea.style.boxShadow = 'none';
	textArea.style.background = 'transparent';

	textArea.value = text;
	document.body.appendChild(textArea);
	textArea.focus();
	textArea.select();
	try {
		document.execCommand('copy');
		const msg = window.AdminPortal.i18n.t('account:gtClipboardSuccess');
		window.AdminPortal.Notifications.Send(2, msg, msg, 5000);
	} catch (err) {
		const msg = window.AdminPortal.i18n.t('account:gtClipboardError');
		window.AdminPortal.Notifications.Send(0, msg, msg);
	}

	document.body.removeChild(textArea);
}