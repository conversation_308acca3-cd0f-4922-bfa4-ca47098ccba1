import * as React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';
import { InputEditor, Button, ConfirmDialog, ConfirmDialogProps, AriaIcon } from 'gces-ui';
import * as classnames from 'classnames';
import { Scrollbars } from 'gces-react-custom-scrollbars';

import { Role, RoleState, roleActionCreators } from '../store';
import * as util from '../../../util';
import { RoleConsts } from '../../../Register/Common';

interface ConnectedProps {
	user: User;
	roles: Role[];
	selectedRoleId: string;
	selectedOrganizationId: string;
	isAddingRole: boolean;
	enableStrictPermissionManagement: boolean;
	dispatch: any;
	t: any;
}

interface LocalState {
	search: string;
	roleName: string;
	deletingRole: Role;
}

@translate('role', { wait: true })
class RolesContainerInner extends React.PureComponent<ConnectedProps, LocalState> {
	state: LocalState = {
		search: '',
		roleName: '',
		deletingRole: null,
	};

	onSearchChange = (value: string) => {
		this.setState({ search: value });
	}

	renderSearchBoxContainer = () => {
		const onAddRoleClick = () => {
			this.setState({ roleName: '' });
			this.props.dispatch(roleActionCreators.setIsAddingRole(true));
			this.props.dispatch(roleActionCreators.setIsAddingMembers(false));
		};
		return (
			<div className='search-box-container'>
				<div className='search-box-wp'>
					<div className='search-box'>
						<AriaIcon type='span' className='mdi mdi-magnify sc-icon' />
						<InputEditor
							value={this.state.search}
							className='sc-input'
							onEveryChange={this.onSearchChange}
						/>
					</div>
				</div>
				<Button
					style='accent'
					size='small'
					aid='add-role-btn'
					icon='mdi mdi-plus'
					text={this.props.t('cmAddRole')}
					title={this.props.t('cmAddRole')}
					onClick={onAddRoleClick}
				/>
			</div>
		);
	}
	renderRoleList = () => {
		const scrollbarsProps = {
			style: { width: '100%', height: '100%' },
			renderThumbVertical: props => <div {...props} style={{ width: '4px' }} className='ef-thumb-vertical' />,
			renderTrackHorizontal: props => <div {...props} style={{ display: 'none' }} />,
			renderThumbHorizontal: props => < div {...props} style={{ display: 'none' }} />,
			autoHide: false,
		};
		const searchFilter = (role: Role) => !this.state.search
			|| role.displayName.toLowerCase().indexOf(this.state.search.toLowerCase()) !== -1;
		const roles = this.props.roles.filter(searchFilter);

		return (
			<div className='role-list'>
				<Scrollbars {...scrollbarsProps}>
					{roles.length > 0
						? roles.map(this.renderRoleItem)
						: (this.state.search && <div className='empty-result-tip'>{this.props.t('cmEmptyResult')}</div>)
					}
				</Scrollbars>
			</div>
		);
	}

	onSelectRole = (roleId: string) => {
		const { selectedOrganizationId, enableStrictPermissionManagement, dispatch } = this.props;
		dispatch(roleActionCreators.selectRole(selectedOrganizationId, roleId, enableStrictPermissionManagement));
		if (selectedOrganizationId === util.GlobalOrganization.Id && roleId === RoleConsts.everyone.id) {
			dispatch(roleActionCreators.setIsAddingMembers(false));
		}
		if (selectedOrganizationId === roleId) {
			dispatch(roleActionCreators.setIsAddingMembers(false));
		}
	}

	renderRoleItem = (role: Role, index: number) => {
		const { selectedRoleId, t } = this.props;
		const className = classnames(
			'role-item',
			{
				odd: (index + 1) % 2 !== 0,
				selected: selectedRoleId === role.id,
			}
		);
		const onDeleteRole = (e: MouseEvent) => {
			e.stopPropagation();
			e.preventDefault();
			this.setState({ deletingRole: role });
		};

		const docDraftEnabled = window.AdminPortal.EnableDocumentDraft;
		const canDelete = !docDraftEnabled
			? ((!role.isBuiltin) || (role.isBuiltin && role.name === RoleConsts.orgapprover.name) || role.id === RoleConsts.approver.id)
			: !role.isBuiltin;
		return (
			<div
				className={className}
				key={role.name}
				onClick={() => this.onSelectRole(role.id)}
			>
				<i className='role-item-icon mdi mdi-account-group-outline' />
				<span className='role-name'>{role.displayName || role.name}</span>
				{canDelete &&
					<Button
						rounded
						size='small'
						style='transparent'
						icon='mdi mdi-delete-forever'
						title={t('rcDeleteRole')}
						onClick={onDeleteRole}
					/>
				}
			</div>
		);
	}
	renderAddRole = () => {
		const { t, dispatch, selectedOrganizationId, enableStrictPermissionManagement } = this.props;
		const onCancelClick = () => {
			this.setState({ roleName: '' });
			dispatch(roleActionCreators.setIsAddingRole(false));
		};
		return (
			<div className='add-role-container'>
				<div className='ar-title' title={t('cmAddRole')}>{t('cmAddRole')}</div>
				<div className='ar-body'>
					<InputEditor
						value={this.state.roleName}
						placeholder={t('rcRoleName')}
						onEveryChange={(roleName) => this.setState({ roleName })}
						maxLength={64}
					/>
				</div>
				<div className='ar-footer'>
					<Button
						size='small'
						text={t('cmCancel')}
						title={t('cmCancel')}
						onClick={onCancelClick}
					/>
					<Button
						style='accent'
						size='small'
						text={t('cmAdd')}
						title={t('cmAdd')}
						onClick={() => dispatch(roleActionCreators.addRole(this.state.roleName, selectedOrganizationId, enableStrictPermissionManagement))}
						disabled={this.state.roleName.trim() === '' ? true : false}
					/>
				</div>
			</div>
		);
	}

	buildDeletingConfirmDialog = () => {
		const { deletingRole } = this.state;
		if (!deletingRole) return null;

		const { selectedOrganizationId, enableStrictPermissionManagement, t, dispatch } = this.props;
		const dlgProps: ConfirmDialogProps = {
			parentSelector: (): HTMLElement => document.querySelector(util.portalAppId),
			title: t('rcDeleteRole'),
			yesText: t('cmDelete'),
			noText: t('cmCancel'),
			portalClassName: '',
			yesCallback: () => {
				this.setState({ deletingRole: null });
				dispatch(roleActionCreators.deleteRole(selectedOrganizationId, deletingRole.id, deletingRole.name, enableStrictPermissionManagement));
			},
			noCallback: () => this.setState({ deletingRole: null }),
		};
		return <ConfirmDialog {...dlgProps} >{t('rcDeleteRoleConfirmMessage', { RoleName: deletingRole.name })}</ConfirmDialog>;
	}

	render() {
		const content = (
			<>
				{this.renderSearchBoxContainer()}
				{this.renderRoleList()}
				{this.buildDeletingConfirmDialog()}
			</>
		);
		return (
			<div className='roles-container'>
				{this.props.isAddingRole
					? this.renderAddRole()
					: content}
			</div>
		);
	}
}

export const RolesContainer = connect(
	(state: { role: RoleState, navApp: { user: User } }) => ({
		user: state.navApp.user,
		roles: state.role.roles,
		selectedRoleId: state.role.selectedRoleId,
		selectedOrganizationId: state.role.selectedOrganizationId,
		isAddingRole: state.role.isAddingRole,
		enableStrictPermissionManagement: state.role.enableStrictPermissionManagement,
	})
)(RolesContainerInner) as React.ComponentClass<{}>;