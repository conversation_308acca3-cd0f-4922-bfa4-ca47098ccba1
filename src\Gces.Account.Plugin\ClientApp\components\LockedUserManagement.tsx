import * as React from 'react';
import { connect } from 'react-redux';
import { Scrollbars } from 'gces-react-custom-scrollbars';
import { translate } from 'react-i18next';

import CGrid, { CGridProps } from 'gces-react-grid';
import ConfirmDialog from './ConfirmDialog';
import EmptyPage from './EmptyPage';

import Actions from '../actions/actions';
import * as util from '../util';
import { Button } from 'gces-ui';

interface LockedUserItem {
	id: string;
	provider: string;
	username: string;
}

interface LockedUserManagementProps {
	lockedUsers: LockedUserItem[];
	isBusy: boolean;
}

interface ConnectProps {
	dispatch: any;
	t: any;
}

interface LocalState {
	needUnlockedIndex: number;
	needUnlockedUser?: any;
}

@translate('account', { wait: true })
class LockedUserManagement extends React.Component<LockedUserManagementProps & ConnectProps, LocalState> {
	constructor(props, context) {
		super(props, context);
		this.state = {
			needUnlockedIndex: null
		};
	}

	componentDidMount = () => {
		this.getLockedUsers();
	}

	getLockedUsers = (callback?: any) => {
		const { dispatch } = this.props;
		dispatch(Actions.StartLoading());
		util.ajax('/api/v2/identity/users/locked', (data) => {
			const { dispatch } = this.props;
			dispatch(Actions.SetLockedUsers(data));
			dispatch(Actions.EndLoading());
			if (callback) {
				callback(data);
			}
		});
	}

	unlockUser = () => {
		const { lockedUsers, dispatch } = this.props;
		const index = this.state.needUnlockedIndex;
		const url = `/api/v2/identity/users/${lockedUsers[index].id}/unlock`;
		const callback = () => {
			const users = lockedUsers.map(u => ({ ...u }));
			users.splice(index, 1);
			dispatch(Actions.SetLockedUsers(users));
		};
		setTimeout(() => this.setState({ needUnlockedIndex: null }), 10);
		util.ajax(url, callback, 'POST');
	}

	buildUnlockUserConfirmDlg = () => {
		const { lockedUsers, t } = this.props;
		const configDlgProps = {
			isOpen: this.state.needUnlockedUser !== null,
			parentSelector: () => document.querySelector(util.portalAppId),
			headerText: t('UnlockUser'),
			contentText: t('UnlockUserConfirm', { user: lockedUsers[this.state.needUnlockedIndex].username }),
			yesText: t('Yes'),
			closeText: t('Close'),
			cancelText: t('No'),
			onYes: this.unlockUser,
			onCancel: () => this.setState({ needUnlockedIndex: null }),
			onClose: () => this.setState({ needUnlockedIndex: null })
		};
		return <ConfirmDialog {...configDlgProps} />;
	}

	renderCell = (key: string, row) => {
		const { t } = this.props;
		if (key === 'actions') {
			return <div className='grid-actions'>
				<Button
					className='lock-user-action-btn'
					icon='mdi mdi-lock-open-outline'
					title={t('unlock')}
					onClick={() => this.setState({ needUnlockedIndex: row.idx })}
					style='transparent'
					size='small'
					rounded
				/>
			</div>;
		}
	}

	render() {
		const { lockedUsers, t, isBusy } = this.props;
		if (isBusy) return null;

		const scrollbarProps = {
			style: { width: '100%', height: '100%' },
			renderThumbVertical: props => <div {...props} style={{ width: '4px' }} className='thumb-vertical' />,
		};

		const gridProps: CGridProps = {
			columns: [{ key: 'username', label: t('Username')  }, { key: 'actions', width: 40 }],
			rows: lockedUsers.map((lu, i) => ({ idx: i, ...lu })),
			onRenderCell: this.renderCell,
			hideGridLine: true,
			rowHeight: 40,
		};

		const emptyPageProps = {
			imageName: 'locked-user-management',
			tip: t('NoLockedUserTip'),
		};
		if (lockedUsers && (lockedUsers.length === 0)) return <EmptyPage {...emptyPageProps} />;

		return <Scrollbars {...scrollbarProps}>
			<div className='locked-user-management'>
				{lockedUsers && < CGrid {...gridProps} />}
				{this.state.needUnlockedIndex !== null && this.buildUnlockUserConfirmDlg()}
			</div>
		</Scrollbars>;
	}
}

const connector = connect(state => {
	const lockedUserManagement = state['account-management'].lockedUser;
	return {
		...lockedUserManagement,
		isBusy: state['account-management'].common.isBusy,
	};
});
const translator = translate('account', { withRef: true });

export default translator(connector(LockedUserManagement));
