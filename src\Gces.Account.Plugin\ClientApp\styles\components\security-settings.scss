.security-settings {
	padding: 0 10px 20px 10px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 100%;

	.setting-items {
		margin-bottom: 10px;

		.setting-item-multiple {
			padding: 5px 0;
			display: flex;
			flex-direction: column;

			.setting-label {
				font-size: 12px;
				width: 232px;

				@include gces-truncate;
			}

			.setting-item {
				padding: 4px;

				.setting-label {
					font-size: 11px;
				}
			}
		}

		.setting-item {
			padding: 5px 0;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.setting-label {
				width: 230px;
				display: block;
				justify-content: flex-start;
				font-size: 12px;
				margin-right: 10px;
				white-space: normal;

				@include gces-truncate-2;
			}

			.setting-value {
				width: 180px;

				.efc-boolean .efc-radio span {
					color: $ef-text;
					max-width: 100px;

					@include gces-truncate;
				}
			}
		}

		.setting-description {
			padding: 0 5px;
			white-space: pre-wrap;
			color: $ef-sub-title-text;
			font-size: 11px;
			overflow: hidden;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-moz-box-orient: vertical;
			-webkit-line-clamp: 8;
		}
	}

	.settings-footer {
		display: flex;
		justify-content: flex-end;
		height: 40px;

		button {
			margin-right: 10px;

			>span {
				display: inline-block;
				max-width: 100px;

				@include gces-truncate;
			}
		}
	}
}