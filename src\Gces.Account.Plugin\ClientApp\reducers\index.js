﻿import { combineReducers } from 'redux'

// account management
import organization from './organization-reducer'
import user from './user-reducer'
import role from './role-reducer'
import property from './property-reducer'
import lockedUser from './locked-user-reducer'

// system configuration
import sp from './security-provider-reducers'
import ep from './external-provider-reducers'
import license from './license-reducer'
import cvlicense from './license-cv-reducer'
import concurrence from './concurrence-reducer'
import ss from './security-settings-reducer'
import common from './common-reducer'
import claimMappings from './claim-mappings-reducer'
import tfa from './tfa-reducer'
import iss from './inactive-session-reducer'

// generate token
import token from './generate-token-reducer'

export default combineReducers({
    organization,
    user,
    role,
    property,
    lockedUser,
    concurrence,
    claimMappings,

    sp,
    ep,

    license,
    cvlicense,
    ss,

	common,
	
    token,

    tfa,
    iss,
})
