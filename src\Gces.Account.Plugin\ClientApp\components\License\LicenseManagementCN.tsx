import * as React from 'react';
import * as Modal from 'react-modal';
import * as update from 'immutability-helper';
import { AriaIcon, BlockLoader, Button, Checkbox, DropdownItemProps, InputEditor } from 'gces-ui';
import { translate } from 'react-i18next';
import * as util from '../../util';
import { Sa3License, Sa3Status, Sa3StatusStrings } from './utils';
import { safeFetchV2 } from '../../utils/safeFetchV2';
import { copyTextToClipboard } from '../../util/generateTokenUtil';
import { Tooltip } from 'react-tooltip';
import CGrid, { CGridProps, Row } from 'gces-react-grid';
import QRCode from 'qrcode.react';
import { TFunction } from 'i18next';

type lmDialogType = 'activate'
	| 'deactivate' | 'deactivateOffline' | 'confirmDeactivateOffline'
	| 'migrate' | 'migrateOffline'
	| 'refresh'
	| 'reload'
	| 'cnActivate' | 'cnActivateOffline' | 'cnMigrateOffline' | 'cnRefreshOffline'
	| 'cnRefreshOfflineByIndicator'
	| 'remove'
	| 'upgrade'
	| 'none';

type lmDialogTypeExt = 'inputLicense' | 'cnGetLicenseData' | 'none';

interface OwnState {
	licenses: Sa3License[];
	busy: boolean;
	dialogType: lmDialogType;
	dialogTypeExt: lmDialogTypeExt;
	dialogBusy: boolean;
	dialogState: {
		key: string;
		oldKey: string;
		oldBasedKey: string;
		credential: string;
		needMigrate: boolean;
		offlineDeactivate: {
			clientCode: string;
			confirm: boolean;
		};
		generateClientCode: {
			type: 'activate' | 'migrate' | 'refresh';
			clientCode: string;
		};
		offlineImport: {
			type: 'activate' | 'migrate' | 'refresh';
			licenseData: string;
		}
		reload: {
			title: string;
			message: string;
		}
	};
	qrCodeString: string;
	clientIdentityCode: string;
	showLicenseDetail: boolean;
	selectedLicense: Sa3License;
}

interface LicenseManagementProps {
	t?: TFunction;
}

@translate('account', { wait: true })
export class LicenseManagement extends React.PureComponent<LicenseManagementProps, OwnState> {
	_productCodeCN: string = 'W';
	_hyphenCount = 5;
	_randomCharactersCount = 16;
	_validationBitCount = 3;
	_licenseKeyRegularLength = this._hyphenCount + this._randomCharactersCount + this._validationBitCount;
	_tempActivationPasswordLenth = 40;
	_shortCodeClientCodeLength = 6;
	_shortOfflineDeactivateClientCodeMaxLength = 15;

	state: OwnState = {
		busy: false,
		licenses: [],
		dialogType: 'none',
		dialogTypeExt: 'none',
		dialogBusy: false,
		dialogState: {
			credential: '',
			key: '',
			oldKey: '',
			oldBasedKey: '',
			needMigrate: false,
			generateClientCode: { clientCode: '', type: 'activate' },
			offlineImport: { type: 'activate', licenseData: '' },
			offlineDeactivate: { clientCode: '', confirm: false },
			reload: { title: '', message: '' },
		},
		qrCodeString: '',
		clientIdentityCode: '',
		showLicenseDetail: false,
		selectedLicense: null,
	};

	notifyLicenseErr = (errors: util.ApiV2Error[], summaryKey: string) => {
		const { t } = this.props;
		if (errors && errors.length > 0) {
			const errorDetails = errors.map(e => {
				let msg = t(e.code, e.message);
				if (e.context?.err) {
					msg += '\n\t' + t(`licenseErr_${e.context.err}`);
				}
				return msg;
			}).join('\n');
			window.AdminPortal.Notifications.Send(0, t(summaryKey), errorDetails);
		}
		else {
			window.AdminPortal.Notifications.Send(0, t(summaryKey), t(summaryKey));
		}
	}

	async componentDidMount() {
		this.setState({ busy: true });
		const { t } = this.props;
		const { result, error } = await safeFetchV2('/api/v2/license', { credentials: 'same-origin', headers: { 'Content-Type': 'application/json' } });
		if (error || (result as any).errors) {
			util.notificationApiV2Errors(error.errors, t('GetLicenseError'), t, 'account');
		} else {
			const licenses = (result as Sa3License[]).filter(r => !!r.displayKey);
			this.setState({ licenses });
		}
		this.setState({ busy: false });
	}

	isLicenseKeyValid = (key: string): boolean => {
		const prodCode = this._productCodeCN;
		if (key.length === 29 && !key.startsWith(this._productCodeCN)) {
			return true;
		}
		if (key.startsWith(this._productCodeCN) && key.length === prodCode.length + this._licenseKeyRegularLength) return true;

		return false;
	}

	onOpenActivationDialog = () => {
		this.setState({
			dialogType: 'cnActivate',
		});
	}

	getDisplayDate = (val: string) => {
		if (!val) {
			return this.props.t('NoExpire');
		}
		return (new Date(val)).toLocaleDateString();
	}

	localeItemCount = (val: number): string => {
		if (val === -1) return this.props.t('Unlimited');
		return val.toString();
	}

	renderLicenseKey = (license: Sa3License, type: 'normal' | 'warn' | 'error', tip: string) => {
		const licenseKey = license.displayKey;
		return <div className='license-item-key'>
			<div className='display-key'>
				<div>{licenseKey}</div>
				{license.needMigration &&
					<div className={`license-item-key-tips ${type}`}>
						<AriaIcon className={'mdi mdi-alert-circle-outline'} />
						<div className='need-migration-tip'>{tip}</div>
					</div>
				}
			</div>
		</div>;
	}

	getLicenseTypeAndTip = (license: Sa3License, formalLicensesCanMigrate: Sa3License[], trialLicensesCanMigrate: Sa3License[], keyToMigrate: Sa3License): { type: 'normal' | 'error' | 'warn', tip: string } => {
		const { t } = this.props;
		const { licenses } = this.state;
		if (!license?.needMigration) return { type: 'normal', tip: null };

		const hasSa3Key = licenses.filter(l => !l.needMigration).length > 0;
		if (hasSa3Key) return { type: 'error', tip: t('OldLicenseShouldBeDeactivated') };
		if (keyToMigrate?.credential === license.credential) return { type: 'warn', tip: t('MigrateByTheActionBtn') };

		const cannotMigrate = license.status !== Sa3Status.Licensed && license.status !== Sa3Status.GracePeriod && license.status !== Sa3Status.VersionNotMatch;
		if (cannotMigrate) return { type: 'error', tip: t('InvalidLicenseCannotMigrate', { status: t(Sa3StatusStrings[license.status]) }) };

		if (license.isTrial) {
			if (formalLicensesCanMigrate.length > 0) return { type: 'error', tip: t('hasFormalKeyToMigrate') };
			if (trialLicensesCanMigrate.length > 1) return { type: 'warn', tip: t('ContactToMarketToMigrate') };
			else return { type: 'warn', tip: t('MigrateByTheActionBtn') };
		} else {
			if (formalLicensesCanMigrate.length > 1) return { type: 'warn', tip: t('ContactToMarketToMigrate') };
			else return { type: 'warn', tip: t('MigrateByTheActionBtn') };
		}
	}

	getCNDisplayInfo = (license: Sa3License) => {
		const { t } = this.props;
		return (
			<div className='license-item-detail'>
				<div className='license-summary'>
					{license.content.dbd.enabled && this.renderLicenseItem(t('dbdConcurrence'), this.localeItemCount(license.content.dbd.concurrence))}
					{license.content.rpt.enabled && this.renderLicenseItem(t('rptConcurrence'), this.localeItemCount(license.content.rpt.concurrence))}
				</div>
				<div className='license-item-detail-btn' onClick={() => this.showLicenseDetail(license)}>
					{t('detail')}
				</div>
			</div>
		);
	}

	renderLicenseItem = (title: string, content: string) => {
		return <div className='detail-item'>
			<div className='item-title' title={title}>{title}:</div>
			<div className='item-content' title={content}>{content}</div>
		</div>;
	}

	showLicenseDetail = (license: Sa3License) => {
		this.setState({
			showLicenseDetail: true,
			selectedLicense: license,
		});
	}

	closeLicenseDetail = () => {
		this.setState({
			showLicenseDetail: false,
		});
	}

	getDialogContent = (license: Sa3License) => {
		const { t } = this.props;
		return <div className='license-item-detail-content'>
			<div className='detail-inner'>
				{
					this.renderLicenseItem(t('IsTrial'), license.isTrial ? t('True') : t('False'))
				}
				{
					license.content.dbd.enabled && this.renderLicenseItem(t('dbdConcurrence'), this.localeItemCount(license.content.dbd.concurrence))
				}
				{
					license.content.rpt.enabled && this.renderLicenseItem(t('rptConcurrence'), this.localeItemCount(license.content.rpt.concurrence))
				}
				{
					license.content.dbd.enabled && this.renderLicenseItem(t('dbdModule'), t('Included'))
				}
				{
					license.content.rpt.enabled && this.renderLicenseItem(t('rptModule'), t('Included'))
				}
				{
					license.content.dbd.nlp && this.renderLicenseItem(t('nlpFeature'), t('Included'))
				}
				{
					license.content.dataMonitoring.enabled && this.renderLicenseItem(t('dataMonitoringModule'), t('Included'))
				}
				{
					license.content.dbd.enabled && this.renderLicenseItem(t('dbdDocCount'), this.localeItemCount(license.content.dbd.documentCount))
				}
				{
					license.content.rpt.enabled && this.renderLicenseItem(t('rptDocCount'), this.localeItemCount(license.content.rpt.documentCount))
				}
				{
					license.content.data.enabled && this.renderLicenseItem(t('dcsDocCount'), this.localeItemCount(license.content.data.datasourceCount))
				}
				{
					license.content.multiServers.enabled && this.renderLicenseItem(t('ServerCount'), this.localeItemCount(license.content.multiServers.nodeCount))
				}
			</div>
		</div>;
	}

	onOpenDialog = (type: lmDialogType, license: Sa3License = null) => {
		this.setState({
			dialogType: type,
		});
		if (type !== 'none' && license !== null) {
			this.setState({
				dialogState: update(this.state.dialogState, {
					key: { $set: '' },
					oldKey: { $set: license.displayKey },
					oldBasedKey: { $set: license.basedKey },
					credential: { $set: license.credential },
					needMigrate: { $set: license.needMigration },
				})
			});
		}
	}

	clearDialog = (close: boolean = true) => {
		this.setState({
			dialogState: {
				credential: '',
				key: '',
				oldKey: '',
				oldBasedKey: '',
				needMigrate: false,
				generateClientCode: { clientCode: '', type: 'activate' },
				offlineImport: { type: 'activate', licenseData: '' },
				offlineDeactivate: { clientCode: '', confirm: false },
				reload: { title: '', message: '' },
			}
		});
		if (close) {
			this.setState({ dialogType: 'none', dialogTypeExt: 'none' });
		}
	}

	handleLicenseActions = async (index: number, value: string) => {
		const { licenses } = this.state;
		const license = licenses[index];
		if (!license) return;
		switch (value) {
			case 'refresh':
			case 'deactivate':
			case 'migrate':
			case 'remove':
				this.onOpenDialog(value, license);
			case 'upgrade':
				this.onOpenDialog(value, license);
				this.setState({ dialogTypeExt: 'inputLicense' });
			default:
				return;
		}
	}

	activateAsync = async () => {
		this.setState({ dialogBusy: true });
		const { dialogState } = this.state;
		const { result, error } = await safeFetchV2('/api/v2/license', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ key: dialogState.key }) });
		const { t } = this.props;
		if (!error && result?.success) {
			if (result.warning && result.warning.warningCode !== 'None') window.AdminPortal.Notifications.Send(1, t('licenseWarn'), t(`licenseWarn_${result.warning.warningCode}`, result.warning.warningMessage));

			const newDialogState = update(this.state.dialogState, { reload: { $set: { title: t('ActivateSuccess'), message: t('ActivateSuccessMessage') } }, });
			this.setState({ dialogState: newDialogState, dialogType: 'reload' });
		}
		else {
			this.notifyLicenseErr(error?.errors, 'ActivateError');
		}
		this.setState({ dialogBusy: false });
	}

	activateOfflineAsync = async () => {
		this.setState({ dialogBusy: true });
		const { dialogState } = this.state;
		const { result, error } = await safeFetchV2('/api/v2/license/offline', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ key: dialogState.key }) });

		if (!error && result?.success) {
			const newDialogState = update(this.state.dialogState, { generateClientCode: { $set: { type: 'activate', clientCode: result.data } }, });
			this.setState({ dialogState: newDialogState });
		} else {
			this.notifyLicenseErr(error?.errors, 'OfflineActivateError');
		}
		this.setState({ dialogBusy: false });
	}

	refreshAsync = async () => {
		const { dialogState } = this.state;
		if (dialogState.needMigrate) window.AdminPortal.Notifications.Send(0, this.props.t('oldLicenseCannotSupportOperation', { operation: this.props.t('Refresh') }), null);
		this.setState({ dialogBusy: true });
		const { result, error } = await safeFetchV2('/api/v2/license', { method: 'PUT', headers: { 'Content-Type': 'application/json' } });
		const { t } = this.props;
		if (result && result.success) {
			if (result.warning && result.warning.warningCode !== 'None') window.AdminPortal.Notifications.Send(1, t('licenseWarn'), t(`licenseWarn_${result.warning.warningCode}`, result.warning.warningMessage));
			const newDialogState = update(this.state.dialogState, { reload: { $set: { title: t('RefreshSuccess'), message: t('RefreshSuccessMessage') } }, });
			this.setState({ dialogState: newDialogState, dialogType: 'reload' });
		} else {
			this.notifyLicenseErr(error?.errors, 'RefreshError');
		}
		this.setState({ dialogBusy: false });
	}

	refreshOfflineAsync = async () => {
		this.setState({ dialogBusy: true });
		const { dialogState } = this.state;
		const { result, error } = await safeFetchV2('/api/v2/license/offline', { method: 'PUT', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ key: dialogState.key }) });

		if (!error && result?.success) {
			const newDialogState = update(this.state.dialogState, { generateClientCode: { $set: { type: 'refresh', clientCode: result.data } }, });
			this.setState({ dialogState: newDialogState });
		} else {
			this.notifyLicenseErr(error?.errors, 'OfflineRefreshError');
		}
		this.setState({ dialogBusy: false });
	}

	deactivateAsync = async () => {
		this.setState({ dialogBusy: true });
		const { dialogState } = this.state;
		const { result, error } = await safeFetchV2(`/api/v2/license/${dialogState.credential + (dialogState.needMigrate ? '?isOld=true' : '')}`, { method: 'DELETE', headers: { 'Content-Type': 'application/json' } });
		const { t } = this.props;
		if (result && result.success) {
			if (result.warning && result.warning.warningCode !== 'None') window.AdminPortal.Notifications.Send(1, t('licenseWarn'), t(`licenseWarn_${result.warning.warningCode}`, result.warning.warningMessage));
			const newDialogState = update(this.state.dialogState, { reload: { $set: { title: t('DeactivateSuccess'), message: t('DeactivateSuccessMessage') } }, });
			this.setState({ dialogState: newDialogState, dialogType: 'reload' });
		} else {
			this.notifyLicenseErr(error?.errors, 'DeactivateError');

		}
		this.setState({ dialogBusy: false });
	}

	deactivateOfflineAsync = async () => {
		this.setState({ dialogBusy: true });
		const { dialogState } = this.state;
		const { result, error } = await safeFetchV2(`/api/v2/license/${dialogState.credential}/offline${dialogState.needMigrate ? '?isOld=true' : ''}`, { method: 'DELETE', headers: { 'Content-Type': 'application/json' } });
		if (result && result.success) {
			const newDialogState = update(this.state.dialogState, { offlineDeactivate: { $set: { clientCode: result.data, confirm: false } } });
			this.setState({ dialogState: newDialogState, dialogType: 'deactivateOffline' });
		}
		else {
			this.notifyLicenseErr(error?.errors, 'OfflineDeactivateError');

		}
		this.setState({ dialogBusy: false });
	}

	migrateAsync = async () => {
		this.setState({ dialogBusy: true });
		const { dialogState } = this.state;
		const { result, error } = await safeFetchV2(`/api/v2/license/migrate/${dialogState.credential}`, { method: 'POST', headers: { 'Content-Type': 'application/json' } });
		const { t } = this.props;
		if (result && result.success) {
			if (result.warning && result.warning.warningCode !== 'None') window.AdminPortal.Notifications.Send(1, t('licenseWarn'), t(`licenseWarn_${result.warning.warningCode}`, result.warning.warningMessage));
			const newDialogState = update(this.state.dialogState, { reload: { $set: { title: t('RefreshSuccess'), message: t('RefreshSuccessMessage') } }, });
			this.setState({ dialogState: newDialogState, dialogType: 'reload' });
		} else {
			this.notifyLicenseErr(error?.errors, 'RefreshError');
		}
		this.setState({ dialogBusy: false });
	}

	migrateOfflineAsync = async () => {
		this.setState({ dialogBusy: true });
		const { dialogState } = this.state;
		const { result, error } = await safeFetchV2(`/api/v2/license/migrate/offline/${dialogState.credential}`, { method: 'POST', headers: { 'Content-Type': 'application/json' } });
		if (result && result.success) {
			const newDialogState = update(this.state.dialogState, { generateClientCode: { $set: { type: 'migrate', clientCode: result.data } } });
			this.setState({ dialogState: newDialogState });
		}
		else {
			this.notifyLicenseErr(error?.errors, 'OfflineRefreshError');
		}
		this.setState({ dialogBusy: false });
	}

	importOfflineAsync = async (type: 'activate' | 'refresh' | 'migrate') => {
		const { dialogState, dialogType } = this.state;
		const licenseData = dialogState.offlineImport.licenseData;

		// license activated by indicator must refresh by whole license data
		if ((dialogType === 'cnRefreshOfflineByIndicator' && licenseData.length < this._tempActivationPasswordLenth)) {
			this.notifyLicenseErr([{ code: 'ImportLicenseError', message: 'ImportLicenseError', context: {} }], 'ImportLicenseError');
			return;
		}

		// temp activation password can used only for offline activation
		if (licenseData.length < this._tempActivationPasswordLenth && dialogType !== 'cnActivateOffline') {
			this.notifyLicenseErr([{ code: 'ImportLicenseError', message: 'ImportLicenseError', context: {} }], 'ImportLicenseError');
			return;
		}

		this.setState({ dialogBusy: true });
		const { result, error } = await safeFetchV2('/api/v2/license/import', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ licenseContent: licenseData }) });
		const { t } = this.props;
		if (result && result.success) {
			if (result.warning && result.warning.warningCode !== 'None') window.AdminPortal.Notifications.Send(1, t('licenseWarn'), t(`licenseWarn_${result.warning.warningCode}`, result.warning.warningMessage));
			const reloadTitle = type === 'activate' ? t('ActivateSuccess') : t('RefreshSuccess');
			const reloadMessage = type === 'activate' ? t('ActivateSuccessMessage') : t('RefreshSuccessMessage');
			const newDialogState = update(this.state.dialogState, { reload: { $set: { title: reloadTitle, message: reloadMessage } }, });
			this.setState({ dialogState: newDialogState, dialogType: 'reload' });
		} else {
			this.notifyLicenseErr(error?.errors, 'ImportLicenseError');
		}
		this.setState({ dialogBusy: false });
	}

	upgradeTempLicense = async () => {
		this.setState({ dialogBusy: true });
		const { dialogState } = this.state;
		const LicenseData = dialogState.offlineImport.licenseData;
		const { result, error } = await safeFetchV2('/api/v2/license/upgrade', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ licenseContent: LicenseData }) });
		this.setState({ dialogBusy: false });
		const { t } = this.props;
		if (result && result.success) {
			if (result.warning && result.warning.warningCode !== 'None') window.AdminPortal.Notifications.Send(1, t('licenseWarn'), t(`licenseWarn_${result.warning.warningCode}`, result.warning.warningMessage));
			const newDialogState = update(this.state.dialogState, { reload: { $set: { title: t('ActivateSuccess'), message: t('ActivateSuccessMessage') } }, });
			this.setState({ dialogState: newDialogState, dialogType: 'reload' });
		} else {
			this.notifyLicenseErr(error?.errors, 'UpgradeLicenseError');
		}
	}

	getClientIdentityCodeAsync = async () => {
		const { dialogState } = this.state;
		const { result, error } = await safeFetchV2('/api/v2/license/client-code', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ key: dialogState.key }) });
		if (!error && result?.success) {
			return result.data;
		}
		else {
			this.notifyLicenseErr(error?.errors, 'GetClientIdentityCodeError');
		}
	}

	onQrCodeClick = async (e: React.MouseEvent<HTMLDivElement>) => {
		const { dialogType } = this.state;
		if (dialogType === 'cnRefreshOffline' || dialogType === 'upgrade' || dialogType === 'cnMigrateOffline' || dialogType === 'deactivateOffline') return;
		if (e.altKey && e.ctrlKey && e.shiftKey) {
			this.setState({ dialogBusy: true });
			const clientCode = await this.getClientIdentityCodeAsync();
			if (!!clientCode) {
				this.setState({ clientIdentityCode: clientCode });
			}
			this.setState({ dialogBusy: false });
		}
	}

	modalProps = {
		isOpen: true,
		parentSelector: () => document.querySelector('#portal-modals'),
		className: {
			base: 'modal-dialog ReactModalPortal',
			afterOpen: 'license-dialog cn-license-dialog',
			beforeClose: ''
		},
		overlayClassName: {
			base: 'modal fade ReactModal__Overlay--after-open',
			afterOpen: 'show',
			beforeClose: ''
		},
		style: { overlay: { display: 'block', zIndex: '10000' } },
		ariaHideApp: false
	};

	renderMigrateDialog = () => {
		const { t } = this.props;
		const { dialogState, dialogType } = this.state;
		if (dialogType !== 'migrate') return null;
		const title = t('Refresh');

		const onMigrateClick = async () => {
			this.setState({ dialogType: 'cnMigrateOffline' });
		};

		return <div className='static-modal'>
			<Modal {...this.modalProps}>
				<div className='modal-content'>
					<div className='modal-header'>
						{title}
					</div>
					<div className='modal-body'>
						<span>
							{t('RefreshConfirmMessage', { license: dialogState.needMigrate ? dialogState.oldBasedKey : dialogState.oldKey })}
						</span>
					</div>
					<div className='modal-footer'>
						<Button style='accent' onClick={onMigrateClick} text={t('MigrateOffline')} title={t('MigrateOffline')} />
						<Button style='accent' onClick={this.migrateAsync} text={title} title={title} />
						<Button onClick={this.clearDialog} text={t('Cancel')} title={t('Cancel')} />
					</div>
				</div>
				{this.state.dialogBusy && <BlockLoader />}
			</Modal>
		</div>;
	}

	renderCNActivateDialog = () => {
		const { t } = this.props;
		const { dialogState, dialogType, dialogBusy } = this.state;
		if (dialogType !== 'cnActivate') return null;
		const title = t('Activate');
		const btnText = t('ActivateOnline');
		const offlineBtnText = t('ActivateOffline');
		const offlineBtnCallback = async () => {
			this.setState({ dialogType: 'cnActivateOffline' });
		};

		const btnCallbackAsync = () => this.activateAsync();
		const isKeyValid = this.isLicenseKeyValid(dialogState.key.trim());
		return <div className='static-modal'>
			<Modal {...this.modalProps}>
				<div className='modal-content'>
					<div className='modal-header'>
						{title}
					</div>
					<div className='modal-body'>
						<span className='license-key label-top'>{t('LicenseKey')}</span>
						<InputEditor
							placeholder={t('LicenseKey')}
							className='key-input'
							invalid={!isKeyValid}
							onEveryChange={(val) => { this.setState({ dialogState: update(dialogState, { key: { $set: val.trim() } }) }); }}
							onChange={(val) => { this.setState({ dialogState: update(dialogState, { key: { $set: val.trim() } }) }); }}
							value={dialogState.key}
						/>
						<Tooltip anchorSelect='.key-input' place='top' variant='error' positionStrategy='fixed' content={!isKeyValid && dialogState.key ? t('licenseKeyInvalid') : ''} />
					</div>
					<div className='modal-footer'>
						<Button style='accent' disabled={!isKeyValid} width='100px' onClick={btnCallbackAsync} text={btnText} title={btnText} />
						<Button style='accent' disabled={!isKeyValid} width='100px' onClick={offlineBtnCallback} text={offlineBtnText} title={offlineBtnText} />
						<Button onClick={this.clearDialog} width='100px' text={t('Cancel')} title={t('Cancel')} />
					</div>
				</div>
				{dialogBusy && <BlockLoader />}
			</Modal>
		</div>;
	}

	renderTips = (msg: string) => {
		return <div className='message-tip'>
			<i className='tip-icon mdi mdi-information-outline' />
			<span title={msg}>{msg}</span>
		</div>;
	}

	renderRefreshOfflineInputLicenseDialog = () => {
		const { t } = this.props;
		const { dialogState, dialogType, dialogTypeExt, dialogBusy } = this.state;
		if (!((dialogType === 'cnRefreshOffline' || dialogType === 'cnRefreshOfflineByIndicator' || dialogType === 'upgrade') && dialogTypeExt === 'inputLicense')) return null;
		const btnCallbackAsync = () => this.setState({ dialogTypeExt: 'none' });
		const isKeyValid = this.isLicenseKeyValid(dialogState.key.trim());

		const title = dialogType === 'upgrade' ? t('Upgrade') : t('RefreshOffline');
		const inputLabel = dialogType === 'upgrade' ? t('UpgradeInputLicense') : t('RefreshInputLicense');

		return <div className='static-modal'>
			<Modal {...this.modalProps}>
				<div className='modal-content'>
					<div className='modal-header'>
						{title}
					</div>
					<div className='modal-body'>
						<span className='license-key label-top' title={inputLabel}>{inputLabel}</span>
						<InputEditor
							placeholder={t('LicenseKey')}
							className='key-input'
							invalid={!isKeyValid}
							onEveryChange={(val) => { this.setState({ dialogState: update(dialogState, { key: { $set: val.trim() } }) }); }}
							onChange={(val) => { this.setState({ dialogState: update(dialogState, { key: { $set: val.trim() } }) }); }}
							value={dialogState.key}
						/>
						<Tooltip anchorSelect='.key-input' place='top' variant='error' positionStrategy='fixed' content={!isKeyValid && dialogState.key ? t('licenseKeyInvalid') : ''} />
					</div>
					<div className='modal-footer'>
						<Button style='accent' disabled={!isKeyValid} width='100px' onClick={btnCallbackAsync} text={t('OK')} title={t('OK')} />
						<Button onClick={this.clearDialog} width='100px' text={t('Cancel')} title={t('Cancel')} />
					</div>
				</div>
				{dialogBusy && <BlockLoader />}
			</Modal>
		</div>;
	}

	renderRefreshOfflineByIndicatorDialog = () => {
		const { t } = this.props;
		const { dialogState, dialogType, dialogTypeExt, dialogBusy } = this.state;
		if (dialogType !== 'cnRefreshOfflineByIndicator') return null;
		if (dialogTypeExt === 'inputLicense') return null;

		const title = t('RefreshOffline');
		const btnText = t('Refresh');
		const getOfflineLicenseDataBtnText = t('getOfflineLicenseData');
		const offlineCodeText = t('offlineRefreshCode');
		const curKey = dialogState.key;

		const onImport = async () => {
			await this.importOfflineAsync('refresh');
		};

		const onCancel = () => {
			this.setState({ dialogTypeExt: 'inputLicense' });
		};

		const getLicenseData = async () => {
			this.setState({ dialogBusy: true });
			const clientCode = await this.getClientIdentityCodeAsync();
			if (!!clientCode) {
				this.setState({ dialogState: update(dialogState, { generateClientCode: { $set: { clientCode } } }) });
			}
			this.setState({ dialogTypeExt: 'cnGetLicenseData', dialogBusy: false });
		};

		return <div className='static-modal'>
			<Modal {...this.modalProps}>
				<div className='modal-content'>
					<div className='modal-header'>
						<div className='header-inner'>
							<label>{title}</label>
							<Button
								className='close-btn'
								icon='mdi mdi-close'
								style='transparent'
								onClick={this.clearDialog}
								size='small'
								rounded
							/>
						</div>
					</div>
					<div className='modal-body'>
						<div className='license-key-container'>
							<span className='license-key label-top'>{t('LicenseKey')}</span>
							<InputEditor
								placeholder={t('LicenseKey')}
								className='key-input'
								value={curKey}
								readOnly
							/>
						</div>
						{this.renderTips(t('getOfflineLicenseDataTips', { action: title }))}
						<div className='license-data-container'>
							<label className='label-top'>{t('offlineLicenseData')}</label>
							<div className='activation-row'>
								<InputEditor
									placeholder={t('inputOfflineLicenseData')}
									className='license-data-input'
									onEveryChange={(val) => { this.setState({ dialogState: update(dialogState, { offlineImport: { licenseData: { $set: val } } }) }); }}
									onChange={(val) => { this.setState({ dialogState: update(dialogState, { offlineImport: { licenseData: { $set: val } } }) }); }}
									value={dialogState.offlineImport.licenseData}
								/>
								<Button
									style='accent'
									className='get-offline-license-data-btn'
									onClick={getLicenseData}
									text={getOfflineLicenseDataBtnText}
									title={getOfflineLicenseDataBtnText}
									size='small'
									disabled={!curKey}
								/>
							</div>
							{dialogTypeExt === 'cnGetLicenseData' &&
								<div className='offline-activation-code-container'>
									<label className='label-top' title={offlineCodeText}>{offlineCodeText}</label>
									<div className='activation-row offline-activation-code'>
										<InputEditor
											value={dialogState.generateClientCode.clientCode}
											readOnly
										/>
										<Button
											style='accent'
											size='small'
											onClick={() => copyTextToClipboard(dialogState.generateClientCode.clientCode)}
											className='copy-text'
											text={t('gtCopyOfflineLicenseString')}
										/>
									</div>
									{this.renderTips(t('obtainOfflineLicenseDataTips', { offlineCode: offlineCodeText }))}
									<div className='arrow-place-holder' />
								</div>
							}
						</div>
					</div>
					<div className='modal-footer'>
						<Button style='accent' disabled={!dialogState.offlineImport.licenseData} width='100px' onClick={onImport} text={btnText} title={btnText} />
						<Button onClick={onCancel} width='100px' text={t('Cancel')} title={t('Cancel')} />
					</div>
				</div>
				{dialogBusy && <BlockLoader />}
			</Modal>
		</div>;
	}

	renderCNActivateOfflineDialog = () => {
		const { t } = this.props;
		const { dialogState, dialogType, dialogTypeExt, dialogBusy } = this.state;
		if (dialogType !== 'cnActivateOffline' && dialogType !== 'cnMigrateOffline' && dialogType !== 'cnRefreshOffline' && dialogType !== 'upgrade') return null;
		if (dialogType === 'cnRefreshOffline' || dialogType === 'upgrade') {
			if (dialogTypeExt === 'inputLicense') return null;
		}
		const title = dialogType === 'cnActivateOffline'
			? t('ActivateOffline')
			: dialogType === 'upgrade' ? t('Upgrade') : t('RefreshOffline');
		const btnText = dialogType === 'cnActivateOffline'
			? t('Activate')
			: dialogType === 'upgrade' ? t('Upgrade') : t('Refresh');
		const getOfflineLicenseDataBtnText = t('getOfflineLicenseData');
		const curKey = dialogType === 'cnMigrateOffline' ? dialogState.oldKey : dialogState.key;

		const onImport = async () => {
			if (dialogType === 'upgrade') {
				await this.upgradeTempLicense();
				return;
			}
			const importType = (() => {
				switch (dialogType) {
					case 'cnActivateOffline':
						return 'activate';
					case 'cnMigrateOffline':
						return 'migrate';
					default:
						return 'refresh';
				}
			})();
			await this.importOfflineAsync(importType);
		};

		const onCancel = () => {
			if (dialogType === 'cnActivateOffline') {
				this.setState({ dialogType: 'cnActivate' });
			} else if (dialogType === 'cnMigrateOffline') {
				this.setState({ dialogType: 'migrate' });
			} else if (dialogType === 'cnRefreshOffline') {
				this.setState({ dialogTypeExt: 'inputLicense' });
			} else {
				this.clearDialog();
			}
		};

		const getLicenseData = async () => {
			if (dialogType === 'cnActivateOffline' || dialogType === 'upgrade') {
				await this.activateOfflineAsync();
			} else if (dialogType === 'cnMigrateOffline') {
				await this.migrateOfflineAsync();
			} else {
				await this.refreshOfflineAsync();
			}
			this.setState({ dialogTypeExt: 'cnGetLicenseData' });
		};

		const offlineCodeText = dialogType === 'cnActivateOffline' ? t('offlineActivationCode') : t('offlineRefreshCode');
		return <div className='static-modal'>
			<Modal {...this.modalProps}>
				<div className='modal-content'>
					<div className='modal-header'>
						<div className='header-inner'>
							<label>{title}</label>
							<Button
								className='close-btn'
								icon='mdi mdi-close'
								style='transparent'
								onClick={this.clearDialog}
								size='small'
								rounded
							/>
						</div>
					</div>
					<div className='modal-body'>
						<div className='license-key-container'>
							<span className='license-key label-top'>{t('LicenseKey')}</span>
							<InputEditor
								placeholder={t('LicenseKey')}
								className='key-input'
								value={curKey}
								readOnly={dialogType !== 'upgrade'}
								onChange={(v) => this.setState({ dialogState: update(dialogState, { key: { $set: v } }) })}
							/>
						</div>
						{this.renderTips(t('getOfflineLicenseDataTips', { action: title }))}
						<div className='license-data-container'>
							<label className='label-top'>{t('offlineLicenseData')}</label>
							<div className='activation-row'>
								<InputEditor
									placeholder={t('inputOfflineLicenseData')}
									className='license-data-input'
									onEveryChange={(val) => { this.setState({ dialogState: update(dialogState, { offlineImport: { licenseData: { $set: val } } }) }); }}
									onChange={(val) => { this.setState({ dialogState: update(dialogState, { offlineImport: { licenseData: { $set: val } } }) }); }}
									value={dialogState.offlineImport.licenseData}
								/>
								<Button
									style='accent'
									className='get-offline-license-data-btn'
									onClick={getLicenseData}
									text={getOfflineLicenseDataBtnText}
									title={getOfflineLicenseDataBtnText}
									size='small'
									disabled={!curKey}
								/>
							</div>
							{dialogTypeExt === 'cnGetLicenseData' &&
								<div className='offline-activation-code-container'>
									<label className='label-top' title={offlineCodeText}>{offlineCodeText}</label>
									<div className='activation-row offline-activation-code'>
										<InputEditor
											value={dialogState.generateClientCode.clientCode}
											readOnly
										/>
										<Button
											style='accent'
											size='small'
											onClick={() => copyTextToClipboard(dialogState.generateClientCode.clientCode)}
											className='copy-text'
											text={t('gtCopyOfflineLicenseString')}
										/>
										{dialogState.generateClientCode.clientCode.length > this._shortCodeClientCodeLength &&
											<Button
												style='accent'
												size='small'
												onClick={() => this.setState({ qrCodeString: dialogState.generateClientCode.clientCode })}
												className='show-qr'
												text={t('gtGenerateQRCode')}
											/>
										}
									</div>
									{this.renderTips(t('obtainOfflineLicenseDataTips', { offlineCode: offlineCodeText }))}
									<div className='arrow-place-holder' />
								</div>
							}
						</div>
					</div>
					<div className='modal-footer'>
						<Button style='accent' disabled={!dialogState.offlineImport.licenseData} width='100px' onClick={onImport} text={btnText} title={btnText} />
						<Button onClick={onCancel} width='100px' text={t('Cancel')} title={t('Cancel')} />
					</div>
				</div>
				{dialogBusy && <BlockLoader />}
			</Modal>
		</div>;
	}

	renderRefreshAndDeactivateDialog = () => {
		const { t } = this.props;
		const { dialogState, dialogType, licenses } = this.state;
		let title: string = null;
		let btnText: string = null;
		let confirmContent: string = null;
		if (dialogType === 'refresh') {
			title = t('Refresh');
			btnText = t('RefreshOnline');
			confirmContent = t('RefreshConfirmMessage', { license: dialogState.needMigrate ? dialogState.oldBasedKey : dialogState.oldKey });
		} else if (dialogType === 'deactivate') {
			title = t('Deactivate');
			btnText = t('Deactivate');
			confirmContent = t('DeactivateConfirmMessage', { license: dialogState.needMigrate ? dialogState.oldBasedKey : dialogState.oldKey });
		} else if (dialogType === 'confirmDeactivateOffline') {
			title = t('DeactivateOffline');
			btnText = t('OK');
			confirmContent = t('OfflineDeactivateConfirmMessage', { license: dialogState.needMigrate ? dialogState.oldBasedKey : dialogState.oldKey });
		} else {
			return null;
		}

		const btnCallbackAsync = dialogType === 'refresh' ? this.refreshAsync : this.deactivateAsync;
		const onDeactivateOfflineBtnClick = () => this.setState({ dialogType: 'confirmDeactivateOffline' });
		const onRefreshOfflineBtnClick = (byIndicator: boolean) => {
			if (!byIndicator) {
				this.setState({ dialogType: 'cnRefreshOffline', dialogTypeExt: 'inputLicense' });
			} else {
				this.setState({ dialogType: 'cnRefreshOfflineByIndicator', dialogTypeExt: 'inputLicense' });
			}
		};
		const selectedLicense = licenses.find(l => l.displayKey === dialogState.oldKey);
		const isLicenseActivatedByIndicator = selectedLicense?.activatedByIndicator;

		const offlineBtnCancelCallback = () => { this.setState({ dialogType: 'deactivate' }); };
		return <div className='static-modal'>
			<Modal {...this.modalProps}>
				<div className='modal-content'>
					<div className='modal-header'>
						{title}
					</div>
					<div className='modal-body'>
						<span>{confirmContent}</span>
					</div>
					<div className='modal-footer'>
						{dialogType === 'confirmDeactivateOffline' && <Button style='accent' onClick={this.deactivateOfflineAsync} text={btnText} title={btnText} />}
						{dialogType !== 'confirmDeactivateOffline' && !isLicenseActivatedByIndicator && <Button style='accent' onClick={btnCallbackAsync} text={btnText} title={btnText} />}
						{dialogType === 'deactivate' && <Button style='accent' onClick={onDeactivateOfflineBtnClick} text={t('DeactivateOffline')} title={t('DeactivateOffline')} />}
						{dialogType === 'refresh' && <Button style='accent' onClick={() => onRefreshOfflineBtnClick(isLicenseActivatedByIndicator)} text={t('RefreshOffline')} title={t('RefreshOffline')} />}
						<Button onClick={dialogType === 'confirmDeactivateOffline' ? offlineBtnCancelCallback : this.clearDialog} text={t('Cancel')} title={t('Cancel')} />
					</div>
				</div>
				{this.state.dialogBusy && <BlockLoader />}
			</Modal>
		</div>;
	}

	confirmLeaveDeactivateOfflineDialog = (event) => {
		event.preventDefault();
	}

	renderDeactivateOfflineDialog = () => {
		const { t } = this.props;
		const { dialogState, dialogType } = this.state;
		if (dialogType !== 'deactivateOffline') return null;
		window.addEventListener('beforeunload', this.confirmLeaveDeactivateOfflineDialog);
		const toggleConfirm = (value: boolean) => {
			const newDialogState = update(this.state.dialogState, { offlineDeactivate: { confirm: { $set: !value } } });
			this.setState({ dialogState: newDialogState });
		};
		return <div className='static-modal'>
			<Modal {...this.modalProps}>
				<div className='modal-content'>
					<div className='modal-header'>
						{t('DeactivateOffline')}
					</div>
					<div className='modal-body'>
						<div>
							<span>{t('offlineDeactivationCode')}</span>
							{
								<div className='generatedInfo-row'>
									<InputEditor
										className='generated-register-input'
										value={dialogState.offlineDeactivate.clientCode}
										readOnly
									/>
									{dialogState.offlineDeactivate.clientCode.length > this._shortOfflineDeactivateClientCodeMaxLength &&
										<Button
											style='transparent'
											size='small'
											onClick={() => this.setState({ qrCodeString: dialogState.offlineDeactivate.clientCode })}
											icon='mdi mdi-qrcode'
											title={t('gtGenerateQRCode')}
											className='generate-QRCode'
											rounded
										/>
									}
									<Button
										style='transparent'
										size='small'
										onClick={s => copyTextToClipboard(dialogState.offlineDeactivate.clientCode)}
										icon='mdi mdi-content-copy'
										title={t('gtCopyOfflineLicenseString')}
										rounded
									/>
								</div>
							}
							<div className='deactivate-offline-confirm'>
								<Checkbox value={dialogState.offlineDeactivate.confirm} text={t('OfflineDeactivateCloseConfirm')} checked={dialogState.offlineDeactivate.confirm} onChange={toggleConfirm} />
							</div>
							<div className='offline-tips'>
								<p className='offline-step' title={t('deactivateStep')}>{t('deactivateStep')}</p>
								<p className='offline-step' title={t('deactivateStepCn1')}>{t('deactivateStepCn1')}</p>
								<p className='offline-step' title={t('deactivateStep2')}>{t('deactivateStep2')}</p>
							</div>
						</div>
					</div>
					<div className='modal-footer'>
						<Button
							style='accent'
							onClick={() => {
								window.removeEventListener('beforeunload', this.confirmLeaveDeactivateOfflineDialog);
								window.location.reload();
							}}
							disabled={!dialogState.offlineDeactivate.confirm}
							text={t('OK')}
							title={t('OK')}
						/>
					</div>
				</div>
				{this.state.dialogBusy && <BlockLoader />}
			</Modal>
		</div>;
	}

	renderRemoveConfirmDialog = () => {
		const { dialogState, dialogType } = this.state;
		if (dialogType !== 'remove') return null;
		const { t } = this.props;
		const confirmContent = t('DeactivateConfirmMessage', { license: dialogState.oldKey });

		return <div className='static-modal'>
			<Modal {...this.modalProps}>
				<div className='modal-content'>
					<div className='modal-header'>
						{t('Deactivate')}
					</div>
					<div className='modal-body'>
						<span>{confirmContent}</span>
					</div>
					<div className='modal-footer'>
						<Button style='accent' onClick={this.deactivateAsync} text={t('Deactivate')} title={t('Deactivate')} />

						<Button onClick={this.clearDialog} text={t('Cancel')} title={t('Cancel')} />
					</div>
				</div>
				{this.state.dialogBusy && <BlockLoader />}
			</Modal>
		</div>;
	}

	renderReloadWindowConfirmDialog = () => {
		const { t } = this.props;
		const { dialogState, dialogType } = this.state;
		if (dialogType !== 'reload') return null;
		return <div className='static-modal'>
			<Modal {...this.modalProps}>
				<div className='modal-content'>
					<div className='modal-header'>
						{dialogState.reload.title}
					</div>
					<div className='modal-body'>
						<span className='reload-confirm'>{dialogState.reload.message}</span>
					</div>
					<div className='modal-footer'>
						<Button onClick={() => window.location.reload()} text={t('OK')} title={t('OK')} />
					</div>
				</div>
				{this.state.dialogBusy && <BlockLoader />}
			</Modal>
		</div>;
	}

	renderLicenseDetailDialog = () => {
		const { t } = this.props;
		const { selectedLicense } = this.state;
		return <div className='static-modal'>
			<Modal {...this.modalProps}>
				<div className='modal-content'>
					<div className='modal-header'>
						{t('licenseInfoDetail')}
					</div>
					<div className='modal-body'>
						{this.getDialogContent(selectedLicense)}
					</div>
					<div className='modal-footer'>
						<Button onClick={this.closeLicenseDetail} text={t('Close')} title={t('Close')} />
					</div>
				</div>
				{this.state.dialogBusy && <BlockLoader />}
			</Modal>
		</div>;
	};

	renderQr = () => {
		const { t } = this.props;
		const { qrCodeString, dialogType } = this.state;
		if (!qrCodeString) return null;
		const modalProps = {
			isOpen: true,
			parentSelector: () => document.querySelector('#portal-modals'),
			className: {
				base: 'modal-dialog ReactModalPortal qr-code-dialog',
				afterOpen: '',
				beforeClose: ''
			},
			overlayClassName: {
				base: 'modal fade ReactModal__Overlay--after-open',
				afterOpen: 'show',
				beforeClose: ''
			},
			style: { overlay: { display: 'block', zIndex: '10000' } },
			ariaHideApp: false
		};

		let operationCode = t('offlineActivationCode');
		if (dialogType === 'deactivateOffline') {
			operationCode = t('offlineDeactivationCode');
		} else if (dialogType === 'cnMigrateOffline' || dialogType === 'cnRefreshOffline') {
			operationCode = t('offlineRefreshCode');
		}

		return (
			<div className='static-modal'>
				<Modal {...modalProps}>
					<div className='modal-content' onClick={(e: React.MouseEvent<HTMLDivElement>) => this.onQrCodeClick(e)}>
						<div className='modal-body'>
							<div className='modal-body-row close-row'>
								<AriaIcon className='mdi mdi-close-circle' onClick={() => { this.setState({ qrCodeString: '' }); }} />
							</div>
							<div className='modal-body-row qr-code-row'>
								<QRCode value={qrCodeString} size={500} />
							</div>
							<div className='modal-body-row'>
								<span className='qr-tip'>{t('gtQRCodeGetOfflineLicenseString', { operationCode })}</span>
							</div>
						</div>
					</div>
				</Modal>
			</div>
		);
	}

	renderClientCode = () => {
		const { clientIdentityCode } = this.state;
		if (!clientIdentityCode) return null;

		const modalProps = {
			isOpen: true,
			parentSelector: () => document.querySelector('#portal-modals'),
			className: {
				base: 'modal-dialog ReactModalPortal client-code-dialog',
				afterOpen: '',
				beforeClose: ''
			},
			overlayClassName: {
				base: 'modal fade ReactModal__Overlay--after-open',
				afterOpen: 'show',
				beforeClose: ''
			},
			style: { overlay: { display: 'block', zIndex: '10000' } },
			ariaHideApp: false
		};

		return (
			<div className='static-modal'>
				<Modal {...modalProps}>
					<div className='modal-content'>
						<div className='modal-body'>
							<div className='modal-body-row close-row'>
								<AriaIcon className='mdi mdi-close-circle' onClick={() => { this.setState({ clientIdentityCode: '' }); }} />
							</div>
							<div className='modal-body-row client-code-row'>
								<div className='client-code'>
									{clientIdentityCode.split('').map((c, index) => <span key={index}>{c}</span>)}
								</div>
							</div>
						</div>
					</div>
				</Modal>
			</div>
		);
	}

	getIcon = (actionType: string) => {
		switch (actionType) {
			case 'refresh':
			case 'migrate':
				return 'mdi-refresh';
			case 'deactivate':
			case 'remove':
				return 'mdi-link-variant-off';
			case 'upgrade':
				return 'mdi-key-arrow-right';
			default:
				return '';
		}
	};

	renderCell = (key: string, row: Row) => {
		if (key === 'actions') {
			return (
				<div className='grid-actions'>
					{row.contextMenu.length > 0 &&
						row.contextMenu.map((context) => {
							const icon = this.getIcon(context.value);
							return (
								<Button
									className='license-action-btn'
									key={context.value}
									icon={`mdi ${icon}`}
									style='transparent'
									text={context.text}
									size='small'
									title={context.text}
									onClick={() => this.handleLicenseActions(row.idx, context.value)}
									rounded
								/>
							);
						})}
				</div>
			);
		}
	};

	getKeyToMigrate = (): Sa3License => {
		const { licenses } = this.state;
		if (licenses.filter(l => !l.needMigration).length > 0) return null;
		const licensesCanMigrate = licenses.filter(l => l.needMigration && (l.status === Sa3Status.Licensed || l.status === Sa3Status.GracePeriod || l.status === Sa3Status.VersionNotMatch));
		if (licensesCanMigrate.length === 0) return null;
		if (licensesCanMigrate.length === 1) {
			return licensesCanMigrate[0];
		} else {
			const formals = licensesCanMigrate.filter(l => !l.isTrial);
			if (formals.length === 1) {
				return formals[0];
			}
			return null;
		}
	}

	getContextMenuAndStatus = (license: Sa3License): { contextMenu: DropdownItemProps[], status: string } => {
		const { t } = this.props;
		const keyToMigrate = this.getKeyToMigrate();
		let licenseStatus = t(Sa3StatusStrings[license.status]);
		let contextMenu: DropdownItemProps[] = [];
		const deactivationValue = license.allowDeactivation ? 'deactivate' : 'remove';

		if (license.isPreActivated || license.isTempActivated) {
			contextMenu.push({ text: t('Deactivate'), value: deactivationValue });
			if (license.isTempActivated) {
				contextMenu.unshift({ text: t('Upgrade'), value: 'upgrade' });
			}
		} else {
			contextMenu.push(
				{ text: t('Refresh'), value: 'refresh' },
				{ text: t('Deactivate'), value: deactivationValue }
			);

			if (license.needMigration) {
				contextMenu = contextMenu.filter(item => item.value !== 'refresh');
				if (keyToMigrate?.credential === license.credential) {
					contextMenu.unshift({ text: t('Refresh'), value: 'migrate' });
				}
				licenseStatus = keyToMigrate?.credential === license.credential ? t('needMigrate') : t('invalid');
			}
		}

		return { contextMenu, status: licenseStatus };
	}

	render(): React.ReactNode {
		const { licenses, showLicenseDetail, busy } = this.state;
		if (busy) { return <BlockLoader />; }
		const { t } = this.props;
		const hasSa3License = licenses.filter(l => !l.needMigration).length > 0;
		const keyToMigrate = this.getKeyToMigrate();
		const licensesCanMigrate = licenses.filter(l => l.needMigration && (l.status === Sa3Status.Licensed || l.status === Sa3Status.GracePeriod || l.status === Sa3Status.VersionNotMatch));
		const formalLicensesCanMigrate = licensesCanMigrate.filter(l => !l.isTrial);
		const trialLicensesCanMigrate = licensesCanMigrate.filter(l => l.isTrial);
		const licenseDisplayInfo = licenses.map((license, idx) => {
			const { contextMenu, status: licenseStatus } = this.getContextMenuAndStatus(license);
			const tempActivatedTip = license.isTempActivated ? `(${t('tempActivated')})` : '';
			const curKeyTypeAndTip = this.getLicenseTypeAndTip(license, formalLicensesCanMigrate, trialLicensesCanMigrate, keyToMigrate);
			const res: Row = {
				installationDate: this.getDisplayDate(license.registeredDate),
				key: this.renderLicenseKey(license, curKeyTypeAndTip.type, curKeyTypeAndTip.tip),
				expireDate: `${this.getDisplayDate(license.expireDate)} (${licenseStatus})${tempActivatedTip}`,
				licenseInfoDisplay: this.getCNDisplayInfo(license),
				contextMenu,
				idx,
				height: 100,
			};
			return res;
		});

		const gridProps: CGridProps = {
			columns: [
				{ key: 'installationDate', label: t('RegistrationDate'), width: 200 },
				{ key: 'key', label: t('LicenseKey'), width: 230 },
				{ key: 'expireDate', label: t('ExpiryDate'), width: 200 },
				{ key: 'licenseInfoDisplay', label: t('LicenseInfo'), width: 250 },
				{ key: 'actions', width: 300 },
			],
			rows: licenseDisplayInfo,
			onRenderCell: this.renderCell,
			hideGridLine: true,
			columnResizing: true,
		};

		let tipMessage = '';
		if (licenses.length === 0) {
			tipMessage = t('NoLicenseTip');
		}

		return <div className='license-management-container'>
			<div className='license-management-header btm-cm-group'>
				<Button
					onClick={this.onOpenActivationDialog}
					text={t('Activate')}
					style='accent'
					icon='mdi mdi-plus'
					disabled={hasSa3License}
				/>
			</div>
			{licenses.length === 0
				? <div className='empty-page-container' >
					<div className='empty-page-bg' style={{ backgroundImage: "url('images/admin/license.png')" }} />
					<div className='empty-page-tip'>
						<div className='main-tip'>{tipMessage}</div>
					</div>
				</div>
				: <CGrid {...gridProps} />
			}

			{this.renderCNActivateDialog()}
			{this.renderCNActivateOfflineDialog()}
			{this.renderRefreshOfflineByIndicatorDialog()}
			{this.renderRefreshOfflineInputLicenseDialog()}
			{this.renderMigrateDialog()}
			{this.renderRefreshAndDeactivateDialog()}
			{this.renderDeactivateOfflineDialog()}
			{this.renderReloadWindowConfirmDialog()}
			{this.renderRemoveConfirmDialog()}
			{showLicenseDetail && this.renderLicenseDetailDialog()}
			{this.renderQr()}
			{this.renderClientCode()}
		</div>;
	}
}