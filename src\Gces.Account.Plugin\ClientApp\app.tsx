import PropertyManagement from './components/PropertyManagement';
import LockedUserManagement from './components/LockedUserManagement';
import { LicenseManagement as LicenseCN } from './components/License/LicenseManagementCN';
import { LicenseManagement as LicenseEN } from './components/License/LicenseManagementEN';
import CVLicenseManagement from './components/CVLicenseManagement';
import SecurityProvider from './components/SecurityProvider';
import SecuritySettings from './components/SecuritySettings';
import ClaimMappings from './components/ClaimMappings';
import GenerateToken from './components/GenerateToken';
import reducer from './reducers';
import sagas from './sagas';
import { accountLocalizationData } from './localization/index';
import { portalLocalizationData } from './localization/portalLocaleData';
import { arrayForEachPolyfill, nodeListForEachPolyfill } from './forEachPolyfill';
import './Register';

arrayForEachPolyfill();
nodeListForEachPolyfill();

window.AdminPortal.Register.StoreExtension('account-management', reducer, sagas);
window.AdminPortal.Register.Locale(accountLocalizationData);
window.AdminPortal.Register.Locale(portalLocalizationData);

window.AdminPortal.Register.SectionItem('account-management-property', 'account-management', 'property', 'mdi mdi-account-edit', 'Property', 'User custom property management.', true, PropertyManagement, undefined, undefined, undefined, 4);
window.AdminPortal.Register.SectionItem('account-management-security-settings', 'security-settings', 'securitySettings', 'mdi mdi-security', 'Security Settings', 'Manage security settings', false, SecuritySettings);
window.AdminPortal.Register.SectionItem('account-management-locked-users', 'security-settings', 'lockedUsers', 'mdi mdi-account-key', 'Locked User', 'Locked user management.', false, LockedUserManagement);
window.AdminPortal.Register.SectionItem('account-management-claim-mappings', 'account-management', 'claimMappings', 'mdi mdi-card-account-details', 'Claim Mappings', 'Claim mappings management', false, ClaimMappings, undefined, undefined, undefined, 5);
window.AdminPortal.Register.SectionItem('account-management-generate-token', 'security-settings', 'generate-token', 'mdi mdi-security-network', 'Generate Token', 'Generate Token', true, GenerateToken);
if (window.AdminPortal.Edition === 'zh') {
	window.AdminPortal.Register.SectionItem('sysconfig-license', 'system-management', 'license', 'mdi mdi-lock-pattern', 'License', 'License activation', true, undefined, undefined, undefined, undefined, 70);
	window.AdminPortal.Register.SectionItem('sysconfig-license-wyn', 'system-management', 'license-wyn', 'mdi mdi-lock-pattern', 'Wyn License', 'Wyn license activation', true, LicenseCN, false, undefined, 'sysconfig-license');
	window.AdminPortal.Register.SectionItem('sysconfig-license-cv', 'system-management', 'license-cv', 'gc gc-custom-visual-license', 'Custom Visual License', 'Custom visual license management', true, CVLicenseManagement, false, undefined, 'sysconfig-license');
} else {
	window.AdminPortal.Register.SectionItem('sysconfig-license', 'system-management', 'license', 'mdi mdi-lock-pattern', 'License', 'License activation', true, LicenseEN, undefined, undefined, undefined, 70);
}
window.AdminPortal.Register.SectionItem('sysconfig-security-provider', 'configuration', 'security-provider', 'mdi mdi-account-key', 'Security Providers', 'Security provider management.', true, SecurityProvider, undefined, undefined, undefined, 80);

window.AdminPortal.Register.Tile('shortcut-org', 'shortcut', { text: 'Organizations', iconCssClass: 'mdi mdi-lan', action: { type: 'nav', data: '/account-management/organization' } }, 1, 1, 2, 2, false, 'Users Shortcut', '', 'mdi mdi-lan', undefined, true);
window.AdminPortal.Register.Tile('shortcut-user', 'shortcut', { text: 'Users', iconCssClass: 'mdi mdi-account', action: { type: 'nav', data: '/account-management/user' } }, 1, 1, 2, 2, false, 'Users Shortcut', '', 'mdi mdi-account', undefined, true);
window.AdminPortal.Register.Tile('shortcut-role', 'shortcut', { text: 'Roles', iconCssClass: 'mdi mdi-account-group', action: { type: 'nav', data: '/account-management/role' } }, 1, 1, 2, 2, false, 'Roles Shortcut', '', 'mdi mdi-account-key', undefined, true);
window.AdminPortal.Register.Tile('shortcut-locked', 'shortcut', { text: 'Locked', iconCssClass: 'mdi mdi-account-key', action: { type: 'nav', data: '/security-settings/lockedUsers' } }, 1, 1, 2, 2, false, 'Locked Shortcut', '', 'mdi mdi-lock');
window.AdminPortal.Register.Tile('shortcut-license', 'shortcut', { text: 'License', iconCssClass: 'mdi mdi-lock-pattern', action: { type: 'nav', data: '/system-management/license' } }, 1, 1, 2, 2, false, 'License Shortcut', '', 'mdi mdi-lock-pattern');
