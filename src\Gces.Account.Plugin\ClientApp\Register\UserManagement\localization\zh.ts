export const userZH: LanguageKeyValueMap = {
	// common
	cmNoMembersInOrg: '当前组织没有用户',

	UserManagement: '用户管理',
	Yes: '确定',
	Edit: '编辑',
	Delete: '删除',
	Save: '保存',
	Cancel: '取消',
	Close: '关闭',
	AddUser: '创建用户',
	SelectMembers: '选择成员',
	EditUser: '编辑用户',
	SearchText: '搜索内容',
	DeleteUser: '删除用户',
	RemoveUserFromOrg: '从组织中移除用户',
	Username: '用户名',
	Email: '邮箱',
	Mobile: '手机号码',
	FirstName: '名字',
	LastName: '姓',
	FullName: '全名',
	Password: '密码',
	ConfirmPassword: '确认密码',
	PasswordNeverExpire: '密码永不过期',
	Roles: '角色',
	HomePage: '主页',
	ManagedBy: '管理者',
	Provider: '提供者',
	Organizations: '组织',
	Status: '状态',
	Enabled: '启用',
	Disabled: '禁用',
	Enable: '启用',
	Disable: '禁用',
	Locked: '被锁定',
	Actions: '操作',
	GetUsersError: '获取用户错误',
	UsernameIsRequired: '用户名是必填项',
	UsernameAlreadyExists: '用户名已经存在',
	EmailIsRequired: '邮箱是必填项',
	EmailIsInvalid: '非法的邮箱地址',
	EmailAlreadyExists: '邮箱地址已经存在',
	MobileAlreadyExists: '电话号码已经存在',
	WeakPasswordRequirement: '密码不能为空，且长度应该在1-150位之间',
	StrongPasswordRequirement: '密码的长度应该在8-150位之间，且必须包含一个大写字母，一个小写字母和一个数字',
	PasswordNotMatch: '密码和确认密码不匹配',
	PasswordIsBlank: '密码不能仅包含空格字符',
	DeleteUserConfirmMessage: '确定要删除用户“{{user}}”吗？',
	RemoveUserConfirmMessage: '确定要从组织"{{organization}}"移除用户“{{user}}”吗?',
	OneRolePerLine: ' （一行一个角色）',
	OneValuePerLine: ' （每行一个值）',
	Import: '导入用户',
	Export: '导出',
	Users: '用户',
	Template: '模板',
	NewUserName: '新用户名',
	NewUserEmail: '新用户邮箱',
	SelectValue: '选择值',
	NullValue: '<空>',
	ImportUsers: '导入用户',
	DragDropFile: '拖拽文件到这里',
	Or: '或者',
	ClickHere: '点击这里',
	SelectFile: '选择文件',
	ExportTemplate: '导出模板',
	UnlockUser: '解除锁定',
	UnlockUserConfirm: '确定解除锁定用户“{{user}}”吗？',
	UserTemplate: '用户模板',
	ErrorRemoveAdminFromAdministratorRole: "不能把用户 '{{userName}}' 从用户组 '{{roleName}}' 中删除。",
	ImportUsersSuccessMsg: '成功导入 {{count}} 个用户。',
	ImportUsersFailMsg: '{{count}} 个用户导入失败, <a href="{{url}}" target="_blank">点击查看详情</a>。',
	ImportUsersFailed: '导入用户失败。',
	UsernameLengthExceedLimit: '用户名不能为空，且长度应该在1-150位之间',
	ShowPropertyValue: '显示值',
	HidePropertyValue: '隐藏值',

	// homePageSelector
	loadingDocuments: '正在加载文档...',
	noSearchResultTip: '没有匹配的搜索结果',
	searchPlaceHolder: '在此处输入搜索文本...',

	// select members
	smSelectMembers: '选择成员',
	smEmptyResult: '没有符合条件的条目',
	smMembers: '({{ count }} 个用户)',
	smAddUser: '添加',

	UserDetail: '用户详情',
	ShowSubOrg: '显示子组织的用户',
	NotShowSubOrg: '不显示子组织的用户',
	RemoveUser: '从当前组织中移除用户',

	udBasicInfo: '基础信息',
	udEmpty: '[未填写]',
	NoOrganizations: '当前用户不属于任何组织',
	NoRoles: '未在组织中分配角色',
	onlyNoMemberOfSubOrg: '从组织中移除用户只能在不显示子组织用户时使用',
	ecNoMemberTip: '当前组织下没有用户，如需添加请点击',

	Error: '错误',
	umError_1024: '手机号码已存在',
	umError_1028: '"admin" 用户不能被删除',
	umError_1039: '系统管理员的管理者只能为全局',
	umError_1040: '权限不足',
	umError_5010: '组织管理员不能将自己从当前组织中移除',

	rt_user: '用户',
	rt_role: '角色',
	'rt_organization user': '组织用户',

	error_V2_007_001_0002: '用户名不能为空。',
	error_V2_007_001_0003: '邮箱地址不能为空。',
	error_V2_007_001_0004: '密码不能为空。',
	error_V2_007_001_0005: '用户名“{{UserName}}”已经存在。',
	error_V2_007_001_0006: '邮箱地址“{{Email}}”已经存在。',
	error_V2_007_001_0015: '非法的邮箱地址“{{Email}}”。',
	error_V2_007_001_0028: '系统管理员的管理者只能为全局组织。',
	error_V2_007_001_0030: '电话号码“{{Mobile}}”已经存在。',
	error_V2_007_004_0003: '自定义属性“{{CustomProperty}}”的值“{{Value}}”不合法。',
};