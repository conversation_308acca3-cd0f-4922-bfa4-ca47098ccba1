import * as moment from 'moment';
import * as React from 'react';
import * as classnames from 'classnames';
import { Button, InputEditor } from 'gces-ui';
import { isValidValue } from '../util';

export interface CMultiValueInputProps {
	value: string;
	valueType: string;
	disabled?: boolean;
	onChange: (value: string) => void;
	aid?: string;
	placeHolder?: string;
	invalid?: boolean;
	className?: string;
	rows?: number;
	required?: boolean;
	visibilityToggle?: boolean;
	noVisibilityToggle?: boolean;
	showEyeTitle?: string;
	hideEyeTitle?: string;
}

interface LocalState {
	invalid: boolean;
	showEncryptedText: boolean;
}

export class CMultiValueInput extends React.PureComponent<CMultiValueInputProps, LocalState> {

	constructor(props: CMultiValueInputProps, context) {
		super(props, context);

		this.state = {
			invalid: false,
			showEncryptedText: false,
		};
	}

	componentWillReceiveProps = (nextProps: CMultiValueInputProps) => {
		const invalid = !this.validateValue(nextProps.value, nextProps.valueType, nextProps.required);
		this.setState({ invalid });
	}
	componentDidMount = () => {
		const { value } = this.props;
		let invalid = false;
		if (value !== '') {
			invalid = !this.validateValue(value, this.props.valueType, this.props.required);
		}
		this.setState({ invalid });
	}

	validateValue = (value: string, valueType: string, required?: boolean) => {
		if (!required && (!value.trim())) {
			return true;
		}
		return isValidValue(value.split('\n').filter(v => !!v), valueType);
	}

	convertTextToValue = (text: string) => {
		const { valueType } = this.props;

		if (text) {
			let items = text.split(/\r?\n/) as any[];
			const reg = /^\s*\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}$/;
			if (items && items.length > 0) {
				if (valueType === 'Integer') {
					items = items.map((item) => parseInt(item));
				}
				else if (valueType === 'Float') {
					items = items.map((item) => parseFloat(item));
				}
				else if (valueType === 'Number') {
					items = items.map(Number);
				}
				else if (valueType === 'Date') {
					items = items.map((item) => !item.match(reg) ? item : moment(item, 'YYYY-MM-DD').toDate());
				}
				else if (valueType === 'DateTime') {
					items = items.map((item) => !item.match(reg) ? item : moment(item, ['YYYY-MM-DD', 'YYYY-MM-DD HH:mm:ss']).toDate());
				}
				else if (valueType === 'Boolean') {
					items = items.map(item => {
						switch (item && item.toLowerCase()) {
							case 'true': return true;
							case 'false': return false;
							default: return null;
						}
					});
				}

				return items;
			}
		}
		else {
			return [''];
		}
	}

	convertValueToText = (values: any[]) => {
		const { valueType } = this.props;

		if (values && values.length > 0) {
			let valueItems = [];

			if (valueType === 'DateTime') {
				valueItems = values.map((value) => {
					return typeof value === 'string'
						? value
						: (value.format ? value.format('YYYY-MM-DD HH:mm:ss') : moment(value).format('YYYY-MM-DD HH:mm:ss'));
				});
			}
			else if (valueType === 'Date') {
				valueItems = values.map((value) => {
					return typeof value === 'string'
						? value
						: (value.format ? value.format('YYYY-MM-DD') : moment(value).format('YYYY-MM-DD'));
				});
			}
			else {
				valueItems = [...values];
			}

			return valueItems.join('\n');
		}

		return '';
	}

	onValueChange = (text: string) => {
		const { onChange, valueType } = this.props;
		const stateVal = { invalid: false };

		const val = this.convertTextToValue(text);
		if (val && val.some(v => v === null || Number.isNaN(v))) {
			stateVal.invalid = true;
		} else {
			stateVal.invalid = !this.validateValue(text, this.props.valueType, this.props.required);
		}

		if (onChange) {
			onChange((stateVal.invalid || valueType === 'Float') ? text : this.convertValueToText(val));
		}

		this.setState(stateVal);
	}

	toggleShowEncryptedText = () => {
		this.setState({ showEncryptedText: !this.state.showEncryptedText });
	}

	render() {
		const { aid, valueType, invalid, rows, className, value, visibilityToggle, noVisibilityToggle, hideEyeTitle, showEyeTitle } = this.props;
		let placeHolder;

		if (valueType === 'DateTime') {
			placeHolder = 'YYYY-MM-DD HH:mm:ss,YYYY-MM-DD HH:mm:ss...';
		}
		else if (valueType === 'Date') {
			placeHolder = 'YYYY-MM-DD,YYYY-MM-DD...';
		}
		else if (valueType === 'Integer') {
			placeHolder = 'Integer1,Integer2...';
		}
		else if (valueType === 'Float') {
			placeHolder = 'Float1,Float2...';
		}
		else if (valueType === 'Number') {
			placeHolder = 'Number1,Number2...';
		}
		else if (valueType === 'Guid') {
			placeHolder = 'Guid1,Guid2...';
		}
		else if (valueType === 'Boolean') {
			placeHolder = 'Boolean1,Boolean2...';
		}
		else {
			placeHolder = 'String1,String2...';
		}

		if (this.props.placeHolder) {
			placeHolder = this.props.placeHolder;
		}

		return (
			<div className={classnames(className, 'efc-sensitive-wrapper', { 'efc-input-password': visibilityToggle })}>
				<InputEditor
					aid={aid}
					className={classnames({ 'efc-text-security': visibilityToggle && !this.state.showEncryptedText })}
					multiline
					rows={rows}
					placeholder={placeHolder}
					invalid={this.state.invalid || invalid}
					disabled={this.props.disabled}
					value={value}
					onEveryChange={this.onValueChange}
				/>
				<Button
					className={classnames('efc-input-visible-toggle', { 'efc-input-visible-hidden': !visibilityToggle }, { 'efc-input-visible-none': noVisibilityToggle })}
					icon={this.state.showEncryptedText ? 'mdi mdi-eye' : 'mdi mdi-eye-off'}
					size='small'
					style='transparent'
					rounded
					onClick={this.toggleShowEncryptedText}
					title={this.state.showEncryptedText ? hideEyeTitle : showEyeTitle}
				/>
			</div>
		);
	}
}