import * as classnames from 'classnames';
import * as React from 'react';

import { Radio } from 'gces-ui/lib/components/Radio';

export type RecSettingEditorProps = {
	text?: string;
	units?: string;
	noRadio?: boolean;

	disabled?: boolean;
	inverted?: boolean;

	value: string;
	checked?: boolean;
	onChange?: (value: string) => void;
};

export class RecSettingEditor extends React.PureComponent<RecSettingEditorProps> {

	onChange = (value: string) => {
		const { noRadio, onChange } = this.props;
		if (!noRadio && onChange) onChange(value);
	}

	public render() {
		const { text, units, value, noRadio, checked, inverted, disabled, children } = this.props;
		const contentClass = classnames('sc-rec-setting-editor', { 'no-units': !units });

		return (
			<div className='sc-rec-setting'>
				<div className='sc-rec-setting-title' role='radiogroup'>
					{!noRadio && <Radio inverted={inverted} inline text={text} value={value} checked={checked} disabled={disabled} onChange={this.onChange} />}
					{noRadio && <span>{text}</span>}
				</div>
				<div className='sc-rec-setting-content'>
					<div className={contentClass}>
						{children}
					</div>
					{units && <div className='sc-rec-setting-units'>
						<span>{units}</span>
					</div>}
				</div>
			</div>
		);
	}

}
