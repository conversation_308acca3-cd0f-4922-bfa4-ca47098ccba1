import * as classnames from 'classnames';
import * as update from 'immutability-helper';
import * as moment from 'moment';
import * as React from 'react';

import { Button } from 'gces-ui/lib/components/Button';
import { DailyRepeatDetail, ScheduledInfo } from '../../store/interface';
import { startTimeMatchDaysOfWeek } from '../../utils';

export interface Day {
	name: string;
	value: number;
}

export interface DayPickerProps {
	inverted: boolean;
	scheduleInfo: ScheduledInfo;
	updateScheduleInfo: (scheduleInfo: ScheduledInfo) => void;
}

export class DayPicker extends React.PureComponent<DayPickerProps> {

	_days: Day[] = [];

	constructor(context, props) {
		super(context, props);

		const localeData = moment.localeData();
		const weekdays = localeData.weekdaysMin();
		const firstDay = localeData.firstDayOfWeek();
		const days: Day[] = [
			{ value: 1, name: weekdays[0] },
			{ value: 2, name: weekdays[1] },
			{ value: 3, name: weekdays[2] },
			{ value: 4, name: weekdays[3] },
			{ value: 5, name: weekdays[4] },
			{ value: 6, name: weekdays[5] },
			{ value: 7, name: weekdays[6] },
		];

		this._days = firstDay > 0 ? [...days.slice(firstDay), ...days.slice(0, firstDay)] : days;
	}

	onDayClick = (value: number) => () => {
		const { scheduleInfo, scheduleInfo: { detail, startDate }, updateScheduleInfo } = this.props;
		const { days: oldDays } = detail as DailyRepeatDetail;
		let newDays = [];
		if (oldDays.indexOf(value) !== -1) {
			newDays = oldDays.reduce((rs, x) => {
				if (x !== value) rs.push(x);
				return rs;
			}, []);
		}
		else {
			newDays = [...oldDays, value];
		}
		if (newDays.length === 0) return;
		const newSchedule = update(scheduleInfo, { detail: { days: { $set: newDays } } });
		if (!startTimeMatchDaysOfWeek(startDate, newSchedule)) {
			return;
		}
		updateScheduleInfo(newSchedule);
	}

	public render() {
		const { scheduleInfo: { repeatType, detail }, inverted } = this.props;

		if (repeatType !== 'Daily' && repeatType !== 'IntervalWeekly') return null;

		const { days } = detail as DailyRepeatDetail;
		const itemClass = classnames('sch-day', { 'ef-inverted': inverted });

		return (
			<div className={itemClass}>
				{this._days.map(day => (<div key={day.value}><Button aid={`day-picker-${day.value}-btn`} text={day.name} title={day.name} onClick={this.onDayClick(day.value)} size='small' style={days.indexOf(day.value) !== -1 ? 'accent' : 'default'} rounded inverted={inverted} /></div>))}
			</div>
		);
	}
}