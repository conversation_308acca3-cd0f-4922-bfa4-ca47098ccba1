.tnt-detail-editor {
	padding: 10px;
	flex: 1;
	min-width: 0;
	display: flex;
	flex-direction: column;
	position: relative;

	.tnt-name {
		flex: 0 0 50px;
		display: flex;
		align-items: center;
		font-size: $ef-font-size-sm;
		font-weight: bold;

		span {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			max-width: 300px;
		}
	}

	.organization-tab {
		flex: 1;

		.tab-container {
			min-width: 0;

			.tab-container-header {
				margin-bottom: 10px;
			}

			.ef-tab span {
				font-size: 12px;
				width: auto;
			}

			.tab-container-content {
				display: flex;
				padding: 0;
			}
		}

		.portal-configuration-editor {
			.tab-container {
				display: none;
			}

			.editor-title {
				display: none;
			}

			.portal-configuration-editor-body {
				.portal-configuration {
					.portal-configuration-body {
						.portal-setting {
							padding: 0;
						}
					}

					.portal-configuration-footer {
						flex: 0 0 60px;

						button {
							margin: 0 15px 0 0;
						}
					}
				}

				.ap-view-tags,
				.document-theme-config {
					padding: 0;

					.footer {
						flex: 0 0 60px;

						button {
							margin: 0 15px 0 0;
						}
					}
				}
			}
		}
	}

	.ec-footer {
		flex: 0 0 60px;
		padding: 0 0 0 10px;
		display: flex;
		align-items: center;

		button {
			margin-right: 15px;
		}
	}

	.nav-bar {
		flex: 0 0 40px;
		border-bottom: 1px solid $ef-accent;
		position: relative;

		.nav-item {
			height: 40px;
			line-height: 40px;
			border-bottom: 2px solid $ef-accent;
			text-align: center;
			display: inline-block;
			padding: 0 20px;
			font-size: $ef-font-size-sm;
			color: $ef-accent;
		}

		button {
			position: absolute;
			bottom: 3px;
			right: 5px;
		}
	}

	.basic-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		padding: 0 0 0 10px;

		.c-grid-wrapper {
			.cg-header {
				.cell-content {
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}

			.cg-body {
				.cg-row {
					.cg-cell {
						align-items: center;

						.property-name {
							max-width: 90%;
							display: flex;

							.property-name-inner {
								flex: 1;
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
							}

							.required-property-star {
								flex: 0 0 5px;
								color: $danger;
								margin-left: 2px;
								font-size: $ef-font-size-sm;
							}
						}

						.property-value-type {
							@include gces-truncate;
						}

						.property-value {
							display: flex;
							flex-direction: column;
							flex-grow: 1;
							min-width: 0;

							&.no-sensitive-property-value {
								max-width: 90%;
							}

							input::-webkit-outer-spin-button,
							input::-webkit-inner-spin-button {
								-webkit-appearance: none;
								margin: 0;
							}

							input[type='number'] {
								-moz-appearance: textfield;
								appearance: none;
							}

							input::-ms-clear {
								display: none;
							}

							.error-text {
								height: 20px;
								line-height: 20px;
								font-size: $ef-font-size-sm;
								color: red;
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
							}

							.efc-sensitive-wrapper {
								display: flex;
								align-items: center;

								.efc-datetime {
									width: 100%;
								}

								> textarea {
									resize: none;
								}
							}

							.efc-input-visible-toggle {
								margin-left: 5px;

								> i {
									font-size: $ef-font-size-lg;
								}

								&.efc-input-visible-hidden {
									visibility: hidden;
								}

								&.efc-input-visible-none {
									display: none;
								}
							}

							.multi-valued {
								.efc-textbox {
									height: 85px;
								}
							}

							.efc-input-password {
								&.multi-valued {
									.efc-text-security {
										-webkit-text-security: disc;
									}
								}

								.efc-text-security {
									&.efc-dropdown {
										.ef-btn {
											-webkit-text-security: disc;
										}
									}

									&.efc-datetime {
										-webkit-text-security: disc;
									}

									&.efc-number-textbox {
										-webkit-text-security: disc;
									}
								}
							}
						}

						.disable-to-view,
						.disable-to-edit {
							white-space: pre;
						}
					}
				}
			}
		}

		.at-footer {
			flex: 0 0 60px;
			display: flex;
			align-items: center;

			button {
				margin-right: 15px;
			}
		}
	}

	.org-permission-list-container {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 0 0 10px;

		.org-permission-tip {
			height: 30px;
			display: flex;
			align-items: center;
			background-color: var(--gces-accent1-transparentize90);
			border-radius: 5px;
			padding: 0 7px;

			i {
				color: var(--gces-accent1);
				margin: 0 10px 0 0;
			}

			span {
				font-size: var(--gces-ef-font-size-sm);
				color: var(--gces-content-text);
				display: inline-block;

				@include gces-truncate;
			}
		}

		.permission-list-wrap {
			flex: 1;
			min-height: 0;

			@import '../../Common/PermissionList/permission-list.scss';
		}
	}

	@import './members-editor.scss';
	@import './tenant-cascade-delete-permission-card.scss';
}
