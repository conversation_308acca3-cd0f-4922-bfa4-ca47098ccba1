@at-root [class*='theme-'].light-mode {
	.organization-info {
		.organization-info-item {
			.organization-path {
				--gces-organization-path-color: var(--gces-content-text-dk-20);
			}
		}
	}
}

@at-root [class*='theme-'].dark-mode {
	.organization-info {
		.organization-info-item {
			.organization-path {
				--gces-organization-path-color: var(--gces-content-text-lt-20);
			}
		}
	}
}

.user-detail {
	position: fixed;
	top: 0;
	right: 0;
	height: 100%;
	width: 455px !important;
	font-size: $ef-font-size-sm;

	.info-area-title {
		margin-bottom: 10px;
	}

	.info-area {
		background-color: var(--gces-content-bg-lt-5);
		border-radius: 3px;
		padding: 10px;
		min-height: 50px;
	}

	.info-item {
		margin-bottom: 30px;
	}

	.basic-info {
		.basic-info-area {
			border-radius: 3px;

			.avatar-name-phone {
				display: flex;
				margin-bottom: 20px;

				.user-avatar {
					height: 70px;
					width: 70px;
					border-radius: 35px;
					color: $text-contrast;
					border: 1px solid $text-contrast;
					background-color: $ef-bg-dk;
					margin-right: 10px;
				}

				.info-detail {
					display: flex;
					flex-direction: column;
					justify-content: center;

					.user-name {
						font-size: $ef-font-size-sm;
						font-weight: normal;
						margin: 0;
					}

					div {
						position: relative;

						i {
							position: absolute;
							top: 1px;
						}

						span {
							margin-left: 15px;
						}
					}
				}
			}

			.builtin-properties,
			.unmultivalued-custom-properties,
			.multivalued-custom-properties {
				width: 100%;
				display: flex;
				flex-wrap: wrap;
				justify-content: flex-start;

				.property-item {
					margin-bottom: 10px;
					flex: 0 0 33%;

					.property-title,
					.property-value {
						display: block;
						max-width: 110px;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}

					.efc-sensitive-wrapper {
						display: flex;
						align-items: center;
						max-width: 110px;

						&.efc-no-sensitive-wrapper,
						&.efc-input-password-show {
							.efc-textbox {
								@include gces-truncate;
							}
						}

						.efc-textbox {
							background-color: transparent;
							padding: 0;
							cursor: text;
							border: none;
						}

						.efc-input-visible-toggle {
							>i {
								font-size: $ef-font-size-lg;
							}

							&.efc-input-visible-hidden {
								visibility: hidden;
							}

							&.efc-input-visible-none {
								display: none;
							}
						}
					}
				}
			}

			.divider {
				height: 2px;
				background-color: $ef-bg-lt;
				margin-top: 10px;
				margin-bottom: 5px;
			}
		}
	}

	.organization-info {
		.organization-info-item {
			margin-bottom: 10px;

			.organization-name {
				display: block;
			}

			.organization-path {
				font-style: italic;
				font-weight: 300;
				color: var(--gces-organization-path-color);
			}
		}
	}

	.role-info {
		.role-info-item {
			margin-bottom: 10px;

			.role-item-icon {
				margin-right: 3px;
			}

			.organization-item-icon {
				margin-right: 3px;
			}
		}
	}

	.gc-drawer-footer {
		.close-detail-button {
			margin-left: 10px;
		}
	}
}