import * as React from 'react';
import { translate } from 'react-i18next';
import { Button, InputEditor, AriaIcon } from 'gces-ui';
import CGrid from 'gces-react-grid';

interface LocalState {
	search: string;
	rightUserIds: string[];
}

export interface MemberEditorUserInfo {
	id?: string;
	username: string;
	isInRight?: boolean;
	provider?: string;
}

export interface MembersEditorCommonProps {
	getSummaryText?: (total: number) => string;
	onSaveClick?: (includedIds: string[]) => void;
	onCancelClick?: () => void;
	allUsers: MemberEditorUserInfo[];
	t?: any;
}

@translate('common', { wait: true })
export class MembersEditorCommon extends React.PureComponent<MembersEditorCommonProps, LocalState> {
	state: LocalState = {
		search: '',
		rightUserIds: [],
	};

	componentWillMount() {
		this.initUserIds(this.props);
	}
	componentWillReceiveProps(nextProps: MembersEditorCommonProps) {
		this.initUserIds(nextProps);
	}
	initUserIds(props: MembersEditorCommonProps) {
		this.setState({ rightUserIds: props.allUsers.filter(s => s.isInRight).map(s => s.id) });
	}

	renderUserGrid = (displayUsers: any, left: boolean = true) => {
		const { t } = this.props;
		const columns = [
			{ key: 'username', label: t('Username') },
			{ key: 'provider', label: t('Provider') },
			{ key: 'contextMenu', label: '', width: 40 }
		];

		const moveSingleItemToRight = (e, row: MemberEditorUserInfo, rowIndex: number) => {
			e.preventDefault();
			e.stopPropagation();
			const rightUserIds = [row.id, ...this.state.rightUserIds];
			this.setState({ rightUserIds });
		};

		const moveSingleItemToLeft = (e, row: MemberEditorUserInfo, rowIndex: number) => {
			e.preventDefault();
			e.stopPropagation();
			const rightUserIds = this.state.rightUserIds.filter(id => id !== row.id);
			this.setState({ rightUserIds });
		};

		const renderCell = (key: string, row: MemberEditorUserInfo, rowIndex: number) => {
			if (key === 'contextMenu') {
				if (left) {
					return <Button
						rounded
						className='member-selector-action'
						size='small'
						style='transparent'
						icon='mdi mdi-close mdi-rotate-45'
						onClick={(e) => moveSingleItemToRight(e, row, rowIndex)}
					/>;
				} else {
					return <Button
						rounded
						className='member-selector-action'
						size='small'
						style='transparent'
						icon='mdi mdi-close'
						onClick={(e) => moveSingleItemToLeft(e, row, rowIndex)}
					/>;
				}
			}
		};

		return (
			<CGrid
				rowHeight={40}
				rows={displayUsers}
				hideGridLine={true}
				columns={columns}
				columnResizing={false}
				useSmallScrollbars={true}
				onRenderCell={renderCell}
				noRowsTip={t('cmEmptyResult')}
			/>
		);
	}

	renderBody = () => {
		const { allUsers, t, getSummaryText } = this.props;
		const { rightUserIds } = this.state;
		const ls = this.state.search.toLowerCase();
		const userNameComparer = (a: MemberEditorUserInfo, b: MemberEditorUserInfo) =>
			a.username.localeCompare(b.username);
		const leftUsers = allUsers
			.filter(u => rightUserIds.indexOf(u.id) === -1)
			.filter(u => !ls || u.username.toLowerCase().indexOf(ls) !== -1 || (u.provider && u.provider.toLowerCase().indexOf(ls) !== -1))
			.sort(userNameComparer);
		const rightUsers = allUsers.filter(u => rightUserIds.indexOf(u.id) !== -1).sort(userNameComparer);
		const textTitle = getSummaryText ? getSummaryText(rightUsers.length) : t('ecMembers', { count: rightUsers.length });

		return (
			<div className='me-body'>
				<div className='left-users'>
					<div className='search-box-wp'>
						<div className='search-box'>
							<AriaIcon type='span' className='mdi mdi-magnify sc-icon' />
							<InputEditor
								value={this.state.search}
								className='sc-input'
								onEveryChange={(search) => this.setState({ search })}
							/>
						</div>
					</div>
					<div className='user-list'>
						{this.renderUserGrid(leftUsers)}
					</div>
				</div>
				<div className='add-btn-wp'>
					<div className='add-btn'>
						<AriaIcon className='mdi mdi-chevron-double-right' />
					</div>
				</div>
				<div className='right-users'>
					<div className='summary'>
						<span title={textTitle}>
							{textTitle}
						</span>
					</div>
					<div className='user-list'>
						{this.renderUserGrid(rightUsers, false)}
					</div>
				</div>
			</div>
		);
	}

	render() {
		const { t, onSaveClick, onCancelClick } = this.props;
		const { rightUserIds } = this.state;

		return (
			<div className='members-editor'>
				<div className='me-header'>
					<h3 className='select-members-title' title={t('ecSelectMembers')}>{t('ecSelectMembers')}</h3>
					<Button
						className='close-select-members-button'
						rounded={true}
						size='small'
						style='transparent'
						icon='mdi mdi-close'
						aid='close select members panel'
						onClick={() => onCancelClick && onCancelClick()}
					/>
				</div>

				{this.renderBody()}
				<div className='me-footer'>
					<div className='btn-group'>
						<Button
							style='accent'
							size='small'
							text={t('cmSave')}
							title={t('cmSave')}
							onClick={() => onSaveClick && onSaveClick(rightUserIds)}
						/>
						<Button
							size='small'
							text={t('cmCancel')}
							title={t('cmCancel')}
							onClick={() => onCancelClick && onCancelClick()}
						/>
					</div>
				</div>
			</div>
		);
	}
}
