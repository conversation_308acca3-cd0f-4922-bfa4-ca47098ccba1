import * as React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';
import { Scrollbars } from 'gces-react-custom-scrollbars';

import ProviderList from './ProviderList';
import ProviderSettings from './ProviderSettings';
import { Provider } from '../interfaces';

import * as util from '../util';

interface ConnectProps {
	dispatch?: any;
	t?: any;
}

interface LocalState {
	providers: Provider[];
	selectedProvider: Provider;
}

class ExternalProvider extends React.Component<ConnectProps, LocalState> {
	constructor(props, context) {
		super(props, context);
		this.state = { providers: null, selectedProvider: null };
	}

	componentDidMount = () => {
		this.fetchExternalProviders(providers => {
			this.setState({ providers });
			if (providers.length > 0) this.selectProvider(providers[0]);
		});
	}

	fetchExternalProviders = (callback: (providers: Provider[]) => void) => {
		util.ajax('/api/v2/identity/external-providers', callback);
	}

	saveProviderSettings = (provider: Provider) => {
		const url = `/api/v2/identity/external-providers/${provider.providerName}`;
		const callback = (data) => {
			this.fetchExternalProviders(providers => {
				this.setState({ providers });
			});
			this.setState({ selectedProvider: data });
		};
		util.ajax(url, callback, 'PUT', provider);
	}

	selectProvider = (provider: Provider) => {
		if (!this.state.selectedProvider || provider.providerName !== this.state.selectedProvider.providerName) {
			this.setState({ selectedProvider: provider });
		}
	}

	render() {
		if (!this.state.providers || !this.state.selectedProvider) return null;

		const providerListProps = {
			providers: this.state.providers,
			nameProp: 'providerName',
			titleProp: 'description',
			selectedProvider: this.state.selectedProvider,
			onSelect: (value) => this.selectProvider(value)
		};

		const providerSettingProps = {
			hideHeader: true,
			provider: { type: 'ExternalLoginProvider', ...this.state.selectedProvider },
			onSave: (providerName) => this.saveProviderSettings(providerName)
		};

		const scrollbarProps = {
			style: { width: '100%', height: '100%' },
			renderThumbVertical: props => <div {...props} style={{ width: '4px' }} className='thumb-vertical' />,
		};

		return <Scrollbars {...scrollbarProps}>
			<div className='provider-container' >
				<div className='left-part'>
					{providerListProps && <ProviderList {...providerListProps} />}
				</div>
				<div className='vertical-separator' />
				<div className='right-part'>
					{providerSettingProps && <ProviderSettings {...providerSettingProps} />}
				</div>
			</div>
		</Scrollbars>;
	}
}

const connector = connect(state => {
	return state['account-management'].ep;
});
const translator = translate('account', { withRef: true });

export default translator(connector(ExternalProvider));
