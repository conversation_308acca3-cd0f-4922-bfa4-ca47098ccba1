#dropdown-root {
	.ef-dd-menu {
		z-index: 99999;
	}
}

.property-management,
.license-management,
.license-management-container,
.concurrence-management,
.cv-license-management {
	height: 100%;
	min-width: 900px;
	display: flex;
	flex-direction: column;
	position: relative;

	.multivalued-value {
		font-size: 16px;

		&:before {
			font-weight: bold;
		}
	}

	table {
		width: 100%;

		tbody {
			width: 100%;

			tr {
				max-width: 100%;

				td {
					max-width: 300px;
					display: inline-block;

					@include gces-truncate;
				}
			}
		}
	}
}

.btm-cm-group {
	justify-content: space-between;
	align-self: flex-end;
	align-items: center;
	overflow: visible;
	width: 100%;
	display: flex;
	height: 40px;
	margin-bottom: 10px;

	.btn {
		> span {
			display: block;
			max-width: 150px;

			@include gces-truncate;
		}
	}

	.toolbar-div {
		width: 100%;

		.btn {
			width: 100px;
		}

		.tb-search-box-container {
			flex-direction: row-reverse;
			float: right;
			margin: 10px 0;

			.tb-search-box {
				background: $ef-bg-dk;
				flex: 0 0 160px;
			}

			.sb-icon {
				color: $ef-accent;
			}

			.ef-btn {
				width: 30px;
			}
		}

		.ef-toolbar {
			float: right;
			margin-top: 10px;
			width: auto;

			.sb-icon {
				color: $ef-accent;
			}

			.ef-btn {
				width: 30px;
			}
		}

		i.mdi-magnify {
			color: $ef-accent;
		}
	}
}

input[type='text']::-ms-clear {
	display: none;
}

.table-wrapper {
	flex-grow: 1;
	overflow: auto;
}

.btn-danger {
	color: #fff !important;
	background-color: #d9534f !important;
	border-color: #d43f3a !important;
}

.property-management {
	.form-item {
		.efc-sensitive-wrapper {
			.efc-input-visible-toggle {
				&.efc-input-visible-none {
					display: none;
				}
			}
		}

		.form-switch-item {
			display: flex;
			align-items: center;

			.efc-content {
				display: flex;
				justify-content: flex-end;
			}
		}
	}
}

.cv-license-management {
	.import-toolbar {
		margin-bottom: 10px;

		.cv-license-register {
			display: flex;
			align-items: center;

			.drop-zone {
				.btn {
					> span {
						display: block;
						max-width: 150px;

						@include gces-truncate;
					}
				}
			}

			.import-info {
				margin-left: 30px;
				margin-right: auto;
				display: block;
				font-size: $ef-font-size;
			}
		}
	}

	.cg-cell {
		.cv-license-valid {
			font-size: $ef-icon-20;
			color: $ef-accent;
		}

		.cv-license-warning {
			font-size: $ef-icon-20;
			color: $warning;
		}

		.cv-license-error {
			font-size: $ef-icon-20;
			color: $danger;
		}

		.cv-license-grid-dropdown,
		.cv-license-grid-checkbox {
			align-self: center;
		}

		.cv-license-grid-checkbox {
			padding-bottom: 27px;
		}
	}

	.cg-h-cell {
		.cv-license-grid-checkbox {
			height: 100%;
			display: flex;
			align-items: center;
			padding-bottom: 22px;
		}

		.cv-license-grid-license-info {
			display: flex;
			flex: 1;

			.license-info-tip {
				color: $ef-accent;
				font-size: $ef-font-size;
				margin-left: 2px;
			}

			> span {
				display: block;

				@include gces-truncate;
			}
		}
	}

	.uploaded-footer {
		position: absolute;
		right: 15px;
		bottom: 15px;
		z-index: 1000;

		.btn {
			width: 80px;
			margin-left: 15px;
		}
	}
}

@import './license-management/common.scss';
@import './license-management/license-dialog.scss';

@at-root [class*='theme-'].light-mode {
	.ReactModalPortal {
		.modal-dialog {
			.modal-content {
				.modal-footer {
					--gces-transformed-color: var(--gces-panels-bg-dk-5);
					--gces-btn-bg: var(--gces-panels-bg-dk-10);
					--gces-btn-hover-bg: var(--gces-panels-bg-dk-10);
					--gces-btn-active-bg: var(--gces-panels-bg-dk-15);
				}
			}
		}
	}
}

@at-root [class*='theme-'].dark-mode {
	.ReactModalPortal {
		.modal-dialog {
			.modal-content {
				.modal-footer {
					--gces-transformed-color: var(--gces-panels-bg-lt-5);
					--gces-btn-bg: var(--gces-panels-bg-lt-10);
					--gces-btn-hover-bg: var(--gces-panels-bg-lt-10);
					--gces-btn-active-bg: var(--gces-panels-bg-lt-15);
				}
			}
		}
	}
}

.ReactModalPortal {
	.ReactModal__Overlay--after-open {
		z-index: 10001;
		background-color: rgba(0, 0, 0, 0.5) !important;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.modal-dialog {
		max-width: 800px;
		font-size: 14px;
		display: flex;
		flex-direction: column;
		border-radius: 1px;
		background: $ef-bg-lt;
		box-shadow: $ef-shadow-border;
		margin: 0;

		&.delete-confirm-dlg {
			width: 520px;
		}

		.modal-content {
			border-radius: unset;
			border: none;
			width: 522px;

			.modal-header {
				padding: 10px 20px;
				border: none;
				color: $text-contrast;
				font-size: 16px;
				line-height: 30px;
				background-color: $ef-accent;
				border-radius: unset;
				display: block;
				max-width: 100%;

				@include gces-truncate;
			}

			.modal-body {
				background-color: $ef-body-bg;
				padding: 20px;
				margin: 0;

				.deactivate-offline-confirm {
					.efc-checkbox-wrapper {
						> div {
							margin: 0 !important;
							flex: 0 !important;
						}
					}
				}

				.offline-tips {
					margin-top: 10px;

					.offline-step {
						margin: 3px 0;
						padding: 0;
						overflow: hidden;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-moz-box-orient: vertical;
						-webkit-line-clamp: 3;
					}
				}
			}

			.modal-footer {
				display: flex;
				justify-content: flex-end;
				background: var(--gces-transformed-color);
				padding: 10px 20px;
				border: none;

				.ef-btn {
					margin-left: 6px;
					height: 30px;
					border-radius: 2px;
					min-width: 50px;
					background-color: var(--gces-btn-bg);
					line-height: 30px;

					&:not([disabled]):not(.disabled):hover {
						background-color: var(--gces-btn-hover-bg);
					}

					&:not([disabled]):not(.disabled):active {
						background-color: var(--gces-btn-active-bg);
					}
				}

				.ef-btn.ef-btn-accent {
					color: $text-contrast;
					background-color: $ef-accent;

					&:not([disabled]):not(.disabled):hover {
						background-color: var(--gces-accent1-dk-5);
					}

					&:not([disabled]):not(.disabled):active {
						background-color: var(--gces-accent1-dk-10);
					}
				}
			}
		}

		&.qr-code-dialog,
		&.client-code-dialog {
			border-radius: 5px;

			.modal-content {
				border-radius: 5px;

				.modal-body {
					border-radius: 5px;
					background-color: white;
					padding: 10px;

					.modal-body-row {
						display: flex;
						justify-content: center;
						font-size: 12px;

						&.close-row {
							display: flex;
							justify-content: flex-end;
							color: #8d8d8d;
							font-size: 18px;

							.mdi {
								cursor: pointer;
							}
						}
					}
				}
			}
		}

		&.client-code-dialog {
			.modal-content {
				width: 350px;
				height: 160px;

				.modal-body-row {
					&.client-code-row {
						padding: 15px 0;

						.client-code {
							width: 90%;
							font-size: 40px;
							font-weight: bold;
							background-color: var(--gces-content-bg-dk);
							border-radius: 5px;
							display: flex;
							justify-content: space-around;
						}
					}
				}
			}
		}

		&.qr-code-dialog {
			.modal-content {
				width: 600px;

				.modal-body-row {
					&.qr-code-row {
						margin-bottom: 10px;
					}

					.qr-tip {
						display: inline-block;
						width: 300px;
						text-align: center;
					}
				}
			}
		}

		&.license-dialog {
			transition: none;

			.generatedInfo-row {
				display: flex;
				align-items: center;
			}
		}
	}
}

.ef-expanded-notifications {
	width: 660px;

	.ef-en-content {
		white-space: pre;
	}
}
