# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/code-frame/-/code-frame-7.16.7.tgz#44416b6bd7624b998f5b1af5d470856c40138789"
  integrity sha512-iAXqUn8IIeBTNd72xsFlgaXHkMBMt6y4HJp1tIaK465CWLT/fG1aqB7ykr95gHHmlBdGbFeWWfyB4NJJ0nmeIg==
  dependencies:
    "@babel/highlight" "^7.16.7"

"@babel/compat-data@^7.13.11", "@babel/compat-data@^7.16.4", "@babel/compat-data@^7.17.0":
  version "7.17.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/compat-data/-/compat-data-7.17.0.tgz#86850b8597ea6962089770952075dcaabb8dba34"
  integrity sha512-392byTlpGWXMv4FbyWw3sAZ/FrW/DrwqLGXpy0mbyNe9Taqv1mg9yON5/o0cnr8XYCkFTZbC1eV+c+LAROgrng==

"@babel/core@7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/core/-/core-7.16.7.tgz#db990f931f6d40cb9b87a0dc7d2adc749f1dcbcf"
  integrity sha512-aeLaqcqThRNZYmbMqtulsetOQZ/5gbR/dWruUCJcpas4Qoyy+QeagfDsPdMrqwsPRDNxJvBlRiZxxX7THO7qtA==
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "@babel/generator" "^7.16.7"
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helpers" "^7.16.7"
    "@babel/parser" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.16.7"
    "@babel/types" "^7.16.7"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.1.2"
    semver "^6.3.0"
    source-map "^0.5.0"

"@babel/generator@^7.16.7", "@babel/generator@^7.17.3":
  version "7.17.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/generator/-/generator-7.17.3.tgz#a2c30b0c4f89858cb87050c3ffdfd36bdf443200"
  integrity sha512-+R6Dctil/MgUsZsZAkYgK+ADNSZzJRRy0TvY65T71z/CR854xHQ1EweBYXdfT+HNeN7w0cSJJEzgxZMv40pxsg==
  dependencies:
    "@babel/types" "^7.17.0"
    jsesc "^2.5.1"
    source-map "^0.5.0"

"@babel/helper-annotate-as-pure@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.16.7.tgz#bb2339a7534a9c128e3102024c60760a3a7f3862"
  integrity sha512-s6t2w/IPQVTAET1HitoowRGXooX8mCgtuP5195wD/QJPV6wYjpujCGF7JuMODVX2ZAJOf1GT6DT9MHEZvLOFSw==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.16.7.tgz#38d138561ea207f0f69eb1626a418e4f7e6a580b"
  integrity sha512-C6FdbRaxYjwVu/geKW4ZeQ0Q31AftgRcdSnZ5/jsH6BzCJbtvXvhpfkbkThYSuutZA7nCXpPR6AD9zd1dprMkA==
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-compilation-targets@^7.13.0", "@babel/helper-compilation-targets@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-compilation-targets/-/helper-compilation-targets-7.16.7.tgz#06e66c5f299601e6c7da350049315e83209d551b"
  integrity sha512-mGojBwIWcwGD6rfqgRXVlVYmPAv7eOpIemUG3dGnDdCY4Pae70ROij3XmfrH6Fa1h1aiDylpglbZyktfzyo/hA==
  dependencies:
    "@babel/compat-data" "^7.16.4"
    "@babel/helper-validator-option" "^7.16.7"
    browserslist "^4.17.5"
    semver "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.16.10", "@babel/helper-create-class-features-plugin@^7.16.7", "@babel/helper-create-class-features-plugin@^7.17.6":
  version "7.17.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.17.6.tgz#3778c1ed09a7f3e65e6d6e0f6fbfcc53809d92c9"
  integrity sha512-SogLLSxXm2OkBbSsHZMM4tUi8fUzjs63AT/d0YQIzr6GSd8Hxsbk2KYDX0k0DweAzGMj/YWeiCsorIdtdcW8Eg==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-member-expression-to-functions" "^7.16.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"

"@babel/helper-create-regexp-features-plugin@^7.16.7":
  version "7.17.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.17.0.tgz#1dcc7d40ba0c6b6b25618997c5dbfd310f186fe1"
  integrity sha512-awO2So99wG6KnlE+TPs6rn83gCz5WlEePJDTnLEqbchMVrBeAujURVphRdigsk094VhvZehFoNOihSlcBjwsXA==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    regexpu-core "^5.0.1"

"@babel/helper-define-polyfill-provider@^0.3.0", "@babel/helper-define-polyfill-provider@^0.3.1":
  version "0.3.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.1.tgz#52411b445bdb2e676869e5a74960d2d3826d2665"
  integrity sha512-J9hGMpJQmtWmj46B3kBHmL38UhJGhYX7eqkcq+2gsstyYt341HmPeWspihX43yVRA0mS+8GGk2Gckc7bY/HCmA==
  dependencies:
    "@babel/helper-compilation-targets" "^7.13.0"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/traverse" "^7.13.0"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"
    semver "^6.1.2"

"@babel/helper-environment-visitor@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-environment-visitor/-/helper-environment-visitor-7.16.7.tgz#ff484094a839bde9d89cd63cba017d7aae80ecd7"
  integrity sha512-SLLb0AAn6PkUeAfKJCCOl9e1R53pQlGAfc4y4XuMRZfqeMYLE0dM1LMhqbGAlGQY0lfw5/ohoYWAe9V1yibRag==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-explode-assignable-expression@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.16.7.tgz#12a6d8522fdd834f194e868af6354e8650242b7a"
  integrity sha512-KyUenhWMC8VrxzkGP0Jizjo4/Zx+1nNZhgocs+gLzyZyB8SHidhoq9KK/8Ato4anhwsivfkBLftky7gvzbZMtQ==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-function-name@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-function-name/-/helper-function-name-7.16.7.tgz#f1ec51551fb1c8956bc8dd95f38523b6cf375f8f"
  integrity sha512-QfDfEnIUyyBSR3HtrtGECuZ6DAyCkYFp7GHl75vFtTnn6pjKeK0T1DB5lLkFvBea8MdaiUABx3osbgLyInoejA==
  dependencies:
    "@babel/helper-get-function-arity" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-get-function-arity@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-get-function-arity/-/helper-get-function-arity-7.16.7.tgz#ea08ac753117a669f1508ba06ebcc49156387419"
  integrity sha512-flc+RLSOBXzNzVhcLu6ujeHUrD6tANAOU5ojrRx/as+tbzf8+stUCj7+IfRRoAbEZqj/ahXEMsjhOhgeZsrnTw==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-hoist-variables@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-hoist-variables/-/helper-hoist-variables-7.16.7.tgz#86bcb19a77a509c7b77d0e22323ef588fa58c246"
  integrity sha512-m04d/0Op34H5v7pbZw6pSKP7weA6lsMvfiIAMeIvkY/R4xQtBSMFEigu9QTZ2qB/9l22vsxtM8a+Q8CzD255fg==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-member-expression-to-functions@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.16.7.tgz#42b9ca4b2b200123c3b7e726b0ae5153924905b0"
  integrity sha512-VtJ/65tYiU/6AbMTDwyoXGPKHgTsfRarivm+YbB5uAzKUyuPjgZSgAFeG87FCigc7KNHu2Pegh1XIT3lXjvz3Q==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-module-imports@^7.12.13", "@babel/helper-module-imports@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-module-imports/-/helper-module-imports-7.16.7.tgz#25612a8091a999704461c8a222d0efec5d091437"
  integrity sha512-LVtS6TqjJHFc+nYeITRo6VLXve70xmq7wPhWTqDJusJEgGmkAACWwMiTNrvfoQo6hEhFwAIixNkvB0jPXDL8Wg==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-module-transforms@^7.16.7":
  version "7.17.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-module-transforms/-/helper-module-transforms-7.17.6.tgz#3c3b03cc6617e33d68ef5a27a67419ac5199ccd0"
  integrity sha512-2ULmRdqoOMpdvkbT8jONrZML/XALfzxlb052bldftkicAUy8AxSCkD5trDPQcwHNmolcl7wP6ehNqMlyUw6AaA==
  dependencies:
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-simple-access" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    "@babel/helper-validator-identifier" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.17.3"
    "@babel/types" "^7.17.0"

"@babel/helper-optimise-call-expression@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.16.7.tgz#a34e3560605abbd31a18546bd2aad3e6d9a174f2"
  integrity sha512-EtgBhg7rd/JcnpZFXpBy0ze1YRfdm7BnBX4uKMBd3ixa3RGAE002JZB66FJyNH7g0F38U05pXmA5P8cBh7z+1w==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.13.0", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.16.7", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-plugin-utils/-/helper-plugin-utils-7.16.7.tgz#aa3a8ab4c3cceff8e65eb9e73d87dc4ff320b2f5"
  integrity sha512-Qg3Nk7ZxpgMrsox6HreY1ZNKdBq7K72tDSliA6dCl5f007jR4ne8iD5UzuNnCJH2xBf2BEEVGr+/OL6Gdp7RxA==

"@babel/helper-remap-async-to-generator@^7.16.8":
  version "7.16.8"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.16.8.tgz#29ffaade68a367e2ed09c90901986918d25e57e3"
  integrity sha512-fm0gH7Flb8H51LqJHy3HJ3wnE1+qtYR2A99K06ahwrawLdOFsCEWjZOrYricXJHoPSudNKxrMBUPEIPxiIIvBw==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-wrap-function" "^7.16.8"
    "@babel/types" "^7.16.8"

"@babel/helper-replace-supers@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-replace-supers/-/helper-replace-supers-7.16.7.tgz#e9f5f5f32ac90429c1a4bdec0f231ef0c2838ab1"
  integrity sha512-y9vsWilTNaVnVh6xiJfABzsNpgDPKev9HnAgz6Gb1p6UUwf9NepdlsV7VXGCftJM+jqD5f7JIEubcpLjZj5dBw==
  dependencies:
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-member-expression-to-functions" "^7.16.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/traverse" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-simple-access@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-simple-access/-/helper-simple-access-7.16.7.tgz#d656654b9ea08dbb9659b69d61063ccd343ff0f7"
  integrity sha512-ZIzHVyoeLMvXMN/vok/a4LWRy8G2v205mNP0XOuf9XRLyX5/u9CnVulUtDgUTama3lT+bf/UqucuZjqiGuTS1g==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-skip-transparent-expression-wrappers@^7.16.0":
  version "7.16.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.16.0.tgz#0ee3388070147c3ae051e487eca3ebb0e2e8bb09"
  integrity sha512-+il1gTy0oHwUsBQZyJvukbB4vPMdcYBrFHa0Uc4AizLxbq6BOYC51Rv4tWocX9BLBDLZ4kc6qUFpQ6HRgL+3zw==
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-split-export-declaration@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.16.7.tgz#0b648c0c42da9d3920d85ad585f2778620b8726b"
  integrity sha512-xbWoy/PFoxSWazIToT9Sif+jJTlrMcndIsaOKvTA6u7QEo7ilkRZpjew18/W3c7nm8fXdUDXh02VXTbZ0pGDNw==
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-string-parser@^7.23.4":
  version "7.23.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-string-parser/-/helper-string-parser-7.23.4.tgz#9478c707febcbbe1ddb38a3d91a2e054ae622d83"
  integrity sha1-lHjHB/68u+Hds4o9kaLgVK5iLYM=

"@babel/helper-validator-identifier@^7.14.9", "@babel/helper-validator-identifier@^7.22.20":
  version "7.22.20"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.20.tgz#c4ae002c61d2879e724581d96665583dbc1dc0e0"
  integrity sha1-xK4ALGHSh55yRYHZZmVYPbwdwOA=

"@babel/helper-validator-identifier@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-validator-identifier/-/helper-validator-identifier-7.16.7.tgz#e8c602438c4a8195751243da9031d1607d247cad"
  integrity sha512-hsEnFemeiW4D08A5gUAZxLBTXpZ39P+a+DGDsHw1yxqyQ/jzFEnxf5uTEGp+3bzAbNOxU1paTgYS4ECU/IgfDw==

"@babel/helper-validator-option@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-validator-option/-/helper-validator-option-7.16.7.tgz#b203ce62ce5fe153899b617c08957de860de4d23"
  integrity sha512-TRtenOuRUVo9oIQGPC5G9DgK4743cdxvtOw0weQNpZXaS16SCBi5MNjZF8vba3ETURjZpTbVn7Vvcf2eAwFozQ==

"@babel/helper-wrap-function@^7.16.8":
  version "7.16.8"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helper-wrap-function/-/helper-wrap-function-7.16.8.tgz#58afda087c4cd235de92f7ceedebca2c41274200"
  integrity sha512-8RpyRVIAW1RcDDGTA+GpPAwV22wXCfKOoM9bet6TLkGIFTkRQSkH1nMQ5Yet4MpoXe1ZwHPVtNasc2w0uZMqnw==
  dependencies:
    "@babel/helper-function-name" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.16.8"
    "@babel/types" "^7.16.8"

"@babel/helpers@^7.16.7":
  version "7.17.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/helpers/-/helpers-7.17.2.tgz#23f0a0746c8e287773ccd27c14be428891f63417"
  integrity sha512-0Qu7RLR1dILozr/6M0xgj+DFPmi6Bnulgm9M8BVa9ZCWxDqlSnqt3cf8IDPB5m45sVXUZ0kuQAgUrdSFFH79fQ==
  dependencies:
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.17.0"
    "@babel/types" "^7.17.0"

"@babel/highlight@^7.16.7":
  version "7.16.10"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/highlight/-/highlight-7.16.10.tgz#744f2eb81579d6eea753c227b0f570ad785aba88"
  integrity sha512-5FnTQLSLswEj6IkgVw5KusNUUFY9ZGqe/TRFnP/BKYHYgfh7tc+C7mwiy95/yNP7Dh9x580Vv8r7u7ZfTBFxdw==
  dependencies:
    "@babel/helper-validator-identifier" "^7.16.7"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.16.7", "@babel/parser@^7.17.3":
  version "7.17.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/parser/-/parser-7.17.3.tgz#b07702b982990bf6fdc1da5049a23fece4c5c3d0"
  integrity sha512-7yJPvPV+ESz2IUTPbOL+YkIGyCqOyNIzdguKQuJGnH7bg1WTIifuM21YqokFt/THWh1AkCRn9IgoykTRCBVpzA==

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.16.7.tgz#4eda6d6c2a0aa79c70fa7b6da67763dfe2141050"
  integrity sha512-anv/DObl7waiGEnC24O9zqL0pSuI9hljihqiDuFHC8d7/bjr/4RLGPWuc8rYOff/QPzbEPSkzG8wGG9aDuhHRg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.16.7.tgz#cc001234dfc139ac45f6bcf801866198c8c72ff9"
  integrity sha512-di8vUHRdf+4aJ7ltXhaDbPoszdkh59AQtJM5soLsuHpQJdFQZOA4uGj0V2u/CZ8bJ/u8ULDL5yq6FO/bCXnKHw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-proposal-optional-chaining" "^7.16.7"

"@babel/plugin-proposal-async-generator-functions@^7.16.7":
  version "7.16.8"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.16.8.tgz#3bdd1ebbe620804ea9416706cd67d60787504bc8"
  integrity sha512-71YHIvMuiuqWJQkebWJtdhQTfd4Q4mF76q2IX37uZPkG9+olBxsX+rH1vkhFto4UeJZ9dPY2s+mDvhDm1u2BGQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-remap-async-to-generator" "^7.16.8"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@7.16.7", "@babel/plugin-proposal-class-properties@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.16.7.tgz#925cad7b3b1a2fcea7e59ecc8eb5954f961f91b0"
  integrity sha512-IobU0Xme31ewjYOShSIqd/ZGM/r/cuOz2z0MDbNrhF5FW+ZVgi0f2lyeoj9KFPDOAqsYxmLWZte1WOwlvY9aww==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-proposal-class-static-block@^7.16.7":
  version "7.17.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.17.6.tgz#164e8fd25f0d80fa48c5a4d1438a6629325ad83c"
  integrity sha512-X/tididvL2zbs7jZCeeRJ8167U/+Ac135AM6jCAx6gYXDUviZV5Ku9UDvWS2NCuWlFjIRXklYhwo6HhAC7ETnA==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.17.6"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-proposal-decorators@7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.16.7.tgz#922907d2e3e327f5b07d2246bcfc0bd438f360d2"
  integrity sha512-DoEpnuXK14XV9btI1k8tzNGCutMclpj4yru8aXKoHlVmbO1s+2A+g2+h4JhcjrxkFJqzbymnLG6j/niOf3iFXQ==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-decorators" "^7.16.7"

"@babel/plugin-proposal-dynamic-import@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.16.7.tgz#c19c897eaa46b27634a00fee9fb7d829158704b2"
  integrity sha512-I8SW9Ho3/8DRSdmDdH3gORdyUuYnk1m4cMxUAdu5oy4n3OfN8flDEH+d60iG7dUfi0KkYwSvoalHzzdRzpWHTg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-namespace-from@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.16.7.tgz#09de09df18445a5786a305681423ae63507a6163"
  integrity sha512-ZxdtqDXLRGBL64ocZcs7ovt71L3jhC1RGSyR996svrCi3PYqHNkb3SwPJCs8RIzD86s+WPpt2S73+EHCGO+NUA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.16.7.tgz#9732cb1d17d9a2626a08c5be25186c195b6fa6e8"
  integrity sha512-lNZ3EEggsGY78JavgbHsK9u5P3pQaW7k4axlgFLYkMd7UBsiNahCITShLjNQschPyjtO6dADrL24757IdhBrsQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.16.7.tgz#be23c0ba74deec1922e639832904be0bea73cdea"
  integrity sha512-K3XzyZJGQCr00+EtYtrDjmwX7o7PLK6U9bi1nCwkQioRFVUv6dJoxbQjtWVtP+bCPy82bONBKG8NPyQ4+i6yjg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.16.7.tgz#141fc20b6857e59459d430c850a0011e36561d99"
  integrity sha512-aUOrYU3EVtjf62jQrCj63pYZ7k6vns2h/DQvHPWGmsJRYzWXZ6/AsfgpiRy6XiuIDADhJzP2Q9MwSMKauBQ+UQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.16.7.tgz#d6b69f4af63fb38b6ca2558442a7fb191236eba9"
  integrity sha512-vQgPMknOIgiuVqbokToyXbkY/OmmjAzr/0lhSIbG/KmnzXPGwW/AdhdKpi+O4X/VkWiWjnkKOBiqJrTaC98VKw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.16.7":
  version "7.17.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.17.3.tgz#d9eb649a54628a51701aef7e0ea3d17e2b9dd390"
  integrity sha512-yuL5iQA/TbZn+RGAfxQXfi7CNLmKi1f8zInn4IgobuCWcAb7i+zj4TYzQ9l8cEzVyJ89PDGuqxK1xZpUDISesw==
  dependencies:
    "@babel/compat-data" "^7.17.0"
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.16.7"

"@babel/plugin-proposal-optional-catch-binding@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.16.7.tgz#c623a430674ffc4ab732fd0a0ae7722b67cb74cf"
  integrity sha512-eMOH/L4OvWSZAE1VkHbr1vckLG1WUcHGJSLqqQwl2GaUqG6QjddvrOaTUMNYiv77H5IKPMZ9U9P7EaHwvAShfA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.16.7.tgz#7cd629564724816c0e8a969535551f943c64c39a"
  integrity sha512-eC3xy+ZrUcBtP7x+sq62Q/HYd674pPTb/77XZMb5wbDPGWIdUbSr4Agr052+zaUPSb+gGRnjxXfKFvx5iMJ+DA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.16.7":
  version "7.16.11"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.16.11.tgz#e8df108288555ff259f4527dbe84813aac3a1c50"
  integrity sha512-F/2uAkPlXDr8+BHpZvo19w3hLFKge+k75XUprE6jaqKxjGkSYcK+4c+bup5PdW/7W/Rpjwql7FTVEDW+fRAQsw==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.10"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-proposal-private-property-in-object@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.16.7.tgz#b0b8cef543c2c3d57e59e2c611994861d46a3fce"
  integrity sha512-rMQkjcOFbm+ufe3bTZLyOfsOUOxyvLXZJCTARhJr+8UMSoZmqTe1K1BgkFcrW37rAchWg57yI69ORxiWvUINuQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.16.7", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.16.7.tgz#635d18eb10c6214210ffc5ff4932552de08188a2"
  integrity sha512-QRK0YI/40VLhNVGIjRNAAQkEHws0cswSdFFjpFyt943YmJIU1da9uW63Iu6NFV6CxTZW5eTDCrwZUstBWgp/Rg==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz#a983fb1aeb2ec3f6ed042a210f640e90e786fe0d"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  version "7.12.13"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz#b5c987274c4a3a82b89714796931a6b53544ae10"
  integrity sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz#195df89b146b4b78b3bf897fd7a257c84659d406"
  integrity sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-decorators@^7.16.7":
  version "7.17.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.17.0.tgz#a2be3b2c9fe7d78bd4994e790896bc411e2f166d"
  integrity sha512-qWe85yCXsvDEluNP0OyeQjH63DlhAR3W7K9BxxU1MvbDb48tgBG+Ao6IJJ6smPDrrVzSQZrbF6donpkFBMcs3A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz#62bf98b2da3cd21d626154fc96ee5b3cb68eacb3"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz#028964a9ba80dbc094c915c487ad7c4e7a66465a"
  integrity sha1-AolkqbqA28CUyRXEh618TnpmRlo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz#01ca21b668cd8218c9e640cb6dd88c5412b2c96a"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.16.7.tgz#50b6571d13f764266a113d77c82b4a6508bbe665"
  integrity sha512-Esxmk7YjA8QysKeT3VhTXvF6y77f/a91SIs4pWb4H2eWGQkCKFgQaG6hdoEVZtGsrAcb2K5BW66XsOErD4WU3Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz#ca91ef46303530448b906652bac2e9fe9941f699"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz#167ed70368886081f74b5c36c65a88c03b66d1a9"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz#b9b070b3e33570cd9fd07ba7fa91c0dd37b9af97"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz#60e225edcbd98a640332a2e72dd3e66f1af55871"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz#6111a265bcfb020eb9efd0fdfd7d26402b9ed6c1"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz#4f69c2ab95167e0180cd5336613f8c5788f7d48a"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz#0dc6671ec0ea22b6e94a1114f857970cd39de1ad"
  integrity sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  version "7.14.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz#c1cfdadc35a646240001f06138247b741c34d94c"
  integrity sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.16.7.tgz#39c9b55ee153151990fb038651d58d3fd03f98f8"
  integrity sha512-YhUIJHHGkqPgEcMYkPCKTyGUdoGKWtopIycQyjJH8OjvRgOYsXsaKehLVPScKJWAULPxMa4N1vCe6szREFlZ7A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-arrow-functions@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.16.7.tgz#44125e653d94b98db76369de9c396dc14bef4154"
  integrity sha512-9ffkFFMbvzTvv+7dTp/66xvZAWASuPD5Tl9LK3Z9vhOmANo6j94rik+5YMBt4CwHVMWLWpMsriIc2zsa3WW3xQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-async-to-generator@^7.16.7":
  version "7.16.8"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.16.8.tgz#b83dff4b970cf41f1b819f8b49cc0cfbaa53a808"
  integrity sha512-MtmUmTJQHCnyJVrScNzNlofQJ3dLFuobYn3mwOTKHnSCMtbNsqvF71GQmJfFjdrXSsAA7iysFmYWw4bXZ20hOg==
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-remap-async-to-generator" "^7.16.8"

"@babel/plugin-transform-block-scoped-functions@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.16.7.tgz#4d0d57d9632ef6062cdf354bb717102ee042a620"
  integrity sha512-JUuzlzmF40Z9cXyytcbZEZKckgrQzChbQJw/5PuEHYeqzCsvebDx0K0jWnIIVcmmDOAVctCgnYs0pMcrYj2zJg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-block-scoping@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.16.7.tgz#f50664ab99ddeaee5bc681b8f3a6ea9d72ab4f87"
  integrity sha512-ObZev2nxVAYA4bhyusELdo9hb3H+A56bxH3FZMbEImZFiEDYVHXQSJ1hQKFlDnlt8G9bBrCZ5ZpURZUrV4G5qQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-classes@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-classes/-/plugin-transform-classes-7.16.7.tgz#8f4b9562850cd973de3b498f1218796eb181ce00"
  integrity sha512-WY7og38SFAGYRe64BrjKf8OrE6ulEHtr5jEYaZMwox9KebgqPi67Zqz8K53EKk1fFEJgm96r32rkKZ3qA2nCWQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.16.7.tgz#66dee12e46f61d2aae7a73710f591eb3df616470"
  integrity sha512-gN72G9bcmenVILj//sv1zLNaPyYcOzUho2lIJBMh/iakJ9ygCo/hEF9cpGb61SCMEDxbbyBoVQxrt+bWKu5KGw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-destructuring@^7.16.7":
  version "7.17.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.17.3.tgz#c445f75819641788a27a0a3a759d9df911df6abc"
  integrity sha512-dDFzegDYKlPqa72xIlbmSkly5MluLoaC1JswABGktyt6NTXSBcUuse/kWE/wvKFWJHPETpi158qJZFS3JmykJg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-dotall-regex@^7.16.7", "@babel/plugin-transform-dotall-regex@^7.4.4":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.16.7.tgz#6b2d67686fab15fb6a7fd4bd895d5982cfc81241"
  integrity sha512-Lyttaao2SjZF6Pf4vk1dVKv8YypMpomAbygW+mU5cYP3S5cWTfCJjG8xV6CFdzGFlfWK81IjL9viiTvpb6G7gQ==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-duplicate-keys@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.16.7.tgz#2207e9ca8f82a0d36a5a67b6536e7ef8b08823c9"
  integrity sha512-03DvpbRfvWIXyK0/6QiR1KMTWeT6OcQ7tbhjrXyFS02kjuX/mu5Bvnh5SDSWHxyawit2g5aWhKwI86EE7GUnTw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-exponentiation-operator@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.16.7.tgz#efa9862ef97e9e9e5f653f6ddc7b665e8536fe9b"
  integrity sha512-8UYLSlyLgRixQvlYH3J2ekXFHDFLQutdy7FfFAMm3CPZ6q9wHCwnUyiXpQCe3gVVnQlHc5nsuiEVziteRNTXEA==
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-for-of@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.16.7.tgz#649d639d4617dff502a9a158c479b3b556728d8c"
  integrity sha512-/QZm9W92Ptpw7sjI9Nx1mbcsWz33+l8kuMIQnDwgQBG5s3fAfQvkRjQ7NqXhtNcKOnPkdICmUHyCaWW06HCsqg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-function-name@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.16.7.tgz#5ab34375c64d61d083d7d2f05c38d90b97ec65cf"
  integrity sha512-SU/C68YVwTRxqWj5kgsbKINakGag0KTgq9f2iZEXdStoAbOzLHEBRYzImmA6yFo8YZhJVflvXmIHUO7GWHmxxA==
  dependencies:
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-literals@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-literals/-/plugin-transform-literals-7.16.7.tgz#254c9618c5ff749e87cb0c0cef1a0a050c0bdab1"
  integrity sha512-6tH8RTpTWI0s2sV6uq3e/C9wPo4PTqqZps4uF0kzQ9/xPLFQtipynvmT1g/dOfEJ+0EQsHhkQ/zyRId8J2b8zQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-member-expression-literals@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.16.7.tgz#6e5dcf906ef8a098e630149d14c867dd28f92384"
  integrity sha512-mBruRMbktKQwbxaJof32LT9KLy2f3gH+27a5XSuXo6h7R3vqltl0PgZ80C8ZMKw98Bf8bqt6BEVi3svOh2PzMw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-modules-amd@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.16.7.tgz#b28d323016a7daaae8609781d1f8c9da42b13186"
  integrity sha512-KaaEtgBL7FKYwjJ/teH63oAmE3lP34N3kshz8mm4VMAw7U3PxjVwwUmxEFksbgsNUaO3wId9R2AVQYSEGRa2+g==
  dependencies:
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.16.7":
  version "7.16.8"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.16.8.tgz#cdee19aae887b16b9d331009aa9a219af7c86afe"
  integrity sha512-oflKPvsLT2+uKQopesJt3ApiaIS2HW+hzHFcwRNtyDGieAeC/dIHZX8buJQ2J2X1rxGPy4eRcUijm3qcSPjYcA==
  dependencies:
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-simple-access" "^7.16.7"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.16.7.tgz#887cefaef88e684d29558c2b13ee0563e287c2d7"
  integrity sha512-DuK5E3k+QQmnOqBR9UkusByy5WZWGRxfzV529s9nPra1GE7olmxfqO2FHobEOYSPIjPBTr4p66YDcjQnt8cBmw==
  dependencies:
    "@babel/helper-hoist-variables" "^7.16.7"
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-identifier" "^7.16.7"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.16.7.tgz#23dad479fa585283dbd22215bff12719171e7618"
  integrity sha512-EMh7uolsC8O4xhudF2F6wedbSHm1HHZ0C6aJ7K67zcDNidMzVcxWdGr+htW9n21klm+bOn+Rx4CBsAntZd3rEQ==
  dependencies:
    "@babel/helper-module-transforms" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-named-capturing-groups-regex@^7.16.7":
  version "7.16.8"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.16.8.tgz#7f860e0e40d844a02c9dcf9d84965e7dfd666252"
  integrity sha512-j3Jw+n5PvpmhRR+mrgIh04puSANCk/T/UA3m3P1MjJkhlK906+ApHhDIqBQDdOgL/r1UYpz4GNclTXxyZrYGSw==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"

"@babel/plugin-transform-new-target@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.16.7.tgz#9967d89a5c243818e0800fdad89db22c5f514244"
  integrity sha512-xiLDzWNMfKoGOpc6t3U+etCE2yRnn3SM09BXqWPIZOBpL2gvVrBWUKnsJx0K/ADi5F5YC5f8APFfWrz25TdlGg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-object-super@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.16.7.tgz#ac359cf8d32cf4354d27a46867999490b6c32a94"
  integrity sha512-14J1feiQVWaGvRxj2WjyMuXS2jsBkgB3MdSN5HuC2G5nRspa5RK9COcs82Pwy5BuGcjb+fYaUj94mYcOj7rCvw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"

"@babel/plugin-transform-parameters@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.16.7.tgz#a1721f55b99b736511cb7e0152f61f17688f331f"
  integrity sha512-AT3MufQ7zZEhU2hwOA11axBnExW0Lszu4RL/tAlUJBuNoRak+wehQW8h6KcXOcgjY42fHtDxswuMhMjFEuv/aw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-property-literals@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.16.7.tgz#2dadac85155436f22c696c4827730e0fe1057a55"
  integrity sha512-z4FGr9NMGdoIl1RqavCqGG+ZuYjfZ/hkCIeuH6Do7tXmSm0ls11nYVSJqFEUOSJbDab5wC6lRE/w6YjVcr6Hqw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-react-display-name@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.16.7.tgz#7b6d40d232f4c0f550ea348593db3b21e2404340"
  integrity sha512-qgIg8BcZgd0G/Cz916D5+9kqX0c7nPZyXaP8R2tLNN5tkyIZdG5fEwBrxwplzSnjC1jvQmyMNVwUCZPcbGY7Pg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-react-jsx-development@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.16.7.tgz#43a00724a3ed2557ed3f276a01a929e6686ac7b8"
  integrity sha512-RMvQWvpla+xy6MlBpPlrKZCMRs2AGiHOGHY3xRwl0pEeim348dDyxeH4xBsMPbIMhujeq7ihE702eM2Ew0Wo+A==
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.16.7"

"@babel/plugin-transform-react-jsx@^7.16.7":
  version "7.17.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.17.3.tgz#eac1565da176ccb1a715dae0b4609858808008c1"
  integrity sha512-9tjBm4O07f7mzKSIlEmPdiE6ub7kfIe6Cd+w+oQebpATfTQMAgW+YOuWxogbKVTulA+MEO7byMeIUtQ1z+z+ZQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-jsx" "^7.16.7"
    "@babel/types" "^7.17.0"

"@babel/plugin-transform-react-pure-annotations@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.16.7.tgz#232bfd2f12eb551d6d7d01d13fe3f86b45eb9c67"
  integrity sha512-hs71ToC97k3QWxswh2ElzMFABXHvGiJ01IB1TbYQDGeWRKWz/MPUTh5jGExdHvosYKpnJW5Pm3S4+TA3FyX+GA==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-regenerator@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.16.7.tgz#9e7576dc476cb89ccc5096fff7af659243b4adeb"
  integrity sha512-mF7jOgGYCkSJagJ6XCujSQg+6xC1M77/03K2oBmVJWoFGNUtnVJO4WHKJk3dnPC8HCcj4xBQP1Egm8DWh3Pb3Q==
  dependencies:
    regenerator-transform "^0.14.2"

"@babel/plugin-transform-reserved-words@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.16.7.tgz#1d798e078f7c5958eec952059c460b220a63f586"
  integrity sha512-KQzzDnZ9hWQBjwi5lpY5v9shmm6IVG0U9pB18zvMu2i4H90xpT4gmqwPYsn8rObiadYe2M0gmgsiOIF5A/2rtg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-shorthand-properties@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.16.7.tgz#e8549ae4afcf8382f711794c0c7b6b934c5fbd2a"
  integrity sha512-hah2+FEnoRoATdIb05IOXf+4GzXYTq75TVhIn1PewihbpyrNWUt2JbudKQOETWw6QpLe+AIUpJ5MVLYTQbeeUg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-spread@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-spread/-/plugin-transform-spread-7.16.7.tgz#a303e2122f9f12e0105daeedd0f30fb197d8ff44"
  integrity sha512-+pjJpgAngb53L0iaA5gU/1MLXJIfXcYepLgXB3esVRf4fqmj8f2cxM3/FKaHsZms08hFQJkFccEWuIpm429TXg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"

"@babel/plugin-transform-sticky-regex@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.16.7.tgz#c84741d4f4a38072b9a1e2e3fd56d359552e8660"
  integrity sha512-NJa0Bd/87QV5NZZzTuZG5BPJjLYadeSZ9fO6oOUoL4iQx+9EEuw/eEM92SrsT19Yc2jgB1u1hsjqDtH02c3Drw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-template-literals@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.16.7.tgz#f3d1c45d28967c8e80f53666fc9c3e50618217ab"
  integrity sha512-VwbkDDUeenlIjmfNeDX/V0aWrQH2QiVyJtwymVQSzItFDTpxfyJh3EVaQiS0rIN/CqbLGr0VcGmuwyTdZtdIsA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-typeof-symbol@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.16.7.tgz#9cdbe622582c21368bd482b660ba87d5545d4f7e"
  integrity sha512-p2rOixCKRJzpg9JB4gjnG4gjWkWa89ZoYUnl9snJ1cWIcTH/hvxZqfO+WjG6T8DRBpctEol5jw1O5rA8gkCokQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-typescript@^7.16.7":
  version "7.16.8"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.16.8.tgz#591ce9b6b83504903fa9dd3652c357c2ba7a1ee0"
  integrity sha512-bHdQ9k7YpBDO2d0NVfkj51DpQcvwIzIusJ7mEUaMlbZq3Kt/U47j24inXZHQ5MDiYpCs+oZiwnXyKedE8+q7AQ==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-typescript" "^7.16.7"

"@babel/plugin-transform-unicode-escapes@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.16.7.tgz#da8717de7b3287a2c6d659750c964f302b31ece3"
  integrity sha512-TAV5IGahIz3yZ9/Hfv35TV2xEm+kaBDaZQCn2S/hG9/CZ0DktxJv9eKfPc7yYCvOYR4JGx1h8C+jcSOvgaaI/Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-unicode-regex@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.16.7.tgz#0f7aa4a501198976e25e82702574c34cfebe9ef2"
  integrity sha512-oC5tYYKw56HO75KZVLQ+R/Nl3Hro9kf8iG0hXoaHP7tjAyCpvqBiSNe6vGrZni1Z6MggmUOC6A7VP7AVmw225Q==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/preset-env@7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/preset-env/-/preset-env-7.16.7.tgz#c491088856d0b3177822a2bf06cb74d76327aa56"
  integrity sha512-urX3Cee4aOZbRWOSa3mKPk0aqDikfILuo+C7qq7HY0InylGNZ1fekq9jmlr3pLWwZHF4yD7heQooc2Pow2KMyQ==
  dependencies:
    "@babel/compat-data" "^7.16.4"
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-option" "^7.16.7"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.16.7"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.16.7"
    "@babel/plugin-proposal-async-generator-functions" "^7.16.7"
    "@babel/plugin-proposal-class-properties" "^7.16.7"
    "@babel/plugin-proposal-class-static-block" "^7.16.7"
    "@babel/plugin-proposal-dynamic-import" "^7.16.7"
    "@babel/plugin-proposal-export-namespace-from" "^7.16.7"
    "@babel/plugin-proposal-json-strings" "^7.16.7"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.16.7"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.16.7"
    "@babel/plugin-proposal-numeric-separator" "^7.16.7"
    "@babel/plugin-proposal-object-rest-spread" "^7.16.7"
    "@babel/plugin-proposal-optional-catch-binding" "^7.16.7"
    "@babel/plugin-proposal-optional-chaining" "^7.16.7"
    "@babel/plugin-proposal-private-methods" "^7.16.7"
    "@babel/plugin-proposal-private-property-in-object" "^7.16.7"
    "@babel/plugin-proposal-unicode-property-regex" "^7.16.7"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-transform-arrow-functions" "^7.16.7"
    "@babel/plugin-transform-async-to-generator" "^7.16.7"
    "@babel/plugin-transform-block-scoped-functions" "^7.16.7"
    "@babel/plugin-transform-block-scoping" "^7.16.7"
    "@babel/plugin-transform-classes" "^7.16.7"
    "@babel/plugin-transform-computed-properties" "^7.16.7"
    "@babel/plugin-transform-destructuring" "^7.16.7"
    "@babel/plugin-transform-dotall-regex" "^7.16.7"
    "@babel/plugin-transform-duplicate-keys" "^7.16.7"
    "@babel/plugin-transform-exponentiation-operator" "^7.16.7"
    "@babel/plugin-transform-for-of" "^7.16.7"
    "@babel/plugin-transform-function-name" "^7.16.7"
    "@babel/plugin-transform-literals" "^7.16.7"
    "@babel/plugin-transform-member-expression-literals" "^7.16.7"
    "@babel/plugin-transform-modules-amd" "^7.16.7"
    "@babel/plugin-transform-modules-commonjs" "^7.16.7"
    "@babel/plugin-transform-modules-systemjs" "^7.16.7"
    "@babel/plugin-transform-modules-umd" "^7.16.7"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.16.7"
    "@babel/plugin-transform-new-target" "^7.16.7"
    "@babel/plugin-transform-object-super" "^7.16.7"
    "@babel/plugin-transform-parameters" "^7.16.7"
    "@babel/plugin-transform-property-literals" "^7.16.7"
    "@babel/plugin-transform-regenerator" "^7.16.7"
    "@babel/plugin-transform-reserved-words" "^7.16.7"
    "@babel/plugin-transform-shorthand-properties" "^7.16.7"
    "@babel/plugin-transform-spread" "^7.16.7"
    "@babel/plugin-transform-sticky-regex" "^7.16.7"
    "@babel/plugin-transform-template-literals" "^7.16.7"
    "@babel/plugin-transform-typeof-symbol" "^7.16.7"
    "@babel/plugin-transform-unicode-escapes" "^7.16.7"
    "@babel/plugin-transform-unicode-regex" "^7.16.7"
    "@babel/preset-modules" "^0.1.5"
    "@babel/types" "^7.16.7"
    babel-plugin-polyfill-corejs2 "^0.3.0"
    babel-plugin-polyfill-corejs3 "^0.4.0"
    babel-plugin-polyfill-regenerator "^0.3.0"
    core-js-compat "^3.19.1"
    semver "^6.3.0"

"@babel/preset-modules@^0.1.5":
  version "0.1.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/preset-modules/-/preset-modules-0.1.5.tgz#ef939d6e7f268827e1841638dc6ff95515e115d9"
  integrity sha512-A57th6YRG7oR3cq/yt/Y84MvGgE0eJG2F1JLhKuyG+jFxEgrd/HAMJatiFtmOiZurz+0DkrvbheCLaV5f2JfjA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/preset-react@7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/preset-react/-/preset-react-7.16.7.tgz#4c18150491edc69c183ff818f9f2aecbe5d93852"
  integrity sha512-fWpyI8UM/HE6DfPBzD8LnhQ/OcH8AgTaqcqP2nGOXEUV+VKBR5JRN9hCk9ai+zQQ57vtm9oWeXguBCPNUjytgA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-option" "^7.16.7"
    "@babel/plugin-transform-react-display-name" "^7.16.7"
    "@babel/plugin-transform-react-jsx" "^7.16.7"
    "@babel/plugin-transform-react-jsx-development" "^7.16.7"
    "@babel/plugin-transform-react-pure-annotations" "^7.16.7"

"@babel/preset-typescript@7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/preset-typescript/-/preset-typescript-7.16.7.tgz#ab114d68bb2020afc069cd51b37ff98a046a70b9"
  integrity sha512-WbVEmgXdIyvzB77AQjGBEyYPZx+8tTsO50XtfozQrkW8QB2rLJpH2lgx0TRw5EJrBxOZQ+wCcyPVQvS8tjEHpQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-validator-option" "^7.16.7"
    "@babel/plugin-transform-typescript" "^7.16.7"

"@babel/runtime@7.0.0":
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/runtime/-/runtime-7.0.0.tgz#adeb78fedfc855aa05bc041640f3f6f98e85424c"
  integrity sha512-7hGhzlcmg01CvH1EHdSPVXYX1aJ8KCEyz6I9xYIi/asDtzBPMyMhVibhM/K6g/5qnKBwjZtp10bNZIEFTRW1MA==
  dependencies:
    regenerator-runtime "^0.12.0"

"@babel/runtime@^7.1.2", "@babel/runtime@^7.2.0", "@babel/runtime@^7.3.1":
  version "7.9.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/runtime/-/runtime-7.9.2.tgz#d90df0583a3a252f09aaa619665367bae518db06"
  integrity sha512-NE2DtOdufG7R5vnfQUTehdTfNycfUANEtCa9PssN9O/xmTzP4E08UI797ixaei6hBEVL9BI/PsdJS5x7mWoB9Q==
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/runtime@^7.12.0", "@babel/runtime@^7.8.4":
  version "7.17.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/runtime/-/runtime-7.17.2.tgz#66f68591605e59da47523c631416b18508779941"
  integrity sha512-hzeyJyMA1YGdJTuWU0e/j4wKXrU4OMFvY2MSlaI9B7VQb0r5cxTE3EAIS2Q7Tn2RIcDkRvTA/v2JsAEhxe99uw==
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/template@^7.16.7":
  version "7.16.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/template/-/template-7.16.7.tgz#8d126c8701fde4d66b264b3eba3d96f07666d155"
  integrity sha512-I8j/x8kHUrbYRTUxXrrMbfCa7jxkE7tZre39x3kjr9hvI82cK1FfqLygotcWN5kdPGWcLdWMHpSBavse5tWw3w==
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "@babel/parser" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/traverse@^7.13.0", "@babel/traverse@^7.16.7", "@babel/traverse@^7.16.8", "@babel/traverse@^7.17.0", "@babel/traverse@^7.17.3":
  version "7.17.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/traverse/-/traverse-7.17.3.tgz#0ae0f15b27d9a92ba1f2263358ea7c4e7db47b57"
  integrity sha512-5irClVky7TxRWIRtxlh2WPUUOLhcPN06AGgaQSB8AEwuyEBgJVuJ5imdHm5zxk8w0QS5T+tDfnDxAlhWjpb7cw==
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "@babel/generator" "^7.17.3"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-hoist-variables" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    "@babel/parser" "^7.17.3"
    "@babel/types" "^7.17.0"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@^7.16.0", "@babel/types@^7.16.7", "@babel/types@^7.16.8", "@babel/types@^7.17.0":
  version "7.23.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/types/-/types-7.23.9.tgz#1dd7b59a9a2b5c87f8b41e52770b5ecbf492e002"
  integrity sha1-Hde1mporXIf4tB5Sdwtey/SS4AI=
  dependencies:
    "@babel/helper-string-parser" "^7.23.4"
    "@babel/helper-validator-identifier" "^7.22.20"
    to-fast-properties "^2.0.0"

"@babel/types@^7.4.4":
  version "7.15.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@babel/types/-/types-7.15.0.tgz#61af11f2286c4e9c69ca8deb5f4375a73c72dcbd"
  integrity sha512-OBvfqnllOIdX4ojTHpwZbpvz4j3EWyjkZEdmjH0/cgsd6QOdSgU8rLSk6ard/pcW7rlmjdVSX/AWOaORR1uNOQ==
  dependencies:
    "@babel/helper-validator-identifier" "^7.14.9"
    to-fast-properties "^2.0.0"

"@discoveryjs/json-ext@^0.5.0":
  version "0.5.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@discoveryjs/json-ext/-/json-ext-0.5.6.tgz#d5e0706cf8c6acd8c6032f8d54070af261bbbb2f"
  integrity sha512-ws57AidsDvREKrZKYffXddNkyaF14iHNHm8VQnZH6t99E8gczjNN0GpvcGny0imC80yQ0tHz1xVUKk/KFQSUyA==

"@floating-ui/core@^1.0.0":
  version "1.6.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@floating-ui/core/-/core-1.6.1.tgz#a4e6fef1b069cda533cbc7a4998c083a37f37573"
  integrity sha1-pOb+8bBpzaUzy8ekmYwIOjfzdXM=
  dependencies:
    "@floating-ui/utils" "^0.2.0"

"@floating-ui/dom@^1.0.0":
  version "1.6.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@floating-ui/dom/-/dom-1.6.5.tgz#323f065c003f1d3ecf0ff16d2c2c4d38979f4cb9"
  integrity sha1-Mj8GXAA/HT7PD/FtLCxNOJefTLk=
  dependencies:
    "@floating-ui/core" "^1.0.0"
    "@floating-ui/utils" "^0.2.0"

"@floating-ui/utils@^0.2.0":
  version "0.2.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@floating-ui/utils/-/utils-0.2.2.tgz#d8bae93ac8b815b2bd7a98078cf91e2724ef11e5"
  integrity sha1-2LrpOsi4FbK9epgHjPkeJyTvEeU=

"@interactjs/actions@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/actions/-/actions-1.9.9.tgz#1eb958dc37a060cd6ca959b6afc64fefd71988d0"
  integrity sha512-+mb/bTscD1CGjqekQJ6Yf7A2q8K3/JzMfub4HGzi8Ri0cc62iUroScQo/psPae2icQ0gy5nHEiCu2db4EMXxPA==
  optionalDependencies:
    "@interactjs/interact" "1.9.9"

"@interactjs/arrange@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/arrange/-/arrange-1.9.9.tgz#1179a85f094fe01fb1f45ab04c1e2e4fc4216737"
  integrity sha512-vVgbOxG1uqzzvw1Sf4eDx/ln63mCyi/Xk0BAzKWGwxLMy5VeGzvJhS6L+EKgI4gNf0vSvkUCjz1VGw6nTprR1g==

"@interactjs/auto-scroll@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/auto-scroll/-/auto-scroll-1.9.9.tgz#37f3fcd0db9024c95b33d572789d0eeff05e91da"
  integrity sha512-/uoQumGmnwKwOLzrH3tGfLpGax9S1zrxImkiKK+Dl/ItStZoHeXP9H4TzvdC1jiZUxMMsJUMMzky12RsBqF13g==
  optionalDependencies:
    "@interactjs/interact" "1.9.9"

"@interactjs/auto-start@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/auto-start/-/auto-start-1.9.9.tgz#c7aec6a0b3d7949c5574a49d5f8ca162fea53809"
  integrity sha512-g2ubcTJy9DAIzhe6fyRyJETJnQTPm3ibx+YzBVPqpcXx1F7nqQIVokrv54muXhCgEzZtQB7Se6gp5dAVl0DhkA==
  optionalDependencies:
    "@interactjs/interact" "1.9.9"

"@interactjs/clone@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/clone/-/clone-1.9.9.tgz#1f6d708612948b02b97f37ff398686acb46e2365"
  integrity sha512-Qoeo4jUl0OEPth8aEMMlugByekqrKJwl9sBItvIpQba8m3+6z5/2/ILXJop55Z1EZFAN2uFP/sNqFHVawdh1Ww==

"@interactjs/components@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/components/-/components-1.9.9.tgz#e679734dd759d2bf5a1af52e53006c16716b01df"
  integrity sha512-Oo+u0Siega9ke6OID/YgIBaNRPN+TBZ/QmfD8HqohrDeMssg5mwDShCpE608K0WLFRp3JODzkl0puYF/g63e4w==

"@interactjs/core@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/core/-/core-1.9.9.tgz#bf2ab0961291f6ff4200bd17d7303b76011700e0"
  integrity sha512-Jj6AWcIJpGvUkOjGmwo9rdYGoh60OlKlcpLwMnAC1/E6/PCzBgXkHPB4BNYZwRrxJH8QY4TU/vCtZFMZSydxyg==

"@interactjs/dev-tools@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/dev-tools/-/dev-tools-1.9.9.tgz#a160187150265a56ac558086644112336418d7d9"
  integrity sha512-h8KNxFFRs1MR/GaOMs/R6WqaUUdnCOatEvflRSHIoH2PdDDnu7881v3q6vy/jGeBFU8tZ/0ASxQImiRMJx/tVQ==
  dependencies:
    "@interactjs/utils" "1.9.9"
  optionalDependencies:
    "@interactjs/interact" "1.9.9"

"@interactjs/feedback@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/feedback/-/feedback-1.9.9.tgz#58c1bc82036d780b9953b5d5fd9f91f140e791c0"
  integrity sha512-mHgtv3OZg87xImUKJqqDBJZ/tYHjrn71N0glqa2jZpnExtr63g43iWIqIvwJpk775M883WROIGDc+X/4tsVP6Q==

"@interactjs/inertia@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/inertia/-/inertia-1.9.9.tgz#aa07cd17c8c6bef25ddcad9fb4c8a87fa0ec2be5"
  integrity sha512-rVkZc52gpRG+jgD8xV1WCw5Ktjxbz7PXe49dAjxbocm+6edpjD0Zp7oKeC/IFq+qE5xdgU9BNNe/6yLEdB74KA==
  dependencies:
    "@interactjs/offset" "1.9.9"
  optionalDependencies:
    "@interactjs/interact" "1.9.9"

"@interactjs/interact@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/interact/-/interact-1.9.9.tgz#028c2eaf5580fcdf30abc58e28dd9a89023f3376"
  integrity sha512-ntAOE5GL+yaICPkRFmS2p6+7dcydOp42OyRK2Hj+td1DCmo7+dWVJ7sowzcMOCAQQ6bQpk50kaww19jQx1PJcg==
  dependencies:
    "@interactjs/core" "1.9.9"
    "@interactjs/types" "1.9.9"
    "@interactjs/utils" "1.9.9"

"@interactjs/interactjs@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/interactjs/-/interactjs-1.9.9.tgz#e5459a1f81f7cbecb6a774e7948b2b69c325cfa0"
  integrity sha512-fQdy6RM2IU1RCZ9XvnEwOmNoRiiIeenXRFu2lgncmL4lfsMP7gbhA1PI0KX2CO1fOVcwbB6E3YHHzgbsFpNcNQ==
  dependencies:
    "@interactjs/actions" "1.9.9"
    "@interactjs/arrange" "1.9.9"
    "@interactjs/auto-scroll" "1.9.9"
    "@interactjs/auto-start" "1.9.9"
    "@interactjs/clone" "1.9.9"
    "@interactjs/components" "1.9.9"
    "@interactjs/core" "1.9.9"
    "@interactjs/dev-tools" "1.9.9"
    "@interactjs/feedback" "1.9.9"
    "@interactjs/inertia" "1.9.9"
    "@interactjs/interact" "1.9.9"
    "@interactjs/modifiers" "1.9.9"
    "@interactjs/multi-target" "1.9.9"
    "@interactjs/offset" "1.9.9"
    "@interactjs/pointer-events" "1.9.9"
    "@interactjs/reflow" "1.9.9"
    "@interactjs/types" "1.9.9"
    "@interactjs/utils" "1.9.9"
    "@interactjs/vue" "1.9.9"

"@interactjs/modifiers@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/modifiers/-/modifiers-1.9.9.tgz#c8828cef7c7d8b9687b8f24005fbe45b7236fa3d"
  integrity sha512-xauU9zjAAZoPWa6vcQ96vFbh39uwT7ZxLZVAiyPAW7bSQRztK/GxUtt2+gwUOrqSKT2Av++9p4BWOlOk5lZxRg==
  dependencies:
    "@interactjs/snappers" "1.9.9"
  optionalDependencies:
    "@interactjs/interact" "1.9.9"

"@interactjs/multi-target@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/multi-target/-/multi-target-1.9.9.tgz#516d097e2268c6cb1d7c36ed8e0b5856c81c52ed"
  integrity sha512-BJk5AwIgFN5jOm85LtvucxcIBBpkS8i9ndS4j2td4O0u0rzhXDTVN3XxQnc4rxV9FZfgmZdI4dnyDEnXTFYFsA==

"@interactjs/offset@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/offset/-/offset-1.9.9.tgz#01cb8b0bcc24ffceb0b9d41d1215e86987a5e330"
  integrity sha512-nMmxzUqmv3InsczdAO6YI5BLUMyc/Gz/41MstJHslLsHjfIVbPUD9ZJbQElcvrnruUoqhXNY/FpRUSyCRqctiQ==
  optionalDependencies:
    "@interactjs/interact" "1.9.9"

"@interactjs/pointer-events@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/pointer-events/-/pointer-events-1.9.9.tgz#38c33d8549a76c0d32f803c38ff1208d55dd5194"
  integrity sha512-MtwD4fDQPveofsagav4L4kLj63ZXPCVpPPd0kauD+keMwfm+6z3e80I2KyWqEbabblkH3XKrjEZNGQaPclflCw==
  optionalDependencies:
    "@interactjs/interact" "1.9.9"

"@interactjs/reflow@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/reflow/-/reflow-1.9.9.tgz#858d4169cf8b4b4ab7b5c9256f76367e584226a7"
  integrity sha512-Mzixx81kgfveILkhqCkHmSNDvCgg5DKLlVJpTwhh+LfxyWlKSt8bUk9CHYvn09LDsZiodXEimKqyy8QwYcZCVg==
  optionalDependencies:
    "@interactjs/interact" "1.9.9"

"@interactjs/snappers@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/snappers/-/snappers-1.9.9.tgz#0326f4821d90cb5a7965a4bcbf3de241f3bc57f2"
  integrity sha512-7Gcp0VKDTE1W4B4gayOeaUppMgRlLReku9/SBPW+GWTqIPsgHu2ndAAsdKYSksO/v9u+v+Jk0Fm90Pe9zGclzg==

"@interactjs/symbol-tree@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/symbol-tree/-/symbol-tree-1.9.9.tgz#537dfbe2054bbeef4c1586105c65ee60087f8dc3"
  integrity sha512-esbSprGGuaNLz8OwQKxr0sDSAo14ic0A7eHolODjvdhARREJiZYGLPYNb9h3yZ8TSNLk1Z4RsONxnJwN965irw==

"@interactjs/types@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/types/-/types-1.9.9.tgz#f870dd1d95aeeeb26a1a2b2a45c2ced0135ceebf"
  integrity sha512-adouBgB8L9RD5HE0efLeYnY7RksXXaHGL3wLZstnDCPIDKpg8PXj6wbR2TovFUP51MlNMmlGFua0xRYIA0tz2g==
  dependencies:
    "@interactjs/actions" "1.9.9"
    "@interactjs/arrange" "1.9.9"
    "@interactjs/auto-scroll" "1.9.9"
    "@interactjs/auto-start" "1.9.9"
    "@interactjs/core" "1.9.9"
    "@interactjs/dev-tools" "1.9.9"
    "@interactjs/inertia" "1.9.9"
    "@interactjs/modifiers" "1.9.9"
    "@interactjs/pointer-events" "1.9.9"
    "@interactjs/reflow" "1.9.9"
    "@interactjs/snappers" "1.9.9"
    "@interactjs/symbol-tree" "1.9.9"
    "@interactjs/utils" "1.9.9"

"@interactjs/utils@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/utils/-/utils-1.9.9.tgz#d88e80b29e19686cab64fe1141a20f3b570d5a67"
  integrity sha512-GegdM3iJHjtH74eNQmZD3lgMe2dl0iTML5tV+Ti8Z6L0AzBtngJXLJ5o/+TjRmqrVeEnvLWwqMvAat74AqjL5g==

"@interactjs/vue@1.9.9":
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@interactjs/vue/-/vue-1.9.9.tgz#f4fa749e4f74b69ee05fb58c3cadc7a973f87bc1"
  integrity sha512-UgZJOWfG5dXNMgkPI/j9FECpUADLSOoIX856GLVH2gxLwKcCEMdw1Ro7NwNxvCkl9/+HjtvNXQWTPv8OA6Xu+w==

"@jest/schemas@^29.4.3":
  version "29.4.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@jest/schemas/-/schemas-29.4.3.tgz#39cf1b8469afc40b6f5a2baaa146e332c4151788"
  integrity sha512-VLYKXQmtmuEz6IxJsrZwzG9NvtkQsWNnWMsKxqWNu3+CnfzJQhp0WDDKWLVV9hLKr0l3SLLFRqcYHjhtyuDVxg==
  dependencies:
    "@sinclair/typebox" "^0.25.16"

"@jest/types@^29.5.0":
  version "29.5.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@jest/types/-/types-29.5.0.tgz#f59ef9b031ced83047c67032700d8c807d6e1593"
  integrity sha512-qbu7kN6czmVRc3xWFQcAN03RAUamgppVUdXrvl1Wr3jlNF93o9mJbGcDWrwGB6ht44u7efB1qCFgVQmca24Uog==
  dependencies:
    "@jest/schemas" "^29.4.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^17.0.8"
    chalk "^4.0.0"

"@jridgewell/gen-mapping@^0.3.0":
  version "0.3.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@jridgewell/gen-mapping/-/gen-mapping-0.3.3.tgz#7e02e6eb5df901aaedb08514203b096614024098"
  integrity sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@3.1.0":
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz#2203b118c157721addfe69d47b70465463066d78"
  integrity sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==

"@jridgewell/set-array@^1.0.1":
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@jridgewell/set-array/-/set-array-1.1.2.tgz#7c6cf998d6d20b914c0a55a91ae928ff25965e72"
  integrity sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==

"@jridgewell/source-map@^0.3.2":
  version "0.3.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@jridgewell/source-map/-/source-map-0.3.3.tgz#8108265659d4c33e72ffe14e33d6cc5eb59f2fda"
  integrity sha512-b+fsZXeLYi9fEULmfBrhxn4IrPlINf8fiNarzTof004v3lFdntdwa9PF7vFJqm3mg7s+ScJMxXaE3Acp1irZcg==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/sourcemap-codec@1.4.14":
  version "1.4.14"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz#add4c98d341472a289190b424efbdb096991bb24"
  integrity sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==

"@jridgewell/sourcemap-codec@^1.4.10":
  version "1.4.15"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz#d7c6e6755c78567a951e04ab52ef0fd26de59f32"
  integrity sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==

"@jridgewell/trace-mapping@^0.3.17", "@jridgewell/trace-mapping@^0.3.9":
  version "0.3.18"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@jridgewell/trace-mapping/-/trace-mapping-0.3.18.tgz#25783b2086daf6ff1dcb53c9249ae480e4dd4cd6"
  integrity sha512-w+niJYzMHdd7USdiH2U6869nqhD2nbfZXND5Yp93qIbEmnDNk7PD48o+YchRVpzMU7M6jVCbenTR7PA1FLQ9pA==
  dependencies:
    "@jridgewell/resolve-uri" "3.1.0"
    "@jridgewell/sourcemap-codec" "1.4.14"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@sinclair/typebox@^0.25.16":
  version "0.25.24"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@sinclair/typebox/-/typebox-0.25.24.tgz#8c7688559979f7079aacaf31aa881c3aa410b718"
  integrity sha512-XJfwUVUKDHF5ugKwIcxEgc9k8b7HbznCp6eUfWgu710hMPNIO4aw4/zB5RogDQz8nd6gyCDpU9O/m6qYEWY6yQ==

"@trysound/sax@0.2.0":
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@trysound/sax/-/sax-0.2.0.tgz#cccaab758af56761eb7bf37af6f03f326dd798ad"
  integrity sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==

"@types/body-parser@*":
  version "1.19.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/body-parser/-/body-parser-1.19.2.tgz#aea2059e28b7658639081347ac4fab3de166e6f0"
  integrity sha512-ALYone6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g==
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/bonjour@^3.5.9":
  version "3.5.10"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/bonjour/-/bonjour-3.5.10.tgz#0f6aadfe00ea414edc86f5d106357cda9701e275"
  integrity sha512-p7ienRMiS41Nu2/igbJxxLDWrSZ0WxM8UQgCeO9KhoVF7cOVFkrKsiDr1EsJIla8vV3oEEjGcz11jc5yimhzZw==
  dependencies:
    "@types/node" "*"

"@types/codemirror@*":
  version "0.0.91"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/codemirror/-/codemirror-0.0.91.tgz#4cb9832388726e57e747f0e3a8ab69105ad02a66"
  integrity sha512-FZcfBNjhVc6slo6RbtbCqYa+KTQa9sykV5OdRLqd3FeMPddVLFuqSR3KNZUbzU9qoEBudBZX0nbItJ52ml37KA==
  dependencies:
    "@types/tern" "*"

"@types/connect-history-api-fallback@^1.3.5":
  version "1.3.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.3.5.tgz#d1f7a8a09d0ed5a57aee5ae9c18ab9b803205dae"
  integrity sha512-h8QJa8xSb1WD4fpKBDcATDNGXghFj6/3GRWG6dhmRcu0RX1Ubasur2Uvx5aeEwlf0MwblEC2bMzzMQntxnw/Cw==
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.35"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/connect/-/connect-3.4.35.tgz#5fcf6ae445e4021d1fc2219a4873cc73a3bb2ad1"
  integrity sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ==
  dependencies:
    "@types/node" "*"

"@types/eslint-scope@^3.7.3":
  version "3.7.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/eslint-scope/-/eslint-scope-3.7.3.tgz#125b88504b61e3c8bc6f870882003253005c3224"
  integrity sha512-PB3ldyrcnAicT35TWPs5IcwKD8S333HMaa2VVv4+wdvebJkjWuW/xESoB8IwRcog8HYVYamb1g/R31Qv5Bx03g==
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*":
  version "8.4.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/eslint/-/eslint-8.4.1.tgz#c48251553e8759db9e656de3efc846954ac32304"
  integrity sha512-GE44+DNEyxxh2Kc6ro/VkIj+9ma0pO0bwv9+uHSyBrikYOHr8zYcdPvnBOp1aw8s+CjRvuSx7CyWqRrNFQ59mA==
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*":
  version "0.0.51"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/estree/-/estree-0.0.51.tgz#cfd70924a25a3fd32b218e5e420e6897e1ac4f40"
  integrity sha512-CuPgU6f3eT/XgKKPqKd/gLZV1Xmvf1a2R5POBOGQa6uv82xpls89HU5zKeVoyR8XzHd1RGNOlQlvUe3CFkjWNQ==

"@types/estree@^1.0.0":
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/estree/-/estree-1.0.1.tgz#aa22750962f3bf0e79d753d3cc067f010c95f194"
  integrity sha512-LG4opVs2ANWZ1TJoKc937iMmNstM/d0ae1vNbnBvBhqCSezgVUOzcLCqbI5elV8Vy6WKwKjaqR+zO9VKirBBCA==

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.18":
  version "4.17.28"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/express-serve-static-core/-/express-serve-static-core-4.17.28.tgz#c47def9f34ec81dc6328d0b1b5303d1ec98d86b8"
  integrity sha512-P1BJAEAW3E2DJUlkgq4tOL3RyMunoWXqbSCygWo5ZIWTjUgN1YnaXWW4VWl/oc8vs/XoYibEGBKP0uZyF4AHig==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express@*", "@types/express@^4.17.13":
  version "4.17.13"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/express/-/express-4.17.13.tgz#a76e2995728999bab51a33fabce1d705a3709034"
  integrity sha512-6bSZTPaTIACxn48l50SR+axgrqm6qXFIxrdAKaG6PaJk3+zuUr35hBlgT7vOmJcum+OEaIBLtHV/qloEAFITeA==
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.18"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/http-proxy@^1.17.8":
  version "1.17.8"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/http-proxy/-/http-proxy-1.17.8.tgz#968c66903e7e42b483608030ee85800f22d03f55"
  integrity sha512-5kPLG5BKpWYkw/LVOGWpiq3nEVqxiN32rTgI53Sk12/xHFQ2rG3ehI9IO+O3W2QoKeyB92dJkoka8SUm6BX1pA==
  dependencies:
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0":
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.4.tgz#8467d4b3c087805d63580480890791277ce35c44"
  integrity sha512-z/QT1XN4K4KYuslS23k62yDIDLwLFkzxOuMplDtObz0+y7VqJCaO2o+SPwHCvLFZh7xazvvoor2tA/hPz9ee7g==

"@types/istanbul-lib-report@*":
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz#c14c24f18ea8190c118ee7562b7ff99a36552686"
  integrity sha512-plGgXAPfVKFoYfa9NpYDAkseG+g6Jr294RqeqcqDixSbU34MZVJRi/P+7Y8GDpzkEwLaGZZOpKIEmeVZNtKsrg==
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/istanbul-reports/-/istanbul-reports-3.0.1.tgz#9153fe98bba2bd565a63add9436d6f0d7f8468ff"
  integrity sha512-c3mAZEuK0lvBp8tmuL74XRKn1+y2dcwOUpH7x4WrF6gk1GIgiluDRgMYQtw2OFcBvAJWlt6ASU3tSqxp0Uu0Aw==
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/json-schema@*", "@types/json-schema@^7.0.5", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  version "7.0.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/json-schema/-/json-schema-7.0.9.tgz#97edc9037ea0c38585320b28964dde3b39e4660d"
  integrity sha512-qcUXuemtEu+E5wZSJHNxUXeCZhAfXKQ41D+duX+VYPde7xyEVZci+/oXKJL13tnRs9lR2pr4fod59GT6/X1/yQ==

"@types/mime@^1":
  version "1.3.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/mime/-/mime-1.3.2.tgz#93e25bf9ee75fe0fd80b594bc4feb0e862111b5a"
  integrity sha1-k+Jb+e51/g/YC1lLxP6w6GIRG1o=

"@types/node@*", "@types/node@^11.11.3":
  version "11.15.10"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/node/-/node-11.15.10.tgz#1898966191bd8ff311a51bfe8874a8745a4e5a97"
  integrity sha512-FHli7aK/ViA02vbWKmiNejJW4BCJxCVb6macS5gi71fST+UrrdqcES6Lh5rx23hU1QCBeUNXfPquYr9jJv7FuA==

"@types/parse-json@^4.0.0":
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/parse-json/-/parse-json-4.0.0.tgz#2f8bb441434d163b35fb8ffdccd7138927ffb8c0"
  integrity sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA==

"@types/prop-types@*", "@types/prop-types@^15.5.2":
  version "15.7.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/prop-types/-/prop-types-15.7.3.tgz#2ab0d5da2e5815f94b0b9d4b95d1e5f243ab2ca7"
  integrity sha1-KrDV2i5YFflLC51LldHl8kOrLKc=

"@types/qs@*":
  version "6.9.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/qs/-/qs-6.9.7.tgz#63bb7d067db107cc1e457c303bc25d511febf6cb"
  integrity sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw==

"@types/quill@1.3.10":
  version "1.3.10"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/quill/-/quill-1.3.10.tgz#dc1f7b6587f7ee94bdf5291bc92289f6f0497613"
  integrity sha512-IhW3fPW+bkt9MLNlycw8u8fWb7oO7W5URC9MfZYHBlA24rex9rs23D5DETChu1zvgVdc5ka64ICjJOgQMr6Shw==
  dependencies:
    parchment "^1.1.2"

"@types/range-parser@*":
  version "1.2.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/range-parser/-/range-parser-1.2.4.tgz#cd667bcfdd025213aafb7ca5915a932590acdcdc"
  integrity sha512-EEhsLsD6UsDM1yFhAvy0Cjr6VwmpMWqFBCb9w07wVugF7w9nfajxLuVmngTIpgS6svCnm6Vaw+MZhoDCKnOfsw==

"@types/react-codemirror@^1.0.2":
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/react-codemirror/-/react-codemirror-1.0.3.tgz#5b0e4d5529a6bc934e992c49f3d1fa77daacc59b"
  integrity sha512-EAyys2Wpys7bJatgnfpXXs4Gi1Vktj23mNngG4Ex2Q0W6eVgfkP4GP7qHyDVU2wbW3T0K7Jqatf2dCob25ptMw==
  dependencies:
    "@types/codemirror" "*"
    "@types/react" "*"

"@types/react-dom@16.0.11":
  version "16.0.11"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/react-dom/-/react-dom-16.0.11.tgz#bd10ccb0d9260343f4b9a49d4f7a8330a5c1f081"
  integrity sha512-x6zUx9/42B5Kl2Vl9HlopV8JF64wLpX3c+Pst9kc1HgzrsH+mkehe/zmHMQTplIrR48H2gpU7ZqurQolYu8XBA==
  dependencies:
    "@types/react" "*"

"@types/react-redux@^6.0.8":
  version "6.0.21"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/react-redux/-/react-redux-6.0.21.tgz#a2fd59d023107b620a15041bb8c0446e149ab75e"
  integrity sha512-B23h4GtBEB+j0+MmBG+mT+c7CzLsYVgM3+iJqzeREPAn7O2JvbGF/EEDKNBu9XQ+LiHBHJHb65zEXNa+mg2+HQ==
  dependencies:
    "@types/react" "*"
    redux "^4.0.0"

"@types/react@*", "@types/react@16.7.17":
  version "16.7.17"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/react/-/react-16.7.17.tgz#3242e796a1ffbba4f49eae5915a67f4c079504e9"
  integrity sha512-YcXcaoXaxo7A76mBCGlKlN2aZu3REQfF0DTrhiyXVJLA7PDdxVCr+wiQOrkVNn44D/zLlIyDSn3U918Ve0AaEA==
  dependencies:
    "@types/prop-types" "*"
    csstype "^2.2.0"

"@types/retry@^0.12.0":
  version "0.12.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/retry/-/retry-0.12.1.tgz#d8f1c0d0dc23afad6dc16a9e993a0865774b4065"
  integrity sha512-xoDlM2S4ortawSWORYqsdU+2rxdh4LRW9ytc3zmT37RIKQh6IHyKwwtKhKis9ah8ol07DCkZxPt8BBvPjC6v4g==

"@types/serve-index@^1.9.1":
  version "1.9.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/serve-index/-/serve-index-1.9.1.tgz#1b5e85370a192c01ec6cec4735cf2917337a6278"
  integrity sha512-d/Hs3nWDxNL2xAczmOVZNj92YZCS6RGxfBPjKzuu/XirCgXdpKEb88dYNbrYGint6IVWLNP+yonwVAuRC0T2Dg==
  dependencies:
    "@types/express" "*"

"@types/serve-static@*":
  version "1.13.10"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/serve-static/-/serve-static-1.13.10.tgz#f5e0ce8797d2d7cc5ebeda48a52c96c4fa47a8d9"
  integrity sha512-nCkHGI4w7ZgAdNkrEu0bv+4xNV/XDqW+DydknebMOQwkpDGx8G+HTlj7R7ABI8i8nKxVw0wtKPi1D+lPOkh4YQ==
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/sockjs@^0.3.33":
  version "0.3.33"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/sockjs/-/sockjs-0.3.33.tgz#570d3a0b99ac995360e3136fd6045113b1bd236f"
  integrity sha512-f0KEEe05NvUnat+boPTZ0dgaLZ4SfSouXUgv5noUiefG2ajgKjmETo9ZJyuqsl7dfl2aHlLJUiki6B4ZYldiiw==
  dependencies:
    "@types/node" "*"

"@types/tern@*":
  version "0.23.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/tern/-/tern-0.23.3.tgz#4b54538f04a88c9ff79de1f6f94f575a7f339460"
  integrity sha1-S1RTjwSojJ/3neH2+U9XWn8zlGA=
  dependencies:
    "@types/estree" "*"

"@types/ws@^8.2.2":
  version "8.5.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/ws/-/ws-8.5.0.tgz#805ceb3f68eaebbed1a3004a66f16e35b7f3bf14"
  integrity sha512-mTClfhq5cuGyW4jthaFuig6Q8OVfB3IRyZfN/9SCyJtiM5H0SubwM89cHoT9UngO6HyUFic88HvT1zSNLNyxWA==
  dependencies:
    "@types/node" "*"

"@types/wyn-portal@7.1.1":
  version "7.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/wyn-portal/-/wyn-portal-7.1.1.tgz#ac3b132aa3880353f269e6302d5fec4fa4bf550f"
  integrity sha512-lgJD1UytLUlim0e0py4qu9mhDZn8iOpcmmbrFuuwZUunoi6TIaFqPy+Zn5K6bF+oXYtBjMrydDA4g4LFJqgFew==

"@types/yargs-parser@*":
  version "21.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/yargs-parser/-/yargs-parser-21.0.0.tgz#0c60e537fa790f5f9472ed2776c2b71ec117351b"
  integrity sha512-iO9ZQHkZxHn4mSakYV0vFHAVDyEOIJQrV2uZ06HxEPcx+mt8swXoZHIbaaJ2crJYFfErySgktuTZ3BeLz+XmFA==

"@types/yargs@^17.0.8":
  version "17.0.24"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@types/yargs/-/yargs-17.0.24.tgz#b3ef8d50ad4aa6aecf6ddc97c580a00f5aa11902"
  integrity sha512-6i0aC7jV6QzQB8ne1joVZ0eSFIstHsCrobmOtghM11yGlH0j43FKL2UhWdELkyps0zuf7qVTUVCCR+tgSlyLLw==
  dependencies:
    "@types/yargs-parser" "*"

"@webassemblyjs/ast@1.11.5", "@webassemblyjs/ast@^1.11.5":
  version "1.11.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webassemblyjs/ast/-/ast-1.11.5.tgz#6e818036b94548c1fb53b754b5cae3c9b208281c"
  integrity sha512-LHY/GSAZZRpsNQH+/oHqhRQ5FT7eoULcBqgfyTB5nQHogFnK3/7QoN7dLnwSE/JkUAF0SrRuclT7ODqMFtWxxQ==
  dependencies:
    "@webassemblyjs/helper-numbers" "1.11.5"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.5"

"@webassemblyjs/floating-point-hex-parser@1.11.5":
  version "1.11.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.11.5.tgz#e85dfdb01cad16b812ff166b96806c050555f1b4"
  integrity sha512-1j1zTIC5EZOtCplMBG/IEwLtUojtwFVwdyVMbL/hwWqbzlQoJsWCOavrdnLkemwNoC/EOwtUFch3fuo+cbcXYQ==

"@webassemblyjs/helper-api-error@1.11.5":
  version "1.11.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.5.tgz#1e82fa7958c681ddcf4eabef756ce09d49d442d1"
  integrity sha512-L65bDPmfpY0+yFrsgz8b6LhXmbbs38OnwDCf6NpnMUYqa+ENfE5Dq9E42ny0qz/PdR0LJyq/T5YijPnU8AXEpA==

"@webassemblyjs/helper-buffer@1.11.5":
  version "1.11.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webassemblyjs/helper-buffer/-/helper-buffer-1.11.5.tgz#91381652ea95bb38bbfd270702351c0c89d69fba"
  integrity sha512-fDKo1gstwFFSfacIeH5KfwzjykIE6ldh1iH9Y/8YkAZrhmu4TctqYjSh7t0K2VyDSXOZJ1MLhht/k9IvYGcIxg==

"@webassemblyjs/helper-numbers@1.11.5":
  version "1.11.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webassemblyjs/helper-numbers/-/helper-numbers-1.11.5.tgz#23380c910d56764957292839006fecbe05e135a9"
  integrity sha512-DhykHXM0ZABqfIGYNv93A5KKDw/+ywBFnuWybZZWcuzWHfbp21wUfRkbtz7dMGwGgT4iXjWuhRMA2Mzod6W4WA==
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.11.5"
    "@webassemblyjs/helper-api-error" "1.11.5"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.11.5":
  version "1.11.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.11.5.tgz#e258a25251bc69a52ef817da3001863cc1c24b9f"
  integrity sha512-oC4Qa0bNcqnjAowFn7MPCETQgDYytpsfvz4ujZz63Zu/a/v71HeCAAmZsgZ3YVKec3zSPYytG3/PrRCqbtcAvA==

"@webassemblyjs/helper-wasm-section@1.11.5":
  version "1.11.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.11.5.tgz#966e855a6fae04d5570ad4ec87fbcf29b42ba78e"
  integrity sha512-uEoThA1LN2NA+K3B9wDo3yKlBfVtC6rh0i4/6hvbz071E8gTNZD/pT0MsBf7MeD6KbApMSkaAK0XeKyOZC7CIA==
  dependencies:
    "@webassemblyjs/ast" "1.11.5"
    "@webassemblyjs/helper-buffer" "1.11.5"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.5"
    "@webassemblyjs/wasm-gen" "1.11.5"

"@webassemblyjs/ieee754@1.11.5":
  version "1.11.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webassemblyjs/ieee754/-/ieee754-1.11.5.tgz#b2db1b33ce9c91e34236194c2b5cba9b25ca9d60"
  integrity sha512-37aGq6qVL8A8oPbPrSGMBcp38YZFXcHfiROflJn9jxSdSMMM5dS5P/9e2/TpaJuhE+wFrbukN2WI6Hw9MH5acg==
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.11.5":
  version "1.11.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webassemblyjs/leb128/-/leb128-1.11.5.tgz#482e44d26b6b949edf042a8525a66c649e38935a"
  integrity sha512-ajqrRSXaTJoPW+xmkfYN6l8VIeNnR4vBOTQO9HzR7IygoCcKWkICbKFbVTNMjMgMREqXEr0+2M6zukzM47ZUfQ==
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.11.5":
  version "1.11.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webassemblyjs/utf8/-/utf8-1.11.5.tgz#83bef94856e399f3740e8df9f63bc47a987eae1a"
  integrity sha512-WiOhulHKTZU5UPlRl53gHR8OxdGsSOxqfpqWeA2FmcwBMaoEdz6b2x2si3IwC9/fSPLfe8pBMRTHVMk5nlwnFQ==

"@webassemblyjs/wasm-edit@^1.11.5":
  version "1.11.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webassemblyjs/wasm-edit/-/wasm-edit-1.11.5.tgz#93ee10a08037657e21c70de31c47fdad6b522b2d"
  integrity sha512-C0p9D2fAu3Twwqvygvf42iGCQ4av8MFBLiTb+08SZ4cEdwzWx9QeAHDo1E2k+9s/0w1DM40oflJOpkZ8jW4HCQ==
  dependencies:
    "@webassemblyjs/ast" "1.11.5"
    "@webassemblyjs/helper-buffer" "1.11.5"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.5"
    "@webassemblyjs/helper-wasm-section" "1.11.5"
    "@webassemblyjs/wasm-gen" "1.11.5"
    "@webassemblyjs/wasm-opt" "1.11.5"
    "@webassemblyjs/wasm-parser" "1.11.5"
    "@webassemblyjs/wast-printer" "1.11.5"

"@webassemblyjs/wasm-gen@1.11.5":
  version "1.11.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webassemblyjs/wasm-gen/-/wasm-gen-1.11.5.tgz#ceb1c82b40bf0cf67a492c53381916756ef7f0b1"
  integrity sha512-14vteRlRjxLK9eSyYFvw1K8Vv+iPdZU0Aebk3j6oB8TQiQYuO6hj9s4d7qf6f2HJr2khzvNldAFG13CgdkAIfA==
  dependencies:
    "@webassemblyjs/ast" "1.11.5"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.5"
    "@webassemblyjs/ieee754" "1.11.5"
    "@webassemblyjs/leb128" "1.11.5"
    "@webassemblyjs/utf8" "1.11.5"

"@webassemblyjs/wasm-opt@1.11.5":
  version "1.11.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webassemblyjs/wasm-opt/-/wasm-opt-1.11.5.tgz#b52bac29681fa62487e16d3bb7f0633d5e62ca0a"
  integrity sha512-tcKwlIXstBQgbKy1MlbDMlXaxpucn42eb17H29rawYLxm5+MsEmgPzeCP8B1Cl69hCice8LeKgZpRUAPtqYPgw==
  dependencies:
    "@webassemblyjs/ast" "1.11.5"
    "@webassemblyjs/helper-buffer" "1.11.5"
    "@webassemblyjs/wasm-gen" "1.11.5"
    "@webassemblyjs/wasm-parser" "1.11.5"

"@webassemblyjs/wasm-parser@1.11.5", "@webassemblyjs/wasm-parser@^1.11.5":
  version "1.11.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webassemblyjs/wasm-parser/-/wasm-parser-1.11.5.tgz#7ba0697ca74c860ea13e3ba226b29617046982e2"
  integrity sha512-SVXUIwsLQlc8srSD7jejsfTU83g7pIGr2YYNb9oHdtldSxaOhvA5xwvIiWIfcX8PlSakgqMXsLpLfbbJ4cBYew==
  dependencies:
    "@webassemblyjs/ast" "1.11.5"
    "@webassemblyjs/helper-api-error" "1.11.5"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.5"
    "@webassemblyjs/ieee754" "1.11.5"
    "@webassemblyjs/leb128" "1.11.5"
    "@webassemblyjs/utf8" "1.11.5"

"@webassemblyjs/wast-printer@1.11.5":
  version "1.11.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webassemblyjs/wast-printer/-/wast-printer-1.11.5.tgz#7a5e9689043f3eca82d544d7be7a8e6373a6fa98"
  integrity sha512-f7Pq3wvg3GSPUPzR0F6bmI89Hdb+u9WXrSKc4v+N0aV0q6r42WoF92Jp2jEorBEBRoRNXgjp53nBniDXcqZYPA==
  dependencies:
    "@webassemblyjs/ast" "1.11.5"
    "@xtuc/long" "4.2.2"

"@webpack-cli/configtest@^1.1.1":
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webpack-cli/configtest/-/configtest-1.1.1.tgz#9f53b1b7946a6efc2a749095a4f450e2932e8356"
  integrity sha512-1FBc1f9G4P/AxMqIgfZgeOTuRnwZMten8E7zap5zgpPInnCrP8D4Q81+4CWIch8i/Nf7nXjP0v6CjjbHOrXhKg==

"@webpack-cli/info@^1.4.1":
  version "1.4.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webpack-cli/info/-/info-1.4.1.tgz#2360ea1710cbbb97ff156a3f0f24556e0fc1ebea"
  integrity sha512-PKVGmazEq3oAo46Q63tpMr4HipI3OPfP7LiNOEJg963RMgT0rqheag28NCML0o3GIzA3DmxP1ZIAv9oTX1CUIA==
  dependencies:
    envinfo "^7.7.3"

"@webpack-cli/serve@^1.6.1":
  version "1.6.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@webpack-cli/serve/-/serve-1.6.1.tgz#0de2875ac31b46b6c5bb1ae0a7d7f0ba5678dffe"
  integrity sha512-gNGTiTrjEVQ0OcVnzsRSqTxaBSr+dmTfm+qJsCDluky8uhdLWep7Gcr62QsAKHTMxjCS/8nEITsmFAhfIx+QSw==

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@xtuc/ieee754/-/ieee754-1.2.0.tgz#eef014a3145ae477a1cbc00cd1e552336dceb790"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/@xtuc/long/-/long-4.2.2.tgz#d291c6a4e97989b5c61d9acf396ae4fe133a718d"
  integrity sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==

abab@^2.0.5:
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/abab/-/abab-2.0.5.tgz#c0b678fb32d60fc1219c784d6a826fe385aeb79a"
  integrity sha512-9IK9EadsbHo6jLWIpxpR6pL0sazTXV6+SQv25ZB+F7Bj9mJNaOc4nCRabwd5M/JwmUa8idz6Eci6eKfJryPs6Q==

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.7:
  version "1.3.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/accepts/-/accepts-1.3.7.tgz#531bc726517a3b2b41f850021c6cc15eaab507cd"
  integrity sha512-Il80Qs2WjYlJIBNzNkK6KYqlVMTbZLXgHx2oT0pU/fjRHyEp+PEfEPY0R3WCwAGVOtauxh1hOxNgIf5bv7dQpA==
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

acorn-import-assertions@^1.7.6:
  version "1.8.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/acorn-import-assertions/-/acorn-import-assertions-1.8.0.tgz#ba2b5939ce62c238db6d93d81c9b111b29b855e9"
  integrity sha512-m7VZ3jwz4eK6A4Vtt8Ew1/mNbP24u0FhdyfA7fSvnJR6LMdfOYnmuIrrJAgrYfYJ10F/otaHTtrtrtmHdMNzEw==

acorn@^3.0.0:
  version "3.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/acorn/-/acorn-3.3.0.tgz#45e37fb39e8da3f25baee3ff5369e2bb5f22017a"
  integrity sha1-ReN/s56No/JbruP/U2niu18iAXo=

acorn@^8.5.0:
  version "8.7.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/acorn/-/acorn-8.7.0.tgz#90951fde0f8f09df93549481e5fc141445b791cf"
  integrity sha512-V/LGr1APy+PXIwKebEWrkZPwoeoF+w1jiOBUmuxuiUIaOHtob8Qc9BTrYo7VuI5fR8tqsy+buA2WFooR5olqvQ==

acorn@^8.7.1:
  version "8.8.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/acorn/-/acorn-8.8.2.tgz#1b2f25db02af965399b9776b0c2c391276d37c4a"
  integrity sha512-xjIYgE8HBrkpd/sJqOGNspf8uHG+NOHGOw6a/Urj8taM2EXfdNAH2oFcPeIFfsv3+kz/mJrS5VuMqbNLjCa2vw==

add-px-to-style@1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/add-px-to-style/-/add-px-to-style-1.0.0.tgz#d0c135441fa8014a8137904531096f67f28f263a"
  integrity sha512-YMyxSlXpPjD8uWekCQGuN40lV4bnZagUwqa2m/uFv1z/tNImSk9fnXVMUI5qwME/zzI3MMQRvjZ+69zyfSSyew==

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/aggregate-error/-/aggregate-error-3.1.0.tgz#92670ff50f5359bdb7a3e0d40d0ec30c5737687a"
  integrity sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv-formats@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ajv-formats/-/ajv-formats-2.1.1.tgz#6e669400659eb74973bbf2e33327180a0996b520"
  integrity sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==
  dependencies:
    ajv "^8.0.0"

ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ajv-keywords/-/ajv-keywords-3.5.2.tgz#31f29da5ab6e00d1c2d329acf7b5929614d5014d"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv-keywords@^5.0.0:
  version "5.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ajv-keywords/-/ajv-keywords-5.1.0.tgz#69d4d385a4733cdbeab44964a1170a88f87f0e16"
  integrity sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==
  dependencies:
    fast-deep-equal "^3.1.3"

ajv@^6.12.4, ajv@^6.12.5:
  version "6.12.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ajv/-/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.0, ajv@^8.8.0:
  version "8.10.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ajv/-/ajv-8.10.0.tgz#e573f719bd3af069017e3b66538ab968d040e54d"
  integrity sha512-bzqAEZOjkrUMl2afH8dknrq5KEk2SrwdBROR+vH1EKVQTqaUbJVPdc/gEdggTMM0Se+s+Ja4ju4TlNcStKl2Hw==
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

align-text@^0.1.1, align-text@^0.1.3:
  version "0.1.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/align-text/-/align-text-0.1.4.tgz#0cd90a561093f35d0a99256c22b7069433fad117"
  integrity sha1-DNkKVhCT810KmSVsIrcGlDP60Rc=
  dependencies:
    kind-of "^3.0.2"
    longest "^1.0.1"
    repeat-string "^1.5.2"

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/amdefine/-/amdefine-1.0.1.tgz#4a5282ac164729e93619bcfd3ad151f817ce91f5"
  integrity sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=

ansi-html-community@^0.0.8:
  version "0.0.8"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ansi-html-community/-/ansi-html-community-0.0.8.tgz#69fbc4d6ccbe383f9736934ae34c3f8290f1bf41"
  integrity sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw==

ansi-regex@^6.0.1:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ansi-regex/-/ansi-regex-6.0.1.tgz#3183e38fae9a65d7cb5e53945cd5897d0260a06a"
  integrity sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ansi-styles/-/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ansi-styles/-/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansis@^1.3.4:
  version "1.3.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ansis/-/ansis-1.3.4.tgz#e94e32c6083c2eac9613ec6174c695a5bec9d9cb"
  integrity sha512-BDXljGSG4gZXmWK64bQzXkI509i5fe8aAa9+eL29e3swaWUqxvxk/XlONjw9AUrNCpQWNdy++0GX7HAhWeR9BQ==

anymatch@^1.3.0:
  version "1.3.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/anymatch/-/anymatch-1.3.2.tgz#553dcb8f91e3c889845dfdba34c77721b90b9d7a"
  integrity sha1-VT3Lj5HjyImEXf26NMd3IbkLnXo=
  dependencies:
    micromatch "^2.1.5"
    normalize-path "^2.0.0"

anymatch@~3.1.2:
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/anymatch/-/anymatch-3.1.2.tgz#c0557c096af32f106198f4f4e2a383537e378716"
  integrity sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arr-diff@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/arr-diff/-/arr-diff-2.0.0.tgz#8f3b827f955a8bd669697e4a4256ac3ceae356cf"
  integrity sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=
  dependencies:
    arr-flatten "^1.0.1"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/arr-diff/-/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.0.1, arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/arr-flatten/-/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/arr-union/-/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/array-flatten/-/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-flatten@^2.1.0:
  version "2.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/array-flatten/-/array-flatten-2.1.2.tgz#24ef80a28c1a893617e2149b0c6d0d788293b099"
  integrity sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ==

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/array-union/-/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array-unique@^0.2.1:
  version "0.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/array-unique/-/array-unique-0.2.1.tgz#a1d97ccafcbc2625cc70fadceb36a50c58b01a53"
  integrity sha1-odl8yvy8JiXMcPrc6zalDFiwGlM=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/array-unique/-/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

asap@~2.0.3:
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/asap/-/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"
  integrity sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==

assert@^1.1.1:
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/assert/-/assert-1.5.0.tgz#55c109aaf6e0aefdb3dc4b71240c70bf574b18eb"
  integrity sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs=
  dependencies:
    object-assign "^4.1.1"
    util "0.10.3"

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/assign-symbols/-/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

async-each@^1.0.0:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/async-each/-/async-each-1.0.3.tgz#b727dbf87d7651602f06f4d4ac387f47d91b0cbf"
  integrity sha512-z/WhQ5FPySLdvREByI2vZiTWwCnF0moMJ1hK9YQwDTHKh6I7/uSckMetoRGb5UBZPC1z0jlw+n/XCgjeH7y1AQ==

async@^0.9.0:
  version "0.9.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/async/-/async-0.9.2.tgz#aea74d5e61c1f899613bf64bda66d4c78f2fd17d"
  integrity sha1-rqdNXmHB+JlhO/ZL2mbUx48v0X0=

async@^1.3.0:
  version "1.5.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/async/-/async-1.5.2.tgz#ec6a61ae56480c0c3cb241c95618e20892f9672a"
  integrity sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo=

async@^2.6.2:
  version "2.6.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/async/-/async-2.6.3.tgz#d72625e2344a3656e3a3ad4fa749fa83299d82ff"
  integrity sha512-zflvls11DCy+dQWzTW2dzuilv8Z5X/pjfmZOWba6TNIVDm+2UDaJmXSOXlasHKfNBs8oo3M0aT50fDEWfKZjXg==
  dependencies:
    lodash "^4.17.14"

async@~0.2.6:
  version "0.2.10"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/async/-/async-0.2.10.tgz#b6bbe0b0674b9d719708ca38de8c237cb526c3d1"
  integrity sha1-trvgsGdLnXGXCMo43owjfLUmw9E=

atob@^2.1.2:
  version "2.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/atob/-/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

attr-accept@^1.1.3:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/attr-accept/-/attr-accept-1.1.3.tgz#48230c79f93790ef2775fcec4f0db0f5db41ca52"
  integrity sha512-iT40nudw8zmCweivz6j58g+RT33I4KbaIvRUhjNmDwO2WmsQUxFEZZYZ5w3vXe5x5MX9D7mfvA/XaLOZYFR9EQ==
  dependencies:
    core-js "^2.5.0"

babel-loader@8.2.3:
  version "8.2.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/babel-loader/-/babel-loader-8.2.3.tgz#8986b40f1a64cacfcb4b8429320085ef68b1342d"
  integrity sha512-n4Zeta8NC3QAsuyiizu0GkmRcQ6clkV9WFUnUf1iXP//IeSKbWjofW3UHyZVwlOB4y039YQKefawyTn64Zwbuw==
  dependencies:
    find-cache-dir "^3.3.1"
    loader-utils "^1.4.0"
    make-dir "^3.1.0"
    schema-utils "^2.6.5"

babel-plugin-dynamic-import-node@^2.3.3:
  version "2.3.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz#84fda19c976ec5c6defef57f9427b3def66e17a3"
  integrity sha1-hP2hnJduxcbe/vV/lCez3vZuF6M=
  dependencies:
    object.assign "^4.1.0"

babel-plugin-polyfill-corejs2@^0.3.0:
  version "0.3.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.1.tgz#440f1b70ccfaabc6b676d196239b138f8a2cfba5"
  integrity sha512-v7/T6EQcNfVLfcN2X8Lulb7DjprieyLWJK/zOWH5DUYcAgex9sP3h25Q+DLsX9TloXe3y1O8l2q2Jv9q8UVB9w==
  dependencies:
    "@babel/compat-data" "^7.13.11"
    "@babel/helper-define-polyfill-provider" "^0.3.1"
    semver "^6.1.1"

babel-plugin-polyfill-corejs3@^0.4.0:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.4.0.tgz#0b571f4cf3d67f911512f5c04842a7b8e8263087"
  integrity sha512-YxFreYwUfglYKdLUGvIF2nJEsGwj+RhWSX/ije3D2vQPOXuyMLMtg/cCGMDpOA7Nd+MwlNdnGODbd2EwUZPlsw==
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.0"
    core-js-compat "^3.18.0"

babel-plugin-polyfill-regenerator@^0.3.0:
  version "0.3.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.3.1.tgz#2c0678ea47c75c8cc2fbb1852278d8fb68233990"
  integrity sha512-Y2B06tvgHYt1x0yz17jGkGeeMr5FeKUu+ASJ+N6nB5lQ8Dapfg42i0OVrf8PNGJ3zKL4A23snMi1IRwrqqND7A==
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.1"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/balanced-match/-/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"
  integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=

base64-js@^1.0.2:
  version "1.3.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/base64-js/-/base64-js-1.3.1.tgz#58ece8cb75dd07e71ed08c736abc5fac4dbf8df1"
  integrity sha1-WOzoy3XdB+ce0IxzarxfrE2/jfE=

base@^0.11.1:
  version "0.11.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/base/-/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

batch@0.6.1:
  version "0.6.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/batch/-/batch-0.6.1.tgz#dc34314f4e679318093fc760272525f94bf25c16"
  integrity sha512-x+VAiMRL6UPkx+kudNvxTl6hB2XNNCG2r+7wixVfIYwu/2HKRXimwQyaumLjMveWvT2Hkd/cAJw+QBMfJ/EKVw==

big.js@^3.1.3:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/big.js/-/big.js-3.2.0.tgz#a5fc298b81b9e0dca2e458824784b65c52ba588e"
  integrity sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4=

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/big.js/-/big.js-5.2.2.tgz#65f0af382f578bcdc742bd9c281e9cb2d7768328"
  integrity sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/binary-extensions/-/binary-extensions-1.13.1.tgz#598afe54755b2868a5330d2aff9d4ebb53209b65"
  integrity sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw==

binary-extensions@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/binary-extensions/-/binary-extensions-2.0.0.tgz#23c0df14f6a88077f5f986c0d167ec03c3d5537c"
  integrity sha1-I8DfFPaogHf1+YbA0WfsA8PVU3w=

bindings@^1.5.0:
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/bindings/-/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df"
  integrity sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=
  dependencies:
    file-uri-to-path "1.0.0"

body-parser@1.19.0:
  version "1.19.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/body-parser/-/body-parser-1.19.0.tgz#96b2709e57c9c4e09a6fd66a8fd979844f69f08a"
  integrity sha512-dhEPs72UPbDnAQJ9ZKMNTP6ptJaionhP5cBb541nXPlW60Jepo9RV/a4fX4XWW9CuFNK22krhrj1+rgzifNCsw==
  dependencies:
    bytes "3.1.0"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.2"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    on-finished "~2.3.0"
    qs "6.7.0"
    raw-body "2.4.0"
    type-is "~1.6.17"

bonjour@^3.5.0:
  version "3.5.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/bonjour/-/bonjour-3.5.0.tgz#8e890a183d8ee9a2393b3844c691a42bcf7bc9f5"
  integrity sha512-RaVTblr+OnEli0r/ud8InrU7D+G0y6aJhlxaLa6Pwty4+xoxboF1BsUI45tujvRpbj9dQVoglChqonGAsjEBYg==
  dependencies:
    array-flatten "^2.1.0"
    deep-equal "^1.0.1"
    dns-equal "^1.0.0"
    dns-txt "^2.0.2"
    multicast-dns "^6.0.1"
    multicast-dns-service-types "^1.1.0"

boolbase@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/boolbase/-/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/brace-expansion/-/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^1.8.2:
  version "1.8.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/braces/-/braces-1.8.5.tgz#ba77962e12dff969d6b76711e914b737857bf6a7"
  integrity sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=
  dependencies:
    expand-range "^1.8.1"
    preserve "^0.2.0"
    repeat-element "^1.1.2"

braces@^2.3.1:
  version "2.3.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/braces/-/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
  integrity sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.1, braces@~3.0.2:
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/braces/-/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

browserify-aes@0.4.0:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/browserify-aes/-/browserify-aes-0.4.0.tgz#067149b668df31c4b58533e02d01e806d8608e2c"
  integrity sha1-BnFJtmjfMcS1hTPgLQHoBthgjiw=
  dependencies:
    inherits "^2.0.1"

browserify-zlib@^0.1.4:
  version "0.1.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/browserify-zlib/-/browserify-zlib-0.1.4.tgz#bb35f8a519f600e0fa6b8485241c979d0141fb2d"
  integrity sha1-uzX4pRn2AOD6a4SFJByXnQFB+y0=
  dependencies:
    pako "~0.2.0"

browserslist@^4.0.0, browserslist@^4.14.5, browserslist@^4.17.5, browserslist@^4.19.1:
  version "4.19.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/browserslist/-/browserslist-4.19.3.tgz#29b7caad327ecf2859485f696f9604214bedd383"
  integrity sha512-XK3X4xtKJ+Txj8G5c30B4gsm71s69lqXlkYui4s6EkKxuv49qjYlY6oVd+IFJ73d4YymtM3+djvvt/R/iJwwDg==
  dependencies:
    caniuse-lite "^1.0.30001312"
    electron-to-chromium "^1.4.71"
    escalade "^3.1.1"
    node-releases "^2.0.2"
    picocolors "^1.0.0"

browserslist@^4.21.4:
  version "4.21.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/browserslist/-/browserslist-4.21.5.tgz#75c5dae60063ee641f977e00edd3cfb2fb7af6a7"
  integrity sha512-tUkiguQGW7S3IhB7N+c2MV/HZPSCPAAiYBZXLsBhFB/PCy6ZKKsZrmBayHV9fdGV/ARIfJ14NkxKzRDjvp7L6w==
  dependencies:
    caniuse-lite "^1.0.30001449"
    electron-to-chromium "^1.4.284"
    node-releases "^2.0.8"
    update-browserslist-db "^1.0.10"

buffer-from@^1.0.0:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/buffer-from/-/buffer-from-1.1.1.tgz#32713bc028f75c02fdb710d7c7bcec1f2c6070ef"
  integrity sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=

buffer-indexof@^1.0.0:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/buffer-indexof/-/buffer-indexof-1.1.1.tgz#52fabcc6a606d1a00302802648ef68f639da268c"
  integrity sha512-4/rOEg86jivtPTeOUUT61jJO1Ya1TrR/OkqCSZDyq84WJh3LuuiphBYJN+fm5xufIk4XAFcEwte/8WzC8If/1g==

buffer@^4.9.0:
  version "4.9.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/buffer/-/buffer-4.9.2.tgz#230ead344002988644841ab0244af8c44bbe3ef8"
  integrity sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg=
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz#85982878e21b98e1c66425e03d0174788f569ee8"
  integrity sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=

bytes@3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/bytes/-/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"
  integrity sha512-pMhOfFDPiv9t5jjIXkHosWmkSyQbvsgEVNkz0ERHbuLh2T/7j4Mqqpz523Fe8MVY89KC6Sh/QfS2sM+SjgFDcw==

bytes@3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/bytes/-/bytes-3.1.0.tgz#f6cf7933a360e0588fa9fde85651cdc7f805d1f6"
  integrity sha512-zauLjrfCG+xvoyaqLoV8bLVXXNGC4JqlxFCutSDWA6fJrTo2ZuvLYTqZ7aHBLZSMOopbzwv8f+wZcVzfVTI2Dg==

cache-base@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/cache-base/-/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/callsites/-/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

camelcase@^1.0.2:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/camelcase/-/camelcase-1.2.1.tgz#9bb5304d2e0b56698b2c758b08a3eaa9daa58a39"
  integrity sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk=

caniuse-api@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/caniuse-api/-/caniuse-api-3.0.0.tgz#5e4d90e2274961d46291997df599e3ed008ee4c0"
  integrity sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30001312:
  version "1.0.30001312"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/caniuse-lite/-/caniuse-lite-1.0.30001312.tgz#e11eba4b87e24d22697dae05455d5aea28550d5f"
  integrity sha512-Wiz1Psk2MEK0pX3rUzWaunLTZzqS2JYZFzNKqAiJGiuxIjRPLgV6+VDPOg6lQOUxmDwhTlh198JsTTi8Hzw6aQ==

caniuse-lite@^1.0.30001449:
  version "1.0.30001481"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/caniuse-lite/-/caniuse-lite-1.0.30001481.tgz#f58a717afe92f9e69d0e35ff64df596bfad93912"
  integrity sha512-KCqHwRnaa1InZBtqXzP98LPg0ajCVujMKjqKDhZEthIpAsJl/YEIa3YvXjGXPVqzZVguccuu7ga9KOE1J9rKPQ==

center-align@^0.1.1:
  version "0.1.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/center-align/-/center-align-0.1.3.tgz#aa0d32629b6ee972200411cbd4461c907bc2b7ad"
  integrity sha1-qg0yYptu6XIgBBHL1EYckHvCt60=
  dependencies:
    align-text "^0.1.3"
    lazy-cache "^1.0.3"

chain-function@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/chain-function/-/chain-function-1.0.1.tgz#c63045e5b4b663fb86f1c6e186adaf1de402a1cc"
  integrity sha512-SxltgMwL9uCko5/ZCLiyG2B7R9fY4pDZUw7hJ4MhirdjBLosoDqkWABi3XMucddHdLiFJMb7PD2MZifZriuMTg==

chalk@^2.0.0:
  version "2.4.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/chalk/-/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.2:
  version "4.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/chalk/-/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

"chokidar@>=2.0.0 <4.0.0", chokidar@^3.5.3:
  version "3.5.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/chokidar/-/chokidar-3.5.3.tgz#1cf37c8707b932bd1af1ae22c0432e2acd1903bd"
  integrity sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chokidar@^1.0.0:
  version "1.7.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/chokidar/-/chokidar-1.7.0.tgz#798e689778151c8076b4b360e5edd28cda2bb468"
  integrity sha1-eY5ol3gVHIB2tLNg5e3SjNortGg=
  dependencies:
    anymatch "^1.3.0"
    async-each "^1.0.0"
    glob-parent "^2.0.0"
    inherits "^2.0.1"
    is-binary-path "^1.0.0"
    is-glob "^2.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.0.0"
  optionalDependencies:
    fsevents "^1.0.0"

chrome-trace-event@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/chrome-trace-event/-/chrome-trace-event-1.0.2.tgz#234090ee97c7d4ad1a2c4beae27505deffc608a4"
  integrity sha512-9e/zx1jw7B4CO+c/RXoCsfg/x1AfUBioy4owYH0bJprEYAx5hRFLRhWBqHAG57D0ZM4H7vxbP7bPe0VwhQRYDQ==
  dependencies:
    tslib "^1.9.0"

ci-info@^3.2.0:
  version "3.8.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ci-info/-/ci-info-3.8.0.tgz#81408265a5380c929f0bc665d62256628ce9ef91"
  integrity sha512-eXTggHWSooYhq49F2opQhuHWgzucfF2YgODK4e1566GQs5BIfP30B0oenwBJHfWxAs2fyPB1s7Mg949zLf61Yw==

class-utils@^0.3.5:
  version "0.3.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/class-utils/-/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

classnames@2.2.5, classnames@^2.2.5:
  version "2.2.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/classnames/-/classnames-2.2.5.tgz#fb3801d453467649ef3603c7d61a02bd129bde6d"
  integrity sha512-DTt3GhOUDKhh4ONwIJW4lmhyotQmV2LjNlGK/J2hkwUcqcbKkCLAdJPtxQnxnlc7SR3f1CEXCyMmc7WLUsWbNA==

classnames@^2.2.6:
  version "2.2.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/classnames/-/classnames-2.2.6.tgz#43935bffdd291f326dad0a205309b38d00f650ce"
  integrity sha512-JR/iSQOSt+LQIWwrwEzJ9uk0xfN3mTVYMwt1Ir5mUcSN6pU+V4zQFFaJsclJbPuAUQH+yfWef6tm7l1quW3C8Q==

classnames@^2.3.0:
  version "2.5.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/classnames/-/classnames-2.5.1.tgz#ba774c614be0f016da105c858e7159eae8e7687b"
  integrity sha1-undMYUvg8BbaEFyFjnFZ6ujnaHs=

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/clean-stack/-/clean-stack-2.2.0.tgz#ee8472dbb129e727b31e8a10a427dee9dfe4008b"
  integrity sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=

cliui@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/cliui/-/cliui-2.1.0.tgz#4b475760ff80264c762c3a1719032e91c7fea0d1"
  integrity sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE=
  dependencies:
    center-align "^0.1.1"
    right-align "^0.1.1"
    wordwrap "0.0.2"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/clone-deep/-/clone-deep-4.0.1.tgz#c19fd9bdbbf85942b4fd979c84dcf7d5f07c2387"
  integrity sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/clone/-/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

clone@^2.1.1:
  version "2.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/clone/-/clone-2.1.2.tgz#1b7f4b9f591f1e8f83670401600345a02887435f"
  integrity sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/collection-visit/-/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/color-convert/-/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/color-convert/-/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/color-name/-/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/color-name/-/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colord@^2.9.1:
  version "2.9.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/colord/-/colord-2.9.2.tgz#25e2bacbbaa65991422c07ea209e2089428effb1"
  integrity sha512-Uqbg+J445nc1TKn4FoDPS6ZZqAvEDnwrH42yo8B40JSOgSLxMZ/gt3h4nmCtPLQeXhjJJkqBx7SCY35WnIixaQ==

colorette@^2.0.10, colorette@^2.0.14:
  version "2.0.16"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/colorette/-/colorette-2.0.16.tgz#713b9af84fdb000139f04546bd4a93f62a5085da"
  integrity sha512-hUewv7oMjCp+wkBv5Rm0v87eJhq4woh5rSR+42YSQJKecCqgIqNkZ6lAlQms/BwHPJA5NKMRlpxPRv0n8HQW6g==

commander@^2.20.0:
  version "2.20.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/commander/-/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@^7.0.0, commander@^7.2.0:
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/commander/-/commander-7.2.0.tgz#a36cb57d0b501ce108e4d20559a150a391d97ab7"
  integrity sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/commondir/-/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/component-emitter/-/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"
  integrity sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg==

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/compressible/-/compressible-2.0.18.tgz#af53cca6b070d4c3c0750fbd77286a6d7cc46fba"
  integrity sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.7.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/compression/-/compression-1.7.4.tgz#95523eff170ca57c29a0ca41e6fe131f41e5bb8f"
  integrity sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

connect-history-api-fallback@^1.6.0:
  version "1.6.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz#8b32089359308d111115d81cad3fceab888f97bc"
  integrity sha512-e54B99q/OUoH64zYYRf3HBP5z24G38h5D3qXu23JGRoigpX5Ss4r9ZnDk3g0Z8uQC2x2lPaJ+UlWBc1ZWBWdLg==

console-browserify@^1.1.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/console-browserify/-/console-browserify-1.2.0.tgz#67063cef57ceb6cf4993a2ab3a55840ae8c49336"
  integrity sha1-ZwY871fOts9Jk6KrOlWECujEkzY=

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/constants-browserify/-/constants-browserify-1.0.0.tgz#c20b96d8c617748aaf1c16021760cd27fcb8cb75"
  integrity sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=

content-disposition@0.5.3:
  version "0.5.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/content-disposition/-/content-disposition-0.5.3.tgz#e130caf7e7279087c5616c2007d0485698984fbd"
  integrity sha512-ExO0774ikEObIAEV9kDo50o+79VCUdEB6n6lzKgGwupcVeRlhrj3qGAfwq8G6uBJjkqLrhT0qEYFcWng8z1z0g==
  dependencies:
    safe-buffer "5.1.2"

content-type@~1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/content-type/-/content-type-1.0.4.tgz#e138cc75e040c727b1966fe5e5f8c9aee256fe3b"
  integrity sha1-4TjMdeBAxyexlm/l5fjJruJW/js=

convert-source-map@^1.7.0:
  version "1.7.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/convert-source-map/-/convert-source-map-1.7.0.tgz#17a2cb882d7f77d3490585e2ce6c524424a3a442"
  integrity sha1-F6LLiC1/d9NJBYXizmxSRCSjpEI=
  dependencies:
    safe-buffer "~5.1.1"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/cookie-signature/-/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.4.0:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/cookie/-/cookie-0.4.0.tgz#beb437e7022b3b6d49019d088665303ebe9c14ba"
  integrity sha512-+Hp8fLp57wnUSt0tY0tHEXh4voZRDnoIrZPqlo3DPiI4y9lwg/jqx+1Om94/W6ZaPDOUbnjOt/99w66zk+l1Xg==

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/copy-descriptor/-/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

core-js-compat@^3.18.0, core-js-compat@^3.19.1:
  version "3.21.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/core-js-compat/-/core-js-compat-3.21.1.tgz#cac369f67c8d134ff8f9bd1623e3bc2c42068c82"
  integrity sha512-gbgX5AUvMb8gwxC7FLVWYT7Kkgu/y7+h/h1X43yJkNqhlK2fuYyQimqvKGNZFAY6CKii/GFKJ2cp/1/42TN36g==
  dependencies:
    browserslist "^4.19.1"
    semver "7.0.0"

core-js@^1.0.0:
  version "1.2.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/core-js/-/core-js-1.2.7.tgz#652294c14651db28fa93bd2d5ff2983a4f08c636"
  integrity sha512-ZiPp9pZlgxpWRu0M+YWbm6+aQ84XEfH1JRXvfOc/fILWI0VKhLC2LX13X1NYq4fULzLMq7Hfh43CSo2/aIaUPA==

core-js@^2.4.0, core-js@^2.5.0:
  version "2.6.11"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/core-js/-/core-js-2.6.11.tgz#38831469f9922bded8ee21c9dc46985e0399308c"
  integrity sha1-OIMUafmSK97Y7iHJ3EaYXgOZMIw=

core-util-is@~1.0.0:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/core-util-is/-/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cosmiconfig@^7.0.1:
  version "7.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/cosmiconfig/-/cosmiconfig-7.0.1.tgz#714d756522cace867867ccb4474c5d01bbae5d6d"
  integrity sha512-a1YWNUV2HwGimB7dU2s1wUMurNKjpx60HxBB6xUM8Re+2s1g1IIfJvFR0/iCF+XHdE0GMTKTuLR32UQff4TEyQ==
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

create-react-class@^15.5.2, create-react-class@^15.6.0:
  version "15.7.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/create-react-class/-/create-react-class-15.7.0.tgz#7499d7ca2e69bb51d13faf59bd04f0c65a1d6c1e"
  integrity sha512-QZv4sFWG9S5RUvkTYWbflxeZX+JG7Cz0Tn33rQBJ+WFQTqTfUTjMjiv9tnfXazjsO5r0KhPs+AqCjyrQX6h2ng==
  dependencies:
    loose-envify "^1.3.1"
    object-assign "^4.1.1"

cross-env@7.0.3:
  version "7.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/cross-env/-/cross-env-7.0.3.tgz#865264b29677dc015ba8418918965dd232fc54cf"
  integrity sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==
  dependencies:
    cross-spawn "^7.0.1"

cross-spawn@^7.0.1, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/cross-spawn/-/cross-spawn-7.0.3.tgz#f73a85b9d5d41d045551c177e2882d4ac85728a6"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-browserify@3.3.0:
  version "3.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/crypto-browserify/-/crypto-browserify-3.3.0.tgz#b9fc75bb4a0ed61dcf1cd5dae96eb30c9c3e506c"
  integrity sha1-ufx1u0oO1h3PHNXa6W6zDJw+UGw=
  dependencies:
    browserify-aes "0.4.0"
    pbkdf2-compat "2.0.1"
    ripemd160 "0.2.0"
    sha.js "2.2.6"

css-declaration-sorter@^6.3.1:
  version "6.4.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/css-declaration-sorter/-/css-declaration-sorter-6.4.0.tgz#630618adc21724484b3e9505bce812def44000ad"
  integrity sha512-jDfsatwWMWN0MODAFuHszfjphEXfNw9JUAhmY4pLu3TyTU+ohUpsbVtbU+1MZn4a47D9kqh03i4eyOm+74+zew==

css-loader@6.6.0:
  version "6.6.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/css-loader/-/css-loader-6.6.0.tgz#c792ad5510bd1712618b49381bd0310574fafbd3"
  integrity sha512-FK7H2lisOixPT406s5gZM1S3l8GrfhEBT3ZiL2UX1Ng1XWs0y2GPllz/OTyvbaHe12VgQrIXIzuEGVlbUhodqg==
  dependencies:
    icss-utils "^5.1.0"
    postcss "^8.4.5"
    postcss-modules-extract-imports "^3.0.0"
    postcss-modules-local-by-default "^4.0.0"
    postcss-modules-scope "^3.0.0"
    postcss-modules-values "^4.0.0"
    postcss-value-parser "^4.2.0"
    semver "^7.3.5"

css-minimizer-webpack-plugin@5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/css-minimizer-webpack-plugin/-/css-minimizer-webpack-plugin-5.0.0.tgz#a2c2d1bc5cc37450519e873e13d942bbe4478ef5"
  integrity sha512-1wZ/PYvg+ZKwi5FX6YrvbB31jMAdurS+CmRQLwWCVSlfzJC85l/a6RVICqUHFa+jXyhilfnCyjafzJGbmz5tcA==
  dependencies:
    cssnano "^6.0.0"
    jest-worker "^29.4.3"
    postcss "^8.4.21"
    schema-utils "^4.0.0"
    serialize-javascript "^6.0.1"
    source-map "^0.6.1"

css-select@^5.1.0:
  version "5.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/css-select/-/css-select-5.1.0.tgz#b8ebd6554c3637ccc76688804ad3f6a6fdaea8a6"
  integrity sha512-nwoRF1rvRRnnCqqY7updORDsuqKzqYJ28+oSMaJMMgOauh3fvwHqMS7EZpIPqK8GL+g9mKxF1vP/ZjSeNjEVHg==
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.1.0"
    domhandler "^5.0.2"
    domutils "^3.0.1"
    nth-check "^2.0.1"

css-tree@^2.2.1:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/css-tree/-/css-tree-2.3.1.tgz#10264ce1e5442e8572fc82fbe490644ff54b5c20"
  integrity sha512-6Fv1DV/TYw//QF5IzQdqsNDjx/wc8TrMBZsqjL9eW01tWb7R7k/mq+/VXfJCl7SoD5emsJop9cOByJZfs8hYIw==
  dependencies:
    mdn-data "2.0.30"
    source-map-js "^1.0.1"

css-tree@~2.2.0:
  version "2.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/css-tree/-/css-tree-2.2.1.tgz#36115d382d60afd271e377f9c5f67d02bd48c032"
  integrity sha512-OA0mILzGc1kCOCSJerOeqDxDQ4HOh+G8NbOJFOTgOCzpw7fCBubk0fEyxp8AgOL/jvLgYA/uV0cMbe43ElF1JA==
  dependencies:
    mdn-data "2.0.28"
    source-map-js "^1.0.1"

css-what@^6.1.0:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/css-what/-/css-what-6.1.0.tgz#fb5effcf76f1ddea2c81bdfaa4de44e79bac70f4"
  integrity sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/cssesc/-/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

cssnano-preset-default@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/cssnano-preset-default/-/cssnano-preset-default-6.0.0.tgz#058726536bdc18711c01b1d328766cbc5691cf71"
  integrity sha512-BDxlaFzObRDXUiCCBQUNQcI+f1/aX2mgoNtXGjV6PG64POcHoDUoX+LgMWw+Q4609QhxwkcSnS65YFs42RA6qQ==
  dependencies:
    css-declaration-sorter "^6.3.1"
    cssnano-utils "^4.0.0"
    postcss-calc "^8.2.3"
    postcss-colormin "^6.0.0"
    postcss-convert-values "^6.0.0"
    postcss-discard-comments "^6.0.0"
    postcss-discard-duplicates "^6.0.0"
    postcss-discard-empty "^6.0.0"
    postcss-discard-overridden "^6.0.0"
    postcss-merge-longhand "^6.0.0"
    postcss-merge-rules "^6.0.0"
    postcss-minify-font-values "^6.0.0"
    postcss-minify-gradients "^6.0.0"
    postcss-minify-params "^6.0.0"
    postcss-minify-selectors "^6.0.0"
    postcss-normalize-charset "^6.0.0"
    postcss-normalize-display-values "^6.0.0"
    postcss-normalize-positions "^6.0.0"
    postcss-normalize-repeat-style "^6.0.0"
    postcss-normalize-string "^6.0.0"
    postcss-normalize-timing-functions "^6.0.0"
    postcss-normalize-unicode "^6.0.0"
    postcss-normalize-url "^6.0.0"
    postcss-normalize-whitespace "^6.0.0"
    postcss-ordered-values "^6.0.0"
    postcss-reduce-initial "^6.0.0"
    postcss-reduce-transforms "^6.0.0"
    postcss-svgo "^6.0.0"
    postcss-unique-selectors "^6.0.0"

cssnano-utils@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/cssnano-utils/-/cssnano-utils-4.0.0.tgz#d1da885ec04003ab19505ff0e62e029708d36b08"
  integrity sha512-Z39TLP+1E0KUcd7LGyF4qMfu8ZufI0rDzhdyAMsa/8UyNUU8wpS0fhdBxbQbv32r64ea00h4878gommRVg2BHw==

cssnano@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/cssnano/-/cssnano-6.0.0.tgz#53f4cb81101cccba0809fad779f006b5d44925ee"
  integrity sha512-RGlcbzGhzEBCHuQe3k+Udyj5M00z0pm9S+VurHXFEOXxH+y0sVrJH2sMzoyz2d8N1EScazg+DVvmgyx0lurwwA==
  dependencies:
    cssnano-preset-default "^6.0.0"
    lilconfig "^2.1.0"

csso@^5.0.5:
  version "5.0.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/csso/-/csso-5.0.5.tgz#f9b7fe6cc6ac0b7d90781bb16d5e9874303e2ca6"
  integrity sha512-0LrrStPOdJj+SPCCrGhzryycLjwcgUSHBtxNA8aIDxf0GLsRh1cKYhB00Gd1lDOS4yGH69+SNn13+TWbVHETFQ==
  dependencies:
    css-tree "~2.2.0"

csstype@^2.2.0:
  version "2.6.10"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/csstype/-/csstype-2.6.10.tgz#e63af50e66d7c266edb6b32909cfd0aabe03928b"
  integrity sha1-5jr1DmbXwmbttrMpCc/Qqr4Dkos=

debug@2.6.9, debug@^2.2.0, debug@^2.3.3:
  version "2.6.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/debug/-/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^3.1.1:
  version "3.2.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/debug/-/debug-3.2.7.tgz#72580b7e9145fb39b6676f9c5e5fb100b934179a"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

debug@^4.1.0, debug@^4.1.1:
  version "4.3.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/debug/-/debug-4.3.3.tgz#04266e0b70a98d4462e6e288e38259213332b664"
  integrity sha512-/zxw5+vh1Tfv+4Qn7a5nsbcJKPaSvCDhojn6FEl9vupwK2VCSDtEiEtqr8DFtzYFOdz63LBkxec7DYuc2jon6Q==
  dependencies:
    ms "2.1.2"

decamelize@^1.0.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/decamelize/-/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/decode-uri-component/-/decode-uri-component-0.2.0.tgz#eb3913333458775cb84cd1a1fae062106bb87545"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

deep-equal@^1.0.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/deep-equal/-/deep-equal-1.1.1.tgz#b5c98c942ceffaf7cb051e24e1434a25a2e6076a"
  integrity sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g==
  dependencies:
    is-arguments "^1.0.4"
    is-date-object "^1.0.1"
    is-regex "^1.0.4"
    object-is "^1.0.1"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.2.0"

deepmerge@^4.2.2:
  version "4.2.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/deepmerge/-/deepmerge-4.2.2.tgz#44d2ea3679b8f4d4ffba33f03d865fc1e7bf4955"
  integrity sha1-RNLqNnm49NT/ujPwPYZfwee/SVU=

default-gateway@^6.0.3:
  version "6.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/default-gateway/-/default-gateway-6.0.3.tgz#819494c888053bdb743edbf343d6cdf7f2943a71"
  integrity sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg==
  dependencies:
    execa "^5.0.0"

define-lazy-prop@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz#3f7ae421129bcaaac9bc74905c98a0009ec9ee7f"
  integrity sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==

define-properties@^1.1.2, define-properties@^1.1.3:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/define-properties/-/define-properties-1.1.3.tgz#cf88da6cbee26fe6db7094f61d870cbd84cee9f1"
  integrity sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/define-property/-/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/define-property/-/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/define-property/-/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

del@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/del/-/del-6.0.0.tgz#0b40d0332cea743f1614f818be4feb717714c952"
  integrity sha512-1shh9DQ23L16oXSZKB2JxpL7iMy2E0S9d517ptA1P8iw0alkPtQcrKH7ru31rYtKwF499HkTu+DRzq3TCKDFRQ==
  dependencies:
    globby "^11.0.1"
    graceful-fs "^4.2.4"
    is-glob "^4.0.1"
    is-path-cwd "^2.2.0"
    is-path-inside "^3.0.2"
    p-map "^4.0.0"
    rimraf "^3.0.2"
    slash "^3.0.0"

depd@~1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/depd/-/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/destroy/-/destroy-1.0.4.tgz#978857442c44749e4206613e37946205826abd80"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

detect-node@^2.0.4:
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/detect-node/-/detect-node-2.0.4.tgz#014ee8f8f669c5c58023da64b8179c083a28c46c"
  integrity sha512-ZIzRpLJrOj7jjP2miAtgqIfmzbxa4ZOr5jJc601zklsfEx9oTzmmj2nVpIPRpNlRTIh8lc1kyViIY7BWSGNmKw==

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/dir-glob/-/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/dns-equal/-/dns-equal-1.0.0.tgz#b39e7f1da6eb0a75ba9c17324b34753c47e0654d"
  integrity sha512-z+paD6YUQsk+AbGCEM4PrOXSss5gd66QfcVBFTKR/HpFL9jCqikS94HYwKww6fQyO7IxrIIyUu+g0Ka9tUS2Cg==

dns-packet@^1.3.1:
  version "1.3.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/dns-packet/-/dns-packet-1.3.1.tgz#12aa426981075be500b910eedcd0b47dd7deda5a"
  integrity sha512-0UxfQkMhYAUaZI+xrNZOz/as5KgDU0M/fQ9b6SpkyLbk3GEswDi6PADJVaYJradtRVsRIlF1zLyOodbcTCDzUg==
  dependencies:
    ip "^1.1.0"
    safe-buffer "^5.0.1"

dns-txt@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/dns-txt/-/dns-txt-2.0.2.tgz#b91d806f5d27188e4ab3e7d107d881a1cc4642b6"
  integrity sha512-Ix5PrWjphuSoUXV/Zv5gaFHjnaJtb02F2+Si3Ht9dyJ87+Z/lMmy+dpNHtTGraNK958ndXq2i+GLkWsWHcKaBQ==
  dependencies:
    buffer-indexof "^1.0.0"

dom-css@^2.0.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/dom-css/-/dom-css-2.1.0.tgz#fdbc2d5a015d0a3e1872e11472bbd0e7b9e6a202"
  integrity sha512-w9kU7FAbaSh3QKijL6n59ofAhkkmMJ31GclJIz/vyQdjogfyxcB6Zf8CZyibOERI5o0Hxz30VmJS7+7r5fEj2Q==
  dependencies:
    add-px-to-style "1.0.0"
    prefix-style "2.0.1"
    to-camel-case "1.0.0"

dom-helpers@^3.2.0, dom-helpers@^3.2.1, dom-helpers@^3.4.0:
  version "3.4.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/dom-helpers/-/dom-helpers-3.4.0.tgz#e9b369700f959f62ecde5a6babde4bccd9169af8"
  integrity sha512-LnuPJ+dwqKDIyotW1VzmOZ5TONUN7CwkCR5hrgawTUbkBGYdeoNLZo6nNfGkCrjtE1nXXaj7iMMpDa8/d9WoIA==
  dependencies:
    "@babel/runtime" "^7.1.2"

dom-serializer@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/dom-serializer/-/dom-serializer-2.0.0.tgz#e41b802e1eedf9f6cae183ce5e622d789d7d8e53"
  integrity sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.2"
    entities "^4.2.0"

domain-browser@^1.1.1:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/domain-browser/-/domain-browser-1.2.0.tgz#3d31f50191a6749dd1375a7f522e823d42e54eda"
  integrity sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=

domelementtype@^2.3.0:
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/domelementtype/-/domelementtype-2.3.0.tgz#5c45e8e869952626331d7aab326d01daf65d589d"
  integrity sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==

domhandler@^5.0.1, domhandler@^5.0.2:
  version "5.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/domhandler/-/domhandler-5.0.3.tgz#cc385f7f751f1d1fc650c21374804254538c7d31"
  integrity sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==
  dependencies:
    domelementtype "^2.3.0"

domutils@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/domutils/-/domutils-3.0.1.tgz#696b3875238338cb186b6c0612bd4901c89a4f1c"
  integrity sha512-z08c1l761iKhDFtfXO04C7kTdPBLi41zwOZl00WS8b5eiaebNpY00HKbztwBq+e3vyqWNwWF3mP9YLUeqIrF+Q==
  dependencies:
    dom-serializer "^2.0.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.1"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ee-first/-/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

electron-to-chromium@^1.4.284:
  version "1.4.374"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/electron-to-chromium/-/electron-to-chromium-1.4.374.tgz#091b2de9d80b970f9b5e689675ea62622cd1d74b"
  integrity sha512-dNP9tQNTrjgVlSXMqGaj0BdrCS+9pcUvy5/emB6x8kh0YwCoDZ0Z4ce1+7aod+KhybHUd5o5LgKrc5al4kVmzQ==

electron-to-chromium@^1.4.71:
  version "1.4.72"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/electron-to-chromium/-/electron-to-chromium-1.4.72.tgz#19b871f1da8be8199b2330d694fc84fcdb72ecd9"
  integrity sha512-9LkRQwjW6/wnSfevR21a3k8sOJ+XWSH7kkzs9/EUenKmuDkndP3W9y1yCZpOxufwGbX3JV8glZZSDb4o95zwXQ==

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/emojis-list/-/emojis-list-2.1.0.tgz#4daa4d9db00f9819880c79fa457ae5b09a1fd389"
  integrity sha1-TapNnbAPmBmIDHn6RXrlsJof04k=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/emojis-list/-/emojis-list-3.0.0.tgz#5570662046ad29e2e916e71aae260abdff4f6a78"
  integrity sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/encodeurl/-/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

encoding@^0.1.11:
  version "0.1.12"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/encoding/-/encoding-0.1.12.tgz#538b66f3ee62cd1ab51ec323829d1f9480c74beb"
  integrity sha512-bl1LAgiQc4ZWr++pNYUdRe/alecaHFeHxIJ/pNciqGdKXghaTCOwKkbKp6ye7pKZGu/GcaSXFk8PBVhgs+dJdA==
  dependencies:
    iconv-lite "~0.4.13"

enhanced-resolve@^5.0.0:
  version "5.9.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/enhanced-resolve/-/enhanced-resolve-5.9.0.tgz#49ac24953ac8452ed8fed2ef1340fc8e043667ee"
  integrity sha512-weDYmzbBygL7HzGGS26M3hGQx68vehdEg6VUmqSOaFzXExFqlnKuSvsEJCVGQHScS8CQMbrAqftT+AzzHNt/YA==
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

enhanced-resolve@^5.13.0:
  version "5.13.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/enhanced-resolve/-/enhanced-resolve-5.13.0.tgz#26d1ecc448c02de997133217b5c1053f34a0a275"
  integrity sha512-eyV8f0y1+bzyfh8xAwW/WTSZpLbjhqc4ne9eGSH4Zo2ejdyiNG9pU6mf9DG8a7+Auk6MFTlNOT4Y2y/9k8GKVg==
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

enhanced-resolve@~0.9.0:
  version "0.9.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/enhanced-resolve/-/enhanced-resolve-0.9.1.tgz#4d6e689b3725f86090927ccc86cd9f1635b89e2e"
  integrity sha1-TW5omzcl+GCQknzMhs2fFjW4ni4=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.2.0"
    tapable "^0.1.8"

entities@^4.2.0:
  version "4.5.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/entities/-/entities-4.5.0.tgz#5d268ea5e7113ec74c4d033b79ea5a35a488fb48"
  integrity sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==

envinfo@^7.7.3:
  version "7.8.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/envinfo/-/envinfo-7.8.1.tgz#06377e3e5f4d379fea7ac592d5ad8927e0c4d475"
  integrity sha512-/o+BXHmB7ocbHEAs6F2EnG0ogybVVUdkRunTT2glZU9XAaGmhqskrvKwqXuDfNjEO0LZKWdejEEpnq8aM0tOaw==

errno@^0.1.3:
  version "0.1.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/errno/-/errno-0.1.7.tgz#4684d71779ad39af177e3f007996f7c67c852618"
  integrity sha1-RoTXF3mtOa8Xfj8AeZb3xnyFJhg=
  dependencies:
    prr "~1.0.1"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/error-ex/-/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.17.0-next.1, es-abstract@^1.17.5:
  version "1.17.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/es-abstract/-/es-abstract-1.17.5.tgz#d8c9d1d66c8981fb9200e2251d799eee92774ae9"
  integrity sha512-BR9auzDbySxOcfog0tLECW8l28eRGpDpU3Dm3Hp4q/N+VtLTmyj4EUN088XZWQDW/hzj6sYRDXeOFsaAODKvpg==
  dependencies:
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"
    is-callable "^1.1.5"
    is-regex "^1.0.5"
    object-inspect "^1.7.0"
    object-keys "^1.1.1"
    object.assign "^4.1.0"
    string.prototype.trimleft "^2.1.1"
    string.prototype.trimright "^2.1.1"

es-module-lexer@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/es-module-lexer/-/es-module-lexer-1.2.1.tgz#ba303831f63e6a394983fde2f97ad77b22324527"
  integrity sha512-9978wrXM50Y4rTMmW5kXIC09ZdXQZqkE4mxhwkd8VbzsGkXGPgV4zWuqQJgCEzYngdo2dYDa0l8xhX4fkSwJSg==

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/es-to-primitive/-/es-to-primitive-1.2.1.tgz#e55cd4c9cdc188bcefb03b366c736323fc5c898a"
  integrity sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/escalade/-/escalade-3.1.1.tgz#d8cfdc7000965c5a0174b4a82eaa5c0552742e40"
  integrity sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/escape-html/-/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

eslint-scope@5.1.1:
  version "5.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/eslint-scope/-/eslint-scope-5.1.1.tgz#e786e59a66cb92b3f6c1fb0d508aab174848f48c"
  integrity sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/esrecurse/-/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/estraverse/-/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/estraverse/-/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/esutils/-/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/etag/-/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

eventemitter3@^2.0.3:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/eventemitter3/-/eventemitter3-2.0.3.tgz#b5e1079b59fb5e1ba2771c0a993be060a58c99ba"
  integrity sha512-jLN68Dx5kyFHaePoXWPsCGW5qdyZQtLYHkxkg02/Mz6g0kYpDx4FyP6XfArhQdlOC4b8Mv+EMxPo/8La7Tzghg==

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/eventemitter3/-/eventemitter3-4.0.7.tgz#2de9b68f6528d5644ef5c59526a1b4a07306169f"
  integrity sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==

events@^1.0.0:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/events/-/events-1.1.1.tgz#9ebdb7635ad099c70dcc4c2a1f5004288e8bd924"
  integrity sha1-nr23Y1rQmccNzEwqH1AEKI6L2SQ=

events@^3.2.0:
  version "3.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/events/-/events-3.3.0.tgz#31a95ad0a924e2d2c419a813aeb2c4e878ea7400"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

execa@^5.0.0:
  version "5.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/execa/-/execa-5.1.1.tgz#f80ad9cbf4298f7bd1d4c9555c21e93741c411dd"
  integrity sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exenv@^1.2.0:
  version "1.2.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/exenv/-/exenv-1.2.2.tgz#2ae78e85d9894158670b03d47bec1f03bd91bb9d"
  integrity sha512-Z+ktTxTwv9ILfgKCk32OX3n/doe+OcLTRtqK9pcL+JsP3J1/VW8Uvl4ZjLlKqeW4rzK4oesDOGMEMRIZqtP4Iw==

expand-brackets@^0.1.4:
  version "0.1.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/expand-brackets/-/expand-brackets-0.1.5.tgz#df07284e342a807cd733ac5af72411e581d1177b"
  integrity sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s=
  dependencies:
    is-posix-bracket "^0.1.0"

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/expand-brackets/-/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expand-range@^1.8.1:
  version "1.8.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/expand-range/-/expand-range-1.8.2.tgz#a299effd335fe2721ebae8e257ec79644fc85337"
  integrity sha1-opnv/TNf4nIeuujiV+x5ZE/IUzc=
  dependencies:
    fill-range "^2.1.0"

express@^4.17.1:
  version "4.17.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/express/-/express-4.17.1.tgz#4491fc38605cf51f8629d39c2b5d026f98a4c134"
  integrity sha512-mHJ9O79RqluphRrcw2X/GTh3k9tVv8YcoyY4Kkh4WDMUYKRZUq0h1o0w2rrrxBqM7VoeUVqgb27xlEMXTnYt4g==
  dependencies:
    accepts "~1.3.7"
    array-flatten "1.1.1"
    body-parser "1.19.0"
    content-disposition "0.5.3"
    content-type "~1.0.4"
    cookie "0.4.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.2"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "~1.1.2"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.5"
    qs "6.7.0"
    range-parser "~1.2.1"
    safe-buffer "5.1.2"
    send "0.17.1"
    serve-static "1.14.1"
    setprototypeof "1.1.1"
    statuses "~1.5.0"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/extend-shallow/-/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/extend-shallow/-/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@^3.0.2:
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/extend/-/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

extglob@^0.3.1:
  version "0.3.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/extglob/-/extglob-0.3.2.tgz#2e18ff3d2f49ab2765cec9023f011daa8d8349a1"
  integrity sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE=
  dependencies:
    is-extglob "^1.0.0"

extglob@^2.0.4:
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/extglob/-/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fast-diff/-/fast-diff-1.1.2.tgz#4b62c42b8e03de3f848460b639079920695d0154"
  integrity sha512-KaJUt+M9t1qaIteSvjc6P3RbMdXsNhK61GRftR6SNxqmhthcd9MGIi4T+o0jD8LUSpSnSKXE20nLtJ3fOHxQig==

fast-glob@^3.2.9:
  version "3.2.11"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fast-glob/-/fast-glob-3.2.11.tgz#a1172ad95ceb8a16e20caa5c5e56480e5129c1d9"
  integrity sha512-xrO3+1bxSo3ZVHAnqzyuewYT6aMFHRAd4Kcs92MAonjwQZLsK9d0SF1IyQ3k5PoirxTW0Oe/RqFgMQ6TcNE5Ew==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fastest-levenshtein@^1.0.12:
  version "1.0.12"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fastest-levenshtein/-/fastest-levenshtein-1.0.12.tgz#9990f7d3a88cc5a9ffd1f1745745251700d497e2"
  integrity sha512-On2N+BpYJ15xIC974QNVuYGMOlEVt4s0EOI3wwMqOmK1fdDY+FN/zltPV8vosq4ad4c/gJ1KHScUn/6AWIgiow==

fastq@^1.6.0:
  version "1.13.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fastq/-/fastq-1.13.0.tgz#616760f88a7526bdfc596b7cab8c18938c36b98c"
  integrity sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw==
  dependencies:
    reusify "^1.0.4"

faye-websocket@^0.11.3:
  version "0.11.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/faye-websocket/-/faye-websocket-0.11.4.tgz#7f0d9275cfdd86a1c963dc8b65fcc451edcbb1da"
  integrity sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g==
  dependencies:
    websocket-driver ">=0.5.1"

fbjs@^0.8.16, fbjs@^0.8.4:
  version "0.8.17"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fbjs/-/fbjs-0.8.17.tgz#c4d598ead6949112653d6588b01a5cdcd9f90fdd"
  integrity sha512-Q1MvLM+cllhk7lv9Pci7dIdpC5W8MS6W0slOWizKG66+te0m9/YqjfIt41rKmH+Nqz+mMiGgdEVonDadPyKnug==
  dependencies:
    core-js "^1.0.0"
    isomorphic-fetch "^2.1.1"
    loose-envify "^1.0.0"
    object-assign "^4.1.0"
    promise "^7.1.1"
    setimmediate "^1.0.5"
    ua-parser-js "^0.7.18"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz#553a7b8446ff6f684359c445f1e37a05dacc33dd"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

filename-regex@^2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/filename-regex/-/filename-regex-2.0.1.tgz#c1c4b9bee3e09725ddb106b75c1e301fe2f18b26"
  integrity sha1-wcS5vuPglyXdsQa3XB4wH+LxiyY=

fill-range@^2.1.0:
  version "2.2.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fill-range/-/fill-range-2.2.4.tgz#eb1e773abb056dcd8df2bfdf6af59b8b3a936565"
  integrity sha512-cnrcCbj01+j2gTG921VZPnHbjmdAf8oQV/iGeV2kZxGSyfYjjTyY79ErsK1WJWMpw6DaApEX72binqJE+/d+5Q==
  dependencies:
    is-number "^2.1.0"
    isobject "^2.0.0"
    randomatic "^3.0.0"
    repeat-element "^1.1.2"
    repeat-string "^1.5.2"

fill-range@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fill-range/-/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fill-range/-/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@~1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/finalhandler/-/finalhandler-1.1.2.tgz#b7e7d000ffd11938d0fdb053506f6ebabe9f587d"
  integrity sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA==
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-cache-dir@^3.3.1:
  version "3.3.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/find-cache-dir/-/find-cache-dir-3.3.2.tgz#b30c5b6eff0730731aea9bbd9dbecbd80256d64b"
  integrity sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig==
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-up@^4.0.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/find-up/-/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

follow-redirects@^1.0.0:
  version "1.14.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/follow-redirects/-/follow-redirects-1.14.9.tgz#dd4ea157de7bfaf9ea9b3fbd85aa16951f78d8d7"
  integrity sha512-MQDfihBQYMcyy5dhRDJUHcw7lb2Pv/TuE6xP1vyraLukNDHKbDxDNaOE3NbCAdKQApno+GPRyo1YAp89yCjK4w==

for-in@^1.0.1, for-in@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/for-in/-/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

for-own@^0.1.4:
  version "0.1.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/for-own/-/for-own-0.1.5.tgz#5265c681a4f294dabbf17c9509b6763aa84510ce"
  integrity sha1-UmXGgaTylNq78XyVCbZ2OqhFEM4=
  dependencies:
    for-in "^1.0.1"

fork-ts-checker-webpack-plugin@^7.2.1:
  version "7.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-7.2.1.tgz#c37a538e12730fe11fd725bcf0fce29487950833"
  integrity sha512-uOfQdg/iQ8iokQ64qcbu8iZb114rOmaKLQFu7hU14/eJaKgsP91cQ7ts7v2iiDld6TzDe84Meksha8/MkWiCyw==
  dependencies:
    "@babel/code-frame" "^7.16.7"
    chalk "^4.1.2"
    chokidar "^3.5.3"
    cosmiconfig "^7.0.1"
    deepmerge "^4.2.2"
    fs-extra "^10.0.0"
    memfs "^3.4.1"
    minimatch "^3.0.4"
    schema-utils "4.0.0"
    semver "^7.3.5"
    tapable "^2.2.1"

forwarded@~0.1.2:
  version "0.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/forwarded/-/forwarded-0.1.2.tgz#98c23dab1175657b8c0573e8ceccd91b0ff18c84"
  integrity sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fragment-cache/-/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fresh/-/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-extra@^10.0.0:
  version "10.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fs-extra/-/fs-extra-10.0.1.tgz#27de43b4320e833f6867cc044bfce29fdf0ef3b8"
  integrity sha512-NbdoVMZso2Lsrn/QwLXOy6rm0ufY2zEOKCDzJR/0kBsb0E6qed0P3iYK+Ath3BfvXEeu4JhEtXLgILx5psUfag==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-monkey@1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fs-monkey/-/fs-monkey-1.0.3.tgz#ae3ac92d53bb328efe0e9a1d9541f6ad8d48e2d3"
  integrity sha512-cybjIfiiE+pTWicSCLFHSrXZ6EilF30oh91FDP9S2B051prEa7QWfrVTQm10/dDpswBDXZugPa1Ogu8Yh+HV0Q==

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^1.0.0:
  version "1.2.12"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fsevents/-/fsevents-1.2.12.tgz#db7e0d8ec3b0b45724fd4d83d43554a8f1f0de5c"
  integrity sha512-Ggd/Ktt7E7I8pxZRbGIs7vwqAPscSESMrCSkx2FtWeqmheJgCo2R74fTsZFCifr0VTPwqRpPv17+6b8Zp7th0Q==
  dependencies:
    bindings "^1.5.0"
    nan "^2.12.1"

fsevents@~2.3.2:
  version "2.3.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/fsevents/-/fsevents-2.3.2.tgz#8a526f78b8fdf4623b709e0b975c52c24c02fd1a"
  integrity sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/function-bind/-/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

gces-cronstrue@0.0.3:
  version "0.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/gces-cronstrue/-/gces-cronstrue-0.0.3.tgz#854fc85c72fdbbd0e3027aca3eda516381350c47"
  integrity sha1-hU/IXHL9u9DjAnrKPtpRY4E1DEc=

gces-ellipsis@7.1.1:
  version "7.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/gces-ellipsis/-/gces-ellipsis-7.1.1.tgz#e9a431ca9fef258ec4838caf58a0fe47054d71fe"
  integrity sha512-WuacxIDeBwF+C5DgA92QJG3KtPfSaXYRql56HV5skVz+ru7J00i5YBEmkyNi0/BtwWdfilpe0EswBgwto+xy8Q==

gces-ellipsis@7.1.3:
  version "7.1.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/gces-ellipsis/-/gces-ellipsis-7.1.3.tgz#ca2143db977482d370d4d0c3a2839813e22ebe81"
  integrity sha512-zSGyQhAQVfBBAYxr9UzCd+lmj2d4uOihKoO7Z6aYey4Wfzf3o5HDPxgKaf0uG88oLytcLws+ElZFoJIH3WjRTg==

gces-react-custom-scrollbars@0.0.3:
  version "0.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/gces-react-custom-scrollbars/-/gces-react-custom-scrollbars-0.0.3.tgz#5cc11a15b25ad475705046f3776c4e66bf7604cd"
  integrity sha512-6W//lqqz1Bk+UdrciYUO0n/SJnerMF1ssBj+fgjRJuCSn8DRnws5jay+8cny9cbferF9LMrgABe1SA/ZhciR0g==
  dependencies:
    dom-css "^2.0.0"
    prop-types "^15.5.10"
    raf "^3.1.0"

gces-react-datetime@1.0.12:
  version "1.0.12"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/gces-react-datetime/-/gces-react-datetime-1.0.12.tgz#df8b22ef94bbb532e68b4fd59a016d4c2764e28e"
  integrity sha1-34si75S7tTLmi0/VmgFtTCdk4o4=
  dependencies:
    create-react-class "^15.5.2"
    object-assign "^3.0.0"
    prop-types "^15.5.7"
    react-onclickoutside "^6.5.0"

gces-react-grid@8.0.3:
  version "8.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/gces-react-grid/-/gces-react-grid-8.0.3.tgz#01ef4283940c8f1b6f7342b6f95fd75ae7a06d9e"
  integrity sha1-Ae9Cg5QMjxtvc0K2+V/XWuegbZ4=
  dependencies:
    classnames "^2.2.6"
    lodash "^4.17.15"
    resize-observer-polyfill "^1.5.1"

gces-ui@0.8.16:
  version "0.8.16"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/gces-ui/-/gces-ui-0.8.16.tgz#342e2fe4e5452e77fe708795c7a2c1a1a35e1e34"
  integrity sha1-NC4v5OVFLnf+cIeVx6LBoaNeHjQ=
  dependencies:
    classnames "2.2.5"
    gces-ellipsis "7.1.1"
    gces-react-custom-scrollbars "0.0.3"
    gces-react-datetime "1.0.12"
    immutability-helper "2.6.6"
    react-datetime "2.16.2"
    react-modal "3.1.11"
    react-quill "1.3.3"
    resize-observer-polyfill "^1.5.1"
    wyn-variables "1.0.12"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/gensync/-/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/get-stream/-/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
  integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/get-value/-/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

glob-base@^0.3.0:
  version "0.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/glob-base/-/glob-base-0.3.0.tgz#dbb164f6221b1c0b1ccf82aea328b497df0ea3c4"
  integrity sha1-27Fk9iIbHAscz4Kuoyi0l98Oo8Q=
  dependencies:
    glob-parent "^2.0.0"
    is-glob "^2.0.0"

glob-parent@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/glob-parent/-/glob-parent-2.0.0.tgz#81383d72db054fcccf5336daa902f182f6edbb28"
  integrity sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg=
  dependencies:
    is-glob "^2.0.0"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/glob-parent/-/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/glob-to-regexp/-/glob-to-regexp-0.1.0.tgz#e0369d426578fd456d47dc23b09de05c1da9ea5d"
  integrity sha512-zNKwUvfFs4IbHMLzBDl4v5YbFNs64e4yGkptl4DncCYwmhMQORQflvs7XsEv50+M5bJqbgjBqnV+zZ8vF490yQ==

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz#c75297087c851b9a578bd217dd59a92f59fe546e"
  integrity sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=

glob@^7.1.3:
  version "7.1.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/glob/-/glob-7.1.6.tgz#141f33b81a7c2492e125594307480c46679278a6"
  integrity sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/globals/-/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globby@^11.0.1:
  version "11.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/globby/-/globby-11.1.0.tgz#bd4be98bb042f83d796f7e3811991fbe82a0d34b"
  integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

graceful-fs@^4.1.11, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.4, graceful-fs@^4.2.6, graceful-fs@^4.2.9:
  version "4.2.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/graceful-fs/-/graceful-fs-4.2.9.tgz#041b05df45755e587a24942279b9d113146e1c96"
  integrity sha512-NtNxqUcXgpW2iMrfqSfR73Glt39K+BLwWsPs94yR63v45T0Wbej7eRmL5cWfwEgqXnmjQp3zaJTshdRW/qC2ZQ==

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/handle-thing/-/handle-thing-2.0.1.tgz#857f79ce359580c340d43081cc648970d0bb234e"
  integrity sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg==

has-flag@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/has-flag/-/has-flag-1.0.0.tgz#9d9e793165ce017a00f00418c43f942a7b1d11fa"
  integrity sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/has-flag/-/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/has-flag/-/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-symbols@^1.0.0, has-symbols@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/has-symbols/-/has-symbols-1.0.1.tgz#9f5214758a44196c406d9bd76cebf81ec2dd31e8"
  integrity sha1-n1IUdYpEGWxAbZvXbOv4HsLdMeg=

has-value@^0.3.1:
  version "0.3.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/has-value/-/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/has-value/-/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/has-values/-/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/has-values/-/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/has/-/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

history@^4.7.2:
  version "4.10.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/history/-/history-4.10.1.tgz#33371a65e3a83b267434e2b3f3b1b4c58aad4cf3"
  integrity sha1-MzcaZeOoOyZ0NOKz87G0xYqtTPM=
  dependencies:
    "@babel/runtime" "^7.1.2"
    loose-envify "^1.2.0"
    resolve-pathname "^3.0.0"
    tiny-invariant "^1.0.2"
    tiny-warning "^1.0.0"
    value-equal "^1.0.1"

hoist-non-react-statics@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/hoist-non-react-statics/-/hoist-non-react-statics-1.2.0.tgz#aa448cf0986d55cc40773b17174b7dd066cb7cfb"
  integrity sha512-r8huvKK+m+VraiRipdZYc+U4XW43j6OFG/oIafe7GfDbRpCduRoX9JI/DRxqgtBSCeL+et6N6ibZoedHS2NyOQ==

hoist-non-react-statics@^2.3.1, hoist-non-react-statics@^2.5.0:
  version "2.5.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/hoist-non-react-statics/-/hoist-non-react-statics-2.5.5.tgz#c5903cf409c0dfd908f388e619d86b9c1174cb47"
  integrity sha512-rqcy4pJo55FTTLWt+bU8ukscqHeE/e9KWvsOW2b/a3afxQZhwkQdT1rPPCJ0rYXdj4vNcasY8zHTH+jF/qStxw==

hoist-non-react-statics@^3.3.0:
  version "3.3.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz#ece0acaf71d62c2969c2ec59feff42a4b1a85b45"
  integrity sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=
  dependencies:
    react-is "^16.7.0"

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/hpack.js/-/hpack.js-2.1.6.tgz#87774c0949e513f42e84575b3c45681fade2a0b2"
  integrity sha512-zJxVehUdMGIKsRaNt7apO2Gqp0BdqW5yaiGHXXmbpvxgBYVZnAql+BJb4RO5ad2MgpbZKn5G6nMnegrH1FcNYQ==
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

html-entities@^2.3.2:
  version "2.3.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/html-entities/-/html-entities-2.3.2.tgz#760b404685cb1d794e4f4b744332e3b00dcfe488"
  integrity sha512-c3Ab/url5ksaT0WyleslpBEthOzWhrjQbg75y7XUsfSzi3Dgzt0l8w5e7DylRn15MTlMMD58dTfzddNS2kcAjQ==

html-parse-stringify2@2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/html-parse-stringify2/-/html-parse-stringify2-2.0.1.tgz#dc5670b7292ca158b7bc916c9a6735ac8872834a"
  integrity sha512-wMKQ3aJ/dwXzDHPpA7XgsRXXCkEhHkAF6Ioh7D51lgZO7Qy0LmcFddC9TI/qNQJvSM1KL8KbcR3FtuybsrzFlQ==
  dependencies:
    void-elements "^2.0.1"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/http-deceiver/-/http-deceiver-1.2.7.tgz#fa7168944ab9a519d337cb0bec7284dc3e723d87"
  integrity sha512-LmpOGxTfbpgtGVxJrj5k7asXHCgNZp5nLfp+hWc8QQRqtb7fUy6kRY3BO1h9ddF6yIPYUARgxGOwB42DnxIaNw==

http-errors@1.7.2, http-errors@~1.7.2:
  version "1.7.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/http-errors/-/http-errors-1.7.2.tgz#4f5029cf13239f31036e5b2e55292bcfbcc85c8f"
  integrity sha512-uUQBt3H/cSIVfch6i1EuPNy/YsRSOUBXTVfZ+yR7Zjez3qjBz6i9+i4zjNaoqcoFVI4lQJ5plg63TvGfRSDCRg==
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/http-errors/-/http-errors-1.6.3.tgz#8b55680bb4be283a0b5bf4ea2e38580be1d9320d"
  integrity sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-parser-js@>=0.5.1:
  version "0.5.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/http-parser-js/-/http-parser-js-0.5.5.tgz#d7c30d5d3c90d865b4a2e870181f9d6f22ac7ac5"
  integrity sha512-x+JVEkO2PoM8qqpbPbOL3cqHPwerep7OwzK7Ay+sMQjKzaKCqWvjoXm5tqMP9tXWWTnTzAjIhXg+J99XYuPhPA==

http-proxy-middleware@^2.0.0:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/http-proxy-middleware/-/http-proxy-middleware-2.0.3.tgz#5df04f69a89f530c2284cd71eeaa51ba52243289"
  integrity sha512-1bloEwnrHMnCoO/Gcwbz7eSVvW50KPES01PecpagI+YLNLci4AcuKJrujW4Mc3sBLpFxMSlsLNHS5Nl/lvrTPA==
  dependencies:
    "@types/http-proxy" "^1.17.8"
    http-proxy "^1.18.1"
    is-glob "^4.0.1"
    is-plain-obj "^3.0.0"
    micromatch "^4.0.2"

http-proxy@^1.18.1:
  version "1.18.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/http-proxy/-/http-proxy-1.18.1.tgz#401541f0534884bbf95260334e72f88ee3976549"
  integrity sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

https-browserify@0.0.1:
  version "0.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/https-browserify/-/https-browserify-0.0.1.tgz#3f91365cabe60b77ed0ebba24b454e3e09d95a82"
  integrity sha1-P5E2XKvmC3ftDruiS0VOPgnZWoI=

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/human-signals/-/human-signals-2.1.0.tgz#dc91fcba42e4d06e4abaed33b3e7a3c02f514ea0"
  integrity sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==

i18next-browser-languagedetector@^2.2.0:
  version "2.2.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/i18next-browser-languagedetector/-/i18next-browser-languagedetector-2.2.4.tgz#b02412d7ab15d7d74e1b1317d67d8a244b219ee3"
  integrity sha512-wPbtH18FdOuB245I8Bhma5/XSDdN/HpYlX+wga1eMy+slhaFQSnrWX6fp+aYSL2eEuj0RlfHeEVz6Fo/lxAj6A==

i18next@^20.6.1:
  version "20.6.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/i18next/-/i18next-20.6.1.tgz#535e5f6e5baeb685c7d25df70db63bf3cc0aa345"
  integrity sha512-yCMYTMEJ9ihCwEQQ3phLo7I/Pwycf8uAx+sRHwwk5U9Aui/IZYgQRyMqXafQOw5QQ7DM1Z+WyEXWIqSuJHhG2A==
  dependencies:
    "@babel/runtime" "^7.12.0"

iconv-lite@0.4.24, iconv-lite@~0.4.13:
  version "0.4.24"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/iconv-lite/-/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/iconv-lite/-/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

icss-utils@^5.0.0, icss-utils@^5.1.0:
  version "5.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/icss-utils/-/icss-utils-5.1.0.tgz#c6be6858abd013d768e98366ae47e25d5887b1ae"
  integrity sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==

ieee754@^1.1.4:
  version "1.1.13"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ieee754/-/ieee754-1.1.13.tgz#ec168558e95aa181fd87d37f55c32bbcb6708b84"
  integrity sha1-7BaFWOlaoYH9h9N/VcMrvLZwi4Q=

ignore@^5.2.0:
  version "5.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ignore/-/ignore-5.2.0.tgz#6d3bac8fa7fe0d45d9f9be7bac2fc279577e345a"
  integrity sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ==

immutability-helper@2.6.4:
  version "2.6.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/immutability-helper/-/immutability-helper-2.6.4.tgz#a931aef97257fcb6d2b5456de652ab6e3bba8408"
  integrity sha512-jhFwZoBOOzfxKXGO86G0zrD0dHEUgAfFhDMOBRizonX2nUtWAG8vepjTVJkmJDoKV6XtPlm/21gNXcToBe0D/A==
  dependencies:
    invariant "^2.2.0"

immutability-helper@2.6.6:
  version "2.6.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/immutability-helper/-/immutability-helper-2.6.6.tgz#9b384c240d65257133c155086e16f678ca563b05"
  integrity sha512-CdLyZ9QuiWGk884SKhRvi8xjtB2PYMCBwa6fc8wZ5QltrdFEhwGz0upikzvjxjrDbsGs7qhgIUIMvI2YFywihA==
  dependencies:
    invariant "^2.2.0"

import-fresh@^3.2.1:
  version "3.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/import-fresh/-/import-fresh-3.3.0.tgz#37162c25fcb9ebaa2e6e53d5b4d88ce17d9e0c2b"
  integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-local@^3.0.2:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/import-local/-/import-local-3.1.0.tgz#b4479df8a5fd44f6cdce24070675676063c95cb4"
  integrity sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/indent-string/-/indent-string-4.0.0.tgz#624f8f4497d619b2d9768531d58f4122854d7251"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

indexof@0.0.1:
  version "0.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/indexof/-/indexof-0.0.1.tgz#82dc336d232b9062179d05ab3293a66059fd435d"
  integrity sha1-gtwzbSMrkGIXnQWrMpOmYFn9Q10=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/inherits/-/inherits-2.0.1.tgz#b17d08d326b4423e568eff719f91b0b1cbdf69f1"
  integrity sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=

inherits@2.0.3:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/inherits/-/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

interactjs@^1.5.1:
  version "1.9.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/interactjs/-/interactjs-1.9.9.tgz#93914291eeb0edd5e252fb13d6de177ffb7993ab"
  integrity sha512-4WMas+Ms6qoATdpIFAdXpKRK9AYouNh6LqCq8B+wk0IpkPGoPCFnz3LDOlzhfZghniocVt+uK2FEFHaLwgCHAQ==
  dependencies:
    "@interactjs/interactjs" "1.9.9"
    "@interactjs/types" "1.9.9"

interpret@^0.6.4:
  version "0.6.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/interpret/-/interpret-0.6.6.tgz#fecd7a18e7ce5ca6abfb953e1f86213a49f1625b"
  integrity sha1-/s16GOfOXKar+5U+H4YhOknxYls=

interpret@^2.2.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/interpret/-/interpret-2.2.0.tgz#1a78a0b5965c40a5416d007ad6f50ad27c417df9"
  integrity sha512-Ju0Bz/cEia55xDwUWEa8+olFpCiQoypjnQySseKtmjNrnps3P+xfpUmGr90T7yjlVJmOtybRvPXhKMbHr+fWnw==

invariant@^2.2.0, invariant@^2.2.1, invariant@^2.2.2, invariant@^2.2.4:
  version "2.2.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/invariant/-/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
  integrity sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==
  dependencies:
    loose-envify "^1.0.0"

ip@^1.1.0:
  version "1.1.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ip/-/ip-1.1.5.tgz#bdded70114290828c0a039e72ef25f5aaec4354a"
  integrity sha512-rBtCAQAJm8A110nbwn6YdveUnuZH3WrC36IwkRXxDnq53JvXA2NVQvB7IHyKomxK1MJ4VDNw3UtFDdXQ+AvLYA==

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ipaddr.js/-/ipaddr.js-1.9.1.tgz#bff38543eeb8984825079ff3a2a8e6cbd46781b3"
  integrity sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==

ipaddr.js@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ipaddr.js/-/ipaddr.js-2.0.1.tgz#eca256a7a877e917aeb368b0a7497ddf42ef81c0"
  integrity sha512-1qTgH9NG+IIJ4yfKs2e6Pp1bZg8wbDbKHT21HrLIeYBTRLgMYKnMTPAuI3Lcs61nfx5h1xlXnbJtH1kX5/d/ng==

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-arguments@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-arguments/-/is-arguments-1.0.4.tgz#3faf966c7cba0ff437fb31f6250082fcf0448cf3"
  integrity sha512-xPh0Rmt8NE65sNzvyUmWgI1tz3mKq74lGA0mL8LYZcoIzKOzDh6HmrYm3d18k60nHerC8A9Km8kYu87zfSFnLA==

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-arrayish/-/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-binary-path/-/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898"
  integrity sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=
  dependencies:
    binary-extensions "^1.0.0"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-binary-path/-/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-buffer/-/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-callable@^1.1.4, is-callable@^1.1.5:
  version "1.1.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-callable/-/is-callable-1.1.5.tgz#f7e46b596890456db74e7f6e976cb3273d06faab"
  integrity sha512-ESKv5sMCJB2jnHTWZ3O5itG+O128Hsus4K4Qh1h2/cgn2vbgnLSVqfV46AeJA9D5EeeLa9w81KUXMtn34zhX+Q==

is-core-module@^2.8.1:
  version "2.8.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-core-module/-/is-core-module-2.8.1.tgz#f59fdfca701d5879d0a6b100a40aa1560ce27211"
  integrity sha512-SdNCUs284hr40hFTFP6l0IfZ/RSrMXF3qgoRHd3/79unUTvrFO/JoXwkGm+5J/Oe3E/b5GsnG330uUNgRpu1PA==
  dependencies:
    has "^1.0.3"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-date-object/-/is-date-object-1.0.2.tgz#bda736f2cd8fd06d32844e7743bfa7494c3bfd7e"
  integrity sha1-vac28s2P0G0yhE53Q7+nSUw7/X4=

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-descriptor/-/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-descriptor/-/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-docker@^2.0.0, is-docker@^2.1.1:
  version "2.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-docker/-/is-docker-2.2.1.tgz#33eeabe23cfe86f14bde4408a02c0cfb853acdaa"
  integrity sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==

is-dotfile@^1.0.0:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-dotfile/-/is-dotfile-1.0.3.tgz#a6a2f32ffd2dfb04f5ca25ecd0f6b83cf798a1e1"
  integrity sha1-pqLzL/0t+wT1yiXs0Pa4PPeYoeE=

is-equal-shallow@^0.1.3:
  version "0.1.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-equal-shallow/-/is-equal-shallow-0.1.3.tgz#2238098fc221de0bcfa5d9eac4c45d638aa1c534"
  integrity sha1-IjgJj8Ih3gvPpdnqxMRdY4qhxTQ=
  dependencies:
    is-primitive "^2.0.0"

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-extendable/-/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-extendable/-/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-extglob/-/is-extglob-1.0.0.tgz#ac468177c4943405a092fc8f29760c6ffc6206c0"
  integrity sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA=

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-extglob/-/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-glob@^2.0.0, is-glob@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-glob/-/is-glob-2.0.1.tgz#d096f926a3ded5600f3fdfd91198cb0888c2d863"
  integrity sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=
  dependencies:
    is-extglob "^1.0.0"

is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-glob/-/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-number@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-number/-/is-number-2.1.0.tgz#01fcbbb393463a548f2f466cce16dece49db908f"
  integrity sha1-Afy7s5NGOlSPL0ZszhbezknbkI8=
  dependencies:
    kind-of "^3.0.2"

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-number/-/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-number/-/is-number-4.0.0.tgz#0026e37f5454d73e356dfe6564699867c6a7f0ff"
  integrity sha1-ACbjf1RU1z41bf5lZGmYZ8an8P8=

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-number/-/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-path-cwd@^2.2.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-path-cwd/-/is-path-cwd-2.2.0.tgz#67d43b82664a7b5191fd9119127eb300048a9fdb"
  integrity sha512-w942bTcih8fdJPJmQHFzkS76NEP8Kzzvmw92cXsazb8intwLqPibPPdXf4ANdKV3rYMuuQYGIWtvz9JilB3NFQ==

is-path-inside@^3.0.2:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-path-inside/-/is-path-inside-3.0.3.tgz#d231362e53a07ff2b0e0ea7fed049161ffd16283"
  integrity sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==

is-plain-obj@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-plain-obj/-/is-plain-obj-3.0.0.tgz#af6f2ea14ac5a646183a5bbdb5baabbc156ad9d7"
  integrity sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA==

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-plain-object/-/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-posix-bracket@^0.1.0:
  version "0.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-posix-bracket/-/is-posix-bracket-0.1.1.tgz#3334dc79774368e92f016e6fbc0a88f5cd6e6bc4"
  integrity sha1-MzTceXdDaOkvAW5vvAqI9c1ua8Q=

is-primitive@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-primitive/-/is-primitive-2.0.0.tgz#207bab91638499c07b2adf240a41a87210034575"
  integrity sha1-IHurkWOEmcB7Kt8kCkGochADRXU=

is-regex@^1.0.4, is-regex@^1.0.5:
  version "1.0.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-regex/-/is-regex-1.0.5.tgz#39d589a358bf18967f726967120b8fc1aed74eae"
  integrity sha512-vlKW17SNq44owv5AQR3Cq0bQPEb8+kF3UKZ2fiZNOWtztYE5i0CzCZxFDwO58qAOWtxdBRVO/V5Qin1wjCqFYQ==
  dependencies:
    has "^1.0.3"

is-stream@^1.0.1:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-stream/-/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-stream/-/is-stream-2.0.1.tgz#fac1e3d53b97ad5a9d0ae9cef2389f5810a5c077"
  integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==

is-symbol@^1.0.2:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-symbol/-/is-symbol-1.0.3.tgz#38e1014b9e6329be0de9d24a414fd7441ec61937"
  integrity sha1-OOEBS55jKb4N6dJKQU/XRB7GGTc=
  dependencies:
    has-symbols "^1.0.1"

is-windows@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-windows/-/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^2.2.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/is-wsl/-/is-wsl-2.2.0.tgz#74a4c76e77ca9fd3f932f290c17ea326cd157271"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

isarray@0.0.1:
  version "0.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/isarray/-/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/isexe/-/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/isobject/-/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/isobject/-/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isomorphic-fetch@^2.1.1:
  version "2.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/isomorphic-fetch/-/isomorphic-fetch-2.2.1.tgz#611ae1acf14f5e81f729507472819fe9733558a9"
  integrity sha512-9c4TNAKYXM5PRyVcwUZrF3W09nQ+sO7+jydgs4ZGW9dhsLG2VOlISJABombdQqQRXCwuYG3sYV/puGf5rp0qmA==
  dependencies:
    node-fetch "^1.0.1"
    whatwg-fetch ">=0.10.0"

jest-util@^29.5.0:
  version "29.5.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/jest-util/-/jest-util-29.5.0.tgz#24a4d3d92fc39ce90425311b23c27a6e0ef16b8f"
  integrity sha512-RYMgG/MTadOr5t8KdhejfvUU82MxsCu5MF6KuDUHl+NuwzUt+Sm6jJWxTJVrDR1j5M/gJVCPKQEpWXY+yIQ6lQ==
  dependencies:
    "@jest/types" "^29.5.0"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-worker@^27.4.5:
  version "27.5.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/jest-worker/-/jest-worker-27.5.1.tgz#8d146f0900e8973b106b6f73cc1e9a8cb86f8db0"
  integrity sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest-worker@^29.4.3:
  version "29.5.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/jest-worker/-/jest-worker-29.5.0.tgz#bdaefb06811bd3384d93f009755014d8acb4615d"
  integrity sha512-NcrQnevGoSp4b5kg+akIpthoAFHxPBcb5P6mYPY0fUNT+sSvmtu6jlkEle3anczUKIKEbMxFimk9oTP/tpIPgA==
  dependencies:
    "@types/node" "*"
    jest-util "^29.5.0"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/js-tokens/-/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/jsesc/-/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/jsesc/-/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-parse-even-better-errors@^2.3.0, json-parse-even-better-errors@^2.3.1:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz#ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2"
  integrity sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==

json5-loader@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/json5-loader/-/json5-loader-4.0.1.tgz#6d17a1181e8f3c3d9204dca2a4ce4627306c8498"
  integrity sha1-bRehGB6PPD2SBNyipM5GJzBshJg=
  dependencies:
    json5 "^2.1.3"
    loader-utils "^2.0.0"
    schema-utils "^3.0.0"

json5@^0.5.0:
  version "0.5.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/json5/-/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821"
  integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=

json5@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/json5/-/json5-1.0.1.tgz#779fb0018604fa854eacbf6252180d83543e3dbe"
  integrity sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow==
  dependencies:
    minimist "^1.2.0"

json5@^2.1.2:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/json5/-/json5-2.2.0.tgz#2dfefe720c6ba525d9ebd909950f0515316c89a3"
  integrity sha1-Lf7+cgxrpSXZ69kJlQ8FFTFsiaM=
  dependencies:
    minimist "^1.2.5"

json5@^2.1.3:
  version "2.2.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/json5/-/json5-2.2.3.tgz#78cd6f1a19bdc12b73db5ad0c61efd66c1e29283"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/jsonfile/-/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

keycode@^2.1.7:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/keycode/-/keycode-2.2.0.tgz#3d0af56dc7b8b8e5cba8d0a97f107204eec22b04"
  integrity sha512-ps3I9jAdNtRpJrbBvQjpzyFbss/skHqzS+eu4RxKLaEAtFqkjZaB6TZMSivPbLxf4K7VI4SjR0P5mRCX5+Q25A==

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/kind-of/-/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/kind-of/-/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/kind-of/-/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/kind-of/-/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

klona@^2.0.4:
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/klona/-/klona-2.0.5.tgz#d166574d90076395d9963aa7a928fabb8d76afbc"
  integrity sha512-pJiBpiXMbt7dkzXe8Ghj/u4FfXOOa98fPW+bihOJ4SjnoijweJrNThJfd3ifXpXhREjpoF2mZVH1GfS9LV3kHQ==

lazy-cache@^1.0.3:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/lazy-cache/-/lazy-cache-1.0.4.tgz#a1d78fc3a50474cb80845d3b3b6e1da49a446e8e"
  integrity sha1-odePw6UEdMuAhF07O24dpJpEbo4=

lilconfig@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/lilconfig/-/lilconfig-2.1.0.tgz#78e23ac89ebb7e1bfbf25b18043de756548e7f52"
  integrity sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/lines-and-columns/-/lines-and-columns-1.2.4.tgz#eca284f75d2965079309dc0ad9255abb2ebc1632"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

loader-runner@^4.2.0:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/loader-runner/-/loader-runner-4.2.0.tgz#d7022380d66d14c5fb1d496b89864ebcfd478384"
  integrity sha512-92+huvxMvYlMzMt0iIOukcwYBFpkYJdpl2xsZ7LrlayO7E8SOv+JJUEK17B/dJIHAOLMfh2dZZ/Y18WgmGtYNw==

loader-utils@^0.2.11:
  version "0.2.17"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/loader-utils/-/loader-utils-0.2.17.tgz#f86e6374d43205a6e6c60e9196f17c0299bfb348"
  integrity sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

loader-utils@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/loader-utils/-/loader-utils-1.4.0.tgz#c579b5e34cb34b1a74edc6c1fb36bfa371d5a613"
  integrity sha512-qH0WSMBtn/oHuwjy/NucEgbx5dbxxnxup9s4PVXJUDHZBQY+s0NWA9rJf53RBnQZxfch7euUui7hpoAPvALZdA==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

loader-utils@^2.0.0:
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/loader-utils/-/loader-utils-2.0.4.tgz#8b5cb38b5c34a9a018ee1fc0e6a066d1dfcc528c"
  integrity sha1-i1yzi1w0qaAY7h/A5qBm0d/MUow=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/locate-path/-/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash-es@^4.2.1:
  version "4.17.15"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/lodash-es/-/lodash-es-4.17.15.tgz#21bd96839354412f23d7a10340e5eac6ee455d78"
  integrity sha512-rlrc3yU3+JNOpZ9zj5pQtxnx2THmvRykwL4Xlxoa8I9lHBlVbbyPhgyPMioxVZ4NqyxaVVtaJnzsyOidQIhyyQ==

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/lodash.debounce/-/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/lodash.memoize/-/lodash.memoize-4.1.2.tgz#bcc6c49a42a2840ed997f323eada5ecd182e0bfe"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/lodash.uniq/-/lodash.uniq-4.5.0.tgz#d0225373aeb652adc1bc82e4945339a842754773"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.4, lodash@^4.17.5, lodash@^4.2.1:
  version "4.17.15"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/lodash/-/lodash-4.17.15.tgz#b447f6670a0455bbfeedd11392eff330ea097548"
  integrity sha1-tEf2ZwoEVbv+7dETku/zMOoJdUg=

longest@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/longest/-/longest-1.0.1.tgz#30a0b2da38f73770e8294a0d22e6625ed77d0097"
  integrity sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc=

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.2.0, loose-envify@^1.3.1, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/loose-envify/-/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/lru-cache/-/lru-cache-6.0.0.tgz#6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

make-dir@^3.0.2, make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/make-dir/-/make-dir-3.1.0.tgz#415e967046b3a7f1d185277d84aa58203726a13f"
  integrity sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==
  dependencies:
    semver "^6.0.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/map-cache/-/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/map-visit/-/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

math-random@^1.0.1:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/math-random/-/math-random-1.0.4.tgz#5dd6943c938548267016d4e34f057583080c514c"
  integrity sha512-rUxjysqif/BZQH2yhd5Aaq7vXMSx9NdEsQcyA07uEzIvxgI7zIr33gGsh+RU0/XjmQpCW7RsVof1vlkvQVCK5A==

mdn-data@2.0.28:
  version "2.0.28"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/mdn-data/-/mdn-data-2.0.28.tgz#5ec48e7bef120654539069e1ae4ddc81ca490eba"
  integrity sha512-aylIc7Z9y4yzHYAJNuESG3hfhC+0Ibp/MAMiaOZgNv4pmEdFyfZhhhny4MNiAfWdBQ1RQ2mfDWmM1x8SvGyp8g==

mdn-data@2.0.30:
  version "2.0.30"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/mdn-data/-/mdn-data-2.0.30.tgz#ce4df6f80af6cfbe218ecd5c552ba13c4dfa08cc"
  integrity sha512-GaqWWShW4kv/G9IEucWScBx9G1/vsFZZJUO+tD26M8J8z3Kw5RDQjaoZe03YAClgeS/SWPOcb4nkFBTEi5DUEA==

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/media-typer/-/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memfs@^3.4.1:
  version "3.4.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/memfs/-/memfs-3.4.1.tgz#b78092f466a0dce054d63d39275b24c71d3f1305"
  integrity sha512-1c9VPVvW5P7I85c35zAdEr1TD5+F11IToIHIlrVIcflfnzPkJa0ZoYEoEdYDP8KgPFoSZ/opDrUsAoZWym3mtw==
  dependencies:
    fs-monkey "1.0.3"

memory-fs@^0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/memory-fs/-/memory-fs-0.2.0.tgz#f2bb25368bc121e391c2520de92969caee0a0290"
  integrity sha1-8rslNovBIeORwlIN6Slpyu4KApA=

memory-fs@~0.3.0:
  version "0.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/memory-fs/-/memory-fs-0.3.0.tgz#7bcc6b629e3a43e871d7e29aca6ae8a7f15cbb20"
  integrity sha1-e8xrYp46Q+hx1+Kaymrop/FcuyA=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/merge-descriptors/-/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/merge-stream/-/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/merge2/-/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@~1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/methods/-/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^2.1.5:
  version "2.3.11"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/micromatch/-/micromatch-2.3.11.tgz#86677c97d1720b363431d04d0d15293bd38c1565"
  integrity sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU=
  dependencies:
    arr-diff "^2.0.0"
    array-unique "^0.2.1"
    braces "^1.8.2"
    expand-brackets "^0.1.4"
    extglob "^0.3.1"
    filename-regex "^2.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.1"
    kind-of "^3.0.2"
    normalize-path "^2.0.1"
    object.omit "^2.0.0"
    parse-glob "^3.0.4"
    regex-cache "^0.4.2"

micromatch@^3.1.10:
  version "3.1.10"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/micromatch/-/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
  integrity sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg==
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^4.0.0, micromatch@^4.0.2, micromatch@^4.0.4:
  version "4.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/micromatch/-/micromatch-4.0.4.tgz#896d519dfe9db25fce94ceb7a500919bf881ebf9"
  integrity sha512-pRmzw/XUcwXGpD9aI9q/0XOwLNygjETJ8y0ao0wdqprrzDa4YnxLcz7fQRZr8voh8V10kGhABbNcHVk5wHgWwg==
  dependencies:
    braces "^3.0.1"
    picomatch "^2.2.3"

mime-db@1.51.0, "mime-db@>= 1.43.0 < 2":
  version "1.51.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/mime-db/-/mime-db-1.51.0.tgz#d9ff62451859b18342d960850dc3cfb77e63fb0c"
  integrity sha512-5y8A56jg7XVQx2mbv1lu49NR4dokRnhZYTtL+KGfaa27uq4pSTXkwQkFJl4pkRMyNFz/EtYDSkiiEHx3F7UN6g==

mime-types@^2.1.27, mime-types@^2.1.31, mime-types@~2.1.17, mime-types@~2.1.24:
  version "2.1.34"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/mime-types/-/mime-types-2.1.34.tgz#5a712f9ec1503511a945803640fafe09d3793c24"
  integrity sha512-6cP692WwGIs9XXdOO4++N+7qjqv0rqxxVvJ3VHPh/Sc9mVZcQP+ZGhkKiTvWMQRr2tbHkJP/Yn7Y0npb3ZBs4A==
  dependencies:
    mime-db "1.51.0"

mime@1.6.0:
  version "1.6.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/mime/-/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/mimic-fn/-/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

mini-css-extract-plugin@2.7.5:
  version "2.7.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/mini-css-extract-plugin/-/mini-css-extract-plugin-2.7.5.tgz#afbb344977659ec0f1f6e050c7aea456b121cfc5"
  integrity sha512-9HaR++0mlgom81s95vvNjxkg52n2b5s//3ZTI1EtzFb98awsLSivs2LMsVqnQ3ay0PVhqWcGNyDaTE961FOcjQ==
  dependencies:
    schema-utils "^4.0.0"

minimalistic-assert@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz#2e194de044626d4a10e7f7fbc00ce73e83e4d5c7"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimatch@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/minimatch/-/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.2.0, minimist@^1.2.5:
  version "1.2.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/minimist/-/minimist-1.2.5.tgz#67d66014b66a6a8aaa0c083c5fd58df4e4e97602"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

minimist@~0.0.1:
  version "0.0.10"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/minimist/-/minimist-0.0.10.tgz#de3f98543dbf96082be48ad1a0c7cda836301dcf"
  integrity sha1-3j+YVD2/lggr5IrRoMfNqDYwHc8=

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/mixin-deep/-/mixin-deep-1.3.2.tgz#1120b43dc359a785dce65b55b82e257ccf479566"
  integrity sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA==
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@^0.5.5, mkdirp@~0.5.0:
  version "0.5.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/mkdirp/-/mkdirp-0.5.5.tgz#d91cefd62d1436ca0f41620e251288d420099def"
  integrity sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=
  dependencies:
    minimist "^1.2.5"

moment@^2.30.1:
  version "2.30.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/moment/-/moment-2.30.1.tgz#f8c91c07b7a786e30c59926df530b4eac96974ae"
  integrity sha1-+MkcB7enhuMMWZJt9TC06slpdK4=

ms@2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ms/-/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ms/-/ms-2.1.1.tgz#30a5864eb3ebb0a66f2ebe6d727af06a09d86e0a"
  integrity sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=

ms@2.1.2, ms@^2.1.1:
  version "2.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ms/-/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

multicast-dns-service-types@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz#899f11d9686e5e05cb91b35d5f0e63b773cfc901"
  integrity sha512-cnAsSVxIDsYt0v7HmC0hWZFwwXSh+E6PgCrREDuN/EsjgLwA5XRmlMHhSiDPrt6HxY1gTivEa/Zh7GtODoLevQ==

multicast-dns@^6.0.1:
  version "6.2.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/multicast-dns/-/multicast-dns-6.2.3.tgz#a0ec7bd9055c4282f790c3c82f4e28db3b31b229"
  integrity sha512-ji6J5enbMyGRHIAkAOu3WdV8nggqviKCEKtXcOqfphZZtQrmHKycfynJ2V7eVPUA4NhJ6V7Wf4TmGbTwKE9B6g==
  dependencies:
    dns-packet "^1.3.1"
    thunky "^1.0.2"

nan@^2.12.1:
  version "2.14.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/nan/-/nan-2.14.0.tgz#7818f722027b2459a86f0295d434d1fc2336c52c"
  integrity sha512-INOFj37C7k3AfaNTtX8RhsTw7qRy7eLET14cROi9+5HAVbbHuIWUHEauBv5qT4Av2tWasiTY1Jw6puUNqRJXQg==

nanoid@^3.2.0:
  version "3.3.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/nanoid/-/nanoid-3.3.1.tgz#6347a18cac88af88f58af0b3594b723d5e99bb35"
  integrity sha512-n6Vs/3KGyxPQd6uO0eH4Bv0ojGSUvuLlIHtC3Y0kEO23YRge8H9x1GCzLn28YX0H66pMkxuaeESFq4tKISKwdw==

nanoid@^3.3.6:
  version "3.3.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/nanoid/-/nanoid-3.3.6.tgz#443380c856d6e9f9824267d960b4236ad583ea4c"
  integrity sha512-BGcqMMJuToF7i1rt+2PWSNVnWIkGCU78jBG3RxO/bZlnZPK2Cmi2QaffxGO/2RvWi9sL+FAiRiXMgsyxQ1DIDA==

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/nanomatch/-/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

negotiator@0.6.2:
  version "0.6.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/negotiator/-/negotiator-0.6.2.tgz#feacf7ccf525a77ae9634436a64883ffeca346fb"
  integrity sha512-hZXc7K2e+PgeI1eDBe/10Ard4ekbfrrqG8Ep+8Jmf4JID2bNg7NvCPOZN+kfF574pFQI7mum2AUqDidoKqcTOw==

neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/neo-async/-/neo-async-2.6.2.tgz#b4aafb93e3aeb2d8174ca53cf163ab7d7308305f"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

node-fetch@^1.0.1:
  version "1.7.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/node-fetch/-/node-fetch-1.7.3.tgz#980f6f72d85211a5347c6b2bc18c5b84c3eb47ef"
  integrity sha512-NhZ4CsKx7cYm2vSrBAr2PvFOe6sWDf0UYLRqA6svUYg7+/TSfVAu49jYC4BvQ4Sms9SZgdqGBgroqfDhJdTyKQ==
  dependencies:
    encoding "^0.1.11"
    is-stream "^1.0.1"

node-forge@^1.2.0:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/node-forge/-/node-forge-1.2.1.tgz#82794919071ef2eb5c509293325cec8afd0fd53c"
  integrity sha512-Fcvtbb+zBcZXbTTVwqGA5W+MKBj56UjVRevvchv5XrcyXbmNdesfZL37nlcWOfpgHhgmxApw3tQbTr4CqNmX4w==

node-glob@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/node-glob/-/node-glob-1.2.0.tgz#5240ffedefc6d663ce8515e5796a4d47a750c0d5"
  integrity sha512-c0R4Wab2SAlwdBr5ehPANnbLzxv5dBMUdEYy8ilqBDkqvEIf74JGhaLhCh/EuhgzPTXuEOUoqDnAKdODpHXMNg==
  dependencies:
    async "^1.3.0"
    glob-to-regexp "^0.1.0"

node-libs-browser@^0.7.0:
  version "0.7.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/node-libs-browser/-/node-libs-browser-0.7.0.tgz#3e272c0819e308935e26674408d7af0e1491b83b"
  integrity sha1-PicsCBnjCJNeJmdECNevDhSRuDs=
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.1.4"
    buffer "^4.9.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "3.3.0"
    domain-browser "^1.1.1"
    events "^1.0.0"
    https-browserify "0.0.1"
    os-browserify "^0.2.0"
    path-browserify "0.0.0"
    process "^0.11.0"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.0.5"
    stream-browserify "^2.0.1"
    stream-http "^2.3.1"
    string_decoder "^0.10.25"
    timers-browserify "^2.0.2"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.10.3"
    vm-browserify "0.0.4"

node-releases@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/node-releases/-/node-releases-2.0.2.tgz#7139fe71e2f4f11b47d4d2986aaf8c48699e0c01"
  integrity sha512-XxYDdcQ6eKqp/YjI+tb2C5WM2LgjnZrfYg4vgQt49EK268b6gYCHsBLrK2qvJo4FmCtqmKezb0WZFK4fkrZNsg==

node-releases@^2.0.8:
  version "2.0.10"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/node-releases/-/node-releases-2.0.10.tgz#c311ebae3b6a148c89b1813fd7c4d3c024ef537f"
  integrity sha512-5GFldHPXVG/YZmFzJvKK2zDSzPKhEp0+ZR5SVaoSag9fsL5YgHbUHDfnG5494ISANDcK4KwPXAx2xqVEydmd7w==

normalize-path@^2.0.0, normalize-path@^2.0.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/normalize-path/-/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/normalize-path/-/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/npm-run-path/-/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

nth-check@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/nth-check/-/nth-check-2.0.1.tgz#2efe162f5c3da06a28959fbd3db75dbeea9f0fc2"
  integrity sha512-it1vE95zF6dTT9lBsYbxvqh0Soy4SPowchj0UBGj/V6cTPnXXtQOPUbhZ6CmGzAD/rW22LQK6E96pcdJXk4A4w==
  dependencies:
    boolbase "^1.0.0"

object-assign@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/object-assign/-/object-assign-3.0.0.tgz#9bedd5ca0897949bca47e7ff408062d549f587f2"
  integrity sha512-jHP15vXVGeVh1HuaA2wY6lxk+whK/x4KBG88VXeRma7CCun7iGD5qPc4eYykQ9sdQvg8jkwFKsSxHln2ybW3xQ==

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/object-copy/-/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-inspect@^1.7.0:
  version "1.7.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/object-inspect/-/object-inspect-1.7.0.tgz#f4f6bd181ad77f006b5ece60bd0b6f398ff74a67"
  integrity sha512-a7pEHdh1xKIAgTySUGgLMx/xwDZskN1Ud6egYYN3EdRW4ZMPNEDUTF+hwy2LUC+Bl+SyLXANnwz/jyh/qutKUw==

object-is@^1.0.1:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/object-is/-/object-is-1.1.2.tgz#c5d2e87ff9e119f78b7a088441519e2eec1573b6"
  integrity sha512-5lHCz+0uufF6wZ7CRFWJN3hp8Jqblpgve06U5CMQ3f//6iDjPr2PEo9MWCjEssDsa+UZEL4PkFpr+BMop6aKzQ==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

object-keys@^1.0.11, object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/object-keys/-/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==

object-visit@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/object-visit/-/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/object.assign/-/object.assign-4.1.0.tgz#968bf1100d7956bb3ca086f006f846b3bc4008da"
  integrity sha512-exHJeq6kBKj58mqGyTQ9DFvrZC/eR6OwxzoM9YRoGBqrXYonaFyGiFMuc9VZrXf7DarreEwMpurG3dd+CNyW5w==
  dependencies:
    define-properties "^1.1.2"
    function-bind "^1.1.1"
    has-symbols "^1.0.0"
    object-keys "^1.0.11"

object.omit@^2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/object.omit/-/object.omit-2.0.1.tgz#1a9c744829f39dbb858c76ca3579ae2a54ebd1fa"
  integrity sha1-Gpx0SCnznbuFjHbKNXmuKlTr0fo=
  dependencies:
    for-own "^0.1.4"
    is-extendable "^0.1.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/object.pick/-/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/obuf/-/obuf-1.1.2.tgz#09bea3343d41859ebd446292d11c9d4db619084e"
  integrity sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/on-finished/-/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/on-headers/-/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"
  integrity sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==

once@^1.3.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.2:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/onetime/-/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

open@^8.0.9:
  version "8.4.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/open/-/open-8.4.0.tgz#345321ae18f8138f82565a910fdc6b39e8c244f8"
  integrity sha512-XgFPPM+B28FtCCgSb9I+s9szOC1vZRSwgWsRUA5ylIxRTgKozqjOCrVOqGsYABPYK5qnfqClxZTFBa8PKt2v6Q==
  dependencies:
    define-lazy-prop "^2.0.0"
    is-docker "^2.1.1"
    is-wsl "^2.2.0"

optimist@~0.6.0:
  version "0.6.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/optimist/-/optimist-0.6.1.tgz#da3ea74686fa21a19a111c326e90eb15a0196686"
  integrity sha1-2j6nRob6IaGaERwybpDrFaAZZoY=
  dependencies:
    minimist "~0.0.1"
    wordwrap "~0.0.2"

os-browserify@^0.2.0:
  version "0.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/os-browserify/-/os-browserify-0.2.1.tgz#63fc4ccee5d2d7763d26bbf8601078e6c2e0044f"
  integrity sha1-Y/xMzuXS13Y9Jrv4YBB45sLgBE8=

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/p-limit/-/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/p-locate/-/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-map@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/p-map/-/p-map-4.0.0.tgz#bb2f95a5eda2ec168ec9274e06a747c3e2904d2b"
  integrity sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==
  dependencies:
    aggregate-error "^3.0.0"

p-retry@^4.5.0:
  version "4.6.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/p-retry/-/p-retry-4.6.1.tgz#8fcddd5cdf7a67a0911a9cf2ef0e5df7f602316c"
  integrity sha512-e2xXGNhZOZ0lfgR9kL34iGlU8N/KO0xZnQxVEwdeOvpqNDQfdnxIYizvWtK8RglUa3bGqI8g0R/BdfzLMxRkiA==
  dependencies:
    "@types/retry" "^0.12.0"
    retry "^0.13.1"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/p-try/-/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==

pako@~0.2.0:
  version "0.2.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/pako/-/pako-0.2.9.tgz#f3f7522f4ef782348da8161bad9ecfd51bf83a75"
  integrity sha1-8/dSL073gjSNqBYbrZ7P1Rv4OnU=

parchment@^1.1.2, parchment@^1.1.4:
  version "1.1.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/parchment/-/parchment-1.1.4.tgz#aeded7ab938fe921d4c34bc339ce1168bc2ffde5"
  integrity sha512-J5FBQt/pM2inLzg4hEWmzQx/8h8D0CiDxaG3vyp9rKrQRSDgBlhjdP5jQGgosEajXPSQouXGHOmVdgo7QmJuOg==

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/parent-module/-/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parse-glob@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/parse-glob/-/parse-glob-3.0.4.tgz#b2c376cfb11f35513badd173ef0bb6e3a388391c"
  integrity sha1-ssN2z7EfNVE7rdFz7wu246OIORw=
  dependencies:
    glob-base "^0.3.0"
    is-dotfile "^1.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.0"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/parse-json/-/parse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/parseurl/-/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/pascalcase/-/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-browserify@0.0.0:
  version "0.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/path-browserify/-/path-browserify-0.0.0.tgz#a0b870729aae214005b7d5032ec2cbbb0fb4451a"
  integrity sha1-oLhwcpquIUAFt9UDLsLLuw+0RRo=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/path-exists/-/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/path-key/-/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/path-parse/-/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/path-to-regexp/-/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-to-regexp@^1.7.0:
  version "1.8.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/path-to-regexp/-/path-to-regexp-1.8.0.tgz#887b3ba9d84393e87a0a0b9f4cb756198b53548a"
  integrity sha1-iHs7qdhDk+h6CgufTLdWGYtTVIo=
  dependencies:
    isarray "0.0.1"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/path-type/-/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pbkdf2-compat@2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/pbkdf2-compat/-/pbkdf2-compat-2.0.1.tgz#b6e0c8fa99494d94e0511575802a59a5c142f288"
  integrity sha1-tuDI+plJTZTgURV1gCpZpcFC8og=

performance-now@^0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/performance-now/-/performance-now-0.2.0.tgz#33ef30c5c77d4ea21c5a53869d91b56d8f2555e5"
  integrity sha512-YHk5ez1hmMR5LOkb9iJkLKqoBlL7WD5M8ljC75ZfzXriuBIVNuecaXuU7e+hOwyqf24Wxhh7Vxgt7Hnw9288Tg==

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/performance-now/-/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/picocolors/-/picocolors-1.0.0.tgz#cb5bdc74ff3f51892236eaf79d68bc44564ab81c"
  integrity sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.3:
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/picomatch/-/picomatch-2.3.0.tgz#f1f061de8f6a4bf022892e2d128234fb98302972"
  integrity sha512-lY1Q/PiJGC2zOv/z391WOTD+Z02bCgsFfvxoXXf6h7kv9o+WmsmzYqrAwY63sNgOxE4xEdq0WyUnXfKeBrSvYw==

pkg-dir@^4.1.0, pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/pkg-dir/-/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==
  dependencies:
    find-up "^4.0.0"

portfinder@^1.0.28:
  version "1.0.28"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/portfinder/-/portfinder-1.0.28.tgz#67c4622852bd5374dd1dd900f779f53462fac778"
  integrity sha1-Z8RiKFK9U3TdHdkA93n1NGL6x3g=
  dependencies:
    async "^2.6.2"
    debug "^3.1.1"
    mkdirp "^0.5.5"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/posix-character-classes/-/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

postcss-calc@^8.2.3:
  version "8.2.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-calc/-/postcss-calc-8.2.4.tgz#77b9c29bfcbe8a07ff6693dc87050828889739a5"
  integrity sha512-SmWMSJmB8MRnnULldx0lQIyhSNvuDl9HfrZkaqqE/WHAhToYsAvDq+yAsA/kIyINDszOp3Rh0GFoNuH5Ypsm3Q==
  dependencies:
    postcss-selector-parser "^6.0.9"
    postcss-value-parser "^4.2.0"

postcss-colormin@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-colormin/-/postcss-colormin-6.0.0.tgz#d4250652e952e1c0aca70c66942da93d3cdeaafe"
  integrity sha512-EuO+bAUmutWoZYgHn2T1dG1pPqHU6L4TjzPlu4t1wZGXQ/fxV16xg2EJmYi0z+6r+MGV1yvpx1BHkUaRrPa2bw==
  dependencies:
    browserslist "^4.21.4"
    caniuse-api "^3.0.0"
    colord "^2.9.1"
    postcss-value-parser "^4.2.0"

postcss-convert-values@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-convert-values/-/postcss-convert-values-6.0.0.tgz#ec94a954957e5c3f78f0e8f65dfcda95280b8996"
  integrity sha512-U5D8QhVwqT++ecmy8rnTb+RL9n/B806UVaS3m60lqle4YDFcpbS3ae5bTQIh3wOGUSDHSEtMYLs/38dNG7EYFw==
  dependencies:
    browserslist "^4.21.4"
    postcss-value-parser "^4.2.0"

postcss-discard-comments@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-discard-comments/-/postcss-discard-comments-6.0.0.tgz#9ca335e8b68919f301b24ba47dde226a42e535fe"
  integrity sha512-p2skSGqzPMZkEQvJsgnkBhCn8gI7NzRH2683EEjrIkoMiwRELx68yoUJ3q3DGSGuQ8Ug9Gsn+OuDr46yfO+eFw==

postcss-discard-duplicates@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-discard-duplicates/-/postcss-discard-duplicates-6.0.0.tgz#c26177a6c33070922e67e9a92c0fd23d443d1355"
  integrity sha512-bU1SXIizMLtDW4oSsi5C/xHKbhLlhek/0/yCnoMQany9k3nPBq+Ctsv/9oMmyqbR96HYHxZcHyK2HR5P/mqoGA==

postcss-discard-empty@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-discard-empty/-/postcss-discard-empty-6.0.0.tgz#06c1c4fce09e22d2a99e667c8550eb8a3a1b9aee"
  integrity sha512-b+h1S1VT6dNhpcg+LpyiUrdnEZfICF0my7HAKgJixJLW7BnNmpRH34+uw/etf5AhOlIhIAuXApSzzDzMI9K/gQ==

postcss-discard-overridden@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-discard-overridden/-/postcss-discard-overridden-6.0.0.tgz#49c5262db14e975e349692d9024442de7cd8e234"
  integrity sha512-4VELwssYXDFigPYAZ8vL4yX4mUepF/oCBeeIT4OXsJPYOtvJumyz9WflmJWTfDwCUcpDR+z0zvCWBXgTx35SVw==

postcss-merge-longhand@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-merge-longhand/-/postcss-merge-longhand-6.0.0.tgz#6f627b27db939bce316eaa97e22400267e798d69"
  integrity sha512-4VSfd1lvGkLTLYcxFuISDtWUfFS4zXe0FpF149AyziftPFQIWxjvFSKhA4MIxMe4XM3yTDgQMbSNgzIVxChbIg==
  dependencies:
    postcss-value-parser "^4.2.0"
    stylehacks "^6.0.0"

postcss-merge-rules@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-merge-rules/-/postcss-merge-rules-6.0.0.tgz#0d95bc73541156b8b4e763bd0de2c3f9d0ecf013"
  integrity sha512-rCXkklftzEkniyv3f4mRCQzxD6oE4Quyh61uyWTUbCJ26Pv2hoz+fivJSsSBWxDBeScR4fKCfF3HHTcD7Ybqnw==
  dependencies:
    browserslist "^4.21.4"
    caniuse-api "^3.0.0"
    cssnano-utils "^4.0.0"
    postcss-selector-parser "^6.0.5"

postcss-minify-font-values@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-minify-font-values/-/postcss-minify-font-values-6.0.0.tgz#68d4a028f9fa5f61701974724b2cc9445d8e6070"
  integrity sha512-zNRAVtyh5E8ndZEYXA4WS8ZYsAp798HiIQ1V2UF/C/munLp2r1UGHwf1+6JFu7hdEhJFN+W1WJQKBrtjhFgEnA==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-minify-gradients@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-minify-gradients/-/postcss-minify-gradients-6.0.0.tgz#22b5c88cc63091dadbad34e31ff958404d51d679"
  integrity sha512-wO0F6YfVAR+K1xVxF53ueZJza3L+R3E6cp0VwuXJQejnNUH0DjcAFe3JEBeTY1dLwGa0NlDWueCA1VlEfiKgAA==
  dependencies:
    colord "^2.9.1"
    cssnano-utils "^4.0.0"
    postcss-value-parser "^4.2.0"

postcss-minify-params@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-minify-params/-/postcss-minify-params-6.0.0.tgz#2b3a85a9e3b990d7a16866f430f5fd1d5961b539"
  integrity sha512-Fz/wMQDveiS0n5JPcvsMeyNXOIMrwF88n7196puSuQSWSa+/Ofc1gDOSY2xi8+A4PqB5dlYCKk/WfqKqsI+ReQ==
  dependencies:
    browserslist "^4.21.4"
    cssnano-utils "^4.0.0"
    postcss-value-parser "^4.2.0"

postcss-minify-selectors@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-minify-selectors/-/postcss-minify-selectors-6.0.0.tgz#5046c5e8680a586e5a0cad52cc9aa36d6be5bda2"
  integrity sha512-ec/q9JNCOC2CRDNnypipGfOhbYPuUkewGwLnbv6omue/PSASbHSU7s6uSQ0tcFRVv731oMIx8k0SP4ZX6be/0g==
  dependencies:
    postcss-selector-parser "^6.0.5"

postcss-modules-extract-imports@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.0.0.tgz#cda1f047c0ae80c97dbe28c3e76a43b88025741d"
  integrity sha512-bdHleFnP3kZ4NYDhuGlVK+CMrQ/pqUm8bx/oGL93K6gVwiclvX5x0n76fYMKuIGKzlABOy13zsvqjb0f92TEXw==

postcss-modules-local-by-default@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.0.0.tgz#ebbb54fae1598eecfdf691a02b3ff3b390a5a51c"
  integrity sha512-sT7ihtmGSF9yhm6ggikHdV0hlziDTX7oFoXtuVWeDd3hHObNkcHRo9V3yg7vCAY7cONyxJC/XXCmmiHHcvX7bQ==
  dependencies:
    icss-utils "^5.0.0"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.1.0"

postcss-modules-scope@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-modules-scope/-/postcss-modules-scope-3.0.0.tgz#9ef3151456d3bbfa120ca44898dfca6f2fa01f06"
  integrity sha512-hncihwFA2yPath8oZ15PZqvWGkWf+XUfQgUGamS4LqoP1anQLOsOJw0vr7J7IwLpoY9fatA2qiGUGmuZL0Iqlg==
  dependencies:
    postcss-selector-parser "^6.0.4"

postcss-modules-values@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz#d7c5e7e68c3bb3c9b27cbf48ca0bb3ffb4602c9c"
  integrity sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==
  dependencies:
    icss-utils "^5.0.0"

postcss-normalize-charset@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-normalize-charset/-/postcss-normalize-charset-6.0.0.tgz#36cc12457259064969fb96f84df491652a4b0975"
  integrity sha512-cqundwChbu8yO/gSWkuFDmKrCZ2vJzDAocheT2JTd0sFNA4HMGoKMfbk2B+J0OmO0t5GUkiAkSM5yF2rSLUjgQ==

postcss-normalize-display-values@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-normalize-display-values/-/postcss-normalize-display-values-6.0.0.tgz#8d2961415078644d8c6bbbdaf9a2fdd60f546cd4"
  integrity sha512-Qyt5kMrvy7dJRO3OjF7zkotGfuYALETZE+4lk66sziWSPzlBEt7FrUshV6VLECkI4EN8Z863O6Nci4NXQGNzYw==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-positions@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-normalize-positions/-/postcss-normalize-positions-6.0.0.tgz#25b96df99a69f8925f730eaee0be74416865e301"
  integrity sha512-mPCzhSV8+30FZyWhxi6UoVRYd3ZBJgTRly4hOkaSifo0H+pjDYcii/aVT4YE6QpOil15a5uiv6ftnY3rm0igPg==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-repeat-style@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-6.0.0.tgz#ddf30ad8762feb5b1eb97f39f251acd7b8353299"
  integrity sha512-50W5JWEBiOOAez2AKBh4kRFm2uhrT3O1Uwdxz7k24aKtbD83vqmcVG7zoIwo6xI2FZ/HDlbrCopXhLeTpQib1A==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-string@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-normalize-string/-/postcss-normalize-string-6.0.0.tgz#948282647a51e409d69dde7910f0ac2ff97cb5d8"
  integrity sha512-KWkIB7TrPOiqb8ZZz6homet2KWKJwIlysF5ICPZrXAylGe2hzX/HSf4NTX2rRPJMAtlRsj/yfkrWGavFuB+c0w==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-timing-functions@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-6.0.0.tgz#5f13e650b8c43351989fc5de694525cc2539841c"
  integrity sha512-tpIXWciXBp5CiFs8sem90IWlw76FV4oi6QEWfQwyeREVwUy39VSeSqjAT7X0Qw650yAimYW5gkl2Gd871N5SQg==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-unicode@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-normalize-unicode/-/postcss-normalize-unicode-6.0.0.tgz#741b3310f874616bdcf07764f5503695d3604730"
  integrity sha512-ui5crYkb5ubEUDugDc786L/Me+DXp2dLg3fVJbqyAl0VPkAeALyAijF2zOsnZyaS1HyfPuMH0DwyY18VMFVNkg==
  dependencies:
    browserslist "^4.21.4"
    postcss-value-parser "^4.2.0"

postcss-normalize-url@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-normalize-url/-/postcss-normalize-url-6.0.0.tgz#d0a31e962a16401fb7deb7754b397a323fb650b4"
  integrity sha512-98mvh2QzIPbb02YDIrYvAg4OUzGH7s1ZgHlD3fIdTHLgPLRpv1ZTKJDnSAKr4Rt21ZQFzwhGMXxpXlfrUBKFHw==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-whitespace@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-normalize-whitespace/-/postcss-normalize-whitespace-6.0.0.tgz#accb961caa42e25ca4179b60855b79b1f7129d4d"
  integrity sha512-7cfE1AyLiK0+ZBG6FmLziJzqQCpTQY+8XjMhMAz8WSBSCsCNNUKujgIgjCAmDT3cJ+3zjTXFkoD15ZPsckArVw==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-ordered-values@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-ordered-values/-/postcss-ordered-values-6.0.0.tgz#374704cdff25560d44061d17ba3c6308837a3218"
  integrity sha512-K36XzUDpvfG/nWkjs6d1hRBydeIxGpKS2+n+ywlKPzx1nMYDYpoGbcjhj5AwVYJK1qV2/SDoDEnHzlPD6s3nMg==
  dependencies:
    cssnano-utils "^4.0.0"
    postcss-value-parser "^4.2.0"

postcss-reduce-initial@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-reduce-initial/-/postcss-reduce-initial-6.0.0.tgz#7d16e83e60e27e2fa42f56ec0b426f1da332eca7"
  integrity sha512-s2UOnidpVuXu6JiiI5U+fV2jamAw5YNA9Fdi/GRK0zLDLCfXmSGqQtzpUPtfN66RtCbb9fFHoyZdQaxOB3WxVA==
  dependencies:
    browserslist "^4.21.4"
    caniuse-api "^3.0.0"

postcss-reduce-transforms@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-reduce-transforms/-/postcss-reduce-transforms-6.0.0.tgz#28ff2601a6d9b96a2f039b3501526e1f4d584a46"
  integrity sha512-FQ9f6xM1homnuy1wLe9lP1wujzxnwt1EwiigtWwuyf8FsqqXUDUp2Ulxf9A5yjlUOTdCJO6lonYjg1mgqIIi2w==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-selector-parser@^6.0.2, postcss-selector-parser@^6.0.4, postcss-selector-parser@^6.0.5, postcss-selector-parser@^6.0.9:
  version "6.0.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-selector-parser/-/postcss-selector-parser-6.0.9.tgz#ee71c3b9ff63d9cd130838876c13a2ec1a992b2f"
  integrity sha512-UO3SgnZOVTwu4kyLR22UQ1xZh086RyNZppb7lLAKBFK8a32ttG5i87Y/P3+2bRSjZNyJ1B7hfFNo273tKe9YxQ==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-svgo@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-svgo/-/postcss-svgo-6.0.0.tgz#7b18742d38d4505a0455bbe70d52b49f00eaf69d"
  integrity sha512-r9zvj/wGAoAIodn84dR/kFqwhINp5YsJkLoujybWG59grR/IHx+uQ2Zo+IcOwM0jskfYX3R0mo+1Kip1VSNcvw==
  dependencies:
    postcss-value-parser "^4.2.0"
    svgo "^3.0.2"

postcss-unique-selectors@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-unique-selectors/-/postcss-unique-selectors-6.0.0.tgz#c94e9b0f7bffb1203894e42294b5a1b3fb34fbe1"
  integrity sha512-EPQzpZNxOxP7777t73RQpZE5e9TrnCrkvp7AH7a0l89JmZiPnS82y216JowHXwpBCQitfyxrof9TK3rYbi7/Yw==
  dependencies:
    postcss-selector-parser "^6.0.5"

postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz#723c09920836ba6d3e5af019f92bc0971c02e514"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@^8.4.21:
  version "8.4.23"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss/-/postcss-8.4.23.tgz#df0aee9ac7c5e53e1075c24a3613496f9e6552ab"
  integrity sha512-bQ3qMcpF6A/YjR55xtoTr0jGOlnPOKAIMdOWiv0EIT6HVPEaJiJB4NLljSbiHoC2RX7DN5Uvjtpbg1NPdwv1oA==
  dependencies:
    nanoid "^3.3.6"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

postcss@^8.4.5:
  version "8.4.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/postcss/-/postcss-8.4.6.tgz#c5ff3c3c457a23864f32cb45ac9b741498a09ae1"
  integrity sha512-OovjwIzs9Te46vlEx7+uXB0PLijpwjXGKXjVGGPIGubGpq7uh5Xgf6D6FiJ/SzJMBosHDp6a2hiXOS97iBXcaA==
  dependencies:
    nanoid "^3.2.0"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

prefix-style@2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/prefix-style/-/prefix-style-2.0.1.tgz#66bba9a870cfda308a5dc20e85e9120932c95a06"
  integrity sha512-gdr1MBNVT0drzTq95CbSNdsrBDoHGlb2aDJP/FoY+1e+jSDPOb1Cv554gH2MGiSr2WTcXi/zu+NaFzfcHQkfBQ==

preserve@^0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/preserve/-/preserve-0.2.0.tgz#815ed1f6ebc65926f865b310c0713bcb3315ce4b"
  integrity sha1-gV7R9uvGWSb4ZbMQwHE7yzMVzks=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/process-nextick-args/-/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process@^0.11.0:
  version "0.11.10"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/process/-/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

promise@^7.1.1:
  version "7.3.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/promise/-/promise-7.3.1.tgz#064b72602b18f90f29192b8b1bc418ffd1ebd3bf"
  integrity sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==
  dependencies:
    asap "~2.0.3"

prop-types@15.6.0:
  version "15.6.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/prop-types/-/prop-types-15.6.0.tgz#ceaf083022fc46b4a35f69e13ef75aed0d639856"
  integrity sha512-H16NHdiZ8szYSKNkCpmKmS8BCogxyABjJ1AqQknIY2iTpy1xC04egoBAzjKm+WU2pbuNxFonw921dnxR0QYAdw==
  dependencies:
    fbjs "^0.8.16"
    loose-envify "^1.3.1"
    object-assign "^4.1.1"

prop-types@^15.5.10, prop-types@^15.5.4, prop-types@^15.5.6, prop-types@^15.5.7, prop-types@^15.5.8, prop-types@^15.6.0, prop-types@^15.6.2, prop-types@^15.7.2:
  version "15.7.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/prop-types/-/prop-types-15.7.2.tgz#52c41e75b8c87e72b9d9360e0206b99dcbffa6c5"
  integrity sha512-8QQikdH7//R2vurIJSutZ1smHYTcLpRWEOlHnzcWHmBYrOGUysKwSsrC89BCiFj3CbrfJ/nXFdJepOVrY1GCHQ==
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.8.1"

prop-types@^15.6.1:
  version "15.8.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/prop-types/-/prop-types-15.8.1.tgz#67d87bf1a694f48435cf332c24af10214a3140b5"
  integrity sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

proxy-addr@~2.0.5:
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/proxy-addr/-/proxy-addr-2.0.6.tgz#fdc2336505447d3f2f2c638ed272caf614bbb2bf"
  integrity sha512-dh/frvCBVmSsDYzw6n926jv974gddhkFPfiN8hPOi30Wax25QZyZEGveluCgliBnqmuM+UJmBErbAUFIoDbjOw==
  dependencies:
    forwarded "~0.1.2"
    ipaddr.js "1.9.1"

prr@~1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/prr/-/prr-1.0.1.tgz#d3fc114ba06995a45ec6893f484ceb1d78f5f476"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

punycode@1.3.2:
  version "1.3.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/punycode/-/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"
  integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=

punycode@^1.2.4:
  version "1.4.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/punycode/-/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"
  integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=

punycode@^2.1.0:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/punycode/-/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

qr.js@0.0.0:
  version "0.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/qr.js/-/qr.js-0.0.0.tgz#cace86386f59a0db8050fa90d9b6b0e88a1e364f"
  integrity sha512-c4iYnWb+k2E+vYpRimHqSu575b1/wKl4XFeJGpFmrJQz5I88v9aY2czh7s0w36srfCM1sXgC/xpoJz5dJfq+OQ==

qrcode.react@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/qrcode.react/-/qrcode.react-1.0.1.tgz#2834bb50e5e275ffe5af6906eff15391fe9e38a5"
  integrity sha512-8d3Tackk8IRLXTo67Y+c1rpaiXjoz/Dd2HpcMdW//62/x8J1Nbho14Kh8x974t9prsLHN6XqVgcnRiBGFptQmg==
  dependencies:
    loose-envify "^1.4.0"
    prop-types "^15.6.0"
    qr.js "0.0.0"

qs@6.7.0:
  version "6.7.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/qs/-/qs-6.7.0.tgz#41dc1a015e3d581f1621776be31afb2876a9b1bc"
  integrity sha512-VCdBRNFTX1fyE7Nb6FYoURo/SPe62QCaAyzJvUjwRaIsc+NePBEniHlvxFmmX56+HZphIGtV0XeCirBtpDrTyQ==

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/querystring-es3/-/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73"
  integrity sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=

querystring@0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/querystring/-/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"
  integrity sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/queue-microtask/-/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

quill-delta@^3.6.2:
  version "3.6.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/quill-delta/-/quill-delta-3.6.3.tgz#b19fd2b89412301c60e1ff213d8d860eac0f1032"
  integrity sha512-wdIGBlcX13tCHOXGMVnnTVFtGRLoP0imqxM696fIPwIf5ODIYUHIvHbZcyvGlZFiFhK5XzDC2lpjbxRhnM05Tg==
  dependencies:
    deep-equal "^1.0.1"
    extend "^3.0.2"
    fast-diff "1.1.2"

quill@^1.2.6:
  version "1.3.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/quill/-/quill-1.3.7.tgz#da5b2f3a2c470e932340cdbf3668c9f21f9286e8"
  integrity sha512-hG/DVzh/TiknWtE6QmWAF/pxoZKYxfe3J/d/+ShUWkDvvkZQVTPeVmUJVu1uE6DDooC4fWTiCLh84ul89oNz5g==
  dependencies:
    clone "^2.1.1"
    deep-equal "^1.0.1"
    eventemitter3 "^2.0.3"
    extend "^3.0.2"
    parchment "^1.1.4"
    quill-delta "^3.6.2"

raf@^3.1.0:
  version "3.4.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/raf/-/raf-3.4.1.tgz#0742e99a4a6552f445d73e3ee0328af0ff1ede39"
  integrity sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==
  dependencies:
    performance-now "^2.1.0"

ramda@^0.23.0:
  version "0.23.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ramda/-/ramda-0.23.0.tgz#ccd13fff73497a93974e3e86327bfd87bd6e8e2b"
  integrity sha512-ZEg7VlLXEeDfdkmqGi/12/2n8vNlxdx3iU74GbEP6VN2Udx4jLTn6Fw0jU4sjmvaPKjrIZIYk8HBj7quaie97Q==

randomatic@^3.0.0:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/randomatic/-/randomatic-3.1.1.tgz#b776efc59375984e36c537b2f51a1f0aff0da1ed"
  integrity sha512-TuDE5KxZ0J461RVjrJZCJc+J+zCkTb1MbH9AQUq68sMhOMcy9jLcb3BrZKgp9q9Ncltdg4QVqWrH02W2EFFVYw==
  dependencies:
    is-number "^4.0.0"
    kind-of "^6.0.0"
    math-random "^1.0.1"

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/randombytes/-/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==
  dependencies:
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/range-parser/-/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==

raw-body@2.4.0:
  version "2.4.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/raw-body/-/raw-body-2.4.0.tgz#a1ce6fb9c9bc356ca52e89256ab59059e13d0332"
  integrity sha512-4Oz8DUIwdvoa5qMJelxipzi/iJIi40O5cGV1wNYp5hvZP8ZN0T+jiNkL0QepXs+EsQ9XJ8ipEDoiH70ySUJP3Q==
  dependencies:
    bytes "3.1.0"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

react-css-themr@^2.1.2:
  version "2.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-css-themr/-/react-css-themr-2.1.2.tgz#e017514e471c232f43a754a55b49d81faf5dafb8"
  integrity sha512-c1+5VLiMRGXHZ8K94uSjehW9ETANHIWRAg0b1BYy+IX6NGbItnegTlX1fO2RkjKkBIB9z4Hjx3YXy6kg8FNSow==
  dependencies:
    hoist-non-react-statics "^1.2.0"
    invariant "^2.2.1"

react-datetime@2.16.2:
  version "2.16.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-datetime/-/react-datetime-2.16.2.tgz#d5d41fe3f6f3fa8fa1b068f2fae75cec25e1bb39"
  integrity sha1-1dQf4/bz+o+hsGjy+udc7CXhuzk=
  dependencies:
    create-react-class "^15.5.2"
    object-assign "^3.0.0"
    prop-types "^15.5.7"
    react-onclickoutside "^6.5.0"

react-dom-factories@^1.0.0:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-dom-factories/-/react-dom-factories-1.0.2.tgz#eb7705c4db36fb501b3aa38ff759616aa0ff96e0"
  integrity sha512-Bmic2N3oKji7vw9qjDr2dmwHvOATbFSnKy7EH0uT/qjvzIUsiXp6Yquk72LJ3WfMtRnq3ujXMMo7GsJeLPfFWw==

react-dom@16.13.1:
  version "16.13.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-dom/-/react-dom-16.13.1.tgz#c1bd37331a0486c078ee54c4740720993b2e0e7f"
  integrity sha1-wb03MxoEhsB47lTEdAcgmTsuDn8=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"
    scheduler "^0.19.1"

react-dropzone@^4.2.9:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-dropzone/-/react-dropzone-4.3.0.tgz#facdd7db16509772633c9f5200621ac01aa6706f"
  integrity sha512-ULfrLaTSsd8BDa9KVAGCueuq1AN3L14dtMsGGqtP0UwYyjG4Vhf158f/ITSHuSPYkZXbvfcIiOlZsH+e3QWm+Q==
  dependencies:
    attr-accept "^1.1.3"
    prop-types "^15.5.7"

react-event-listener@^0.6.0:
  version "0.6.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-event-listener/-/react-event-listener-0.6.6.tgz#758f7b991cad9086dd39fd29fad72127e1d8962a"
  integrity sha512-+hCNqfy7o9wvO6UgjqFmBzARJS7qrNoda0VqzvOuioEpoEXKutiKuv92dSz6kP7rYLmyHPyYNLesi5t/aH1gfw==
  dependencies:
    "@babel/runtime" "^7.2.0"
    prop-types "^15.6.0"
    warning "^4.0.1"

react-i18next@^7.6.1:
  version "7.13.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-i18next/-/react-i18next-7.13.0.tgz#a6f64fd749215ec70400f90da6cbde2a9c5b1588"
  integrity sha512-35M+MZFPqHwVIas7tXWQKFrf+ozCJukNplUTiGqL8mczSk+VRBsHxxXuuQKRkz/4CcWkONGWbp/AzxfM6wZncg==
  dependencies:
    hoist-non-react-statics "^2.3.1"
    html-parse-stringify2 "2.0.1"
    prop-types "^15.6.0"

react-is@^16.13.1, react-is@^16.7.0, react-is@^16.8.1, react-is@^16.8.2:
  version "16.13.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-is/-/react-is-16.13.1.tgz#789729a4dc36de2999dc156dd6c1d9c18cea56a4"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-lifecycles-compat@^3.0.0, react-lifecycles-compat@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-lifecycles-compat/-/react-lifecycles-compat-3.0.4.tgz#4f1a273afdfc8f3488a8c516bfda78f872352362"
  integrity sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA==

react-md@^1.0.19:
  version "1.16.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-md/-/react-md-1.16.1.tgz#0cf2a40fadfddbfe00f43086283126c971946437"
  integrity sha512-JlZXcK+KPDyf4MNBz37ovYv0MJnIoiYSA49iJm48c1nKhwHigzfAcEt39qE55B7vG/ShKm1qGCC0D6UTpYWFdg==
  dependencies:
    classnames "^2.2.5"
    prop-types "15.6.0"
    react-motion "^0.5.0"
    react-prop-types "^0.4.0"
    react-swipeable-views "^0.12.8"
    react-transition-group "^1.2.1"
    resize-observer-polyfill "^1.4.2"

react-modal@3.1.11:
  version "3.1.11"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-modal/-/react-modal-3.1.11.tgz#95c8223fcee7013258ad2d149c38c9f870c89958"
  integrity sha512-Pm4QAc+sWYrfRC+WRERV+JGeGZIfodZGdbvWmjPzeSWqP+EW5ATK4N1U/btNHZWFzKL1UOmkmNtozEQlEg7c+A==
  dependencies:
    exenv "^1.2.0"
    prop-types "^15.5.10"
    warning "^3.0.0"

react-modal@^3.3.1:
  version "3.11.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-modal/-/react-modal-3.11.2.tgz#bad911976d4add31aa30dba8a41d11e21c4ac8a4"
  integrity sha512-o8gvvCOFaG1T7W6JUvsYjRjMVToLZgLIsi5kdhFIQCtHxDkA47LznX62j+l6YQkpXDbvQegsDyxe/+JJsFQN7w==
  dependencies:
    exenv "^1.2.0"
    prop-types "^15.5.10"
    react-lifecycles-compat "^3.0.0"
    warning "^4.0.3"

react-motion@^0.5.0:
  version "0.5.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-motion/-/react-motion-0.5.2.tgz#0dd3a69e411316567927917c6626551ba0607316"
  integrity sha512-9q3YAvHoUiWlP3cK0v+w1N5Z23HXMj4IF4YuvjvWegWqNPfLXsOBE/V7UvQGpXxHFKRQQcNcVQE31g9SB/6qgQ==
  dependencies:
    performance-now "^0.2.0"
    prop-types "^15.5.8"
    raf "^3.1.0"

react-onclickoutside@^6.5.0:
  version "6.11.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-onclickoutside/-/react-onclickoutside-6.11.2.tgz#790e2100b9a3589eefca1404ecbf0476b81b7928"
  integrity sha512-640486eSwU/t5iD6yeTlefma8dI3bxPXD93hM9JGKyYITAd0P1JFkkcDeyHZRqNpY/fv1YW0Fad9BXr44OY8wQ==

react-prop-types@^0.4.0:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-prop-types/-/react-prop-types-0.4.0.tgz#f99b0bfb4006929c9af2051e7c1414a5c75b93d0"
  integrity sha512-IyjsJhDX9JkoOV9wlmLaS7z+oxYoIWhfzDcFy7inwoAKTu+VcVNrVpPmLeioJ94y6GeDRsnwarG1py5qofFQMg==
  dependencies:
    warning "^3.0.0"

react-quill@1.3.3:
  version "1.3.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-quill/-/react-quill-1.3.3.tgz#95b8e088ad4e4acc6c79c2f85bdc0460eebe08eb"
  integrity sha512-T9RubLaWJ8gCfp7sOqmFupjiTiEp/EdGqhCG+PWGKc5UHiK6xIWNKWYsOHHEhQ+sZCKs8u/DPx47gc1VfFmcLg==
  dependencies:
    "@types/quill" "1.3.10"
    "@types/react" "*"
    create-react-class "^15.6.0"
    lodash "^4.17.4"
    prop-types "^15.5.10"
    quill "^1.2.6"
    react-dom-factories "^1.0.0"

react-redux@6.0.1:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-redux/-/react-redux-6.0.1.tgz#0d423e2c1cb10ada87293d47e7de7c329623ba4d"
  integrity sha512-T52I52Kxhbqy/6TEfBv85rQSDz6+Y28V/pf52vDWs1YRXG19mcFOGfHnY2HsNFHyhP+ST34Aih98fvt6tqwVcQ==
  dependencies:
    "@babel/runtime" "^7.3.1"
    hoist-non-react-statics "^3.3.0"
    invariant "^2.2.4"
    loose-envify "^1.4.0"
    prop-types "^15.7.2"
    react-is "^16.8.2"

react-router-dom@4.2.2:
  version "4.2.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-router-dom/-/react-router-dom-4.2.2.tgz#c8a81df3adc58bba8a76782e946cbd4eae649b8d"
  integrity sha1-yKgd863Fi7qKdngulGy9Tq5km40=
  dependencies:
    history "^4.7.2"
    invariant "^2.2.2"
    loose-envify "^1.3.1"
    prop-types "^15.5.4"
    react-router "^4.2.0"
    warning "^3.0.0"

react-router@^4.2.0:
  version "4.3.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-router/-/react-router-4.3.1.tgz#aada4aef14c809cb2e686b05cee4742234506c4e"
  integrity sha1-qtpK7xTICcsuaGsFzuR0IjRQbE4=
  dependencies:
    history "^4.7.2"
    hoist-non-react-statics "^2.5.0"
    invariant "^2.2.4"
    loose-envify "^1.3.1"
    path-to-regexp "^1.7.0"
    prop-types "^15.6.1"
    warning "^4.0.1"

react-style-proptype@^3.0.0:
  version "3.2.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-style-proptype/-/react-style-proptype-3.2.2.tgz#d8e998e62ce79ec35b087252b90f19f1c33968a0"
  integrity sha512-ywYLSjNkxKHiZOqNlso9PZByNEY+FTyh3C+7uuziK0xFXu9xzdyfHwg4S9iyiRRoPCR4k2LqaBBsWVmSBwCWYQ==
  dependencies:
    prop-types "^15.5.4"

react-swipeable-views-core@^0.12.17:
  version "0.12.17"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-swipeable-views-core/-/react-swipeable-views-core-0.12.17.tgz#0998f55fd2f8595bcd01bead1c19516dc561c1cf"
  integrity sha512-KfQ+BPfLVBe7kxb+0zbVJp3eGQfZlt1gn5J+GYAgnYoZ29GrqkTfiQFKmrG4tmVnhxvRiXFA7Q0q9EBMYTc/FA==
  dependencies:
    "@babel/runtime" "7.0.0"
    warning "^4.0.1"

react-swipeable-views-utils@^0.12.18:
  version "0.12.18"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-swipeable-views-utils/-/react-swipeable-views-utils-0.12.18.tgz#6edfcc9d722901f0edaf79c78bc6c4a7cc25da81"
  integrity sha512-Y8APE9bSfZhebYHRGDOoHeIqNJbrZMlpIiHqD0hovdlYv+HvBAdqAKdwppzIE1QXISqBKwJAlYl86plvfTW1dg==
  dependencies:
    "@babel/runtime" "7.0.0"
    fbjs "^0.8.4"
    keycode "^2.1.7"
    prop-types "^15.6.0"
    react-event-listener "^0.6.0"
    react-swipeable-views-core "^0.12.17"

react-swipeable-views@^0.12.8:
  version "0.12.18"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-swipeable-views/-/react-swipeable-views-0.12.18.tgz#3b5d75bbfb826553fe0e42bc04b9c1c4b45acd77"
  integrity sha512-/8OCD2jBWzPNUi+GgRVwNqAz/SANoAzYDKnxE0VtchkL8gSssjEICVCaNF5aGFavY9fizjHxc2avDLfNYMy+Dg==
  dependencies:
    "@babel/runtime" "7.0.0"
    dom-helpers "^3.2.1"
    prop-types "^15.5.4"
    react-swipeable-views-core "^0.12.17"
    react-swipeable-views-utils "^0.12.18"
    warning "^4.0.1"

react-toolbox@^2.0.0-beta.12:
  version "2.0.0-beta.13"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-toolbox/-/react-toolbox-2.0.0-beta.13.tgz#43f80c2e672ebb8bd56baff24167f65e88858950"
  integrity sha512-i2/Gd+75FMS1DE4NYL5xGMxzvR8Du5Qvr8vBgkk074vS1xlurel6VqwKe5ar4JhUqMqyQr12biGj5beRQjSDig==
  dependencies:
    "@types/prop-types" "^15.5.2"
    classnames "^2.2.5"
    core-js "^2.4.0"
    ramda "^0.23.0"
    react-css-themr "^2.1.2"
    react-style-proptype "^3.0.0"
    react-transition-group "^2.2.1"

react-tooltip@5.11.1:
  version "5.11.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-tooltip/-/react-tooltip-5.11.1.tgz#75857ffaed1d13e3e35189934e8a373751fc78c0"
  integrity sha1-dYV/+u0dE+PjUYmTToo3N1H8eMA=
  dependencies:
    "@floating-ui/dom" "^1.0.0"
    classnames "^2.3.0"

react-transition-group@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-transition-group/-/react-transition-group-1.2.1.tgz#e11f72b257f921b213229a774df46612346c7ca6"
  integrity sha512-CWaL3laCmgAFdxdKbhhps+c0HRGF4c+hdM4H23+FI1QBNUyx/AMeIJGWorehPNSaKnQNOAxL7PQmqMu78CDj3Q==
  dependencies:
    chain-function "^1.0.0"
    dom-helpers "^3.2.0"
    loose-envify "^1.3.1"
    prop-types "^15.5.6"
    warning "^3.0.0"

react-transition-group@^2.2.1:
  version "2.9.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react-transition-group/-/react-transition-group-2.9.0.tgz#df9cdb025796211151a436c69a8f3b97b5b07c8d"
  integrity sha512-+HzNTCHpeQyl4MJ/bdE0u6XRMe9+XG/+aL4mCxVN4DnPBQ0/5bfHWPDuOZUzYdMj94daZaZdCCc1Dzt9R/xSSg==
  dependencies:
    dom-helpers "^3.4.0"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"
    react-lifecycles-compat "^3.0.4"

react@16.13.1:
  version "16.13.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/react/-/react-16.13.1.tgz#2e818822f1a9743122c063d6410d85c1e3afe48e"
  integrity sha1-LoGIIvGpdDEiwGPWQQ2FweOv5I4=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"

readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.0.5, readable-stream@^2.3.6:
  version "2.3.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/readable-stream/-/readable-stream-2.3.7.tgz#1eca1cf711aef814c04f62252a36a62f6cb23b57"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6:
  version "3.6.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/readable-stream/-/readable-stream-3.6.0.tgz#337bbda3adc0706bd3e024426a286d4b4b2c9198"
  integrity sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@^2.0.0:
  version "2.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/readdirp/-/readdirp-2.2.1.tgz#0e87622a3325aa33e892285caf8b4e846529a525"
  integrity sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ==
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/readdirp/-/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.7.0:
  version "0.7.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/rechoir/-/rechoir-0.7.1.tgz#9478a96a1ca135b5e88fc027f03ee92d6c645686"
  integrity sha512-/njmZ8s1wVeR6pjTZ+0nCnv8SpZNRMT2D1RLOJQESlYFDBvwpTA4KWJpZ+sBJ4+vhjILRcK7JIFdGCdxEAAitg==
  dependencies:
    resolve "^1.9.0"

redux-saga@0.16.0:
  version "0.16.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/redux-saga/-/redux-saga-0.16.0.tgz#0a231db0a1489301dd980f6f2f88d8ced418f724"
  integrity sha512-wPh8sx5QA7ujYs2nOXBzMS5EnTM14f+8qJEq1zKHU6HGJuQ48Sqd6+dFKpCh02VJPGHgko7IL3tfKEIzR+JqjQ==

redux-thunk@2.2.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/redux-thunk/-/redux-thunk-2.2.0.tgz#e615a16e16b47a19a515766133d1e3e99b7852e5"
  integrity sha512-OOFWh9mt/7i94QPq4IAxhSIUyfIJJRnk6pe1IwgXethQik3kyg1wuxVZZlW9QOmL5rP/MrwzV+Cb+/HBKlvM8Q==

redux@3.7.2:
  version "3.7.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/redux/-/redux-3.7.2.tgz#06b73123215901d25d065be342eb026bc1c8537b"
  integrity sha512-pNqnf9q1hI5HHZRBkj3bAngGZW/JMCmexDlOxw4XagXY2o1327nHH54LoTjiPJ0gizoqPDRqWyX/00g0hD6w+A==
  dependencies:
    lodash "^4.2.1"
    lodash-es "^4.2.1"
    loose-envify "^1.1.0"
    symbol-observable "^1.0.3"

redux@^4.0.0:
  version "4.0.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/redux/-/redux-4.0.5.tgz#4db5de5816e17891de8a80c424232d06f051d93f"
  integrity sha1-TbXeWBbheJHeioDEJCMtBvBR2T8=
  dependencies:
    loose-envify "^1.4.0"
    symbol-observable "^1.2.0"

regenerate-unicode-properties@^10.0.1:
  version "10.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/regenerate-unicode-properties/-/regenerate-unicode-properties-10.0.1.tgz#7f442732aa7934a3740c779bb9b3340dccc1fb56"
  integrity sha512-vn5DU6yg6h8hP/2OkQo3K7uVILvY4iu0oI4t3HFa81UPkhGJwkRwM10JEc3upjdhHjs/k8GJY1sRBhk5sr69Bw==
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/regenerate/-/regenerate-1.4.2.tgz#b9346d8827e8f5a32f7ba29637d398b69014848a"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.12.0:
  version "0.12.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/regenerator-runtime/-/regenerator-runtime-0.12.1.tgz#fa1a71544764c036f8c49b13a08b2594c9f8a0de"
  integrity sha512-odxIc1/vDlo4iZcfXqRYFj0vpXFNoGdKMAUieAlFYO6m/nl5e9KR/beGf41z4a1FI+aQgtjhuaSlDxQ0hmkrHg==

regenerator-runtime@^0.13.4:
  version "0.13.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/regenerator-runtime/-/regenerator-runtime-0.13.5.tgz#d878a1d094b4306d10b9096484b33ebd55e26697"
  integrity sha512-ZS5w8CpKFinUzOwW3c83oPeVXoNsrLsaCoLtJvAClH135j/R77RuymhiSErhm2lKcwSCIpmvIWSbDkIfAqKQlA==

regenerator-transform@^0.14.2:
  version "0.14.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/regenerator-transform/-/regenerator-transform-0.14.5.tgz#c98da154683671c9c4dcb16ece736517e1b7feb4"
  integrity sha1-yY2hVGg2ccnE3LFuznNlF+G3/rQ=
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-cache@^0.4.2:
  version "0.4.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/regex-cache/-/regex-cache-0.4.4.tgz#75bdc58a2a1496cec48a12835bc54c8d562336dd"
  integrity sha1-db3FiioUls7EihKDW8VMjVYjNt0=
  dependencies:
    is-equal-shallow "^0.1.3"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/regex-not/-/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.2.0:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/regexp.prototype.flags/-/regexp.prototype.flags-1.3.0.tgz#7aba89b3c13a64509dabcf3ca8d9fbb9bdf5cb75"
  integrity sha512-2+Q0C5g951OlYlJz6yu5/M33IcsESLlLfsyIaLJaG4FA2r4yP8MvVMJUUP/fVBkSpbbbZlS5gynbEWLipiiXiQ==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

regexpu-core@^5.0.1:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/regexpu-core/-/regexpu-core-5.0.1.tgz#c531122a7840de743dcf9c83e923b5560323ced3"
  integrity sha512-CriEZlrKK9VJw/xQGJpQM5rY88BtuL8DM+AEwvcThHilbxiTAy8vq4iJnd2tqq8wLmjbGZzP7ZcKFjbGkmEFrw==
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.0.1"
    regjsgen "^0.6.0"
    regjsparser "^0.8.2"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.0.0"

regjsgen@^0.6.0:
  version "0.6.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/regjsgen/-/regjsgen-0.6.0.tgz#83414c5354afd7d6627b16af5f10f41c4e71808d"
  integrity sha512-ozE883Uigtqj3bx7OhL1KNbCzGyW2NQZPl6Hs09WTvCuZD5sTI4JY58bkbQWa/Y9hxIsvJ3M8Nbf7j54IqeZbA==

regjsparser@^0.8.2:
  version "0.8.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/regjsparser/-/regjsparser-0.8.4.tgz#8a14285ffcc5de78c5b95d62bbf413b6bc132d5f"
  integrity sha512-J3LABycON/VNEu3abOviqGHuB/LOtOQj8SKmfP9anY5GfAVw/SPjwzSjxGjbZXIxbGfqTHtJw58C2Li/WkStmA==
  dependencies:
    jsesc "~0.5.0"

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

repeat-element@^1.1.2:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/repeat-element/-/repeat-element-1.1.3.tgz#782e0d825c0c5a3bb39731f84efee6b742e6b1ce"
  integrity sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=

repeat-string@^1.5.2, repeat-string@^1.6.1:
  version "1.6.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/repeat-string/-/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/require-from-string/-/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"
  integrity sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/requires-port/-/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==

resize-observer-polyfill@^1.4.2, resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz#0e9020dd3d21024458d4ebd27e23e40269810464"
  integrity sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/resolve-cwd/-/resolve-cwd-3.0.0.tgz#0f0075f1bb2544766cf73ba6a6e2adfebcb13f2d"
  integrity sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/resolve-from/-/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/resolve-from/-/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==

resolve-pathname@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/resolve-pathname/-/resolve-pathname-3.0.0.tgz#99d02224d3cf263689becbb393bc560313025dcd"
  integrity sha1-mdAiJNPPJjaJvsuzk7xWAxMCXc0=

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/resolve-url/-/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.14.2, resolve@^1.9.0:
  version "1.22.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/resolve/-/resolve-1.22.0.tgz#5e0b8c67c15df57a89bdbabe603a002f21731198"
  integrity sha512-Hhtrw0nLeSrFQ7phPp4OOcVjLPIeMnRlr5mcnVuMe7M/7eBn98A3hmFRLoFo3DLZkivSYwhRUJTyPyWAk56WLw==
  dependencies:
    is-core-module "^2.8.1"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

ret@~0.1.10:
  version "0.1.15"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ret/-/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

retry@^0.13.1:
  version "0.13.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/retry/-/retry-0.13.1.tgz#185b1587acf67919d63b357349e03537b2484658"
  integrity sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/reusify/-/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

right-align@^0.1.1:
  version "0.1.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/right-align/-/right-align-0.1.3.tgz#61339b722fe6a3515689210d24e14c96148613ef"
  integrity sha1-YTObci/mo1FWiSENJOFMlhSGE+8=
  dependencies:
    align-text "^0.1.1"

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/rimraf/-/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

ripemd160@0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ripemd160/-/ripemd160-0.2.0.tgz#2bf198bde167cacfa51c0a928e84b68bbe171fce"
  integrity sha1-K/GYveFnys+lHAqSjoS2i74XH84=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/run-parallel/-/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

safe-buffer@5.1.2, safe-buffer@>=5.1.0, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/safe-buffer/-/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/safe-regex/-/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/safer-buffer/-/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sass-loader@12.6.0:
  version "12.6.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/sass-loader/-/sass-loader-12.6.0.tgz#5148362c8e2cdd4b950f3c63ac5d16dbfed37bcb"
  integrity sha512-oLTaH0YCtX4cfnJZxKSLAyglED0naiYfNG1iXfU5w1LNZ+ukoA5DtyDIN5zmKVZwYNJP4KRc5Y3hkWga+7tYfA==
  dependencies:
    klona "^2.0.4"
    neo-async "^2.6.2"

sass@^1.26.3:
  version "1.26.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/sass/-/sass-1.26.3.tgz#412df54486143b76b5a65cdf7569e86f44659f46"
  integrity sha1-QS31RIYUO3a1plzfdWnob0Rln0Y=
  dependencies:
    chokidar ">=2.0.0 <4.0.0"

scheduler@^0.19.1:
  version "0.19.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/scheduler/-/scheduler-0.19.1.tgz#4f3e2ed2c1a7d65681f4c854fa8c5a1ccb40f196"
  integrity sha1-Tz4u0sGn1laB9MhU+oxaHMtA8ZY=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

schema-utils@4.0.0, schema-utils@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/schema-utils/-/schema-utils-4.0.0.tgz#60331e9e3ae78ec5d16353c467c34b3a0a1d3df7"
  integrity sha512-1edyXKgh6XnJsJSQ8mKWXnN/BVaIbFMLpouRUrXgVq7WYne5kw3MW7UPhO44uRXQSIpTSXoJbmrR2X0w9kUTyg==
  dependencies:
    "@types/json-schema" "^7.0.9"
    ajv "^8.8.0"
    ajv-formats "^2.1.1"
    ajv-keywords "^5.0.0"

schema-utils@^2.6.5:
  version "2.7.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/schema-utils/-/schema-utils-2.7.1.tgz#1ca4f32d1b24c590c203b8e7a50bf0ea4cd394d7"
  integrity sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=
  dependencies:
    "@types/json-schema" "^7.0.5"
    ajv "^6.12.4"
    ajv-keywords "^3.5.2"

schema-utils@^3.0.0:
  version "3.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/schema-utils/-/schema-utils-3.3.0.tgz#f50a88877c3c01652a15b622ae9e9795df7a60fe"
  integrity sha1-9QqIh3w8AWUqFbYirp6Xld96YP4=
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

schema-utils@^3.1.1:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/schema-utils/-/schema-utils-3.1.1.tgz#bc74c4b6b6995c1d88f76a8b77bea7219e0c8281"
  integrity sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw==
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

schema-utils@^3.1.2:
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/schema-utils/-/schema-utils-3.1.2.tgz#36c10abca6f7577aeae136c804b0c741edeadc99"
  integrity sha512-pvjEHOgWc9OWA/f/DE3ohBWTD6EleVLf7iFUkoSwAxttdBhB9QUebQgxER2kWueOvRJXPHNnyrvvh9eZINB8Eg==
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/select-hose/-/select-hose-2.0.0.tgz#625d8658f865af43ec962bfc376a37359a4994ca"
  integrity sha512-mEugaLK+YfkijB4fx0e6kImuJdCIt2LxCRcbEYPqRGCs4F2ogyfZU5IAZRdjCP8JPq2AtdNoC/Dux63d9Kiryg==

selfsigned@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/selfsigned/-/selfsigned-2.0.0.tgz#e927cd5377cbb0a1075302cff8df1042cc2bce5b"
  integrity sha512-cUdFiCbKoa1mZ6osuJs2uDHrs0k0oprsKveFiiaBKCNq3SYyb5gs2HxhQyDNLCmL51ZZThqi4YNDpCK6GOP1iQ==
  dependencies:
    node-forge "^1.2.0"

semver@7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/semver/-/semver-7.0.0.tgz#5f3ca35761e47e05b206c6daff2cf814f0316b8e"
  integrity sha1-XzyjV2HkfgWyBsba/yz4FPAxa44=

semver@^6.0.0, semver@^6.1.1, semver@^6.1.2, semver@^6.3.0:
  version "6.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/semver/-/semver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
  integrity sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==

semver@^7.3.4, semver@^7.3.5:
  version "7.3.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/semver/-/semver-7.3.5.tgz#0b621c879348d8998e4b0e4be94b3f12e6018ef7"
  integrity sha512-PoeGJYh8HK4BTO/a9Tf6ZG3veo/A7ZVsYrSA6J8ny9nb3B1VrpkuN+z9OE5wfE5p6H4LchYZsegiQgbJD94ZFQ==
  dependencies:
    lru-cache "^6.0.0"

send@0.17.1:
  version "0.17.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/send/-/send-0.17.1.tgz#c1d8b059f7900f7466dd4938bdc44e11ddb376c8"
  integrity sha512-BsVKsiGcQMFwT8UxypobUKyv7irCNRHk1T0G680vk88yf6LBByGcZJOTJCrTP2xVN6yI+XjPJcNuE3V4fT9sAg==
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "~1.7.2"
    mime "1.6.0"
    ms "2.1.1"
    on-finished "~2.3.0"
    range-parser "~1.2.1"
    statuses "~1.5.0"

serialize-javascript@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/serialize-javascript/-/serialize-javascript-6.0.0.tgz#efae5d88f45d7924141da8b5c3a7a7e663fefeb8"
  integrity sha512-Qr3TosvguFt8ePWqsvRfrKyQXIiW+nGbYpy8XK24NQHE83caxWt+mIymTT19DGFbNWNLfEwsrkSmN64lVWB9ag==
  dependencies:
    randombytes "^2.1.0"

serialize-javascript@^6.0.1:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/serialize-javascript/-/serialize-javascript-6.0.1.tgz#b206efb27c3da0b0ab6b52f48d170b7996458e5c"
  integrity sha512-owoXEFjWRllis8/M1Q+Cw5k8ZH40e3zhp/ovX+Xr/vi1qj6QesbyXXViFbpNvWvPNAD62SutwEXavefrLJWj7w==
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/serve-index/-/serve-index-1.9.1.tgz#d3768d69b1e7d82e5ce050fff5b453bea12a9239"
  integrity sha512-pXHfKNP4qujrtteMrSBb0rc8HJ9Ms/GrXwcUtUtD5s4ewDJI8bT3Cz2zTVRMKtri49pLx2e0Ya8ziP5Ya2pZZw==
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.14.1:
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/serve-static/-/serve-static-1.14.1.tgz#666e636dc4f010f7ef29970a88a674320898b2f9"
  integrity sha512-JMrvUwE54emCYWlTI+hGrGv5I8dEwmco/00EvkzIIsR7MqrHonbD9pO2MOfFnpFntl7ecpZs+3mW+XbQZu9QCg==
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.17.1"

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/set-value/-/set-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b"
  integrity sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw==
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.4, setimmediate@^1.0.5:
  version "1.0.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/setimmediate/-/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/setprototypeof/-/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"
  integrity sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/setprototypeof/-/setprototypeof-1.1.1.tgz#7e95acb24aa92f5885e0abef5ba131330d4ae683"
  integrity sha512-JvdAWfbXeIGaZ9cILp38HntZSFSo3mWg6xGcJJsd+d4aRMOqauag1C63dJfDw7OaMYwEbHMOxEZ1lqVRYP2OAw==

sha.js@2.2.6:
  version "2.2.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/sha.js/-/sha.js-2.2.6.tgz#17ddeddc5f722fb66501658895461977867315ba"
  integrity sha1-F93t3F9yL7ZlAWWIlUYZd4ZzFbo=

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/shallow-clone/-/shallow-clone-3.0.1.tgz#8f2981ad92531f55035b01fb230769a40e02efa3"
  integrity sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==
  dependencies:
    kind-of "^6.0.2"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/shebang-command/-/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/shebang-regex/-/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

signal-exit@^3.0.3:
  version "3.0.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/signal-exit/-/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

slash@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/slash/-/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/snapdragon-node/-/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/snapdragon-util/-/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/snapdragon/-/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

sockjs@^0.3.21:
  version "0.3.24"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/sockjs/-/sockjs-0.3.24.tgz#c9bc8995f33a111bea0395ec30aa3206bdb5ccce"
  integrity sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ==
  dependencies:
    faye-websocket "^0.11.3"
    uuid "^8.3.2"
    websocket-driver "^0.7.4"

source-list-map@~0.1.7:
  version "0.1.8"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/source-list-map/-/source-list-map-0.1.8.tgz#c550b2ab5427f6b3f21f5afead88c4f5587b2106"
  integrity sha1-xVCyq1Qn9rPyH1r+rYjE9Vh7IQY=

source-map-js@^1.0.1, source-map-js@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/source-map-js/-/source-map-js-1.0.2.tgz#adbc361d9c62df380125e7f161f71c826f1e490c"
  integrity sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==

source-map-loader@3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/source-map-loader/-/source-map-loader-3.0.1.tgz#9ae5edc7c2d42570934be4c95d1ccc6352eba52d"
  integrity sha512-Vp1UsfyPvgujKQzi4pyDiTOnE3E4H+yHvkVRN3c/9PJmQS4CQJExvcDvaX/D+RV+xQben9HJ56jMJS3CgUeWyA==
  dependencies:
    abab "^2.0.5"
    iconv-lite "^0.6.3"
    source-map-js "^1.0.1"

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/source-map-resolve/-/source-map-resolve-0.5.3.tgz#190866bece7553e1f8f267a2ee82c606b5509a1a"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@~0.5.20:
  version "0.5.21"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/source-map-support/-/source-map-support-0.5.21.tgz#04fe7c7f9e1ed2d662233c28cb2b35b9f63f6e4f"
  integrity sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/source-map-url/-/source-map-url-0.4.0.tgz#3e935d7ddd73631b97659956d55128e87b5084a3"
  integrity sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=

source-map@^0.5.0, source-map@^0.5.6, source-map@~0.5.1:
  version "0.5.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/source-map/-/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1:
  version "0.6.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/source-map/-/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@~0.4.1:
  version "0.4.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/source-map/-/source-map-0.4.4.tgz#eba4f5da9c0dc999de68032d8b4f76173652036b"
  integrity sha1-66T12pwNyZneaAMti092FzZSA2s=
  dependencies:
    amdefine ">=0.0.4"

source-map@~0.7.2:
  version "0.7.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/source-map/-/source-map-0.7.3.tgz#5302f8169031735226544092e64981f751750383"
  integrity sha1-UwL4FpAxc1ImVECS5kmB91F1A4M=

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/spdy-transport/-/spdy-transport-3.0.0.tgz#00d4863a6400ad75df93361a1608605e5dcdcf31"
  integrity sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw==
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/spdy/-/spdy-4.0.2.tgz#b74f466203a3eda452c02492b91fb9e84a27677b"
  integrity sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA==
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/split-string/-/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

static-extend@^0.1.1:
  version "0.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/static-extend/-/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/statuses/-/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==

stream-browserify@^2.0.1:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/stream-browserify/-/stream-browserify-2.0.2.tgz#87521d38a44aa7ee91ce1cd2a47df0cb49dd660b"
  integrity sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-http@^2.3.1:
  version "2.8.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/stream-http/-/stream-http-2.8.3.tgz#b2d242469288a5a27ec4fe8933acf623de6514fc"
  integrity sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw==
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

string.prototype.trimend@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/string.prototype.trimend/-/string.prototype.trimend-1.0.1.tgz#85812a6b847ac002270f5808146064c995fb6913"
  integrity sha1-hYEqa4R6wAInD1gIFGBkyZX7aRM=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trimleft@^2.1.1:
  version "2.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/string.prototype.trimleft/-/string.prototype.trimleft-2.1.2.tgz#4408aa2e5d6ddd0c9a80739b087fbc067c03b3cc"
  integrity sha512-gCA0tza1JBvqr3bfAIFJGqfdRTyPae82+KTnm3coDXkZN9wnuW3HjGgN386D7hfv5CHQYCI022/rJPVlqXyHSw==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"
    string.prototype.trimstart "^1.0.0"

string.prototype.trimright@^2.1.1:
  version "2.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/string.prototype.trimright/-/string.prototype.trimright-2.1.2.tgz#c76f1cef30f21bbad8afeb8db1511496cfb0f2a3"
  integrity sha512-ZNRQ7sY3KroTaYjRS6EbNiiHrOkjihL9aQE/8gfQ4DtAC/aEBRHFJa44OmoWxGGqXuJlfKkZW4WcXErGr+9ZFg==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"
    string.prototype.trimend "^1.0.0"

string.prototype.trimstart@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/string.prototype.trimstart/-/string.prototype.trimstart-1.0.1.tgz#14af6d9f34b053f7cfc89b72f8f2ee14b9039a54"
  integrity sha1-FK9tnzSwU/fPyJty+PLuFLkDmlQ=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string_decoder@^0.10.25:
  version "0.10.31"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/string_decoder/-/string_decoder-0.10.31.tgz#62e203bc41766c6c28c9fc84301dab1c5310fa94"
  integrity sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=

string_decoder@^1.1.1, string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/string_decoder/-/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^7.0.0:
  version "7.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/strip-ansi/-/strip-ansi-7.0.1.tgz#61740a08ce36b61e50e65653f07060d000975fb2"
  integrity sha512-cXNxvT8dFNRVfhVME3JAe98mkXDYN2O1l7jmcwMnOslDeESg1rF/OZMtK0nRAhiari1unG5cD4jG3rapUAkLbw==
  dependencies:
    ansi-regex "^6.0.1"

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/strip-final-newline/-/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

style-loader@3.3.1:
  version "3.3.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/style-loader/-/style-loader-3.3.1.tgz#057dfa6b3d4d7c7064462830f9113ed417d38575"
  integrity sha512-GPcQ+LDJbrcxHORTRes6Jy2sfvK2kS6hpSfI/fXhPt+spVzxF6LJ1dHLN9zIGmVaaP044YKaIatFaufENRiDoQ==

stylehacks@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/stylehacks/-/stylehacks-6.0.0.tgz#9fdd7c217660dae0f62e14d51c89f6c01b3cb738"
  integrity sha512-+UT589qhHPwz6mTlCLSt/vMNTJx8dopeJlZAlBMJPWA3ORqu6wmQY7FBXf+qD+FsqoBJODyqNxOUP3jdntFRdw==
  dependencies:
    browserslist "^4.21.4"
    postcss-selector-parser "^6.0.4"

supports-color@^3.1.0:
  version "3.2.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/supports-color/-/supports-color-3.2.3.tgz#65ac0504b3954171d8a64946b2ae3cbb8a5f54f6"
  integrity sha1-ZawFBLOVQXHYpklGsq48u4pfVPY=
  dependencies:
    has-flag "^1.0.0"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/supports-color/-/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/supports-color/-/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/supports-color/-/supports-color-8.1.1.tgz#cd6fc17e28500cff56c1b86c0a7fd4a54a73005c"
  integrity sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

svgo@^3.0.2:
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/svgo/-/svgo-3.0.2.tgz#5e99eeea42c68ee0dc46aa16da093838c262fe0a"
  integrity sha512-Z706C1U2pb1+JGP48fbazf3KxHrWOsLme6Rv7imFBn5EnuanDW1GPaA/P1/dvObE670JDePC3mnj0k0B7P0jjQ==
  dependencies:
    "@trysound/sax" "0.2.0"
    commander "^7.2.0"
    css-select "^5.1.0"
    css-tree "^2.2.1"
    csso "^5.0.5"
    picocolors "^1.0.0"

symbol-observable@^1.0.3, symbol-observable@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/symbol-observable/-/symbol-observable-1.2.0.tgz#c22688aed4eab3cdc2dfeacbb561660560a00804"
  integrity sha1-wiaIrtTqs83C3+rLtWFmBWCgCAQ=

tapable@^0.1.8, tapable@~0.1.8:
  version "0.1.10"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/tapable/-/tapable-0.1.10.tgz#29c35707c2b70e50d07482b5d202e8ed446dafd4"
  integrity sha1-KcNXB8K3DlDQdIK10gLo7URtr9Q=

tapable@^2.1.1, tapable@^2.2.0, tapable@^2.2.1:
  version "2.2.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/tapable/-/tapable-2.2.1.tgz#1967a73ef4060a82f12ab96af86d52fdb76eeca0"
  integrity sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==

terser-webpack-plugin@5.3.1:
  version "5.3.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/terser-webpack-plugin/-/terser-webpack-plugin-5.3.1.tgz#0320dcc270ad5372c1e8993fabbd927929773e54"
  integrity sha512-GvlZdT6wPQKbDNW/GDQzZFg/j4vKU96yl2q6mcUkzKOgW4gwf1Z8cZToUCrz31XHlPWH8MVb1r2tFtdDtTGJ7g==
  dependencies:
    jest-worker "^27.4.5"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.0"
    source-map "^0.6.1"
    terser "^5.7.2"

terser-webpack-plugin@^5.3.7:
  version "5.3.7"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/terser-webpack-plugin/-/terser-webpack-plugin-5.3.7.tgz#ef760632d24991760f339fe9290deb936ad1ffc7"
  integrity sha512-AfKwIktyP7Cu50xNjXF/6Qb5lBNzYaWpU6YfoX3uZicTx0zTy0stDDCsvjDapKsSDvOeWo5MEq4TmdBy2cNoHw==
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.17"
    jest-worker "^27.4.5"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.1"
    terser "^5.16.5"

terser@^5.16.5:
  version "5.17.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/terser/-/terser-5.17.1.tgz#948f10830454761e2eeedc6debe45c532c83fd69"
  integrity sha512-hVl35zClmpisy6oaoKALOpS0rDYLxRFLHhRuDlEGTKey9qHjS1w9GMORjuwIMt70Wan4lwsLYyWDVnWgF+KUEw==
  dependencies:
    "@jridgewell/source-map" "^0.3.2"
    acorn "^8.5.0"
    commander "^2.20.0"
    source-map-support "~0.5.20"

terser@^5.7.2:
  version "5.11.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/terser/-/terser-5.11.0.tgz#2da5506c02e12cd8799947f30ce9c5b760be000f"
  integrity sha512-uCA9DLanzzWSsN1UirKwylhhRz3aKPInlfmpGfw8VN6jHsAtu8HJtIpeeHHK23rxnE/cDc+yvmq5wqkIC6Kn0A==
  dependencies:
    acorn "^8.5.0"
    commander "^2.20.0"
    source-map "~0.7.2"
    source-map-support "~0.5.20"

thunky@^1.0.2:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/thunky/-/thunky-1.1.0.tgz#5abaf714a9405db0504732bbccd2cedd9ef9537d"
  integrity sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==

timers-browserify@^2.0.2:
  version "2.0.11"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/timers-browserify/-/timers-browserify-2.0.11.tgz#800b1f3eee272e5bc53ee465a04d0e804c31211f"
  integrity sha1-gAsfPu4nLlvFPuRloE0OgEwxIR8=
  dependencies:
    setimmediate "^1.0.4"

tiny-invariant@^1.0.2:
  version "1.3.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/tiny-invariant/-/tiny-invariant-1.3.3.tgz#46680b7a873a0d5d10005995eb90a70d74d60127"
  integrity sha1-RmgLeoc6DV0QAFmV65CnDXTWASc=

tiny-warning@^1.0.0:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/tiny-warning/-/tiny-warning-1.0.3.tgz#94a30db453df4c643d0fd566060d60a875d84754"
  integrity sha1-lKMNtFPfTGQ9D9VmBg1gqHXYR1Q=

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz#7d229b1fcc637e466ca081180836a7aabff83f43"
  integrity sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=

to-camel-case@1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/to-camel-case/-/to-camel-case-1.0.0.tgz#1a56054b2f9d696298ce66a60897322b6f423e46"
  integrity sha512-nD8pQi5H34kyu1QDMFjzEIYqk0xa9Alt6ZfrdEMuHCFOfTLhDG5pgTu/aAM9Wt9lXILwlXmWP43b8sav0GNE8Q==
  dependencies:
    to-space-case "^1.0.0"

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/to-fast-properties/-/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-no-case@^1.0.0:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/to-no-case/-/to-no-case-1.0.2.tgz#c722907164ef6b178132c8e69930212d1b4aa16a"
  integrity sha512-Z3g735FxuZY8rodxV4gH7LxClE4H0hTIyHNIHdk+vpQxjLm0cwnKXq/OFVZ76SOQmto7txVcwSCwkU5kqp+FKg==

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/to-object-path/-/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/to-regex-range/-/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/to-regex-range/-/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/to-regex/-/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
  integrity sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw==
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

to-space-case@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/to-space-case/-/to-space-case-1.0.0.tgz#b052daafb1b2b29dc770cea0163e5ec0ebc9fc17"
  integrity sha512-rLdvwXZ39VOn1IxGL3V6ZstoTbwLRckQmn/U8ZDLuWwIXNpuZDhQ3AiRUlhTbOXFVE9C+dR51wM0CBDhk31VcA==
  dependencies:
    to-no-case "^1.0.0"

toidentifier@1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/toidentifier/-/toidentifier-1.0.0.tgz#7e1be3470f1e77948bc43d94a3c8f4d7752ba553"
  integrity sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=

ts-loader@9.2.6:
  version "9.2.6"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ts-loader/-/ts-loader-9.2.6.tgz#9937c4dd0a1e3dbbb5e433f8102a6601c6615d74"
  integrity sha512-QMTC4UFzHmu9wU2VHZEmWWE9cUajjfcdcws+Gh7FhiO+Dy0RnR1bNz0YCHqhI0yRowCE9arVnNxYHqELOy9Hjw==
  dependencies:
    chalk "^4.1.0"
    enhanced-resolve "^5.0.0"
    micromatch "^4.0.0"
    semver "^7.3.4"

tslib@^1.9.0:
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/tslib/-/tslib-1.11.1.tgz#eb15d128827fbee2841549e171f45ed338ac7e35"
  integrity sha512-aZW88SY8kQbU7gpV19lN24LtXh/yD4ZZg6qieAJDDg+YBsJcSmLGK9QpnUjAKVG/xefmvJGd1WUmfpT/g6AJGA==

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/tty-browserify/-/tty-browserify-0.0.0.tgz#a157ba402da24e9bf957f9aa69d524eed42901a6"
  integrity sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=

type-is@~1.6.17, type-is@~1.6.18:
  version "1.6.18"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/type-is/-/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typescript@3.9.2:
  version "3.9.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/typescript/-/typescript-3.9.2.tgz#64e9c8e9be6ea583c54607677dd4680a1cf35db9"
  integrity sha512-q2ktq4n/uLuNNShyayit+DTobV2ApPEo/6so68JaD5ojvc/6GClBipedB9zNWYxRSAlZXAe405Rlijzl6qDiSw==

ua-parser-js@^0.7.18:
  version "0.7.21"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ua-parser-js/-/ua-parser-js-0.7.21.tgz#853cf9ce93f642f67174273cc34565ae6f308777"
  integrity sha512-+O8/qh/Qj8CgC6eYBVBykMrNtp5Gebn4dlGD/kKXVkJNDwyrAwSIqwz8CDf+tsAIWVycKcku6gIXJ0qwx/ZXaQ==

uglify-js@~2.7.3:
  version "2.7.5"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/uglify-js/-/uglify-js-2.7.5.tgz#4612c0c7baaee2ba7c487de4904ae122079f2ca8"
  integrity sha1-RhLAx7qu4rp8SH3kkErhIgefLKg=
  dependencies:
    async "~0.2.6"
    source-map "~0.5.1"
    uglify-to-browserify "~1.0.0"
    yargs "~3.10.0"

uglify-to-browserify@~1.0.0:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz#6e0924d6bda6b5afe349e39a6d632850a0f882b7"
  integrity sha1-bgkk1r2mta/jSeOabWMoUKD4grc=

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz#301acdc525631670d39f6146e0e77ff6bbdebddc"
  integrity sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ==

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz#54fd16e0ecb167cf04cf1f756bdcc92eba7976c3"
  integrity sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.0.0.tgz#1a01aa57247c14c568b89775a54938788189a714"
  integrity sha512-7Yhkc0Ye+t4PNYzOGKedDhXbYIBe1XEQYQxOPyhcXNMJ0WCABqqj6ckydd6pWRZTHV4GuCPKdBAUiMc60tsKVw==

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.0.0.tgz#0a36cb9a585c4f6abd51ad1deddb285c165297c8"
  integrity sha512-5Zfuy9q/DFr4tfO7ZPeVXb1aPoeQSdeFMLpYuFebehDAhbuevLs5yxSZmIFN1tP5F9Wl4IpJrYojg85/zgyZHQ==

union-value@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/union-value/-/union-value-1.0.1.tgz#0b6fe7b835aecda61c6ea4d4f02c14221e109847"
  integrity sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg==
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

universalify@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/universalify/-/universalify-2.0.0.tgz#75a4984efedc4b08975c5aeb73f530d02df25717"
  integrity sha1-daSYTv7cSwiXXFrrc/Uw0C3yVxc=

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/unpipe/-/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/unset-value/-/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

update-browserslist-db@^1.0.10:
  version "1.0.11"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/update-browserslist-db/-/update-browserslist-db-1.0.11.tgz#9a2a641ad2907ae7b3616506f4b977851db5b940"
  integrity sha512-dCwEFf0/oT85M1fHBg4F0jtLwJrutGoHSQXCh7u4o2t1drG+c0a9Flnqww6XUKSfQMPpJBRjU8d4RXB09qtvaA==
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

uri-js@^4.2.2:
  version "4.2.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/uri-js/-/uri-js-4.2.2.tgz#94c540e1ff772956e2299507c010aea6c8838eb0"
  integrity sha512-KY9Frmirql91X2Qgjry0Wd4Y+YTdrdZheS8TFwvkbLWf/G5KNJDCh6pKL5OZctEW4+0Baa5idK2ZQuELRwPznQ==
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/urix/-/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url@^0.11.0:
  version "0.11.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/url/-/url-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"
  integrity sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

use@^3.1.0:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/use/-/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util@0.10.3:
  version "0.10.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/util/-/util-0.10.3.tgz#7afb1afe50805246489e3db7fe0ed379336ac0f9"
  integrity sha1-evsa/lCAUkZInj23/g7TeTNqwPk=
  dependencies:
    inherits "2.0.1"

util@^0.10.3:
  version "0.10.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/util/-/util-0.10.4.tgz#3aa0125bfe668a4672de58857d3ace27ecb76901"
  integrity sha512-0Pm9hTQ3se5ll1XihRic3FDIku70C+iHUdT/W926rSgHV5QgXsYbKZN8MSC3tJtSkhuROzvsQjAaFENRXr+19A==
  dependencies:
    inherits "2.0.3"

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/utils-merge/-/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/uuid/-/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

value-equal@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/value-equal/-/value-equal-1.0.1.tgz#1e0b794c734c5c0cade179c437d356d931a34d6c"
  integrity sha1-Hgt5THNMXAyt4XnEN9NW2TGjTWw=

vary@~1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/vary/-/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vm-browserify@0.0.4:
  version "0.0.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/vm-browserify/-/vm-browserify-0.0.4.tgz#5d7ea45bbef9e4a6ff65f95438e0a87c357d5a73"
  integrity sha1-XX6kW7755Kb/ZflUOOCofDV9WnM=
  dependencies:
    indexof "0.0.1"

void-elements@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/void-elements/-/void-elements-2.0.1.tgz#c066afb582bb1cb4128d60ea92392e94d5e9dbec"
  integrity sha512-qZKX4RnBzH2ugr8Lxa7x+0V6XD9Sb/ouARtiasEQCHB1EVU4NXtmHsDDrx1dO4ne5fc3J6EW05BP1Dl0z0iung==

warning@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/warning/-/warning-3.0.0.tgz#32e5377cb572de4ab04753bdf8821c01ed605b7c"
  integrity sha512-jMBt6pUrKn5I+OGgtQ4YZLdhIeJmObddh6CsibPxyQ5yPZm1XExSyzC1LCNX7BzhxWgiHmizBWJTHJIjMjTQYQ==
  dependencies:
    loose-envify "^1.0.0"

warning@^4.0.1, warning@^4.0.3:
  version "4.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/warning/-/warning-4.0.3.tgz#16e9e077eb8a86d6af7d64aa1e05fd85b4678ca3"
  integrity sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==
  dependencies:
    loose-envify "^1.0.0"

watchpack@^0.2.1:
  version "0.2.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/watchpack/-/watchpack-0.2.9.tgz#62eaa4ab5e5ba35fdfc018275626e3c0f5e3fb0b"
  integrity sha1-Yuqkq15bo1/fwBgnVibjwPXj+ws=
  dependencies:
    async "^0.9.0"
    chokidar "^1.0.0"
    graceful-fs "^4.1.2"

watchpack@^2.4.0:
  version "2.4.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/watchpack/-/watchpack-2.4.0.tgz#fa33032374962c78113f93c7f2fb4c54c9862a5d"
  integrity sha512-Lcvm7MGST/4fup+ifyKi2hjyIAwcdI4HRgtvTpIUxBRhB+RFtUh8XtDOxUfctVCnhVi+QQj49i91OyvzkJl6cg==
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/wbuf/-/wbuf-1.7.3.tgz#c1d8d149316d3ea852848895cb6a0bfe887b87df"
  integrity sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA==
  dependencies:
    minimalistic-assert "^1.0.0"

webpack-cli@^4.9.2:
  version "4.9.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/webpack-cli/-/webpack-cli-4.9.2.tgz#77c1adaea020c3f9e2db8aad8ea78d235c83659d"
  integrity sha512-m3/AACnBBzK/kMTcxWHcZFPrw/eQuY4Df1TxvIWfWM2x7mRqBQCqKEd96oCUa9jkapLBaFfRce33eGDb4Pr7YQ==
  dependencies:
    "@discoveryjs/json-ext" "^0.5.0"
    "@webpack-cli/configtest" "^1.1.1"
    "@webpack-cli/info" "^1.4.1"
    "@webpack-cli/serve" "^1.6.1"
    colorette "^2.0.14"
    commander "^7.0.0"
    execa "^5.0.0"
    fastest-levenshtein "^1.0.12"
    import-local "^3.0.2"
    interpret "^2.2.0"
    rechoir "^0.7.0"
    webpack-merge "^5.7.3"

webpack-core@~0.6.9:
  version "0.6.9"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/webpack-core/-/webpack-core-0.6.9.tgz#fc571588c8558da77be9efb6debdc5a3b172bdc2"
  integrity sha1-/FcViMhVjad76e+23r3Fo7FyvcI=
  dependencies:
    source-list-map "~0.1.7"
    source-map "~0.4.1"

webpack-dev-middleware@^5.3.1:
  version "5.3.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/webpack-dev-middleware/-/webpack-dev-middleware-5.3.1.tgz#aa079a8dedd7e58bfeab358a9af7dab304cee57f"
  integrity sha512-81EujCKkyles2wphtdrnPg/QqegC/AtqNH//mQkBYSMqwFVCQrxM6ktB2O/SPlZy7LqeEfTbV3cZARGQz6umhg==
  dependencies:
    colorette "^2.0.10"
    memfs "^3.4.1"
    mime-types "^2.1.31"
    range-parser "^1.2.1"
    schema-utils "^4.0.0"

webpack-dev-server@^4.7.4:
  version "4.7.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/webpack-dev-server/-/webpack-dev-server-4.7.4.tgz#d0ef7da78224578384e795ac228d8efb63d5f945"
  integrity sha512-nfdsb02Zi2qzkNmgtZjkrMOcXnYZ6FLKcQwpxT7MvmHKc+oTtDsBju8j+NMyAygZ9GW1jMEUpy3itHtqgEhe1A==
  dependencies:
    "@types/bonjour" "^3.5.9"
    "@types/connect-history-api-fallback" "^1.3.5"
    "@types/express" "^4.17.13"
    "@types/serve-index" "^1.9.1"
    "@types/sockjs" "^0.3.33"
    "@types/ws" "^8.2.2"
    ansi-html-community "^0.0.8"
    bonjour "^3.5.0"
    chokidar "^3.5.3"
    colorette "^2.0.10"
    compression "^1.7.4"
    connect-history-api-fallback "^1.6.0"
    default-gateway "^6.0.3"
    del "^6.0.0"
    express "^4.17.1"
    graceful-fs "^4.2.6"
    html-entities "^2.3.2"
    http-proxy-middleware "^2.0.0"
    ipaddr.js "^2.0.1"
    open "^8.0.9"
    p-retry "^4.5.0"
    portfinder "^1.0.28"
    schema-utils "^4.0.0"
    selfsigned "^2.0.0"
    serve-index "^1.9.1"
    sockjs "^0.3.21"
    spdy "^4.0.2"
    strip-ansi "^7.0.0"
    webpack-dev-middleware "^5.3.1"
    ws "^8.4.2"

webpack-env@^0.8.0:
  version "0.8.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/webpack-env/-/webpack-env-0.8.0.tgz#97771b39ccd0e532db1c87d459379c9d7cd887dc"
  integrity sha1-l3cbOczQ5TLbHIfUWTecnXzYh9w=
  dependencies:
    webpack "^1.12.2"

webpack-merge@^5.7.3:
  version "5.8.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/webpack-merge/-/webpack-merge-5.8.0.tgz#2b39dbf22af87776ad744c390223731d30a68f61"
  integrity sha512-/SaI7xY0831XwP6kzuwhKWVKDP9t1QY1h65lAFLbZqMPIuYcD9QAW4u9STIbU9kaJbPBB/geU/gLr1wDjOhQ+Q==
  dependencies:
    clone-deep "^4.0.1"
    wildcard "^2.0.0"

webpack-remove-empty-scripts@0.7.3:
  version "0.7.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/webpack-remove-empty-scripts/-/webpack-remove-empty-scripts-0.7.3.tgz#f57b9823f5bd7016b44a4990f9257defd018513d"
  integrity sha512-yipqb25A0qtH7X9vKt6yihwyYkTtSlRiDdBb2QsyrkqGM3hpfAcfOO1lYDef9HQUNm3s8ojmorbNg32XXX6FYg==
  dependencies:
    ansis "^1.3.4"

webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/webpack-sources/-/webpack-sources-3.2.3.tgz#2d4daab8451fd4b240cc27055ff6a0c2ccea0cde"
  integrity sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==

webpack@^1.12.2:
  version "1.15.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/webpack/-/webpack-1.15.0.tgz#4ff31f53db03339e55164a9d468ee0324968fe98"
  integrity sha1-T/MfU9sDM55VFkqdRo7gMklo/pg=
  dependencies:
    acorn "^3.0.0"
    async "^1.3.0"
    clone "^1.0.2"
    enhanced-resolve "~0.9.0"
    interpret "^0.6.4"
    loader-utils "^0.2.11"
    memory-fs "~0.3.0"
    mkdirp "~0.5.0"
    node-libs-browser "^0.7.0"
    optimist "~0.6.0"
    supports-color "^3.1.0"
    tapable "~0.1.8"
    uglify-js "~2.7.3"
    watchpack "^0.2.1"
    webpack-core "~0.6.9"

webpack@^5.80.0:
  version "5.81.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/webpack/-/webpack-5.81.0.tgz#27a2e8466c8b4820d800a8d90f06ef98294f9956"
  integrity sha512-AAjaJ9S4hYCVODKLQTgG5p5e11hiMawBwV2v8MYLE0C/6UAGLuAF4n1qa9GOwdxnicaP+5k6M5HrLmD4+gIB8Q==
  dependencies:
    "@types/eslint-scope" "^3.7.3"
    "@types/estree" "^1.0.0"
    "@webassemblyjs/ast" "^1.11.5"
    "@webassemblyjs/wasm-edit" "^1.11.5"
    "@webassemblyjs/wasm-parser" "^1.11.5"
    acorn "^8.7.1"
    acorn-import-assertions "^1.7.6"
    browserslist "^4.14.5"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.13.0"
    es-module-lexer "^1.2.1"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.9"
    json-parse-even-better-errors "^2.3.1"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^3.1.2"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.3.7"
    watchpack "^2.4.0"
    webpack-sources "^3.2.3"

websocket-driver@>=0.5.1, websocket-driver@^0.7.4:
  version "0.7.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/websocket-driver/-/websocket-driver-0.7.4.tgz#89ad5295bbf64b480abcba31e4953aca706f5760"
  integrity sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/websocket-extensions/-/websocket-extensions-0.1.4.tgz#7f8473bc839dfd87608adb95d7eb075211578a42"
  integrity sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==

whatwg-fetch@>=0.10.0:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/whatwg-fetch/-/whatwg-fetch-2.0.3.tgz#9c84ec2dcf68187ff00bc64e1274b442176e1c84"
  integrity sha512-SA2KdOXATOroD3EBUYvcdugsusXS5YiQFqwskSbsp5b1gK8HpNi/YP0jcy/BDpdllp305HMnrsVf9K7Be9GiEQ==

which@^2.0.1:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/which/-/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

wildcard@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/wildcard/-/wildcard-2.0.0.tgz#a77d20e5200c6faaac979e4b3aadc7b3dd7f8fec"
  integrity sha512-JcKqAHLPxcdb9KM49dufGXn2x3ssnfjbcaQdLlfZsL9rH9wgDQjUtDxbo8NE0F6SFvydeu1VhZe7hZuHsB2/pw==

window-size@0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/window-size/-/window-size-0.1.0.tgz#5438cd2ea93b202efa3a19fe8887aee7c94f9c9d"
  integrity sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0=

wordwrap@0.0.2:
  version "0.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/wordwrap/-/wordwrap-0.0.2.tgz#b79669bb42ecb409f83d583cad52ca17eaa1643f"
  integrity sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8=

wordwrap@~0.0.2:
  version "0.0.3"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/wordwrap/-/wordwrap-0.0.3.tgz#a3d5da6cd5c0bc0008d37234bbaf1bed63059107"
  integrity sha1-o9XabNXAvAAI03I0u68b7WMFkQc=

wrappy@1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

ws@^8.4.2:
  version "8.5.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/ws/-/ws-8.5.0.tgz#bfb4be96600757fe5382de12c670dab984a1ed4f"
  integrity sha512-BWX0SWVgLPzYwF8lTzEy1egjhS4S4OEAHfsO8o65WOVsrnSRGaSiUaa9e0ggGlkMTtBlmOpEXiie9RUcBO86qg==

wyn-portal-types@7.1.1:
  version "7.1.1"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/wyn-portal-types/-/wyn-portal-types-7.1.1.tgz#f93fe4575e0f5c0ed22ac8c8e9cee40eea238513"
  integrity sha512-Nb9t/tzXL5s/2UwzkpdFBQ32Yavbm8ClTOzSD+1C3nABoxK5bPyfrl9sdwrEInCcDp+JS/cqAxEX2gpOQVj/8w==

wyn-utils@6.1.2:
  version "6.1.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/wyn-utils/-/wyn-utils-6.1.2.tgz#1d74f78f35c85f6bb9b1f46f31460db545d73037"
  integrity sha512-BB1ZVMilOJJnDJFxPy8Sw1J6DGuUJoO1hdLsFW93H1pLYA/vRhtamoU+/vh1rbLUniZzuuR8+xj2ETbmssmxqQ==

wyn-variables@1.0.12:
  version "1.0.12"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/wyn-variables/-/wyn-variables-1.0.12.tgz#eafdb6fcca60e76173e4db5b9209e189beb09673"
  integrity sha1-6v22/Mpg52Fz5Ntbkgnhib6wlnM=

xtend@^4.0.0:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/xtend/-/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/yallist/-/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yaml@^1.10.0:
  version "1.10.2"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/yaml/-/yaml-1.10.2.tgz#2301c5ffbf12b467de8da2333a459e29e7920e4b"
  integrity sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==

yargs@~3.10.0:
  version "3.10.0"
  resolved "https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/yargs/-/yargs-3.10.0.tgz#f7ee7bd857dd7c1d2d38c0e74efbd681d1431fd1"
  integrity sha1-9+572FfdfB0tOMDnTvvWgdFDH9E=
  dependencies:
    camelcase "^1.0.2"
    cliui "^2.1.0"
    decamelize "^1.0.0"
    window-size "0.1.0"
