﻿export const accountEN = {
	// common
	Add: 'Add',
	Edit: 'Edit',
	Save: 'Save',
	Cancel: 'Cancel',
	Home: 'Home',
	Delete: 'Delete',
	Remove: 'Remove',
	DeleteConfirm: 'Delete Confirm',
	Yes: 'Yes',
	No: 'No',
	All: 'All',
	MoveUp: 'Move Up',
	MoveDown: 'Move Down',
	SearchText: 'search text',
	Enabled: 'Enabled',
	Error: 'Error',
	Close: 'Close',
	More: 'More',
	Created: 'Created',

	Username: 'Username',
	Roles: 'Roles',
	Organizations: 'Organizations',
	Locked: 'Locked',
	Users: 'Users',
	SelectValue: 'select value',
	UnlockUser: 'Unlock User',
	UnlockUserConfirm: 'Do you want to unlock user "{{user}}" ?',
	Settings: 'Settings',
	cgridMore: 'More',

	// Generate token
	gtUser: 'User',
	gtPassword: 'Password',
	gtAvatarMenu: 'Hide avatar menu',
	gtWelcomeScreen: 'Hide welcome screen',
	gtGenerateToken: 'Generate Token',
	gtToken: 'Token',
	gtTitle: 'Title',
	gtDescription: 'Description',
	gtOrgPath: 'Organization Path',
	gtCreatedTime: 'Created Time',
	gtTokenList: 'Tokens',
	gtIntegratePortalUrl: 'Integrate Portal URL',
	gtIntegrateAdminPortalUrl: 'Integrate Admin Portal URL',
	gtIntegrateResourcePortalUrl: 'Integrate Resource Portal URL',
	invalid_username_or_password: 'Invalid user or password',
	error_description_default: 'Failed to get token, maybe it\'s due to user disabled or deleted.',
	gtRevoke: 'Revoke token',
	'gtRevoke!btn': 'Revoke',
	gtGenerateTokenSuccess: 'Token generated successfully',
	gtRevokeTokenSuccess: 'Token revoked successfully',
	gtGenerateUrl: 'Generate integration url...',
	gtGenerateUrlTitle: 'Generate URL',
	gtGetToken: 'Get token',
	gtClipboardError: 'Cannot write data to clipboard',
	gtClipboardSuccess: 'Successfully copied to clipboard',
	gtRevokeToken: 'Revoke Token',
	gtRevokeTokenConfirmMessage: "Do you want to revoke token '{{title}}'?",
	gtCopyUrl: 'Copy URL',
	gtCopyOfflineLicenseString: 'Copy string',
	gtGenerateQRCode: 'Generate QR code',
	gtQRCodeGetOfflineLicenseString: 'Scan the QR code to get the {{operationCode}}',
	gtExpiryTime: 'Expiry Time',
	error_100001: 'Invalid parameter: {{name}}',
	error_100002: 'Unknown error: {{message}}',
	error_100003: 'Integration token not found {{id}}',
	error_100004: 'Revoke token error: {{message}}',
	error_100005: 'Please confirm that the "Portal URI" in "UI Settings" is already provided before you generate the Integration URL.',
	error_100006: 'Request authentication failed on Portal URI "{{url}}". Could not request user claims.',
	gtExpireTimeInvalid: 'The expiry time is invalid',
	gtExpireTimeLessThanNow: 'The expiry time cannot be less than or equal current time',
	gtState: 'State',
	gtValid: 'Valid',
	gtExpired: 'Expired',

	// Customize properties
	AddProperty: 'Add Property',
	EditProperty: 'Edit Property',
	DeleteProperty: 'Delete Property',
	PropertyName: 'Property Name',
	PropertyValueType: 'Property Value Type',
	PropertyValueType_String: 'String',
	PropertyValueType_Boolean: 'Boolean',
	PropertyValueType_Integer: 'Integer',
	PropertyValueType_Float: 'Float',
	PropertyValueType_Date: 'Date',
	PropertyValueType_DateTime: 'DateTime',
	AvailableValues: 'Available Values',
	AvailableValuesDesc: 'Available Values(One value per line)',
	ShowInList: 'Show In List',
	DeletePropertyConfirmMessage: 'Do you want to delete property "{{property}}" permanently?',
	PropertyNameRequirement: "The max length of customize property name is 64, and it can not contain '<', '>', '$', '/' and '\\'.",
	ContinueUpdate: 'Continue Update?',
	UpdatePropertyConfirm: 'The property values "{{propValues}}" are in use, these values will be cleared if you update the property, do you want to continue?',
	NewPropertyName: 'New property name',
	NoCustomizePropertiesTip: 'No extended properties in the system.To add,please click',
	AllowEdit: 'Allow User To Edit',
	Multivalued: 'Multivalued',
	ShowInProfile: 'Display In User Profile Page',
	MultivaluedChangedWarning: 'Modifying the value of multivalued property of the custom property will cause the value of the custom property of all roles in the system to change, please be careful!',
	Sensitive: 'Sensitive',

	// Locked  user management
	NoLockedUserTip: 'No locked users in the system.',

	// Concurrence management
	Ban: 'Ban User',
	IpAddress: 'IP Address',
	BrowserCount: 'Browser Count',
	UserAgent: 'User Agent',
	loginDate: 'Login Date',
	NoLoginUserTip: 'No Logged-in user in the system',
	BanDesc: 'Click to restrict the user from accessing portal for next 3 minutes',

	// License
	RegistrationDate: 'Registration Date',
	LicenseKey: 'License Key',
	ServerGeneratedInfo: 'Offline License Data',
	ExpiryDate: 'Expiry Date',
	LicenseStatus: 'Status',
	LicenseInfo: 'License Info',

	Activate: 'Activate',
	Deactivate: 'Deactivate',
	Refresh: 'Refresh',
	RefreshOnline: 'Refresh',
	RefreshOffline: 'Refresh Offline',
	ActivateOffline: 'Activate Offline',
	ActivateOnline: 'Activate',
	DeactivateOffline: 'Deactivate Offline',
	ImportLicense: 'Enter License Data',
	MigrateOffline: 'Refresh Offline',
	Upgrade: 'Upgrade',
	RefreshInputLicense: 'Please input the whole license key to refresh offline',
	UpgradeInputLicense: 'Please input the license key to upgrade',

	DeactivateConfirm: 'Confirm to deactivate license',
	DeactivateConfirmMessage: 'Are you sure you want to deactivate license "{{license}}"?',
	OfflineDeactivateConfirmMessage: 'Are you sure you want to offline deactivate license "{{license}}"?\nThis action is irrevocable, and confirmation immediately removes the license from the system and generates an offline deactivation code.',
	Succeed: "Success.",
	RefreshConfirm: 'Confirm License Refresh',
	RefreshConfirmMessage: 'Are you sure you want to refresh license "{{license}}"?',
	getOfflineLicenseDataTips: 'Click the \"$t(getOfflineLicenseData)\" button to get license data, then enter it to {{action}}. If already obtained, input directly to {{action}}.',
	obtainOfflineLicenseDataTips: 'Please copy or generate the QR code and send the {{offlineCode}} to the after-sales or sales staff to provide you with offline license data.',
	inputOfflineLicenseData: 'Please input offline license data',
	getOfflineLicenseData: 'Get offline license data',
	offlineLicenseData: 'Offline License Data',
	offlineActivationCode: 'Offline Activation Code',
	offlineRefreshCode: 'Offline Refresh Code',
	offlineDeactivationCode: 'Offline Deactivation Code',

	ActivateSuccess: 'Activate Successfully',
	ActivateSuccessMessage: 'Activate license successfully.',
	RefreshSuccess: 'Refresh Successfully',
	RefreshSuccessMessage: 'Refresh license successfully.',
	DeactivateSuccess: 'Deactivate Successfully',
	DeactivateSuccessMessage: 'Deactivate license successfully.',
	OfflineDeactivateCloseConfirm: 'Please confirm that you have saved the offline deactivation code. After closing the dialog, you will not be able to regenerate the information which is used to deactivate your license.',
	OK: 'OK',

	Included: 'Included',
	Unlimited: 'Unlimited',
	NoExpire: 'Never Expire',

	KeyType: 'Key Type',
	KeyTypeTrial: 'Trial',
	KeyTypePerpetual: 'Perpetual',
	KeyTypeAnnual: 'Annual',
	Expired: 'Expired',
	VersionNotMatch: 'Version not matched',
	MismatchDeployment: 'Deployment not matched',
	GracePeriod: 'Grace Period',
	Licensed: 'Valid',
	needMigrate: 'need refresh',
	invalid: 'invalid',
	tempActivated: "temporary",

	rptModule: 'Report Module',
	rptDocCount: 'Report Documents Count',
	rptConcurrence: 'Report Concurrence Count',
	dbdModule: 'Dashboard Module',
	dbdDocCount: 'Dashboard Documents Count',
	dbdConcurrence: 'Dashboard Concurrence Count',
	dataMonitoringModule: 'Data Monitoring',
	wynSheetModule: 'Spread Sheet',
	wynSheetDocCount: 'Spread Sheet Documents Count',
	dcsDocCount: 'Datasource Count',
	concurrence: 'Concurrence Count',
	clpFeature: 'Custom Language Package',
	nlpFeature: 'AI Chat Analysis',
	ServerCount: 'Server Count',
	IsTrial: 'Is Trial',
	True: 'True',
	False: 'False',
	NoLicenseTip: 'The current environment has no product license',
	BuiltinTrialLicenseTip: 'The product trial will expire on {{expireDate}}',
	BuiltinTrialHasExpiredTip: 'The product trial has expired',
	OldLicenseShouldBeDeactivated: 'The new license key has been activated. Please ensure that the old license key is deactivated.',
	MigrateByTheActionBtn: 'Please refresh the current license by clicking on the button on the right.',
	ContactToMarketToMigrate: 'Multiple old license keys are activated on the current system. Please contact us to merge the license keys.',
	InvalidLicenseCannotMigrate: 'The old license key with status "{{status}}" does not support migration. Please deactivate it directly.',
	hasFormalKeyToMigrate: 'A formal old license key awaiting migration has been detected. For trial licenses, please deactivate directly.',
	deactivateStep: 'To deactivate the license in offline mode, please follow the steps below.',
	deactivateStepEn1: '1. Open our offline license deactivation page on a machine with internet access and use the offline deactivation code displayed above to deactivate the license.',
	deactivateStepCn1: '1. Send us the offline deactivation code. We will assist you in deactivating your license.',
	deactivateStep2: '2. Click the OK button to close this dialog when the license is deactivated successfully. Otherwise, please keep the offline deactivation code and contact us.',
	deactivateOfflinePage: 'the deactivation page',
	activateStep: 'To activate the license in offline mode, please follow the steps below.',
	activateStepEn1: '1. Open our offline license activation page on a machine with internet access and use the offline activation code displayed above to obtain the license data.',
	activateStepCn1: '1. Send us the offline activation code. We will provide you with the offline license data.',
	activateStep2: '2. Click the Enter License Data button after you receive the offline license data.',
	activateOfflinePage: 'the activation page',
	licenseKeyInvalid: 'The license key is invalid.',
	detail: 'Detail',
	licenseInfoDetail: 'License Info Detail',

	// Custom visual license
	registerCVLicense: 'Register Custom Visual License',
	'Import!title': 'Import',
	'ImportCancel!title': 'Cancel',
	uploadingInfo: "'{{fileName}}' Uploading.",
	uploadedInfo: "'{{fileName}}' Uploaded.",
	importingInfo: "'{{fileName}}' Importing.",
	LicenseType: 'Type',
	LicenseType_Trial: 'Trial',
	LicenseType_Perpetual: 'Perpetual',
	'license-info!title': 'The license is only for the custom visual with the same major version.',
	'validCVLicense!title': 'Valid custom visual license',
	'invalidCVLicense!title': 'Invalid custom visual license',
	'disabledCVLicense!title': 'This custom visual license does not match Wyn license.',
	'cvNotFoundLicense!title': 'Custom visual {{name}} ({{version}}) not found. Please import this custom visual first.',
	'existedCVLicense!title': 'There is already a custom visual license. If you want to override it, please check the box manually.\n\nLicense Type: {{licenseType}}\nRegister Date: {{registerDate}}\nExpiration Date: {{expirationDate}}\nLicense Info: {{licenseInfo}}',
	'expiredCVLicense!title': 'The custom visual license has expired.',
	'unmatchedCVVersionLicense!title': 'Custom visual {{name}} is found, but the major version does not match. Please import the corresponding version of this custom visual first.',

	// System configurations
	AddProvider: 'Add Provider',
	ProviderName: 'Provider Name',
	Description: 'Description',
	AddSecurityProvider: 'Add Security Provider',
	RemoveSecurityProvider: 'Remove Security Provider',
	RemoveSecurityProviderConfirm: 'Do you want to remove security provider "{{provider}}"?',

	// Client Management
	AllowedGrantTypes: 'Allowed Grant Types',
	AllowedScopes: 'Allowed Scopes',
	AllowedUris: 'Allowed Uris',
	AccessTokenLifetime: 'Access Token Lifetime',

	// Security Providers and External Providers
	'account_provider_local': 'Built-In Account Provider',
	'account_provider_local_description': 'The built-in account management system provided by local identity service.',
	'account_provider_WeChat4Work': 'WeCom',
	'account_provider_WeChat4Work_description': 'The account management system provided by WeCom.',
	'account_provider_AD Security Provider': 'Active Directory',
	'account_provider_AD Security Provider_description': 'Active directory security provider.',
	'account_provider_DingTalk': 'DingTalk',
	'account_provider_DingTalk_description': 'The account management system provided by DingTalk',

	NoSecurityProviderTip: 'You are currently using the built-in account mode for login',
	NoAvailableSecurityProvidersTip: 'There is no any Providers available',

	'ShowPassword': 'Show password',
	'HidePassword': 'Hide password',

	EnableExternalLoginProviderExplainText: 'To enable the external login provider, you need to fill in the mandatory setting items(With an asterisk) correctly, then you can synchronize the data from the external login provider and enable the other functions(such as single sign-on, scan QR code login and automatic data synchronization.',
	DataSyncingExplainText: "The data syncing function will synchronize all the authorized organizations, roles and users in the external login provider to Wyn, the original data will be overwritten and some special characters('/', '\\', '<', '>' and '$') in the organization name and the role name will be removed after the data synchronized.",
	DataSyncingFailsExplainText: 'Notes: The data syncing will fail if there are duplicated role names, or duplicated organization names at the same level of the organization structure. The duplicate user names will be formatted to something like "username[userid]".',

	SyncData: 'Sync Data',
	SyncingData: 'Syncing Data...',
	SyncDataSuccessTitle: "Data synchronized successfully.",
	SyncDataSuccess: "External login provider's data synchronized successfully.",
	SyncDataDesc: "The original data will be overwritten, and the special characters('/', '\\', '<', '>' and '$') in the organization name and the role name will be removed after the data synchronized.",

	SPTestDefaultResult: 'No test result.',
	SPTestSuccessResult: 'Login test successful.',
	SPTestFailResult: 'Login test failed.',
	LoginTest: 'Login Test',
	UserId: 'User Id',
	UserName: 'User Name',
	UserContext: 'User Context',
	Exception: 'Exception',
	ErrorMessage: 'Error Message',
	SPShowDetail: 'Show Details',
	SPHideDetail: 'Hide Details',
	Password: 'Password',
	Test: 'Test',
	Testing: 'Testing',
	CustomParam: 'Custom Parameter',
	CustomParamDescribe: 'The custom parameter is made up of some key-value pairs, one key-value pair per line, and the key and value split by ":".',

	'setting_item_name!ad security provider!server url': 'Server URL',
	'setting_item_desc!ad security provider!server url': 'Server URL',
	'setting_item_name!ad security provider!admin user': 'Admin User',
	'setting_item_desc!ad security provider!admin user': 'Admin User',
	'setting_item_name!ad security provider!admin password': 'Admin Password',
	'setting_item_desc!ad security provider!admin password': 'Admin Password',
	'setting_item_name!ad security provider!admin groups': 'Admin Groups',
	'setting_item_desc!ad security provider!admin groups': 'Admin Groups',
	'setting_item_name!ad security provider!use ssl/tls': 'Use SSL/TLS',
	'setting_item_desc!ad security provider!use ssl/tls': 'Use SSL/TLS',
	'setting_item_name!ad security provider!user context': 'User Context',
	'setting_item_desc!ad security provider!user context': 'User Context',

	'setting_item_name!open ldap security provider!server url': 'Server URL',
	'setting_item_desc!open ldap security provider!server url': 'Server URL',
	'setting_item_desc!open ldap security provider!admin user': 'Admin User',
	'setting_item_name!open ldap security provider!admin user': 'Admin User',
	'setting_item_desc!open ldap security provider!admin password': 'Admin Password',
	'setting_item_name!open ldap security provider!admin password': 'Admin Password',
	'setting_item_desc!open ldap security provider!admin groups': 'Admin Groups',
	'setting_item_name!open ldap security provider!admin groups': 'Admin Groups',
	'setting_item_desc!open ldap security provider!user name': 'User Name',
	'setting_item_name!open ldap security provider!user name': 'User Name',
	'setting_item_desc!open ldap security provider!user display name': 'User Display Name',
	'setting_item_name!open ldap security provider!user display name': 'User Display Name',
	'setting_item_desc!open ldap security provider!use member chain rule group search': 'Use member chain rule group search',
	'setting_item_name!open ldap security provider!use member chain rule group search': 'Use member chain rule group search',
	'setting_item_desc!open ldap security provider!use ssl/tls': 'Use SSL/TLS',
	'setting_item_name!open ldap security provider!use ssl/tls': 'Use SSL/TLS',
	'setting_item_desc!open ldap security provider!user context': 'User Context',
	'setting_item_name!open ldap security provider!user context': 'User Context',

	'setting_item_name!dingtalk!corpid': 'CorpId',
	'setting_item_desc!dingtalk!corpid': 'CorpId',
	'setting_item_name!dingtalk!appkey': 'AppKey',
	'setting_item_desc!dingtalk!appkey': 'AppKey',
	'setting_item_name!dingtalk!appsecret': 'AppSecret',
	'setting_item_desc!dingtalk!appsecret': 'AppSecret',
	'setting_item_name!dingtalk!agentid': 'AgentId',
	'setting_item_desc!dingtalk!agentid': 'AgentId',
	'setting_item_name!dingtalk!maxconcurrentrequests': 'Max Concurrent Requests',
	'setting_item_desc!dingtalk!maxconcurrentrequests': 'Allowed max number of the concurrent requests when syncing data',
	'setting_item_name!dingtalk!qrcodeappid': 'ScanCodeAppId',
	'setting_item_desc!dingtalk!qrcodeappid': 'The id of the scanning code login application',
	'setting_item_name!dingtalk!qrcodeappsecret': 'ScanCodeAppSecret',
	'setting_item_desc!dingtalk!qrcodeappsecret': 'The secret of the scanning code login application',
	'setting_item_name!dingtalk!enableqrcodelogin': 'EnableScanCodeLogin',
	'setting_item_desc!dingtalk!enableqrcodelogin': 'Enable logging in by scanning QR code',
	'setting_item_name!dingtalk!enablesendingmessage': 'EnableSendingMessage',
	'setting_item_desc!dingtalk!enablesendingmessage': 'Enable sending messages to DingDing',
	'setting_item_name!dingtalk!enableautomaticsynchronization': 'EnableAutomaticSynchronization',
	'setting_item_desc!dingtalk!enableautomaticsynchronization': 'Enable automatically synchronize the data of DingDing',
	'setting_item_name!dingtalk!automaticsynchronizationinterval': 'AutomaticSynchronizationInterval',
	'setting_item_desc!dingtalk!automaticsynchronizationinterval': 'The interval of the automatic data synchronization, in hours, the valid minimum value is 1 hour',
	'setting_item_name!dingtalk!hiderootorganizationinloginpage': 'HideRootOrganizationInLoginPage',
	'setting_item_desc!dingtalk!hiderootorganizationinloginpage': 'Hide the virtual root organization(钉钉) in the login page',

	'setting_item_name!wechat4work!corpid': 'CorpId',
	'setting_item_desc!wechat4work!corpid': 'CorpId',
	'setting_item_name!wechat4work!secret': 'Secret',
	'setting_item_desc!wechat4work!secret': 'Secret',
	'setting_item_name!wechat4work!agentid': 'AgentId',
	'setting_item_desc!wechat4work!agentid': 'AgentId',
	'setting_item_name!wechat4work!maxconcurrentrequests': 'Max Concurrent Requests',
	'setting_item_desc!wechat4work!maxconcurrentrequests': 'Allowed max number of the concurrent requests when syncing data',
	'setting_item_name!wechat4work!enableqrcodelogin': 'EnableScanCodeLogin',
	'setting_item_desc!wechat4work!enableqrcodelogin': 'Enable logging in by scanning QR code',
	'setting_item_name!wechat4work!enablesendingmessage': 'EnableSendingMessage',
	'setting_item_desc!wechat4work!enablesendingmessage': 'Enable sending messages to WeCom',
	'setting_item_name!wechat4work!enableautomaticsynchronization': 'EnableAutomaticSynchronization',
	'setting_item_desc!wechat4work!enableautomaticsynchronization': 'Enable automatically synchronize the data of WeCom',
	'setting_item_name!wechat4work!automaticsynchronizationinterval': 'AutomaticSynchronizationInterval',
	'setting_item_desc!wechat4work!automaticsynchronizationinterval': 'The interval of the automatic data synchronization, in hours, the valid minimum value is 1 hour',
	'setting_item_name!wechat4work!hiderootorganizationinloginpage': 'HideRootOrganizationInLoginPage',
	'setting_item_desc!wechat4work!hiderootorganizationinloginpage': 'Hide the virtual root organization(企业微信) in the login page',

	// Security Settings
	EnableStrongPasswordPolicy: 'Enable strong password policy',
	EnableStrongPasswordPolicyDescription: 'The password must contain the following: \n    \u2022 1 uppercase letter\n    \u2022 1 lowercase letter\n    \u2022 1 number\n    \u2022 Length between 8-150 characters\nThe default password policy only requires that the password length be between 1 - 150 characters, and can contain any character. ',
	UserLockedTime: 'User locked time (minutes)',
	UserLockedTimeDescription: 'When a user enters the wrong password more than 5 times consecutively, that user will be locked out. During the locked time, the user cannot login, even with the correct password. You can change the locked time to a different value, or set it to 0 to disable the lock behavior. ',
	AllowUserResetPassword: 'Allow user reset password',
	AllowUserResetPasswordDescription: 'Enable this option allow user to reset his/her password.',
	CookieLifetimeSettings: 'Cookie lifetime settings (in days)',
	DefaultCookieLifetime: 'Default lifetime',
	CookieLifetimeForRememberLogin: 'Remember me',
	DefaultCookieLifetimeDesc: 'The default cookie lifetime should be between 0 and 30 days.',
	CookieLifetimeForRememberLoginDesc: 'The cookie lifetime for remember me should be between 1 and 365 days.',
	CookieLifetimeDescription: 'If the option "Remember me" is checked when logging a user in, the lifetime of the cookie will be set to the value of "Remember me", otherwise, the lifetime of the cookie will be set to the value of "Default lifetime", the value 0 means using a session cookie(the session will be ended when browser closed). The default cookie lifetime should be between 0 and 30 days, and the cookie lifetime for remember me should be between 1 and 365 days.',
	ForceChangePasswordOnFirstLogin: 'Change password on first login',
	ForceChangePasswordOnFirstLoginDescription: 'Enable this option to force the user to change the password on the first login.',
	ForcePasswordExpiration: 'Force password expiration',
	ForcePasswordExpirationDescription: 'Enable this option to force the user to change the password when the password expires. The password expiration period should be between 15 and 365 days, and the default expiration period is 90 days.',
	PasswordExpirationDays: 'Password expiration period (in days)',
	IntegrationClientSecret: 'Integration client secret',
	IntegrationClientSecretDescription: 'You can change the secret of the integration client here, this secret will be used when generating integration tokens. The secret can only contain letters, numbers, and the symbols \'-\', \'_\', and \'.\', and the secret length should be between 16 and 64.',

	// Claim Mappings
	ClaimName: 'Claim Name',
	UserPropName: 'User Property Name',
	AddClaim: 'Add Claim',
	DeleteClaim: 'Delete Claim',
	DeleteClaimConfirmMessage: "Do you want to delete user context '{{claim}}'?",
	Create: 'Create',

	// Two Factor Authentication
	defaultSubject: 'Wyn Login Verification Code',
	defaultBodyTemplate: 'Your login verification code is {@code}, valid for 5 minutes, please do not tell others.',
	SMS: 'SMS',
	EMAIL: 'Email',
	EnableTFA: 'Enable',
	Lifetime: 'Lifetime (in minutes)',
	TFAType: 'Verification Mode',
	accessKeyId: 'AccessKeyId',
	accessKeySecret: 'AccessKeySecret',
	signName: 'Sign Name',
	templateCode: 'Template Code',
	subject: 'Email Subject',
	bodyTemplate: 'Email Body',
	SendVerificationCodeFailed: 'Send verification code failed, the original error message is: {{error}}',
	SendVerificationCodeSuccess: 'Send the verification code successfully, please make sure you have received the verification code, then you can save the settings.',
	ShowSecret: 'Show secret',
	HideSecret: 'Hide secret',
	TestTFA: 'Test Settings',
	EnableTFADescription: 'If the 2FA is enabled, all the local users(the users whose provider is "local") are required to provide the verification code when logging in, so please make sure that all the local users have the correct mobile numer or email address.',
	BodyTemplateDescription: 'The email body must contain the text "{@code}" and it will be replaced with the actual verification code when sending the email.',
	Configure: 'Configure',
	NoEmailSettingsWarning: 'Warning: You have not completed your email configuration in the notification center.',
	TFA_EmptyMobile: 'The user mobile number is empty.',
	TFA_EmptyEmail: 'The user email is empty.',
	TFA_InvalidSettings: 'Invalid tow-factor authentication settings detected.',
	TFA_InvalidEmailSettings: 'Invalid email settings detected.',
	TFA_NoAvailableEmailSender: 'There is no available email sender found.',

	// Inactive Session Settings
	deleteUser: 'Delete user',
	providerPlaceText: 'User Provider',
	namePlaceText: 'User Name',
	UserName: 'Name',
	Provider: 'Provider',
	LoginPage: 'Login Page',
	SessionDisconnectedPage: 'Session Disconnected Page',
	CustomAddress: 'Custom Address',
	Enable: 'Enable',
	EnableInactiveSessionDescription: 'Once this function is enabled, the inactive sessions will be disconnected after a certain period of time.',
	Timeout: 'Timeout Period (minutes)',
	RedirectURL: 'Redirect URL',
	UnrestrictedUsers: 'Whitelist Users',
	addUser: 'Add External User',
	selectUsers: 'Add Internal User',
	AvailableUsers: 'Available Users',
	unlock: 'Unlock',

	// Identity Server User Controller Error Code Range 1000 ~ 2000
	error_1001: 'Invalid PageSize or PageNumber',
	error_1002: 'User name can not be empty.',
	error_1003: 'User email can not be empty.',
	error_1004: 'The password should contain at least 1 uppercase letter, 1 lowercase letter and 1 number, and the length should be between 8 and 150.',
	error_1005: 'User name already exists.',
	error_1006: 'User email already exists.',
	error_1007: 'Role \'{{role}}\' does not exist.',
	error_1008: 'Invalid parameter.',
	error_1009: 'User not found.',
	error_1010: 'User already exists.',
	error_1011: 'Extend property \'{{prop}}\' not found.',
	error_1012: 'Value \'{{value}}\' is invalid for extend property \'{{prop}}\'.',
	error_1013: 'Save data to database failed.',
	error_1014: 'User name can not be modified.',
	error_1015: 'No file found.',
	error_1016: 'No visible spread sheet found.',
	error_1017: 'Invalid user template format.',
	error_1018: 'No user found.',
	error_1019: 'Locked user with id \'{{id}}\' does not exist.',
	error_1020: 'Invalid password.',
	error_1021: 'Invalid security token.',
	error_1022: 'Forbidden ip address.',
	error_1023: 'Language is not supported.',
	error_1024: 'User mobile number can not be empty.',
	error_1025: 'Invalid email address.',

	// Import users error
	error_1100: "Row [{{rowIndex}}]: User id '{{userId}}' already exists.",
	error_1101: "Row [{{rowIndex}}]: User name can not be empty.",
	error_1102: "Row [{{rowIndex}}]: User name '{{userName}}' already exists.",
	error_1103: "Row [{{rowIndex}}]: User email can not be empty.",
	error_1104: "Row [{{rowIndex}}]: User email '{{userEmail}}' already exists.",
	error_1105: "Row [{{rowIndex}}]: User mobile '{{userMobile}}' already exists.",
	error_1106: "Row [{{rowIndex}}]: User password can not be empty.",
	error_1107: "Row [{{rowIndex}}]: Invalid password format. The password must contain at least 8 and less than 150 characters, and contain at least 1 lowercase, 1 uppercase letter and 1 number.",
	error_1108: "Row [{{rowIndex}}]: Invalid creation time '{{creationTime}}'.",
	error_1109: "Row [{{rowIndex}}]: Invalid value '{{value}}' for field 'enabled'.",
	error_1110: "Row [{{rowIndex}}]: Role '{{role}}' does not exist.",
	error_1111: "Row [{{rowIndex}}]: Invalid email address '{{userEmail}}'.",

	// Identity Server Role Controller Error Code Range 2001 ~ 2999
	error_2001: 'Role \'{{name}}\' already exists.',
	error_2002: 'Role name can not conflict with permission name.',
	error_2003: 'Built-in role can not be deleted.',
	error_2004: "Can not update the members of role 'everyone'.",
	error_2005: "Can not remove member 'admin' from role 'administrator'.",
	error_2006: "Can not modify the permissions of role 'administrator'.",
	// Identity Server Claim Mapping Errors
	error_3001: "User context '{{claimName}}' already exists.",
	error_3002: "User context with id '{{claimId}}' does not exist.",
	error_3003: "Customize property '{{propName}}' does not exist.",
	error_3004: "Built-in user context can not be modified.",
	error_3005: "Built-in user context can not be deleted.",
	error_3006: "Invalid claim mapping name. The length of the claim mapping name should be between 1 and 64, and the name can not contains '<', '>', '/', '\\', '$' and some invalid keywords.",
	// Identity Server Custom Property Errors
	error_4001: "Custom property '{{propName}}' already exists.",
	error_4002: "Custom property '{{propName}}' does not exist.",
	error_4003: "Custom property with id '{{propId}}' does not exist.",
	error_4004: "Invalid customize property name. The length of customize property name should be between 1 and 64, and the name can not contains '<', '>', '/', '\\', '$' and some invalid keywords.",
	// Identity Server Tenant Errors
	error_5001: "Tenant name can not be empty.",
	error_5002: "Tenant '{{tenantName}}' already exists.",
	error_5003: "Tenant property name can not be empty.",
	error_5004: "Tenant property '{{tenantPropName}}' already exists.",
	error_5005: "Tenant property '{{tenantPropName}}' is reserved.",
	error_5006: "Tenant from email '{{fromEmail}}' already exists.",
	// External Login Provider Errors
	error_6001: "The external login provider '{{providerName}}' does not exist.",
	error_6002: "The sending message function is disabled for the external login provider '{{providerName}}'.",
	error_6003: "Sending message to external login provider '{{providerName}}' failed, error code: {{errCode}}, error message: {{errMsg}}.",
	error_6004: "No valid message recipients found.",
	error_6005: "Duplicated user name '{{userName}}' detected.",
	error_6006: "Duplicated role name '{{roleName}}' detected.",
	error_6007: "Duplicated organization name '{{organizationName}}' detected.",
	error_6008: "Syncing data failed, the original error is: {{errMsg}}",
	// Security Provider Errors
	error_7001: "The security provider '{{providerName}}' is not enabled.",
	error_7002: "Load security provider '{{providerName}}' failed.",
	error_7003: 'Invalid user name or password.',
	error_7004: 'User name and password are required.',
	error_7005: 'Invalid parameter was passed.',
	error_7006: 'Login with the security provider failed.',
	// System Config Errors
	error_9001: 'Invalid cookie lifetime specified. The default cookie lifetime should be between 0 and 30 days, and the cookie lifetime for remember me should be between 1 and 365 days.',

	//LicenseActiveResult
	ActivateError: 'Failed to activate license',
	OfflineActivateError: 'Failed to activate license offline',
	RefreshError: 'Failed to refresh license',
	OfflineRefreshError: 'Failed to refresh license offline',
	DeactivateError: 'Failed to deactivate license',
	OfflineDeactivateError: 'Failed to deactivate license offline',
	ImportLicenseError: 'Failed to import license information',
	GetLicenseError: 'Failed to get license info',
	UpgradeLicenseError: 'Failed to upgrade license',
	GetClientIdentityCodeError: 'Failed to get client code',

	V2_007_009_0001: 'Activation error',
	V2_007_009_0002: 'Offline activation error',
	V2_007_009_0003: 'Deactivation error',
	V2_007_009_0004: 'Offline deactivation error',
	V2_007_009_0005: 'Refresh error',
	V2_007_009_0006: 'Import offline license error',
	V2_007_009_0007: 'Old license not found',
	V2_007_009_0008: 'Refresh error (Migrate)',
	V2_007_009_0009: 'Offline Refresh error (Migrate)',
	V2_007_009_0010: 'Get Licenses error',
	V2_007_009_0011: 'Offline Refresh error',
	V2_007_009_0012: 'Temporary Activation error',
	V2_007_009_0013: 'Upgrade Temporary Activation error',
	V2_007_009_0014: 'Get Client Code error',

	licenseErr_1000: 'Request error.',
	licenseErr_1001: 'Invalid data format(1001).',
	licenseErr_1002: 'Invalid data format(1002).',
	licenseErr_1003: 'Invalid date(1003). Please fix system date if it is incorrect and try again.',
	licenseErr_1004: 'Invalid key(1004). Please check your serial key and try again.',
	licenseErr_1005: 'Invalid Data Format(1005).',
	licenseErr_1006: 'Invalid Data Format(1006).',
	licenseErr_1007: 'Invalid activation information(1007). The activation status on client does not match with license server. Please attempt deactivate your license and activate again.',
	licenseErr_1008: 'Invalid data format(1008).',
	licenseErr_1009: 'Invalid key(1009). Please attempt deactivate your license and activate again.',
	licenseErr_2000: 'license information error(2000).',
	licenseErr_2001: 'license information error(2001).',
	licenseErr_2002: 'license information error(2002).',
	licenseErr_2003: 'license information error(2003). The license data lose effectiveness. Please try to get new license data again.',
	licenseErr_2004: 'license information error(2004). The license data lose effectiveness. Please try to get new license data again.',
	licenseErr_3000: 'License information error(3000).',
	licenseErr_3001: 'The serial key is disabled(3001).',
	licenseErr_3002: 'The serial key has expired(3002).',
	licenseErr_3003: 'Invalid version(3003). The serial cannot be used for the current version of the product.',
	licenseErr_3004: 'Invalid version(3004). The serial cannot be used for the current version of the product.',
	licenseErr_4000: 'Operation Error(4000).',
	licenseErr_4001: 'Offline Operation is not Allowed with This Key.',
	licenseErr_4002: 'Deactivation is not Allowed with This Key.',
	licenseErr_4003: 'License has Exceeded the Available Count.',
	licenseErr_4004: 'License has Exceeded the Frequency Limit. Please try it after 24 hours.',
	licenseErr_5000: 'Server Error(5000).',
	licenseErr_5001: 'Server Error(5001).',
	licenseErr_5002: 'Server Error(5002).',
	licenseErr_6000: 'The key cannot be migrated to new license (6000). Please contact us.',
	licenseErr_6001: 'The key cannot be migrated to new license (6001). Please contact us.',
	licenseErr_6002: 'The key cannot be migrated to new license (6002). Please contact us.',
	licenseErr_6003: 'The key cannot be migrated to new license (6003). Please contact us.',
	licenseErr_6004: 'The key cannot be migrated to new license (6004). Please contact us.',
	licenseErr_7001: 'Temporary Activation failed with invalid license file(7001). Please contact us.',
	licenseErr_7002: 'Temporary Activation failed with invalid encryption mode(7002). Please contact us.',
	licenseErr_7003: 'Temporary Activation failed with invalid license key(7003). Please contact us.',
	licenseErr_7004: 'Temporary Activation failed with invalid license content(7004). Please contact us.',
	licenseErr_7005: 'Temporary Activation failed with invalid date(7005). Please contact us.',
	licenseErr_7006: 'Temporary Activation failed with signature verification failure(7006). Please contact us.',
	licenseErr_65535: 'Unknown error. Please contact with us.',

	licenseErr_0000_0001: 'Already activated 1 license.',
	licenseErr_0000_0002: 'Invalid license key.',
	licenseErr_0000_0003: 'Failed to decrypt the license data.',
	licenseErr_0000_0004: 'The license is not found.',
	licenseErr_0001_0001: 'Activate license error. Please check the log to get more information.',
	licenseErr_0001_0002: 'Generate offline activation info error.',
	licenseErr_0002_0001: 'Deactivate license error. Please check the log to get more information.',
	licenseErr_0002_0002: 'Generate offline deactivation info error.',
	licenseErr_0003_0001: 'Refresh license error. Please check the log to get more information.',
	licenseErr_0003_0002: 'Refresh license error. No any license found to refresh.',
	licenseErr_0003_0003: 'Refresh license error. Generate offline refresh info error.',
	licenseErr_0003_0004: 'Refresh license error. The input license key does not match.',
	licenseErr_0004_0001: 'Import license info error. Please check the log to get more information.',
	licenseErr_0005_0001: 'Cannot find a license key corresponding to the credential.',
	licenseErr_0005_0002: 'The current operation needs an old license but not found.',
	licenseErr_0006_0001: 'Migrate license error. Please check the log to get more information.',
	licenseErr_0006_0002: 'Generate offline migration info error.',
	licenseErr_0007_0001: 'Get License Info error.',
	licenseErr_0008_0001: 'Temporary activation error. Please check the log to get more information.',
	licenseErr_0009_0001: 'Failed to upgrade temporary activation. Please check the log to get more information.',
	licenseErr_0010_0001: 'Failed to get client identity code. The input license key does not match.',
	LicenseErr_ImportLicenseError: 'Import license error. License information error.',

	licenseWarn: 'License operation warning',

	licenseWarn_DeactivationFailed: 'Failed to deactivate.',
	licenseWarn_AlmostlyOverFrequency: 'Your operation is too frequent.',
	licenseWarn_AlmostlyOverMaxInstanceCount: 'Approaching the maximum number of activations.',

	// Custom Visual License Errors
	GetCustomVisualLicenseError: "Failed to get custom visual license list.",
	AnalysisCustomVisualLicenseFileError: "Failed to upload custom visual license file.",
	GetCustomVisualListError: "Failed to get custom visual list.",
	ImportCustomVisualLicenseFileError: "Failed to import custom visual license.",
	ImportCustomVisualLicenseFileSuccess: "Import custom visual successfully.",
	DeleteCustomVisualLicenseError: "Failed to delete custom visual license.",

	'rt_claim mapping': 'user context',
	'rt_custom property': 'custom property',

	error_V2_007_000_0001: 'The {{resourceType}} "{{resourceIdentifier}}" is not found.',
	error_V2_007_000_0002: 'The {{resourceType}} "{{resourceIdentifier}}" does not exist or has been removed.',
	error_V2_007_000_0003: 'The {{resourceType}} "{{resourceIdentifier}}" already exists.',
	error_V2_007_000_0006: 'You don\'t have sufficient privileges to perform this operation.',
	error_V2_007_000_0010: 'Unknown error occurrs, the original error is "{{message}}", please check the log for more details.',
	error_V2_007_003_0003: "Invalid claim name. The length of the claim name should be between 1 and 64, and the name can not contains '<', '>', '/', '\\', '$' and some invalid keywords.",
	error_V2_007_004_0001: "Invalid custom property name. The length of custom property name should be between 1 and 64, and the name can not contains '<', '>', '/', '\\', '$' and some invalid keywords.",
	error_V2_007_004_0004: 'The provided available values "{{InvalidAvailableValue}}" do not match the specified property value type or format.',

	error_V2_007_006_0004: "Duplicated user name '{{UserName}}' detected.",
	error_V2_007_006_0005: "Duplicated role name '{{RoleName}}' detected.",
	error_V2_007_006_0006: "Duplicated organization name '{{OrganizationName}}' detected.",
	error_V2_007_006_0007: 'Syncing data failed, the original error is: {{ErrMsg}}',
	error_V2_007_010_0002: 'The session timeout peroid should be between 1 - 120 minutes.',
	error_V2_007_010_0003: 'The redirect URL should start with "/", or it should be a valid URL which starts with "http://" or "https://".',
	error_V2_007_010_0004: 'The user name and the user provider cannot be empty.',

	error_V2_008_002_0001: 'Please confirm that the "Portal URI" in "UI Settings" is already provided before you generate the Integration URL.',
	error_V2_008_001_0003: 'Generate token failed. {{error_description}}',
	error_V2_008_001_0004: 'The token has expired.',

	error_V2_008_003_0001: 'Failed to get user information with the token, maybe the token has expired or has been deleted.',

	customVisualLicenseErr_V2_007_011_0001: 'The custom visual license file is empty.',
	customVisualLicenseErr_V2_007_011_0002: 'Failed to upload custom visual license file.',
	customVisualLicenseErr_V2_007_011_0003: 'The parameter is null or the custom visual license list to be imported is empty.',
	customVisualLicenseErr_V2_007_011_0004: 'Failed to import custom visual license.',
	customVisualLicenseErr_V2_007_011_0005: 'Failed to get custom visual license list.',
	customVisualLicenseErr_V2_007_011_0006: 'Failed to delete custom visual license.',
};
