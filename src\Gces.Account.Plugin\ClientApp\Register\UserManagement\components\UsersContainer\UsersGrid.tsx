import * as React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';
import CGrid, { Column } from 'gces-react-grid';
import { Button, Dropdown, DropdownProps, DropdownItemProps, ConfirmDialog, ConfirmDialogProps } from 'gces-ui';
import { userActionCreators, UserState, UserInfo, Organization, UserProperty } from '../../store';
import { hasManageUserPermission, isAdministrator } from '../../utils';
import EmptyPage from '../../../../components/EmptyPage';
import { GlobalOrganization } from '../../../../util';
import { CInputEditor } from '../../../../components/c-input';

interface ConnectedProps {
	curUser: User;
	allUsers: UserInfo[];
	properties: UserProperty[];
	selectedOrganizationId: string;
	showMembersOfSubOrg: boolean;
	searchText: string;
	organizations: Organization[];
	dispatch?: any;
	t?: any;
}

interface UsersContainerOwnState {
	deletingUser: UserInfo;
	removeUserFromOrg: UserInfo;
	selectedRowIndex: number;
	sensitivePropDic: Record<string, Record<string, boolean>>;
}

@translate('user', { wait: true })
class UsersGridInner extends React.Component<ConnectedProps, UsersContainerOwnState> {
	state: UsersContainerOwnState = {
		deletingUser: null,
		removeUserFromOrg: null,
		selectedRowIndex: 0,
		sensitivePropDic: {},
	};
	UserStatus = {
		Enabled: 1,
		Disabled: 2,
	};
	UserStatusText = {
		1: this.props.t && this.props.t('Enabled'),
		2: this.props.t && this.props.t('Disabled'),
	};

	componentWillReceiveProps(nextProps: ConnectedProps) {
		if (nextProps.selectedOrganizationId !== this.props.selectedOrganizationId
			|| nextProps.allUsers.length - 1 < this.state.selectedRowIndex
			|| nextProps.allUsers.length !== this.props.allUsers.length) {
			this.setState({ selectedRowIndex: 0 });
		}
		if (nextProps.showMembersOfSubOrg !== this.props.showMembersOfSubOrg) {
			const { selectedRowIndex } = this.state;
			const selectedUser = this.calcDisplayUsers(this.props)[selectedRowIndex];
			const newSelectedRowIndex = selectedUser ? this.calcDisplayUsers(nextProps).findIndex(u => u.id === selectedUser.id) : 0;
			this.setState({ selectedRowIndex: newSelectedRowIndex > -1 ? newSelectedRowIndex : 0 });
		}
	}

	showDeleteConfirmDialog = (user: UserInfo) => {
		this.setState({ deletingUser: user });
	};

	showRemoveConfirmDialog = (e, user: UserInfo, rowIndex: number) => {
		e?.preventDefault();
		e?.stopPropagation();
		this.setState({ removeUserFromOrg: user, selectedRowIndex: rowIndex });
	};

	handleUserActions = (row: UserInfo, value: string, rowIndex: number) => {
		switch (value) {
			case 'edit': return this.props.dispatch(userActionCreators.setEditingUser(row));
			case 'enable': case 'disable': return this.props.dispatch(userActionCreators.toggleUserStatus(row.id, row.status === this.UserStatus.Disabled));
			case 'delete': return this.showDeleteConfirmDialog(row);
			case 'remove': return ((e) => this.showRemoveConfirmDialog(e, row, rowIndex))();
		}
	};

	onEditUser = (e, row: UserInfo, rowIndex?: number) => {
		e.preventDefault();
		e.stopPropagation();
		const { allUsers, dispatch } = this.props;
		this.setState({ selectedRowIndex: rowIndex });
		dispatch(userActionCreators.setEditingUser(allUsers.find(u => u.id === row.id)));
		dispatch(userActionCreators.setDetailUserId(''));
	}

	generateRowDropDownItems = (row: UserInfo): DropdownItemProps[] => {
		const { curUser, showMembersOfSubOrg, t } = this.props;
		const canEdit = this.canEdit(row);

		const items = [
			{
				value: 'edit',
				text: t('Edit'),
				title: t('Edit'),
				aid: 'user-item-edit',
				disabled: !canEdit,
			},
			{
				value: row.status === this.UserStatus.Disabled ? 'enable' : 'disable',
				text: row.status === this.UserStatus.Disabled ? this.props.t('Enable') : this.props.t('Disable'),
				title: row.status === this.UserStatus.Disabled ? this.props.t('Enable') : this.props.t('Disable'),
				aid: `user-item-${row.status === this.UserStatus.Disabled ? 'enable' : 'disable'}`,
				disabled: !canEdit || row.id === curUser.id,
			},
			{
				value: 'delete',
				text: t('Delete'),
				title: t('Delete'),
				aid: 'user-item-delete',
				disabled: !canEdit || row.id === curUser.id,
			}
		];

		if (!isAdministrator(curUser.roles) && row.id !== curUser.id && !showMembersOfSubOrg) {
			items.push({
				value: 'remove',
				text: t('RemoveUser'),
				title: t('RemoveUser'),
				aid: 'user-item-remove-from-org',
				disabled: this.props.showMembersOfSubOrg,
			});
		}

		if (row.id !== 'user_id_for_admin') return items;
		else return [items[0]];
	}

	canEdit = (displayUser: UserInfo) => {
		const { curUser } = this.props;
		return isAdministrator(curUser.roles) || (hasManageUserPermission(this.props.curUser.roles) && displayUser.organizationIdPath.includes(curUser.orgId));
	}

	onClickSensitiveToggle = (showEncryptedText: boolean, id: string, propertyName: string, rowIndex: number) => {
		this.setState({ selectedRowIndex: rowIndex });

		const { sensitivePropDic } = this.state;
		if (!sensitivePropDic[id]) {
			sensitivePropDic[id] = {};
		}
		sensitivePropDic[id][propertyName] = showEncryptedText;
	}

	renderCell = (key: string, row: UserInfo, rowIndex: number) => {
		if (key === 'status') {
			const status = this.UserStatusText[row && row.status];
			return <div className='grid-cell-inner user-status' title={status}> {status} </div>;
		}
		const canEdit = this.canEdit(row);
		if (key === 'contextMenu') {
			const { selectedOrganizationId, curUser, t } = this.props;
			if (selectedOrganizationId !== (curUser.orgId || GlobalOrganization.Id)) {
				return (
					<div className='row-action-box'>
						<Button
							rounded={true}
							icon='mdi mdi-pencil'
							className='user-item-actions edit-user-button'
							title={t('EditUser')}
							style='transparent'
							size='small'
							aid='edit-user'
							disabled={!canEdit}
							onClick={(e) => this.onEditUser(e, row, rowIndex)}
						/>
						<Button
							rounded={true}
							icon='mdi mdi-close'
							className='user-item-actions remove-user-button'
							title={this.props.showMembersOfSubOrg ? t('onlyNoMemberOfSubOrg') : t('RemoveUser')}
							style='transparent'
							size='small'
							aid='remove-user-from-organization'
							disabled={this.props.showMembersOfSubOrg}
							onClick={(e) => this.showRemoveConfirmDialog(e, row, rowIndex)}
						/>
					</div>
				);
			}
			const contextMenuProps: DropdownProps = {
				items: this.generateRowDropDownItems(row),
				icon: 'mdi mdi-dots-vertical',
				style: 'transparent',
				size: 'small',
				rounded: true,
				inline: true,
				className: 'user-item-actions',
				onSelect: (value) => this.handleUserActions(row, value, rowIndex),
				onToggle: () => this.setState({ selectedRowIndex: rowIndex }),
				aid: 'user-item-actions',
				hiddenChevron: true,
				offset: true,
			};
			return <Dropdown {...contextMenuProps} />;
		}

		const property = this.props.properties.find(p => p.name === key);
		if (property && property.sensitive) {
			return (
				<CInputEditor
					key={key}
					readOnly
					value={row[key]}
					visibilityToggle={!!row[key]}
					showEyeTitle={this.props.t('ShowPropertyValue')}
					hideEyeTitle={this.props.t('HidePropertyValue')}
					onClick={(showEncryptedText: boolean) => this.onClickSensitiveToggle(showEncryptedText, row.id, key, rowIndex)}
					defaultShowEncryptedText={this.state.sensitivePropDic[row.id] && this.state.sensitivePropDic[row.id][key]}
				/>
			);
		}
	};

	onUserRowClick = (row: UserInfo, e, rowIndex: number) => {
		this.setState({ selectedRowIndex: rowIndex });
		this.props.dispatch(userActionCreators.setDetailUserId(row.id));
	};

	searchUsers = (usersWithProperty: UserInfo[], text: string, propertyNames: string[]) => {
		let displayedUsers = [];
		if (text && text !== '') {
			const value = text.toLowerCase();
			usersWithProperty.forEach(u => {
				if ((u.username && u.username.toLowerCase().indexOf(value) > -1) ||
					(u.email && u.email.toLowerCase().indexOf(value) > -1) ||
					(u.firstName && u.firstName.toLowerCase().indexOf(value) > -1) ||
					(u.lastName && u.lastName.toLowerCase().indexOf(value) > -1) ||
					(u.fullName && u.fullName.toLowerCase().indexOf(value) > -1) ||
					(u.mobile && u.mobile.toLowerCase().indexOf(value) > -1) ||
					(u.provider && u.provider.toLowerCase().indexOf(value) > -1)) {
					displayedUsers.push({ ...u });
					return;
				}
				if (propertyNames?.length > 0) {
					if (propertyNames.some(name => u[name] && u[name].toLowerCase().indexOf(value) > -1)) {
						displayedUsers.push({ ...u });
					}
				}
			});
		} else {
			displayedUsers = usersWithProperty.map(u => ({ ...u }));
		}
		return displayedUsers;
	};

	buildDeleteUserDialog = () => {
		const { organizations, selectedOrganizationId, dispatch, t } = this.props;
		const { deletingUser, removeUserFromOrg } = this.state;
		if (!deletingUser && !removeUserFromOrg) return null;
		let title = '';
		let content = '';
		let yesCallback = null;
		if (deletingUser) {
			title = t('DeleteUser');
			content = t('DeleteUserConfirmMessage', { user: deletingUser.username });
			yesCallback = () => {
				dispatch(userActionCreators.deleteUser(deletingUser.id));
				this.setState({ deletingUser: null });
			};
		}
		else if (removeUserFromOrg) {
			title = t('RemoveUserFromOrg');
			const curOrgIndex = organizations.findIndex(o => o.id === selectedOrganizationId);
			content = t('RemoveUserConfirmMessage', { user: removeUserFromOrg.username, organization: organizations[curOrgIndex].name });
			yesCallback = () => {
				dispatch(userActionCreators.removeUserFromOrganization(this.props.selectedOrganizationId, removeUserFromOrg.id));
				this.setState({ removeUserFromOrg: null });
			};
		}

		const dlgProps: ConfirmDialogProps = {
			parentSelector: () => document.querySelector('#portal-modals'),
			title,
			buttonSize: 'small',
			yesText: t('Yes'),
			noText: t('Cancel'),
			portalClassName: '',
			yesCallback,
			noCallback: () => this.setState({ deletingUser: null, removeUserFromOrg: null }),
		};
		return <ConfirmDialog {...dlgProps} >{content}</ConfirmDialog>;
	}

	calcDisplayUsers = (props: ConnectedProps) => {
		const { allUsers, properties, searchText, organizations, selectedOrganizationId, showMembersOfSubOrg } = props;

		const inListProperties = properties ? properties.filter(s => s.showInList) : [];

		let displayedUsers = [];
		const selectedOrg = organizations.length ? organizations.find(o => o.id === selectedOrganizationId) : null;
		let allUsersWithProperty = allUsers ? allUsers : [];

		if (selectedOrganizationId !== GlobalOrganization.Id) {
			if (showMembersOfSubOrg) {
				allUsersWithProperty = (allUsers && selectedOrg) ? allUsers.filter(u => u.organizations.findIndex(o => o.startsWith(selectedOrg.path)) > -1) : [];
			} else {
				allUsersWithProperty = (allUsers && selectedOrg) ? allUsers.filter(u => u.organizations.findIndex(o => o === selectedOrg.path) > -1) : [];
			}
		}

		if (inListProperties.length && allUsersWithProperty.length) {
			allUsersWithProperty.forEach(user => {
				inListProperties.forEach(p => {
					let propValue = '';
					if (p.multivalued) {
						propValue = user.customizeProperties
							? (user.customizeProperties[p.name] && user.customizeProperties[p.name].length > 0 ? user.customizeProperties[p.name].sort((v1, v2) => v1.toUpperCase().localeCompare(v2.toUpperCase())) : []).join(', ')
							: '';
					} else {
						propValue = user.customizeProperties ? (user.customizeProperties[p.name] && user.customizeProperties[p.name].length > 0 ? user.customizeProperties[p.name][0] : '') : '';
					}
					user[p.name] = propValue;
				});
			});
		}
		if (allUsersWithProperty && allUsersWithProperty.length) {
			displayedUsers = this.searchUsers(allUsersWithProperty, searchText, inListProperties?.map(p => p.name) ?? []);
		}
		displayedUsers.forEach(u => { u.showMembersOfSubOrg = showMembersOfSubOrg; u.selectedOrgId = selectedOrganizationId; });

		return displayedUsers;
	}

	render() {
		const { searchText, properties, dispatch, t } = this.props;
		const { selectedRowIndex } = this.state;
		const columns: Column[] = [
			{ key: 'username', label: t('Username') },
			{ key: 'email', label: t('Email') },
			{ key: 'provider', label: t('Provider') },
			{ key: 'status', label: t('Status') },
			{ key: 'contextMenu', label: '', width: 80 }
		];
		const inListProperties = properties ? properties.filter(s => s.showInList) : [];
		columns.splice(-1, 0, ...inListProperties.map(p => ({ key: p.name, label: p.name, minWidth: p.sensitive && 70 })));

		const displayUsers = this.calcDisplayUsers(this.props);

		if (!searchText && !displayUsers.length) {
			const emptyPageProps = {
				imageName: 'locked-user-management',
				tip: t('ecNoMemberTip'),
				buttonText: t('SelectMembers'),
				onclick: () => dispatch(userActionCreators.setIsSelectingMembers(true)),
			};
			return <EmptyPage {...emptyPageProps} />;
		}

		return (
			<div className='user-grid-container'>
				<CGrid
					rowHeight={40}
					rows={displayUsers}
					hideGridLine={true}
					columns={columns}
					columnResizing={true}
					onRenderCell={this.renderCell}
					onRowClick={this.onUserRowClick}
					noRowsTip={searchText ? t('smEmptyResult') : t('cmNoMembersInOrg')}
					selectedRowIndex={selectedRowIndex}
					// measureLabelContext={{ fontSize: '12px', fontWeight: 'normal', padding: '0 18px 0 8px' }}
				/>
				{this.buildDeleteUserDialog()}
			</div>
		);
	}
}

export const UsersGrid = connect(
	(state: { user: UserState, navApp: { user: User } }) => ({
		curUser: state.navApp.user,
		allUsers: state.user.allUsers,
		properties: state.user.properties,
		searchText: state.user.searchText,
		selectedOrganizationId: state.user.selectedOrganizationId,
		organizations: state.user.organizations,
		showMembersOfSubOrg: state.user.showMembersOfSubOrg,
	})
)(UsersGridInner) as React.ComponentClass<{}>;
