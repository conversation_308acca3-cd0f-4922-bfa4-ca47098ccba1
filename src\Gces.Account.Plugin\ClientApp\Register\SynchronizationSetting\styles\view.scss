.synchronization-view {
	padding: 0 10px;
	flex: auto;
	display: flex;
	flex-direction: column;
	position: relative;

	.synchronization-results {
		display: flex;
		flex-direction: column;
		flex: 0 0 170px;

		&.full {
			flex: 0 0 100%;

			.view-results-content {
				flex: auto;
			}
		}

		.view-results-content {
			flex: 0 0 150px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			.last-task-result-icon {
				font-size: 26px;

				&.mdi-check-circle {
					color: var(--gces-primary);
				}

				&.mdi-alert-circle {
					color: var(--gces-warning);
				}

				&.mdi-alert {
					color: var(--gces-danger);
				}
			}

			.progress-bar {
				display: flex;
				flex-direction: row;
				background-color: transparent;

				.progress-item {
					width: 30px;
					height: 10px;
					margin-right: 5px;

					&.done {
						background-color: #49cb64;
					}

					&.active {
						animation: 2s demo linear infinite;
						animation-direction: alternate-reverse;

						@keyframes demo {
							0% {
								background-color: #d9d9d9;
							}

							100% {
								background-color: #49cb64;
							}
						}
					}

					&.inactive {
						background-color: #d9d9d9;
					}

					&.failed {
						background-color: #d22b28;
					}
				}
			}

			.no-record-tip {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				margin-top: -20px;
			}
		}
	}

	.synchronization-records {
		.view-records-content {
			height: 240px;
		}
	}

	.synchronization-information-not-synced {
		padding: 20px 0;
		flex: auto;
		display: flex;
		flex-direction: column;

		.view-error-content {
			flex: auto;
			display: flex;
			flex-direction: column;

			.cg-cell {
				span {
					@include gces-truncate;
				}
			}

			.not-sync-reason {
				width: 100%;
				display: flex;

				.mdi {
					margin-right: 5px;

					&.mdi-alert {
						color: var(--gces-danger);
					}

					&.mdi-alert-circle {
						color: var(--gces-warning);
					}
				}

				span {
					@include gces-truncate;
				}
			}

			.column-header-with-dropdown,
			.column-header-with-input-search {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.column-header-title {
					@include gces-truncate;
				}

				.column-header-filter {
					--column-header-size: 26px;
					--column-header-radius: calc(var(--column-header-size) / 2);

					button {
						min-width: var(--column-header-size);
						min-height: var(--column-header-size);
						width: var(--column-header-size);
						height: var(--column-header-size);
						border-radius: var(--column-header-radius);

						i {
							width: var(--column-header-size);
							height: var(--column-header-size);
							font-size: var(--gces-ef-font-size);
							line-height: var(--column-header-size);
						}
					}

					.column-header-filter-dropdown {
						width: var(--column-header-size);
						min-width: var(--column-header-size);
						height: var(--column-header-size);
						min-height: var(--column-header-size);
					}

					&.un-filtering {
						.tb-search-box-container {
							display: none;
							width: 0;
							min-width: 0;
						}
					}

					&.filtering {
						.start-search-btn {
							display: none;
							width: 0;
							min-width: 0;
						}
					}
				}

				.start-search-btn {
					margin: 0 10px 0 0;
				}

				.tb-search-box-container {
					.header-filter-input-box {
						height: var(--column-header-size);
						border-radius: var(--column-header-radius);

						.sb-icon {
							flex: 0 0 var(--column-header-size);
							font-size: var(--gces-ef-font-size);
							line-height: var(--column-header-size);

							&::before {
								height: var(--column-header-size);
								line-height: var(--column-header-size);
							}
						}
					}
				}
			}
		}
	}
}
