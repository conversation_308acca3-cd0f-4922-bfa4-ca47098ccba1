import * as React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';
import { Button, Toolbar, ToolbarProps, Checkbox } from 'gces-ui';
import * as util from '../../utils';
import { userActionCreators, UserState } from '../../store';
import { GlobalOrganization } from '../../../../util';

interface ConnectedProps {
	curUser: User;
	searchText: string;
	selectedOrgId: string;
	showMembersOfSubOrg: boolean;
	dispatch: any;
	t: any;
}

@translate('user', { wait: true })
class ToolbarContainerInner extends React.Component<ConnectedProps> {
	exportUsers = (e) => {
		e.stopPropagation();
		const a = document.createElement('a');
		a.href = `api/v2/identity/users/export${window.token ? `?${window.grapecity?.wyn?.integrationTokenName || 'token'}=${window.token}` : ''}`;
		a.click();
	};

	onSearchTextChanged = (e) => {
		const value = e.target.value;
		this.props.dispatch(userActionCreators.setSearchText(value));
	}
	onAddUser = () => {
		this.props.dispatch(userActionCreators.setIsAddingUser(true));
	}

	onSelectMembers = () => {
		this.props.dispatch(userActionCreators.setIsSelectingMembers(true));
	}

	onImportUser = () => {
		this.props.dispatch(userActionCreators.setIsImportingUser(true));
	}

	switchShowStrategy = (value: boolean) => {
		const { dispatch } = this.props;
		dispatch(userActionCreators.setShowMembersOfSubOrg(!value));
	}

	render() {
		const { curUser, showMembersOfSubOrg, searchText, selectedOrgId, t } = this.props;
		const toolbarProps: ToolbarProps = {
			className: 'search-user-input',
			items: [
				{
					kind: 'input',
					title: t('SearchText'),
					type: 'text',
					icon: 'mdi mdi-magnify',
					value: searchText,
					onChange: this.onSearchTextChanged
				}
			],
			style: 'transparent',
			size: 'small',
			rounded: true,
			align: 'right',
			ariaLabel: 'Search User'
		};
		return (
			<div className='toolbar-div'>
				{selectedOrgId !== GlobalOrganization.Id &&
					<Checkbox
						className='toolbar-checkbox switch-show-strategy'
						checked={showMembersOfSubOrg}
						value={showMembersOfSubOrg}
						text={t('ShowSubOrg')}
						title={t('ShowSubOrg')}
						onChange={this.switchShowStrategy}
						aid='switch show strategy'
					/>
				}
				<div className='tool-bar-btn-wrapper'>
					<Toolbar {...toolbarProps} />
					{util.hasManageUserPermission(curUser.roles) &&
						<Button
							className='toolbar-button select-members-button'
							style='accent'
							icon='mdi mdi-plus'
							size='small'
							title={t('AddUser')}
							text={t('AddUser')}
							onClick={this.onAddUser}

						/>
					}
					{selectedOrgId !== GlobalOrganization.Id &&
						<Button
							className='toolbar-button select-members-button'
							style='accent'
							icon='mdi mdi-plus'
							size='small'
							title={t('SelectMembers')}
							text={t('SelectMembers')}
							onClick={this.onSelectMembers}

						/>
					}
					{selectedOrgId === GlobalOrganization.Id &&
						<Button
							className='toolbar-button import-user-button'
							style='accent'
							size='small'
							icon='mdi mdi-import'
							title={t('Import')}
							text={t('Import')}
							onClick={this.onImportUser}
						/>
					}
					{selectedOrgId === GlobalOrganization.Id &&
						<Button
							className='toolbar-button export-user-button'
							style='accent'
							size='small'
							icon='mdi mdi-export'
							title={t('Export')}
							text={t('Export')}
							onClick={(e) => this.exportUsers(e)}
						/>
					}
				</div>
			</div>
		);
	}
}

export const ToolbarContainer = connect(
	(state: { user: UserState, navApp: { user: User } }) => ({
		curUser: state.navApp.user,
		searchText: state.user.searchText,
		selectedOrgId: state.user.selectedOrganizationId,
		showMembersOfSubOrg: state.user.showMembersOfSubOrg,
	})
)(ToolbarContainerInner) as React.ComponentClass<{}>;