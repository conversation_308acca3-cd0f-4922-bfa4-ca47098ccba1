import * as React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';
import { Scrollbars } from 'gces-react-custom-scrollbars';
import { But<PERSON>, CheckboxWrapper } from 'gces-ui';
import * as classname from 'classnames';
import ProviderList from './ProviderList';
import ProviderSettings from './ProviderSettings';
import ConfirmDialog from './ConfirmDialog';
import * as util from '../util';
import { SecurityProvider } from '../interfaces';
import { safeFetchV2 } from '../utils/safeFetchV2';

interface ConnectProps {
	dispatch: any;
	t: any;
}

interface LocalState {
	securityProviders: SecurityProvider[];
	availableSecurityProviders: SecurityProvider[];
	selectedSecurityProvider: SecurityProvider;
	addingSecurityProvider: boolean;
	removingSecurityProvider: SecurityProvider;
	unavailableSecurityProviders: SecurityProvider[];
}
class SecurityProviderInner extends React.Component<ConnectProps, LocalState> {
	private _moveSecurityProviderUrl = '/api/v2/identity/security-providers/move';
	private _enableSecurityProviderUrl = '/api/v2/identity/security-providers/enable';
	private _removeSecurityProviderUrl = '/api/v2/identity/security-providers';
	private _reloadSecurityProvidersUrl = '/api/v2/identity/security-providers/reload';
	private _viewContent: React.RefObject<HTMLDivElement>;
	constructor(props, context) {
		super(props, context);

		this.state = {
			securityProviders: null,
			availableSecurityProviders: null,
			selectedSecurityProvider: null,
			addingSecurityProvider: false,
			removingSecurityProvider: null,
			unavailableSecurityProviders: null
		};
		this._viewContent = React.createRef<HTMLDivElement>();
	}

	SecurityProviderContextMenuActions = {
		MoveUp: { text: this.props.t('MoveUp'), value: 'up' },
		MoveDown: { text: this.props.t('MoveDown'), value: 'down' },
		Remove: { text: this.props.t('Remove'), value: 'remove' }
	};

	componentDidMount = () => {
		this.getSecurityProviders(providers => {
			const availableSecurityProviders = this.getAvailableSecurityProviders(providers);
			this.setState({
				securityProviders: providers,
				availableSecurityProviders,
				unavailableSecurityProviders: this.getUnavailableSecurityProviders(providers),
				selectedSecurityProvider: availableSecurityProviders.length > 0 ? availableSecurityProviders[0] : null
			});
		});
	}
	componentDidUpdate = () => {
		if (this._viewContent.current) this._viewContent.current.classList.add('open');
	}

	setSecurityProviders = (providers: SecurityProvider[]) => {
		this.setState({
			securityProviders: providers,
			availableSecurityProviders: this.getAvailableSecurityProviders(providers),
			unavailableSecurityProviders: this.getUnavailableSecurityProviders(providers),
		});
	}
	getSecurityProviders = (callback: (providers) => void) => {
		safeFetchV2('/api/v2/identity/security-providers', { credentials: 'same-origin' })
			.then((providers) => {
				if (providers && callback) callback(providers.result);
			}).catch((ex) => window.AdminPortal.Notifications.Send(0, this.props.t('Error'), ex.message));
	}
	removeSecurityProvider = (providerName: string) => {
		util.ajax(`${this._removeSecurityProviderUrl}/${providerName}`, this.reloadSecurityProviders, 'DELETE');
	}
	reloadSecurityProviders = () => {
		util.ajax(this._reloadSecurityProvidersUrl, (providers) => this.setSecurityProviders(providers), 'POST');
	}

	getAvailableSecurityProviders = (providers: SecurityProvider[]) => {
		const availableProviders = providers
			.filter(p => p.enabled)
			.sort((p1, p2) => p1.ordinal - p2.ordinal);
		return availableProviders;
	}
	getUnavailableSecurityProviders = (providers: SecurityProvider[]) => {
		return providers.reduce((ps, p) => {
			if (!p.enabled) ps.push({ ...p });
			return ps;
		}, []);
	}

	providerCheckStateChanged = (idx: number, checked: boolean) => {
		const providers = this.state.unavailableSecurityProviders.concat([]);
		providers[idx].checked = checked;

		this.setState({ unavailableSecurityProviders: providers });
	}

	buildAddSecurityProviderView = () => {
		const { t } = this.props;
		const { unavailableSecurityProviders, securityProviders } = this.state;
		const hasUnavailableSP = unavailableSecurityProviders && unavailableSecurityProviders.length > 0;
		return <div className='add-security-provider-view'>
			<div className='view-content' ref={this._viewContent}>
				{hasUnavailableSP
					? <div className='security-provider-list'>
						{unavailableSecurityProviders.map((p, i) => {
							return <div key={p.providerName} className={classname('security-provider', { 'odd': i % 2 === 0 })}>
								<div>
									<CheckboxWrapper checked={p.checked} onChange={(value) => this.providerCheckStateChanged(i, value)} />
								</div>
								<div className='provider-name' title={p.providerName} >{p.providerName}</div>
							</div>;
						})}
					</div>
					: <div className='add-security-provider-no-tip' title={t('NoAvailableSecurityProvidersTip')}>{t('NoAvailableSecurityProvidersTip')}</div>
				}
				<div className='btn-group'>
					<Button style='accent' maxWidth='120px' title={t('Save')} text={t('Save')} inline onClick={this.addSecurityProvider} disabled={!hasUnavailableSP} />
					<Button
						inline
						title={t('Cancel')}
						text={t('Cancel')}
						maxWidth='120px'
						onClick={() => this.setState({
							addingSecurityProvider: false,
							unavailableSecurityProviders: this.getUnavailableSecurityProviders(securityProviders),
						})}
					/>
				</div>
			</div>
		</div>;
	}

	getRemoveSecurityProviderConfirmDlg = () => {
		const { t } = this.props;
		const confirmDlgProps = {
			isOpen: this.state.removingSecurityProvider ? true : false,
			parentSelector: () => document.querySelector(util.portalAppId),
			headerText: t('RemoveSecurityProvider'),
			contentText: t('RemoveSecurityProviderConfirm', { provider: this.state.removingSecurityProvider.providerName }),
			yesText: t('Yes'),
			closeText: t('Close'),
			cancelText: t('Cancel'),
			onYes: this.handleRemoveSecurityProvider,
			onCancel: () => this.setState({ removingSecurityProvider: null }),
			onClose: () => this.setState({ removingSecurityProvider: null })
		};
		return <ConfirmDialog {...confirmDlgProps} />;
	}

	moveUpSecurityProvider = (provider: SecurityProvider) => {
		util.ajax(this._moveSecurityProviderUrl, this.reloadSecurityProviders, 'POST', { providerName: provider.providerName, offset: -1 });
	}
	moveDownSecurityProvider = (provider: SecurityProvider) => {
		util.ajax(this._moveSecurityProviderUrl, this.reloadSecurityProviders, 'POST', { providerName: provider.providerName, offset: 1 });
	}
	addSecurityProvider = () => {
		const providerNames = this.state.unavailableSecurityProviders.reduce((names, p) => {
			if (p.checked) names.push(p.providerName);
			return names;
		}, []);

		util.ajax(this._enableSecurityProviderUrl, () => {
			this.getSecurityProviders((providers) => {
				const availableSecurityProviders = this.getAvailableSecurityProviders(providers);
				this.setState({
					securityProviders: providers,
					availableSecurityProviders,
					unavailableSecurityProviders: this.getUnavailableSecurityProviders(providers),
					selectedSecurityProvider: providers.find(p => p.providerName === providerNames[0])
				});
			});
		}, 'POST', { providerNames, enable: true });
		this.setState({ addingSecurityProvider: false });
	}
	handleRemoveSecurityProvider = () => {
		const providerName = this.state.removingSecurityProvider.providerName;
		util.ajax(this._enableSecurityProviderUrl, () => {
			this.getSecurityProviders((providers) => {
				const availableSecurityProviders = this.getAvailableSecurityProviders(providers);
				this.setState({
					securityProviders: providers,
					availableSecurityProviders,
					unavailableSecurityProviders: this.getUnavailableSecurityProviders(providers),
					selectedSecurityProvider: availableSecurityProviders.length > 0 ? availableSecurityProviders[0] : null
				});
			});
			this.removeSecurityProvider(providerName);
		}, 'POST', { providerNames: [providerName], enable: false });
		this.setState({ removingSecurityProvider: null });
	}
	handleProviderActions = (provider: SecurityProvider, action: string) => {
		switch (action) {
			case this.SecurityProviderContextMenuActions.MoveUp.value:
				return this.moveUpSecurityProvider(provider);

			case this.SecurityProviderContextMenuActions.MoveDown.value:
				return this.moveDownSecurityProvider(provider);

			case this.SecurityProviderContextMenuActions.Remove.value:
				return this.showRemoveSecurityProviderConfirmDlg(provider);
		}
	}
	showRemoveSecurityProviderConfirmDlg = (provider: SecurityProvider) => {
		this.setState({ removingSecurityProvider: provider });
	}

	saveProviderSettings = (provider: SecurityProvider) => {
		const callback = (data) => {
			this.getSecurityProviders(providers => this.setSecurityProviders(providers));
			this.setState({ selectedSecurityProvider: data });
		};
		util.ajax(`/api/v2/identity/security-providers/${provider.providerName}`, callback, 'PUT', provider);
	}

	render() {
		if (!this.state.availableSecurityProviders || !this.state.selectedSecurityProvider || !this.state.unavailableSecurityProviders) return null;

		const dropdownProps = {
			rounded: true,
			style: 'transparent',
			icon: 'mdi mdi-dots-vertical',
			hiddenChevron: true,
			menuClassName: 'ef-inverted',
			title: this.props.t('More'),
		};

		const providerListProps = {
			providers: this.state.availableSecurityProviders,
			selectedProvider: this.state.selectedSecurityProvider,
			nameProp: 'providerName',
			titleProp: 'description',

			onSelect: (value) => { this.setState({ selectedSecurityProvider: value }); },
			onAdd: () => { this.setState({ addingSecurityProvider: true }); },

			dropdownBuilder: (provider) => {
				const dropdownItems = [];
				if (provider.ordinal !== 0) {
					dropdownItems.push(this.SecurityProviderContextMenuActions.MoveUp);
				}

				if (provider.ordinal !== this.state.availableSecurityProviders.length - 1) {
					dropdownItems.push(this.SecurityProviderContextMenuActions.MoveDown);
				}

				if (provider.providerName !== 'local') {
					dropdownItems.push({ divider: true });
					dropdownItems.push(this.SecurityProviderContextMenuActions.Remove);
				}

				return {
					...dropdownProps,
					items: dropdownItems,
					size: 'small',
					onSelect: (action) => this.handleProviderActions(provider, action),
					icon: 'mdi mdi-dots-vertical',
					style: 'transparent',
					rounded: true,
					offset: true
				};
			}
		};
		const providerSettingProps = {
			hideHeader: true,
			provider: { type: 'SecurityProvider', ...this.state.selectedSecurityProvider },
			onSave: (provider: SecurityProvider) => this.saveProviderSettings(provider)
		};

		const scrollbarProps = {
			style: { width: '100%', height: '100%' },
			renderThumbVertical: props => <div {...props} style={{ width: '4px' }} className='thumb-vertical' />,
		};

		return <Scrollbars {...scrollbarProps}>
			<div className='provider-container'>
				<div className='left-part'>
					{!this.state.addingSecurityProvider && <ProviderList {...providerListProps} />}
					{this.state.addingSecurityProvider && this.buildAddSecurityProviderView()}
				</div>

				<div className='right-part'>
					<ProviderSettings {...providerSettingProps} />
				</div>

				{this.state.removingSecurityProvider && this.getRemoveSecurityProviderConfirmDlg()}
			</div>
		</Scrollbars>;
	}
}

const connector = connect(state => {
	return state['account-management'].sp;
});
const translator = translate('account', { withRef: true });

export default translator(connector(SecurityProviderInner));
