import * as React from 'react';
import * as classnames from 'classnames';
import { Button, InputEditor } from 'gces-ui';

export interface CInputEditorProps {
	value: string;
	onEveryChange?: (values: any) => void;
	onChange?: (value: any) => void;
	aid?: string;
	invalid?: boolean;
	className?: string;
	visibilityToggle?: boolean;
	noVisibilityToggle?: boolean;
	placeholder?: string;
	maxLength?: number;
	readOnly?: boolean;
	disabled?: boolean;
	type?: 'text' | 'password' | 'email' | 'url' | 'search';
	showEyeTitle?: string;
	hideEyeTitle?: string;
	onClick?: (showEncryptedText: boolean) => void;
	defaultShowEncryptedText?: boolean;
}

interface LocalState {
	showEncryptedText: boolean;
}

export class CInputEditor extends React.PureComponent<CInputEditorProps, LocalState> {

	constructor(props: CInputEditorProps, context) {
		super(props, context);

		this.state = {
			showEncryptedText: !!props.defaultShowEncryptedText,
		};
	}

	toggleShowEncryptedText = (e) => {
		e.stopPropagation();
		this.setState({ showEncryptedText: !this.state.showEncryptedText });

		if (this.props.onClick) {
			this.props.onClick(!this.state.showEncryptedText);
		}
	}

	render() {
		const { invalid, value, className, visibilityToggle, noVisibilityToggle, placeholder, maxLength, readOnly, disabled, type, hideEyeTitle, showEyeTitle, onEveryChange, onChange } = this.props;

		return (
			<div className={classnames(className, 'efc-sensitive-wrapper', { 'efc-input-password': visibilityToggle }, { 'efc-input-password-show': visibilityToggle && this.state.showEncryptedText }, { 'efc-input-password-hidden': visibilityToggle && !this.state.showEncryptedText })}>
				<InputEditor
					maxLength={maxLength}
					invalid={invalid}
					placeholder={placeholder}
					value={value}
					onEveryChange={onEveryChange}
					onChange={onChange}
					type={type || (visibilityToggle && !this.state.showEncryptedText ? 'password' : 'text')}
					readOnly={readOnly}
					disabled={disabled}
				/>
				<Button
					className={classnames('efc-input-visible-toggle', { 'efc-input-visible-hidden': !visibilityToggle }, { 'efc-input-visible-none': noVisibilityToggle })}
					icon={this.state.showEncryptedText ? 'mdi mdi-eye' : 'mdi mdi-eye-off'}
					size='small'
					style='transparent'
					rounded
					onClick={this.toggleShowEncryptedText}
					title={this.state.showEncryptedText ? hideEyeTitle : showEyeTitle}
				/>
			</div>
		);
	}
}