import * as React from 'react';
import * as moment from 'moment';
import * as classnames from 'classnames';
import { Button, DateTimePicker } from 'gces-ui';

export interface CDateTimePickerProps {
	value: string | moment.Moment;
	mode?: 'time' | 'date' | 'dateTime';
	placeholder?: string;
	dateFormat?: string;
	timeFormat?: string;
	aid?: string;
	onChange: (value: any) => void;
	onBlur?: (value: any) => void;
	parentSelector?: () => Element;
	invalid?: boolean;
	className?: string;
	visibilityToggle?: boolean;
	noVisibilityToggle?: boolean;
	showEyeTitle?: string;
	hideEyeTitle?: string;
}

interface LocalState {
	showEncryptedText: boolean;
}

export class CDateTimePicker extends React.PureComponent<CDateTimePickerProps, LocalState> {

	constructor(props: CDateTimePickerProps, context) {
		super(props, context);

		this.state = {
			showEncryptedText: false,
		};
	}

	toggleShowEncryptedText = () => {
		this.setState({ showEncryptedText: !this.state.showEncryptedText });
	}

	render() {
		const { placeholder, value, mode, dateFormat, timeFormat, invalid, className, visibilityToggle, noVisibilityToggle, hideEyeTitle, showEyeTitle, onChange, onBlur } = this.props;

		return (
			<div className={classnames(className, 'efc-sensitive-wrapper', { 'efc-input-password': visibilityToggle })}>
				<DateTimePicker
					className={classnames({ 'efc-text-security': visibilityToggle && !this.state.showEncryptedText })}
					placeholder={placeholder}
					value={value}
					mode={mode}
					onChange={onChange}
					onBlur={onBlur}
					dateFormat={dateFormat}
					timeFormat={timeFormat}
					invalid={invalid}
				/>
				<Button
					className={classnames('efc-input-visible-toggle', { 'efc-input-visible-hidden': !visibilityToggle }, { 'efc-input-visible-none': noVisibilityToggle })}
					icon={this.state.showEncryptedText ? 'mdi mdi-eye' : 'mdi mdi-eye-off'}
					size='small'
					style='transparent'
					rounded
					onClick={this.toggleShowEncryptedText}
					title={this.state.showEncryptedText ? hideEyeTitle : showEyeTitle}
				/>
			</div>
		);
	}
}