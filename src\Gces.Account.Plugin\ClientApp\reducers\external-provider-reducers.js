﻿import ActionTypes from './../actions/action-types'

const ExternalProviderReducer = (state = {}, action) => {
    switch (action.type) {
        case ActionTypes.ExternalProviders:
            return Object.assign({}, state, { externalProviders: action.externalProviders ? action.externalProviders.map(p => ({ ...p })) : null })

        default:
            return state
    }
}

export default ExternalProviderReducer
