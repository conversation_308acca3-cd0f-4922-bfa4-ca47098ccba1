import * as moment from 'moment';

interface SafeFetchRes {
	result?: any;
	error?: any;
}

export const safeFetchV2 = (url: RequestInfo, init: RequestInit = null): Promise<SafeFetchRes> =>
	(init != null && fetch(url, init) || fetch(url))
		.then(checkStatus)
		.then(handleResolvedResponseBody)
		.then(result => ({ result }))
		.catch(handleRejectedResponseBody)
		.catch(error => errorCatch(error));

const checkStatus = (res: Response): Promise<Response> => {
	if (res.status >= 200 && res.status < 300) return Promise.resolve(res);
	return Promise.reject(res);
};

export const handleResolvedResponseBody = (res: Response) => {
	const resClone = res.clone();
	try {
		const contentType = res.headers.get('content-type')?.trim();
		if (res.status === 204) {
			return {};
		} else {
			if (!contentType || contentType.includes('application/json')) {
				return res.json();
			} else if (contentType.includes('application/text')) {
				return res.text();
			} else if (contentType.includes('application/octet-stream')) {
				let fileName = res.headers.get('content-disposition')?.split('filename=')[1];
				const utcNow = moment(new Date()).format('YYYYMMDDHHmmss');
				const dotIdx = fileName?.lastIndexOf('.');
				if (fileName) {
					if (dotIdx !== -1) {
						fileName = `${fileName.substring(0, dotIdx)}_${utcNow}${fileName.substring(dotIdx)}`;
					} else {
						fileName = fileName + utcNow;
					}
				} else {
					fileName = utcNow;
				}

				const data = res.blob();
				data.then(blob => {
					if (dotIdx === -1) {
						const dataType = blob.type;
						fileName = fileName + '.' + dataType;
					}

					const blobURL = window.URL.createObjectURL(blob);
					const tempLink = document.createElement('a');
					tempLink.style.display = 'none';
					tempLink.href = blobURL;
					tempLink.setAttribute('download', fileName);
					if (typeof tempLink.download === 'undefined') {
						tempLink.setAttribute('target', '_blank');
					}
					document.body.appendChild(tempLink);
					tempLink.click();
					document.body.removeChild(tempLink);
					setTimeout(() => {
						window.URL.revokeObjectURL(blobURL);
					}, 100);
				});
				return { success: true, fileName };
			}
			else {
				return res;
			}
		}
	} catch (e) {
		console.error('safe fetch v2 resolve error: ', e);
		return resClone;
	}
};

const handleRejectedResponseBody = (res: Response) => {
	if (res.status === 400 || res.status === 403 || res.status === 404) {
		const contentType = res.headers.get('content-type').trim();
		if (!contentType || contentType.includes('application/json')) {
			return res.json().then(json => ({
				error: json
			}));
		} else if (contentType.includes('application/text')) {
			return res.text().then(txt => ({
				error: txt
			}));
		} else {
			console.warn(`SafeFetch v2 direct rejects response whose status is ${res.status} and content-Type is ${contentType}.`);
			return Promise.reject(res);
		}
	} else {
		return Promise.reject(res);
	}
};

const errorCatch = (error) => {
	try {
		switch (error.status) {
			case 401:
				error.text().then(txt => {
					if (txt === 'ServerDisconnected') {
						window.location.replace('ServerDisconnected');
					}
					else if (txt === 'ConcurrenceCountInsufficient') {
						window.location.replace('ConcurrenceCountInsufficient');
					}
					else {
						window.location.replace('logout');
					}
				});
				return { error: '401' };
			default:
				return { error: `${error.status}` };
		}
	} catch (e) {
		console.error('safe fetch v2 reject error: ', e);
		return { error };
	}
};