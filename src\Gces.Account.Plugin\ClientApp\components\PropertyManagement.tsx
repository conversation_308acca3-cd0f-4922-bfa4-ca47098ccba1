﻿import { Button, CheckboxWrapper, Dropdown, DropdownProps, InputEditor, Label, Switch } from 'gces-ui';
import * as update from 'immutability-helper';
import * as React from 'react';
import { translate } from 'react-i18next';
import { connect } from 'react-redux';
import CGrid, { CGridProps, Column } from 'gces-react-grid';
import Actions from '../actions/actions';
import * as util from '../util';
import ConfirmDialog from './ConfirmDialog';
import Drawer from './Drawer';
import EmptyPage from './EmptyPage';
import { safeFetchV2 } from '../utils/safeFetchV2';
import { CMultiValueInput } from './c-multivalue-input';
import * as moment from 'moment';
import { Scrollbars } from 'gces-react-custom-scrollbars';

interface Property {
	id?: string;
	name: string;
	valueType: PropertyValueType;
	availableValues: any;
	showInList: boolean;
	multivalued: boolean;
	allowUserEdit: boolean;
	showInProfile: boolean;
	sensitive: boolean;
}

enum PropertyValueType {
	String,
	Boolean,
	Integer,
	Float,
	Date,
	DateTime,
}

interface ConnectProps {
	properties: Property[];
	isBusy: boolean;
	dispatch?: any;
	t?: any;
}

interface LocalState {
	addingProperty: boolean;
	editingProperty: Property;
	deletingProperty: Property;
	newProperty: Property;
	updatePropertyConfirm: boolean;
	invalidAvailableValues: string[];
	isMultivaluedChanged: boolean;

}

class PropertyManagement extends React.Component<ConnectProps, LocalState> {
	private _saving = false;
	private _deleting = false;

	constructor(props, context) {
		super(props, context);
		this.state = {
			addingProperty: false,
			editingProperty: null,
			deletingProperty: null,
			newProperty: {
				name: null,
				valueType: null,
				availableValues: null,
				showInList: null,
				multivalued: null,
				allowUserEdit: null,
				showInProfile: null,
				sensitive: null,
			},
			updatePropertyConfirm: false,
			invalidAvailableValues: null,
			isMultivaluedChanged: false,
		};
	}

	componentDidMount = () => this.getProperties((properties) => {
		const { dispatch } = this.props;
		dispatch(Actions.SetProperties(properties));
	})
	componentWillUnmount = () => {
		const { dispatch } = this.props;
		dispatch(Actions.SetProperties([]));
	}

	getProperties = (successCallback?, suppressBusy?) => {
		const { dispatch } = this.props;
		if (!suppressBusy) dispatch(Actions.StartLoading());

		safeFetchV2('/api/v2/identity/custom-properties', { credentials: 'same-origin' })
			.then((properties) => {
				if (successCallback && properties) {
					successCallback(properties.result);
					if (!suppressBusy) dispatch(Actions.EndLoading());
				}
			}).catch((ex) => {
				if (ex) window.AdminPortal.Notifications.Send(0, this.props.t('Error'), ex);
			});
	}

	addCustomizeProperty = (model) => {
		const { dispatch } = this.props;
		const callback = () => {
			this.getProperties((properties) => {
				dispatch(Actions.SetProperties(properties));
				this.hidePropertyDrawer();
			});
		};
		util.ajax('/api/v2/identity/custom-properties', callback, 'POST', model);
	}
	validateAvailableValues = (model) => {
		const callback = (data) => {
			if (data.hasInvalidValues) {
				this.setState({ updatePropertyConfirm: true, invalidAvailableValues: data.invalidValues });
			} else {
				this.updateCustomizeProperty({
					Id: this.state.editingProperty.id,
					Name: this.getPropertyName(),
					ValueType: model.ValueType,
					AvailableValues: model.AvailableValues,
					ShowInList: model.ShowInList,
					AllowUserEdit: model.AllowUserEdit,
					Multivalued: model.Multivalued,
					ShowInProfile: model.ShowInProfile,
					Sensitive: model.Sensitive,
				});
			}
		};
		util.ajax(`/api/v2/identity/custom-properties/${this.state.editingProperty.id}/available-values-validation`, callback, 'POST', {
			AvailableValues: model.AvailableValues
		});
	}
	updateCustomizeProperty = (model) => {
		const { dispatch } = this.props;
		const callback = () => {
			this.getProperties((properties) => {
				dispatch(Actions.SetProperties(properties));
				this.hidePropertyDrawer();
			});
		};
		util.ajax(`/api/v2/identity/custom-properties/${this.state.editingProperty.id}`, callback, 'PUT', model);
	}

	handlePropertyDrawerSave = () => {
		if (this._saving) {
			return;
		}

		this._saving = true;
		const name = this.state.newProperty.name || this.state.editingProperty.name;
		if (!name || !util.isValidFieldName(name)) {
			this._saving = false;
			return window.AdminPortal.Notifications.Send(0, this.props.t('PropertyNameRequirement'), this.props.t('PropertyNameRequirement'));
		}

		const availableValues = this.state.addingProperty ?
			(this.state.newProperty.availableValues !== null && this.state.newProperty.availableValues !== undefined) ? this.state.newProperty.availableValues.filter(s => !!s) : null :
			(this.state.newProperty.availableValues !== null && this.state.newProperty.availableValues !== undefined) ? this.state.newProperty.availableValues.filter(s => !!s) : this.state.editingProperty.availableValues;

		const showInList = this.state.addingProperty ?
			this.state.newProperty.showInList !== null ? this.state.newProperty.showInList : true :
			this.state.newProperty.showInList !== null ? this.state.newProperty.showInList : this.state.editingProperty.showInList;

		const multivalued = this.state.addingProperty ?
			this.state.newProperty.multivalued !== null ? this.state.newProperty.multivalued : false :
			this.state.newProperty.multivalued !== null ? this.state.newProperty.multivalued : this.state.editingProperty.multivalued;

		const allowUserEdit = this.state.addingProperty ?
			this.state.newProperty.allowUserEdit !== null ? this.state.newProperty.allowUserEdit : true :
			this.state.newProperty.allowUserEdit !== null ? this.state.newProperty.allowUserEdit : this.state.editingProperty.allowUserEdit;

		const showInProfile = this.state.addingProperty ?
			(this.state.newProperty.showInProfile !== null ? this.state.newProperty.showInProfile : true) :
			(this.state.newProperty.showInProfile !== null ? this.state.newProperty.showInProfile : this.state.editingProperty.showInProfile);

		const sensitive = this.state.addingProperty ?
			(this.state.newProperty.sensitive !== null ? this.state.newProperty.sensitive : false) :
			(this.state.newProperty.sensitive !== null ? this.state.newProperty.sensitive : this.state.editingProperty.sensitive);

		if (this.state.addingProperty) {
			this.addCustomizeProperty({
				Name: this.state.newProperty.name,
				ValueType: this.state.newProperty.valueType ?? PropertyValueType.String,
				AvailableValues: availableValues,
				ShowInList: showInList,
				AllowUserEdit: allowUserEdit,
				Multivalued: multivalued,
				ShowInProfile: showInProfile,
				Sensitive: sensitive,
			});
		} else {
			this.validateAvailableValues({
				Id: this.state.editingProperty.id,
				Name: this.state.editingProperty.name,
				ValueType: this.state.editingProperty.valueType ?? PropertyValueType.String,
				AvailableValues: availableValues,
				ShowInList: showInList,
				AllowUserEdit: allowUserEdit,
				Multivalued: multivalued,
				ShowInProfile: showInProfile,
				Sensitive: sensitive,
			});
		}
		this.setState({ isMultivaluedChanged: false });
		setTimeout(() => this._saving = false, 100);
	}

	buildUpdatePropertyConfirmDialog = () => {
		const { t } = this.props;
		const name = this.getPropertyName();
		const valueType = this.getPropertyValueType();
		const availableValues = (this.state.newProperty.availableValues !== null && this.state.newProperty.availableValues !== undefined) ?
			this.state.newProperty.availableValues.filter(s => !!s) :
			this.state.editingProperty.availableValues;
		const showInList = (this.state.newProperty.showInList !== null && this.state.newProperty.showInList !== undefined) ?
			this.state.newProperty.showInList :
			this.state.editingProperty.showInList;
		const multivalued = (this.state.newProperty.multivalued !== null && this.state.newProperty.multivalued !== undefined) ?
			this.state.newProperty.multivalued :
			this.state.editingProperty.multivalued;
		const allowUserEdit = (this.state.newProperty.allowUserEdit !== null && this.state.newProperty.allowUserEdit !== undefined) ?
			this.state.newProperty.allowUserEdit :
			this.state.editingProperty.allowUserEdit;
		const showInProfile = (this.state.newProperty.showInProfile !== null && this.state.newProperty.showInProfile !== undefined) ?
			this.state.newProperty.showInProfile :
			this.state.editingProperty.showInProfile;
		const sensitive = (this.state.newProperty.sensitive !== null && this.state.newProperty.sensitive !== undefined) ?
			this.state.newProperty.sensitive :
			this.state.editingProperty.sensitive;
		const model = {
			Id: this.state.editingProperty.id,
			Name: name,
			ValueType: valueType,
			AvailableValues: availableValues,
			ShowInList: showInList,
			Multivalued: multivalued,
			AllowUserEdit: allowUserEdit,
			ShowInProfile: showInProfile,
			Sensitive: sensitive,
		};
		const dlgProps = {
			isOpen: true,
			parentSelector: () => document.body,
			headerText: t('ContinueUpdate'),
			contentText: t('UpdatePropertyConfirm', { propValues: this.state.invalidAvailableValues }),
			yesText: t('Yes'),
			closeText: t('Close'),
			cancelText: t('Cancel'),
			onYes: () => this.updateCustomizeProperty(model),
			onCancel: () => this.setState({ updatePropertyConfirm: false }),
			onClose: () => this.setState({ updatePropertyConfirm: false })
		};
		return <ConfirmDialog {...dlgProps} />;
	}

	handlePropertyActions = (index, value) => {
		const { properties } = this.props;
		switch (value) {
			case 'edit':
				this.handleEditProperty(properties[index]);
				break;
			case 'delete':
				this.showDeleteConfirmDialog(properties[index]);
				break;
		}
	}

	handleAddProperty = () => {
		this.setState({ addingProperty: true });
	}
	handleEditProperty = (property) => {
		this.setState({ editingProperty: property });
	}
	hidePropertyDrawer = () => {
		this.setState({
			addingProperty: false,
			editingProperty: null,
			newProperty: {
				name: null,
				valueType: null,
				availableValues: null,
				showInList: null,
				multivalued: null,
				allowUserEdit: null,
				showInProfile: null,
				sensitive: null,
			},
			updatePropertyConfirm: false,
			isMultivaluedChanged: false
		});
	}
	showDeleteConfirmDialog = (property) => {
		this.setState({ deletingProperty: property });
	}
	hideDeleteConfirmDialog = () => {
		this.setState({ deletingProperty: null });
	}

	deleteProperty = () => {
		if (!this._deleting) {
			this._deleting = true;

			safeFetchV2(`/api/v2/identity/custom-properties/${this.state.deletingProperty.id}`, {
				credentials: 'same-origin',
				method: 'DELETE'
			}).then((response) => {
				this._deleting = false;
				if (typeof response === 'string') {
					window.AdminPortal.Notifications.Send(0, this.props.t('Error'), response);
				} else {
					this.getProperties((properties) => {
						const { dispatch } = this.props;
						dispatch(Actions.SetProperties(properties));
						this.hideDeleteConfirmDialog();
					});
				}
			}).catch((ex) => {
				this._deleting = false;
				window.AdminPortal.Notifications.Send(0, this.props.t('Error'), ex.message);
			});
		}
	}

	getPropertyName = () => {
		if (this.state.addingProperty) {
			return this.state.newProperty.name;
		} else {
			if (this.state.newProperty.name !== null && this.state.newProperty.name !== undefined) {
				return this.state.newProperty.name;
			} else {
				return this.state.editingProperty.name;
			}
		}
	}

	getPropertyValueType = () => {
		if (this.state.addingProperty) {
			return this.state.newProperty.valueType ?? PropertyValueType.String;
		} else {
			if (this.state.newProperty.valueType !== null && this.state.newProperty.valueType !== undefined) {
				return this.state.newProperty.valueType;
			} else {
				return this.state.editingProperty.valueType;
			}
		}
	}

	getPropertyAvailableValues = () => {
		if (this.state.addingProperty) {
			return this.state.newProperty.availableValues;
		} else {
			if (this.state.newProperty.availableValues !== null && this.state.newProperty.availableValues !== undefined) {
				return this.state.newProperty.availableValues;
			} else {
				if (this.state.editingProperty.availableValues !== null && this.state.editingProperty.availableValues !== undefined) {
					return this.state.editingProperty.availableValues;
				} else {
					return null;
				}
			}
		}
	}

	validPropertyAvailableValues = () => {
		const availableValues = this.getPropertyAvailableValues();
		const valueType = this.getPropertyValueType();
		if (!availableValues || availableValues.length === 0) {
			return true;
		}
		return util.isValidValue(availableValues, util.ContextValueTypes[valueType]);
	}

	getPropertyShowInList = () => {
		if (this.state.addingProperty) {
			if (this.state.newProperty.showInList !== null && this.state.newProperty.showInList !== undefined) {
				return this.state.newProperty.showInList;
			} else {
				return true;
			}
		} else {
			if (this.state.newProperty.showInList !== null && this.state.newProperty.showInList !== undefined) {
				return this.state.newProperty.showInList;
			} else {
				return this.state.editingProperty.showInList;
			}
		}
	}

	getPropertyMultivalued = () => {
		if (this.state.addingProperty) {
			if (this.state.newProperty.multivalued !== null && this.state.newProperty.multivalued !== undefined) {
				return this.state.newProperty.multivalued;
			} else {
				return false;
			}
		} else {
			if (this.state.newProperty.multivalued !== null && this.state.newProperty.multivalued !== undefined) {
				return this.state.newProperty.multivalued;
			} else {
				return this.state.editingProperty.multivalued;
			}
		}
	}

	getPropertyAllowUserEdit = () => {
		if (this.state.addingProperty) {
			if (this.state.newProperty.allowUserEdit !== null && this.state.newProperty.allowUserEdit !== undefined) {
				return this.state.newProperty.allowUserEdit;
			} else {
				return true;
			}
		} else {
			if (this.state.newProperty.allowUserEdit !== null && this.state.newProperty.allowUserEdit !== undefined) {
				return this.state.newProperty.allowUserEdit;
			} else {
				return this.state.editingProperty.allowUserEdit;
			}
		}
	}

	getPropertyShowInProfile = () => {
		if (this.state.addingProperty) {
			if (this.state.newProperty.showInProfile !== null && this.state.newProperty.showInProfile !== undefined) {
				return this.state.newProperty.showInProfile;
			} else {
				return true;
			}
		} else {
			if (this.state.newProperty.showInProfile !== null && this.state.newProperty.showInProfile !== undefined) {
				return this.state.newProperty.showInProfile;
			} else {
				return this.state.editingProperty.showInProfile;
			}
		}
	}

	getPropertySensitive = () => {
		if (this.state.addingProperty) {
			if (this.state.newProperty.sensitive !== null && this.state.newProperty.sensitive !== undefined) {
				return this.state.newProperty.sensitive;
			} else {
				return false;
			}
		} else {
			if (this.state.newProperty.sensitive !== null && this.state.newProperty.sensitive !== undefined) {
				return this.state.newProperty.sensitive;
			} else {
				return this.state.editingProperty.sensitive;
			}
		}
	}

	buildDeletePropertyDialog = () => {
		const { t } = this.props;
		const dlgProps = {
			isOpen: true,
			parentSelector: () => document.querySelector(util.portalAppId),
			headerText: t('DeleteProperty'),
			contentText: t('DeletePropertyConfirmMessage', { property: this.state.deletingProperty.name }),
			yesText: t('Yes'),
			closeText: t('Close'),
			cancelText: t('Cancel'),
			onYes: this.deleteProperty,
			onCancel: this.hideDeleteConfirmDialog,
			onClose: this.hideDeleteConfirmDialog
		};
		return <ConfirmDialog {...dlgProps} />;
	}

	convertMultiValuesToMultiStringValues = (values: any[], valueType: PropertyValueType) => {
		if (values && values.length === 1 && !values[0]) {
			return [];
		}
		switch (util.ContextValueTypes[valueType]) {
			case util.DataType.String:
			case util.DataType.Boolean:
			case util.DataType.Integer:
			case util.DataType.Float:
				return values.map(v => v + '');
			case util.DataType.Date:
				return values.map(v => typeof v === 'string' ? v : (v.format ? v.format(util.DateFormat) : moment(v).format(util.DateFormat)));
			case util.DataType.DateTime:
				return values.map(v => typeof v === 'string' ? v : (v.format ? v.format(util.DateTimeFormat) : moment(v).format(util.DateTimeFormat)));
			default: return values;
		}
	}

	onCellChange = (rowIndex, key, value) => {
		const { dispatch, properties } = this.props;

		const callback = () => {
			this.getProperties((newProperties) => {
				dispatch(Actions.SetProperties(newProperties));
			}, true);
		};

		const propId = properties[rowIndex].id;
		const propName = properties[rowIndex].name;
		if (key === 'showInList') {
			util.ajax(`${util.customizePropertiesUrl}/${propId}`, callback, 'Put', { Id: propId, Name: propName, ShowInList: value });
		} else if (key === 'showInProfile') {
			const data: any = { Id: propId, Name: propName, ShowInProfile: value };
			if (!value) data.AllowUserEdit = value;
			util.ajax(`${util.customizePropertiesUrl}/${propId}`, callback, 'Put', data);
		} else if (key === 'allowUserEdit') {
			const data: any = { Id: propId, Name: propName, AllowUserEdit: value };
			if (value) data.ShowInProfile = value;
			util.ajax(`${util.customizePropertiesUrl}/${propId}`, callback, 'Put', data);
		} else if (key === 'sensitive') {
			util.ajax(`${util.customizePropertiesUrl}/${propId}`, callback, 'Put', { Id: propId, Name: propName, Sensitive: value });
		}
	}

	renderCell = (key: string, row) => {
		const { t } = this.props;
		if (key === 'valueType') {
			return t(`PropertyValueType_${util.ContextValueTypes[row.valueType]}`);
		}

		if (key === 'showInList' || key === 'showInProfile' || key === 'allowUserEdit' || key === 'sensitive') {
			return <div className='grid-actions'>
				<CheckboxWrapper checked={row[key]} onChange={(val: boolean) => this.onCellChange(row.idx, key, val)} />
			</div>;
		}

		if (key === 'actions') {
			const dropdownProps: DropdownProps = {
				items: [
					{ text: t('Edit'), value: 'edit' },
					{ text: t('Delete'), value: 'delete' }
				],
				className: 'property-actions',
				icon: 'mdi mdi-dots-vertical',
				style: 'transparent',
				size: 'small',
				rounded: true,
				inline: true,
				onSelect: (val) => this.handlePropertyActions(row.idx, val),
				hiddenChevron: true,
				offset: true
			};

			return <div className='grid-actions'>
				<Dropdown {...dropdownProps} />
			</div>;
		}
	}

	render() {
		const { properties, t, isBusy } = this.props;

		const displayProperties: any = [...properties];
		displayProperties.forEach((property, idx) => {
			property.availableValuesStr = property.availableValues ? property.availableValues.join(', ') : null;
			property.multivaluedDisplayText = property.multivalued ? <i className='mdi mdi-check multivalued-value' /> : '';
			property.idx = idx;
		});

		const columns: Column[] = [
			{ key: 'name', label: t('PropertyName') },
			{ key: 'valueType', label: t('PropertyValueType') },
			{ key: 'availableValuesStr', label: t('AvailableValues') },
			{ key: 'showInList', label: t('ShowInList') },
			{ key: 'showInProfile', label: t('ShowInProfile') },
			{ key: 'allowUserEdit', label: t('AllowEdit') },
			{ key: 'sensitive', label: t('Sensitive') },
			{ key: 'multivaluedDisplayText', label: t('Multivalued') },
			{ key: 'actions', width: 40 },
		];

		const gridProps: CGridProps = {
			columns,
			rows: displayProperties,
			onRenderCell: this.renderCell,
			hideGridLine: true,
			rowHeight: 40,
		};

		const emptyPageProps = {
			imageName: 'property-management',
			tip: t('NoCustomizePropertiesTip'),
			buttonText: t('AddProperty'),
			onclick: this.handleAddProperty,
		};

		const scrollbarProps = {
			style: { width: '100%', height: '100%' },
			renderThumbVertical: props => <div {...props} style={{ width: '4px' }} className='thumb-vertical' />,
		};

		return (
			<div className='property-management properties'>
				<div className='btm-cm-group'>
					<Button
						style='accent'
						onClick={this.handleAddProperty}
						text={t('AddProperty')}
						title={t('AddProperty')}
						icon='mdi mdi-plus'
					/>
				</div>

				{!isBusy && (properties.length === 0 ? <EmptyPage {...emptyPageProps} /> : <CGrid {...gridProps} />)}

				{(this.state.addingProperty || this.state.editingProperty) && <Drawer
					open={true}
					onDismiss={this.hidePropertyDrawer}
					title={this.state.addingProperty ? t('AddProperty') : t('EditProperty')}
					closeButtonTitle={t('Close')}
					bodyRender={() => {
						return (
							<Scrollbars {...scrollbarProps}>
								<div>
									<div className='form-item'>
										<Label text={t('PropertyName')} />
										<InputEditor
											value={this.getPropertyName() || ''}
											placeholder={t('NewPropertyName')}
											disabled={false}
											onChange={(value) => {
												const newProperty = update(this.state.newProperty, { name: { $set: value } });
												this.setState({ newProperty });
											}}
										/>
									</div>
									<div className='form-item'>
										<Label text={t('PropertyValueType')} />
										<Dropdown
											offset
											width='100%'
											menuWidth='100%'
											size='small'
											textAlign='left'
											text={t(`PropertyValueType_${util.ContextValueTypes[this.getPropertyValueType()]}`)}
											disabled={!!this.state.editingProperty}
											items={util.ContextValueTypes.map((type, index) => {
												return {
													value: { type, index },
													text: t(`PropertyValueType_${type}`),
													title: t(`PropertyValueType_${type}`),
													selected: this.getPropertyValueType() === index,
												};
											})}
											onSelect={(value) => {
												const newProperty = update(this.state.newProperty, { valueType: { $set: value.index } });
												this.setState({ newProperty });
											}}
										/>
									</div>
									{this.getPropertyValueType() !== PropertyValueType.Boolean && <div className='form-item'>
										<Label text={t('AvailableValues')} />
										<CMultiValueInput
											value={(this.getPropertyAvailableValues() || []).join('\n')}
											valueType={util.ContextValueTypes[this.getPropertyValueType()]}
											placeHolder={t('AvailableValuesDesc')}
											onChange={(value) => {
												const newProperty = update(this.state.newProperty, { availableValues: { $set: this.convertMultiValuesToMultiStringValues(value.split('\n'), this.getPropertyValueType()) } });
												this.setState({ newProperty });
											}}
											noVisibilityToggle
										/>
									</div>}
									<div className='form-item'>
										<Label controlClass='form-switch-item' text={t('ShowInList')}>
											<Switch
												value={this.getPropertyShowInList()}
												trueString={t('Yes')}
												falseString={t('No')}
												onChange={(value: boolean) => {
													const newProperty = update(this.state.newProperty, { showInList: { $set: value } });
													this.setState({ newProperty });
												}}
											/>
										</Label>
									</div>
									<div className='form-item'>
										<Label controlClass='form-switch-item' text={t('ShowInProfile')}>
											<Switch
												value={this.getPropertyShowInProfile()}
												trueString={t('Yes')}
												falseString={t('No')}
												onChange={(value: boolean) => {
													const newProperty = value ?
														update(this.state.newProperty, { showInProfile: { $set: value } }) :
														update(this.state.newProperty, { showInProfile: { $set: value }, allowUserEdit: { $set: value } });
													this.setState({ newProperty });
												}}
											/>
										</Label>
									</div>
									<div className='form-item'>
										<Label controlClass='form-switch-item' text={t('AllowEdit')}>
											<Switch
												value={this.getPropertyAllowUserEdit()}
												trueString={t('Yes')}
												falseString={t('No')}
												onChange={(value: boolean) => {
													const newProperty = value ?
														update(this.state.newProperty, { allowUserEdit: { $set: value }, showInProfile: { $set: value } }) :
														update(this.state.newProperty, { allowUserEdit: { $set: value } });
													this.setState({ newProperty });
												}}
											/>
										</Label>
									</div>
									{this.getPropertyValueType() !== PropertyValueType.Boolean && <div className='form-item'>
										<Label controlClass='form-switch-item' text={t('Multivalued')}>
											<Switch
												value={this.getPropertyMultivalued()}
												trueString={t('Yes')}
												falseString={t('No')}
												onChange={(value: boolean) => {
													const oldMultivalued = this.state.editingProperty && this.state.editingProperty.multivalued;
													const newProperty = update(this.state.newProperty, { multivalued: { $set: value } });
													this.setState({ newProperty, isMultivaluedChanged: this.state.editingProperty ? oldMultivalued !== value : false });
												}}
											/>
										</Label>
									</div>}
									<div className='form-item'>
										<Label controlClass='form-switch-item' text={t('Sensitive')}>
											<Switch
												value={this.getPropertySensitive()}
												trueString={t('Yes')}
												falseString={t('No')}
												onChange={(value: boolean) => {
													const newProperty = update(this.state.newProperty, { sensitive: { $set: value } });
													this.setState({ newProperty });
												}}
											/>
										</Label>
									</div>
									{(!this.state.addingProperty && this.state.isMultivaluedChanged) && <div className='form-item'>
										<div className='custom-property-alert'>
											<i className='mdi mdi-alert' />
											<span title={t('MultivaluedChangedWarning')}>{t('MultivaluedChangedWarning')} </span>
										</div>
									</div>}
								</div>
							</Scrollbars>
						);
					}}
					footerRender={() => {
						return (
							<div>
								<Button style='accent' disabled={!this.getPropertyName() || this.getPropertyName().trim().length === 0 || !this.validPropertyAvailableValues()} inline={true} onClick={this.handlePropertyDrawerSave} title={t('Save')} text={t('Save')} />
								{' '}
								<Button inline={true} title={t('Cancel')} onClick={this.hidePropertyDrawer} text={t('Cancel')} />
							</div>
						);
					}}
				/>}

				{this.state.updatePropertyConfirm && this.buildUpdatePropertyConfirmDialog()}

				{this.state.deletingProperty && this.buildDeletePropertyDialog()}
			</div>
		);
	}
}

const connector = connect(state => {
	const propertyManagement = state['account-management'].property;
	return {
		...propertyManagement,
		isBusy: state['account-management'].common.isBusy,
	};
});
const translator = translate('account', { withRef: true });

export default translator(connector(PropertyManagement));
