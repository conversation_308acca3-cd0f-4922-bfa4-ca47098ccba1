.members-editor {
	padding: 10px 10px 10px 20px;
	position: absolute;
	left: 0;
	top: 0;
	bottom: 0;
	right: 0;
	background: $ef-bg-lt;
	display: flex;
	flex-direction: column;

	.me-header {
		flex: 0 0 50px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: $ef-font-size-sm;
		font-weight: bold;
	}

	.me-body {
		flex: 1;
		display: flex;
		padding-bottom: 10px;

		.no-tenant-users {
			flex: 1;
			box-shadow: 0 0 3px 3px $ef-bg;

			.user-row {
				display: flex;
				align-items: center;
				height: 40px;

				.checkbox-wp {
					flex: 0 0 60px;
					display: flex;
					align-items: center;
					height: 40px;
					padding-left: 20px;

					.efc-checkbox {
						height: 30px;
					}
				}

				.username {
					height: 40px;
					flex: 1;
					display: flex;
					align-items: center;
					font-size: $ef-font-size-sm;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				&.odd {
					background: $ef-bg;
				}
			}

			.tb-search-box-container {
				flex-direction: row-reverse;
				padding-right: 10px;

				.tb-search-box {
					flex: 0 0 160px;
					background-color: $ef-bg-dk;
					margin: 10px 0;

					.sb-icon {
						color: $ef-accent;
					}
				}
			}

			.search-box-wp {
				height: 50px;
				display: flex;
				align-items: center;
				flex-direction: row-reverse;
				padding-right: 10px;

				.search-box {
					height: 30px;
					flex: 0 0 160px;
					margin: 10px 0;
					background: $ef-bg-dk;
					border-radius: 15px;
					display: flex;
					align-items: center;

					.sc-icon {
						flex: 0 0 30px;
						color: $ef-accent;
						font-size: $ef-icon-18;
						display: flex;
						align-items: center;
						justify-content: center;
					}

					.sc-input {
						flex: 1;
						background: none !important;
						border: none !important;
					}
				}
			}
		}

		.move-op {
			flex: 0 0 50px;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;

			i {
				margin-top: 50px;
				font-size: 24px;
				color: $ef-accent;
				cursor: pointer;
			}

			span {
				font-size: $ef-font-size-sm;
				color: $ef-accent;
				cursor: pointer;
			}

			&.disabled {
				i,
				span {
					color: $ef-text-disabled;
					cursor: not-allowed;
				}
			}
		}

		.selected-users {
			flex: 1;
			box-shadow: 0 0 3px 3px $ef-bg;

			.members-tip {
				flex: 0 0 60px;
				display: flex;
				align-items: center;
				flex-direction: row-reverse;
				height: 40px;
				padding-right: 20px;
				font-size: $ef-font-size-sm;
				font-weight: bold;
			}

			.user-row {
				height: 40px;
				display: flex;
				align-items: center;

				.username {
					flex: 1;
					display: flex;
					align-items: center;
					font-size: $ef-font-size-sm;
					padding-left: 20px;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				.actions {
					flex: 0 0 60px;
					display: flex;
					align-items: center;
					justify-content: center;

					button {
						color: $ef-accent;
					}
				}

				&.odd {
					background: $ef-bg;
				}
			}
		}

		.no-result-tip {
			padding: 20px 0;
			text-align: center;
			opacity: .62;
			font-size: $ef-font-size-sm;
			font-style: italic;
		}
	}

	.me-footer {
		flex: 0 0 40px;
		display: flex;
		flex-direction: row-reverse;
		align-items: center;

		button {
			margin-left: 5px;
		}
	}
}
