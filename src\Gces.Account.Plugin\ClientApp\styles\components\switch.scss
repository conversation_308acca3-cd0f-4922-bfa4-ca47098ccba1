.switch-container {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-right: 5px;

	.switch-button {
		cursor: pointer;
		position: relative;
		display: block;
		width: 36px;
		height: 17px;
		border: 2px solid var(--gces-content-bg-dk-5);
		border-radius: 20px;
		transition: $ef-transition;

		&:hover:not(.disabled),
		&.checked:not(.disabled) {
			border-color: $ef-accent;

			span {
				background: $ef-accent;
			}
		}

		&.checked span {
			left: calc(100% - 11px);
		}

		&.disabled {
			border-color: $ef-bg;

			span {
				background: $ef-bg;
			}
		}

		&:hover.disabled {
			border-color: var(--gces-content-bg-dk-10);

			span {
				background: var(--gces-content-bg-dk-10);
			}
		}

		span {
			position: absolute;
			top: 2px;
			left: 2px;
			display: block;
			width: 9px;
			height: 9px;
			background: var(--gces-content-bg-dk-5);
			transition: $ef-transition;
			border-radius: 13px;
		}
	}

	.switch-info {
		margin-right: 5px;
		font-size: $ef-font-size-sm;
	}
}