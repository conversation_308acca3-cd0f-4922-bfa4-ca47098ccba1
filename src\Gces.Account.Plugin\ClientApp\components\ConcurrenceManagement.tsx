import * as React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';
import { Scrollbars } from 'gces-react-custom-scrollbars';
import * as moment from 'moment';

import CGrid, { CGridProps } from 'gces-react-grid';
import EmptyPage from './EmptyPage';
import Actions from '../actions/actions';
import { Dropdown, DropdownProps, DropdownItemProps, CssClass, AriaIcon } from 'gces-ui';

interface ConcurrenceStatusItem {
	ipAddress: string;
	ipRaw: string;
	loginDate: string;
	modules: string[];
	length: number;
	userAgent: string;
	userId: string;
	userName: string;
}

interface ConnectProps {
	concurrenceStatus: ConcurrenceStatusItem[];
	isBusy: boolean;
	dispatch?: any;
	t?: any;
}

class ConcurrenceManagement extends React.Component<ConnectProps> {
	private refreshingHandle = null;
	constructor(props, context) {
		super(props, context);
	}

	componentDidMount = () => {
		this.getConcurrenceStatus();
		this.refreshingConcurrenceStatus();
	}

	componentWillUnmount = () => {
		const { dispatch } = this.props;

		clearTimeout(this.refreshingHandle);
		dispatch(Actions.SetConcurrenceStatus([]));
	}

	refreshingConcurrenceStatus = () => {
		this.refreshingHandle = setTimeout(() => {
			this.getConcurrenceStatus();
			this.refreshingConcurrenceStatus();
		}, 10000);
	}

	getConcurrenceStatus = () => {
		this.props.dispatch(Actions.GetConcurrenceStatus());
	}

	handlePropertyActions = (userId: string, ipRaw: string) => {
		this.props.dispatch(Actions.BanUserInConcurrence(userId, ipRaw));
	}

	getItemClass = (item: DropdownItemProps) => {
		const itemClass = new CssClass(['btn', 'btn-default', 'btn-dropdown-item']);
		if (item.classNames) itemClass.add(item.classNames);
		if (item.selected) itemClass.add('selected');
		// Mouseenter event not triggered when cursor moves from disabled button: https://github.com/facebook/react/issues/10109
		if (item.disabled) itemClass.add('disabled');
		return itemClass.css();
	}
	renderBanItem = (item: DropdownItemProps) => {
		const { t } = this.props;
		if (!item) {
			return null;
		}
		return (
			<span className='ef-dd-item-text'>{item.text}
				<AriaIcon className={item.icon} style={{ margin: '0 0 0 5px'}} title={t('BanDesc')} />
			</span>
		);
	}

	renderCell = (key: string, row: ConcurrenceStatusItem) => {
		const { t } = this.props;
		if (key === 'actions') {
			const dropdownProps: DropdownProps = {
				items: [
					{ text: t('Ban'), value: 'ban', icon: 'mdi mdi-help-circle-outline', onRenderCustomDropdownItem: this.renderBanItem }
				],
				className: 'concurrence-actions',
				icon: 'mdi mdi-dots-vertical',
				style: 'transparent',
				size: 'small',
				rounded: true,
				inline: true,
				onSelect: () => this.handlePropertyActions(row.userId, row.ipRaw),
				hiddenChevron: true,
				offset: true
			};

			return <div className='grid-actions'>
				<Dropdown {...dropdownProps} />
			</div>;
		}
	}

	render() {
		const { concurrenceStatus, t, isBusy } = this.props;
		if (isBusy) return null;
		const displayStatus = (concurrenceStatus instanceof Array ? concurrenceStatus : []).map(s => {
			const clonedObj: any = { ...s };
			clonedObj.loginDateDisplay = moment(clonedObj.loginDate).format('YYYY-MM-DD HH:mm:ss');
			return clonedObj;
		});

		const emptyPageProps = {
			imageName: 'concurrence-management',
			tip: t('NoLoginUserTip'),
		};
		if (displayStatus && displayStatus.length === 0) return <EmptyPage {...emptyPageProps} />;

		const columns = [
			{ key: 'userName', label: t('Username') },
			{ key: 'ipAddress', label: t('IpAddress') },
			{ key: 'userAgent', label: t('UserAgent') },
			{ key: 'browserCount', label: t('BrowserCount') },
			{ key: 'loginDateDisplay', label: t('loginDate') },
			{ key: 'actions', width: 40 }
		];

		const gridProps: CGridProps = {
			columns,
			rows: displayStatus,
			onRenderCell: this.renderCell,
			hideGridLine: true,
			rowHeight: 40
		};

		const scrollbarProps = {
			style: { width: '100%', height: '100%' },
			renderThumbVertical: props => <div {...props} style={{ width: '4px' }} className='thumb-vertical' />,
		};

		return <Scrollbars {...scrollbarProps} >
			<div className='concurrence-management' style={{ paddingTop: '10px' }}>
				<CGrid {...gridProps} />
			</div>
		</Scrollbars >;
	}
}

const connector = connect(state => {
	const concurrenceManagement = state['account-management'].concurrence;
	return {
		...concurrenceManagement,
		isBusy: state['account-management'].common.isBusy,
	};
});
const translator = translate('account', { withRef: true });

export default translator(connector(ConcurrenceManagement));
