import { put, takeEvery, all, call, select } from 'redux-saga/effects';
import { isAdministrator, getRole, sendRequestV2 } from '../utils';
import { getDefaultSelectedOrgId, getRoleDisplayName, } from '../../../utils';
import { Organization } from './interfaces';
import {
	roleActionCreators,
	SagaActionTypes,
	SelectOrgAction,
	AddRoleAction,
	SelectRoleAction,
	UpdateRolePermissionsAction,
	DeleteRoleAction,
	UpdateRoleUsersAction,
	DeleteRoleUserAction,
	UpdateDocumentColumnsAction,
	UpdateRoleNameAction,
} from './actions';
import { GlobalOrganization, accountRolesV2Url, isGlobalOrg } from '../../../util';
import { safeFetchV2 } from '../../../utils/safeFetchV2';
import { RoleConsts } from '../../../Register/Common';

function* init() {
	yield put(roleActionCreators.setBusy(true));

	const user = (yield select()).navApp.user as User;
	const allUsersUrl = (isAdministrator(user.roles) || !window.AdminPortal.LimitOrganizationMembersVisibility) ?
		'/api/v2/identity/users?Paging=false' :
		`/api/v2/identity/organizations/${user.orgId}/users?includeSubOrganizationUsers=true`;
	const enableStrictPermissionManagementUrl = '/api/v2/identity/organizations/permission-management-mode';
	const { result: { enableStrictPermissionManagement } } = yield sendRequestV2(enableStrictPermissionManagementUrl);
	yield put(roleActionCreators.setEnableStrictPermissionManagement(enableStrictPermissionManagement));
	let allPermissionsUrl: string;
	if (enableStrictPermissionManagement) {
		allPermissionsUrl = `/api/v2/identity/organizations/${user.orgId}/available-permissions`;
	} else {
		allPermissionsUrl = '/api/v2/identity/permissions?enabled=true';
	}
	const [allUsers, allPermissions] = yield all([call(sendRequestV2, allUsersUrl), call(sendRequestV2, allPermissionsUrl)]);
	if (!allUsers.error && allUsers.result) {
		const users = allUsers.result.models || allUsers.result;
		users.forEach(user => {
			user.organizations = user.tenantRoles ? Object.keys(user.tenantRoles) : [];
		});
		yield put(roleActionCreators.setAllUsers(users));
	}
	if (!allPermissions.error && allPermissions.result && Array.isArray(allPermissions.result)) {
		yield put(roleActionCreators.setAvailablePermissions(allPermissions.result));
	}

	const organizations = (yield* getAllOrganizations()) as Organization[];
	yield put(roleActionCreators.setOrganizations(organizations));
	const { tenantId } = (yield select()).navApp.user;
	const defaultId = getDefaultSelectedOrgId(organizations, tenantId);
	if (defaultId) {
		yield* selectOrganizationCore(defaultId, enableStrictPermissionManagement);
	}
	else {
		yield* selectOrganizationCore(GlobalOrganization.Id, enableStrictPermissionManagement);
	}
	yield put(roleActionCreators.setBusy(false));
}

function* getAllOrganizations() {
	const user = (yield select()).navApp.user as User;
	const isSuperAdmin = isAdministrator(user.roles);
	const organizationsUrl = isSuperAdmin ? '/api/v2/identity/organizations' : `/api/v2/identity/organizations/${user.orgId}/sub-organizations`;
	const { result, error } = yield sendRequestV2(organizationsUrl);
	if (!error && result && Array.isArray(result)) {
		if (!isSuperAdmin) {
			const org = yield sendRequestV2(`/api/v2/identity/organizations/${user.orgId}`);
			if (org.result && !org.error) {
				result.push(org.result);
			}
			return result;
		} else {
			const tenants = [...result];
			for (const t of tenants) {
				if (!t.parentTenantId && t.id !== GlobalOrganization.Id) {
					t.parentTenantId = GlobalOrganization.Id;
				}
			}
			return tenants;
		}
	}
	return [];
}

function* selectOrganization(action: SelectOrgAction) {
	yield put(roleActionCreators.setBusy(true));
	yield* selectOrganizationCore(action.payload.selectedOrganizationId, action.payload.enableStrictPermissionManagement);
	yield put(roleActionCreators.setBusy(false));
}

function* selectOrganizationCore(orgId: string, enableStrictPermissionManagement: boolean, nextToSelectedRoleId?: string) {
	yield put(roleActionCreators.setSelectedOrganizationId(orgId));
	const rolesUrl = orgId === GlobalOrganization.Id ? '/api/v2/identity/roles/non-org-roles' : `/api/v2/identity/organizations/${orgId}/roles`;
	const { result, error } = yield sendRequestV2(rolesUrl);
	if (!error && result && Array.isArray(result)) {
		const roles = result as Role[];
		for (const role of roles) {
			role.displayName = getRoleDisplayName(role.name);
		}
		yield put(roleActionCreators.setRoles(result));
		const selectedRoleId = nextToSelectedRoleId || result[0].id;
		if (selectedRoleId) {
			yield* selectRoleCore(orgId, selectedRoleId, enableStrictPermissionManagement);
		}
		return result;
	}
	return [];
}

function* selectRoleCore(orgId: string, roleId: string, enableStrictPermissionManagement: boolean) {
	yield put(roleActionCreators.setSelectedRoleId(roleId));
	const urlPrefix = getUrlPrefix(orgId);
	const usersUrl = `${urlPrefix}/roles/${roleId}/users`;
	const permissionsUrl = `${urlPrefix}/roles/${roleId}/permissions`;
	const [users, permissions] = yield all([call(sendRequestV2, usersUrl), call(sendRequestV2, permissionsUrl)]);
	if (users.result && permissions.result) {
		if (Array.isArray(users.result)) yield put(roleActionCreators.setUsers(users.result));
		if (Array.isArray(permissions.result)) yield put(roleActionCreators.setPermissions(permissions.result));
	}
	if (enableStrictPermissionManagement) {
		const availablePermissions = yield call(sendRequestV2, `/api/v2/identity/organizations/${orgId}/available-permissions`);
		yield put(roleActionCreators.setAvailablePermissions(availablePermissions.result));
	}
	yield getDocumentColumns();
}

function* getDocumentColumns() {
	const props = yield select();
	yield put(roleActionCreators.setBusy(true));
	const { selectedOrganizationId, selectedRoleId, roles } = props.role;
	const role = getRole(selectedOrganizationId, roles, selectedRoleId);
	const { result } = yield safeFetchV2(`${accountRolesV2Url}/${role}/document-columns`, {
		credentials: 'same-origin',
		method: 'GET',
		headers: { 'Content-type': 'application/json', 'Accept': 'application/json' }
	});
	if (result) {
		yield put(roleActionCreators.setDocumentColumns(result.documentColumns));
		yield put(roleActionCreators.setOriginalDocumentColumns(result.documentColumns));
	}
	yield put(roleActionCreators.setBusy(false));
}

function* updateRoleDocumentColumn(action: UpdateDocumentColumnsAction) {
	const { role, documentColumns } = action.payload;
	yield put(roleActionCreators.setBusy(true));
	const { result, error } = yield safeFetchV2(`${accountRolesV2Url}/${role}/document-columns`, {
		credentials: 'same-origin',
		method: 'PUT',
		headers: { 'Content-type': 'application/json', 'Accept': 'application/json' },
		body: JSON.stringify({
			Columns: documentColumns
		})
	});
	if (!error && result && result.success) {
		yield getDocumentColumns();
	} else if (result.success === false) {
		window.AdminPortal.Notifications.Send(0, window.AdminPortal.i18n.t('role:updateRoleDocumentColumnsError'), window.AdminPortal.i18n.t('role:updateRoleDocumentColumnsError'));
	}
	yield put(roleActionCreators.setBusy(false));
}

const updateSystemStatistics = () => window.AdminPortal && window.AdminPortal.UpdateSystemStatistics && window.AdminPortal.UpdateSystemStatistics();

function* addRole(action: AddRoleAction) {
	yield put(roleActionCreators.setBusy(true));
	const { name, orgId, enableStrictPermissionManagement } = action.payload;
	const names = Object.values(RoleConsts).map(c => window.AdminPortal.i18n.t(`portal:roleName_${c.name}`).toLowerCase());
	if (names.includes(name.toLowerCase())) {
		window.AdminPortal.Notifications.Send(0, window.AdminPortal.i18n.t('account:Error'), window.AdminPortal.i18n.t('role:error_V2_007_002_0009', { RoleName: name }));
		yield put(roleActionCreators.setBusy(false));
		return;
	}
	const user = (yield select()).navApp.user as User;
	const urlPrefix = getUrlPrefix(orgId);
	const { result, error } = yield sendRequestV2(`${urlPrefix}/roles`, 'POST', name);
	if (!error && result && (result.id || (result[0] && result[0].id))) {
		yield* selectOrganizationCore(orgId, enableStrictPermissionManagement, result.id || (result[0] && result[0].id));
		yield put(roleActionCreators.setIsAddingRole(false));
		if (isGlobalOrg(user.orgId)) {
			updateSystemStatistics();
		}
	}
	yield put(roleActionCreators.setBusy(false));
}

function* deleteRole(action: DeleteRoleAction) {
	yield put(roleActionCreators.setBusy(true));
	const user = (yield select()).navApp.user as User;
	const { orgId, roleId, enableStrictPermissionManagement } = action.payload;
	const urlPrefix = getUrlPrefix(orgId);

	// delete role in identity server
	yield sendRequestV2(`${urlPrefix}/roles/${roleId}`, 'DELETE');
	// delete role controlled document columns in account plugin
	yield deleteRoleControlColumns(action);

	yield* selectOrganizationCore(orgId, enableStrictPermissionManagement);
	if (isGlobalOrg(user.orgId)) {
		updateSystemStatistics();
	}
	yield put(roleActionCreators.setBusy(false));
}

function* deleteRoleControlColumns(action: DeleteRoleAction) {
	const { result } = yield safeFetchV2(`${accountRolesV2Url}/${action.payload.orgId}$${action.payload.roleName}/document-columns`, {
		credentials: 'same-origin',
		method: 'DELETE',
		headers: { 'Content-type': 'application/json', 'Accept': 'application/json' }
	});
	if (result && result.success) {
		yield put(roleActionCreators.setDocumentColumns([]));
	}
}

function* selectRole(action: SelectRoleAction) {
	yield put(roleActionCreators.setBusy(true));
	yield* selectRoleCore(action.payload.orgId, action.payload.roleId, action.payload.enableStrictPermissionManagement);
	yield put(roleActionCreators.setBusy(false));
}

function* updateRolePermissions(action: UpdateRolePermissionsAction) {
	yield put(roleActionCreators.setBusy(true));
	const { orgId, roleId, permissions } = action.payload;
	const urlPrefix = getUrlPrefix(orgId);
	const method = orgId === GlobalOrganization.Id ? 'POST' : 'PUT';
	const { result, error } = yield sendRequestV2(`${urlPrefix}/roles/${roleId}/permissions`, method, { permissions });
	if (!error && result) {
		const permissionsFetch = yield sendRequestV2(`${urlPrefix}/roles/${roleId}/permissions`);
		if (Array.isArray(permissionsFetch.result)) yield put(roleActionCreators.setPermissions(permissionsFetch.result));
	}
	yield put(roleActionCreators.setBusy(false));
}

function* updateRoleUsers(action: UpdateRoleUsersAction) {
	yield put(roleActionCreators.setBusy(true));
	const { orgId, roleId, userIds, enableStrictPermissionManagement } = action.payload;
	const urlPrefix = getUrlPrefix(orgId);
	const { result, error } = yield sendRequestV2(`${urlPrefix}/roles/${roleId}/users`, 'PUT', { userIds });
	if (!error && result) {
		yield selectRoleCore(orgId, roleId, enableStrictPermissionManagement);
	}
	yield put(roleActionCreators.setBusy(false));
	yield put(roleActionCreators.setIsAddingMembers(false));
}

function* deleteRoleUser(action: DeleteRoleUserAction) {
	yield put(roleActionCreators.setBusy(true));
	const { orgId, roleId, userId, enableStrictPermissionManagement } = action.payload;
	const urlPrefix = getUrlPrefix(orgId);
	const { result, error } = yield sendRequestV2(`${urlPrefix}/roles/${roleId}/users/${userId}`, 'DELETE');
	if (!error && result) {
		yield selectRoleCore(orgId, roleId, enableStrictPermissionManagement);
	}
	yield put(roleActionCreators.setBusy(false));
}

function getUrlPrefix(orgId: string) {
	return orgId === GlobalOrganization.Id ? '/api/v2/identity' : `/api/v2/identity/organizations/${orgId}`;
}

function* updateRoleName(action: UpdateRoleNameAction) {
	yield put(roleActionCreators.setBusy(true));
	const { orgId, roleId, displayName, enableStrictPermissionManagement, callback } = action.payload;
	const names = Object.values(RoleConsts).map(c => window.AdminPortal.i18n.t(`portal:roleName_${c.name}`).toLowerCase());
	if (names.includes(displayName.toLowerCase())) {
		window.AdminPortal.Notifications.Send(0, window.AdminPortal.i18n.t('account:Error'), window.AdminPortal.i18n.t('role:error_V2_007_002_0018', { RoleName: displayName }));
		if (callback) {
			callback(false);
		}
		yield put(roleActionCreators.setBusy(false));
		return;
	}
	const urlPrefix = getUrlPrefix(orgId);
	const { result, error } = yield sendRequestV2(`${urlPrefix}/roles/${roleId}`, 'PUT', { name: displayName });
	if (!error && result) {
		yield selectOrganizationCore(orgId, enableStrictPermissionManagement, roleId);
		if (callback) {
			callback(true);
		}
	} else {
		if (callback) {
			callback(false);
		}
	}
	yield put(roleActionCreators.setBusy(false));
}

export function* watchRole() {
	yield takeEvery(SagaActionTypes.Init, init);
	yield takeEvery(SagaActionTypes.SelectOrg, selectOrganization);
	yield takeEvery(SagaActionTypes.AddRole, addRole);
	yield takeEvery(SagaActionTypes.DeleteRole, deleteRole);
	yield takeEvery(SagaActionTypes.SelectRole, selectRole);
	yield takeEvery(SagaActionTypes.UpdateRolePermissions, updateRolePermissions);
	yield takeEvery(SagaActionTypes.UpdateRoleUsers, updateRoleUsers);
	yield takeEvery(SagaActionTypes.DeleteRoleUser, deleteRoleUser);
	yield takeEvery(SagaActionTypes.GetRoleDocumentColumn, getDocumentColumns);
	yield takeEvery(SagaActionTypes.UpdateRoleDocumentColumn, updateRoleDocumentColumn);
	yield takeEvery(SagaActionTypes.UpdateRoleName, updateRoleName);
}