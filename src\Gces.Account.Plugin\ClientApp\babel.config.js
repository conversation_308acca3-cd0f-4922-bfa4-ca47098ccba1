module.exports = api => {
	api.cache(true);
	return {
		plugins: [
			["@babel/plugin-proposal-decorators", { "legacy": true }],
			["@babel/plugin-proposal-private-methods", { "loose": true }],
			['@babel/plugin-proposal-private-property-in-object', { loose: true }],
			["@babel/plugin-proposal-class-properties", { "loose": true }]
		],
		presets: [
			[
				"@babel/preset-env",
				{
					"modules": false
				}
			]
		]
	}
}