﻿import * as React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';
import ReactDropZone from 'react-dropzone';
import { AriaIcon, Button, Checkbox, Dropdown, DropdownProps, DropdownItemProps, BlockLoader } from 'gces-ui';
import CGrid, { CGridProps, Column, Row } from 'gces-react-grid';
import Actions from '../actions/actions';
import { CustomVisualInfo, CVLicenseInfo, CVLicenseType, CVLicenseViewStatus } from '../interfaces';
import { safeFetchV2 } from '../utils/safeFetchV2';
import * as util from '../util';

interface ConnectProps {
	isBusy: boolean;
	dispatch?: any;
	t?: any;
}
interface LocalState {
	fileName: string;
	viewStatus: CVLicenseViewStatus;
	uploadedLicense: CVLicenseInfo[];
	sessionId: string;
	licenseInfo: CVLicenseInfo[];
}

const requestInit: RequestInit = { credentials: 'same-origin', headers: { 'Content-Type': 'application/json' } };

class CVLicenseManagement extends React.Component<ConnectProps, LocalState> {
	constructor(props, context) {
		super(props, context);
		this.state = {
			fileName: '',
			viewStatus: CVLicenseViewStatus.LicenseList,
			uploadedLicense: [],
			sessionId: '',
			licenseInfo: [],
		};
	}

	componentDidMount = async () => {
		await this.getLicenses();
	};

	componentWillUnmount = () => {
		const { dispatch } = this.props;
		dispatch(Actions.SetCVLicense(null));
	}

	getDisplayLicenseType = (type: CVLicenseType) => {
		return this.props.t(`LicenseType_${type}`);
	}

	getDisplayExpirationDate = (type: CVLicenseType, date: string) => {
		return (new Date(date)).toLocaleDateString();
	}

	getDisplayRegistrationDate = (date: string) => {
		return (new Date(date)).toLocaleDateString();
	}

	getDisplayInfo = (info: CVLicenseInfo) => {
		return `${info.name} (v${info.version})`;
	}

	getDisplayValidity = (info: CVLicenseInfo) => {
		const { t } = this.props;
		switch (info.validity) {
			case 'Valid': return <AriaIcon className='mdi mdi-plus-circle cv-license-valid' title={t('validCVLicense!title')} />;
			case 'Disabled': return <AriaIcon className='mdi mdi-alert-circle cv-license-error' title={t('disabledCVLicense!title')} />;
			case 'CVNotFound': return <AriaIcon className='mdi mdi-alert-circle cv-license-error' title={t('cvNotFoundLicense!title', { name: info.name, version: info.version })} />;
			case 'UnmatchedCVVersion': return <AriaIcon className='mdi mdi-alert-circle cv-license-error' title={t('unmatchedCVVersionLicense!title', { name: info.name })} />;
			case 'Invalid': return <AriaIcon className='mdi mdi-alert-circle cv-license-error' title={t('invalidCVLicense!title')} />;
			case 'Expired': return <AriaIcon className='mdi mdi-alert-circle cv-license-error' title={t('expiredCVLicense!title')} />;
			case 'Existed': return <AriaIcon className='mdi mdi-alert-circle cv-license-warning' title={t('existedCVLicense!title', { licenseType: this.getDisplayLicenseType(info.existedLicense.licenseType), registerDate: this.getDisplayRegistrationDate(info.existedLicense.registrationTime), expirationDate: this.getDisplayExpirationDate(info.existedLicense.licenseType, info.existedLicense.expirationDate), licenseInfo: `${info.existedLicense.name} (${info.existedLicense.version})` })} />;
			default: return null;
		}
	}

	isSameMajorVersion = (version1: string, version2: string) => {
		if (!version1 || !version2) {
			return false;
		}
		return version1?.split('.')[0] === version2?.split('.')[0];
	}

	isDisabledLicense = (license: CVLicenseInfo) => {
		return license.validity === 'CVNotFound' || license.validity === 'Disabled' || license.validity === 'Invalid' || license.validity === 'Expired' || license.validity === 'UnmatchedCVVersion';
	}

	getLicenses = async () => {
		const { t, dispatch } = this.props;
		dispatch(Actions.StartLoading());
		const { result, error } = await safeFetchV2('/api/v2/license/custom-visual', {
			...requestInit,
			method: 'GET',
		});

		if (!error && result) {
			const licInfos = result.licenses ? (result.licenses as CVLicenseInfo[]) : [];
			this.setState({ licenseInfo: licInfos, viewStatus: CVLicenseViewStatus.LicenseList });
		} else {
			util.notificationApiV2Errors(error.errors, t('GetCustomVisualLicenseError'), t, 'account', 'customVisualLicenseErr_');
			this.setState({ licenseInfo: [], fileName: null, viewStatus: CVLicenseViewStatus.LicenseList });
		}
		dispatch(Actions.EndLoading());
	}

	renderInfo() {
		const { t } = this.props;
		if (!this.state.fileName) return null;

		switch (this.state.viewStatus) {
			case CVLicenseViewStatus.Uploading:
				return t('uploadingInfo', { fileName: this.state.fileName });
			case CVLicenseViewStatus.Uploaded:
				return t('uploadedInfo', { fileName: this.state.fileName });
			case CVLicenseViewStatus.Importing:
				return t('importingInfo', { fileName: this.state.fileName });
			default:
				return null;
		}
	}

	onUpload = async (droppedFiles: File[]) => {
		const { t } = this.props;
		this.setState({ fileName: droppedFiles[0].name, viewStatus: CVLicenseViewStatus.Uploading });
		const file = droppedFiles[0];
		const { result, error } = await safeFetchV2('/api/v2/license/custom-visual/upload', {
			...requestInit,
			method: 'POST',
			body: file,
		});

		if (!error && result) {
			const licInfos = result.licenses ? (result.licenses as CVLicenseInfo[]) : [];
			const { result: visualResult, error: visualError } = await safeFetchV2('/api/resources/visuals', {
				...requestInit,
				method: 'GET',
			});
			if (visualError) {
				window.AdminPortal.Notifications.Send(0, this.props.t('Error'), this.props.t('GetCustomVisualListError'));
				return;
			}
			const customVisuals = visualResult as CustomVisualInfo[];
			for (const license of licInfos) {
				license.checked = license.validity === 'Valid';
				const index = customVisuals.findIndex(cv => cv.info.visualId === license.cvId);
				if (index === -1) {
					license.validity = 'CVNotFound';
					license.checked = false;
				} else if (!this.isSameMajorVersion(license.version, customVisuals[index].info.visualVersion)) {
					license.validity = 'UnmatchedCVVersion';
					license.checked = false;
				}
			}
			this.setState({ sessionId: result?.sessionId, uploadedLicense: licInfos, viewStatus: CVLicenseViewStatus.Uploaded });
		} else {
			this.setState({ uploadedLicense: [], fileName: null, viewStatus: CVLicenseViewStatus.Uploaded });
			util.notificationApiV2Errors(error.errors, t('AnalysisCustomVisualLicenseFileError'), t, 'account', 'customVisualLicenseErr_');
		}
	}

	onImport = async () => {
		if (!this.state.fileName
			|| !this.state.uploadedLicense
			|| this.state.uploadedLicense.length <= 0
			|| !this.state.sessionId) {
			return;
		}

		const { t } = this.props;

		const customVisualList = this.state.uploadedLicense.map(m => {
			return {
				id: m.checked && m.cvId,
				version: m.checked && m.version,
			};
		});
		this.setState({ viewStatus: CVLicenseViewStatus.Importing });
		const { result, error } = await safeFetchV2(`/api/v2/license/custom-visual/import/${this.state.sessionId}`, {
			...requestInit,
			method: 'POST',
			body: JSON.stringify({ customVisualList }),
		});
		if (!error && result) {
			this.setState({ viewStatus: CVLicenseViewStatus.LicenseList, fileName: '', uploadedLicense: [], sessionId: '' });
			window.AdminPortal.Notifications.Send(2, this.props.t('Succeed'), this.props.t('ImportCustomVisualLicenseFileSuccess'));
			await this.getLicenses();
		} else {
			this.setState({ viewStatus: CVLicenseViewStatus.Uploaded, fileName: '', uploadedLicense: [], sessionId: '' });
			util.notificationApiV2Errors(error.errors, t('ImportCustomVisualLicenseFileError'), t, 'account', 'customVisualLicenseErr_');
		}
	}
	onCancel = async () => {
		this.setState({ fileName: '', uploadedLicense: [], sessionId: '', viewStatus: CVLicenseViewStatus.LicenseList });
		await this.getLicenses();
	}

	deleteLicense = async (license: CVLicenseInfo) => {
		const { t } = this.props;

		const { result, error } = await safeFetchV2(`/api/v2/license/custom-visual/${license.id}`, {
			...requestInit,
			method: 'DELETE',
		});

		if (!error && result) {
			if (!result.succeed) {
				window.AdminPortal.Notifications.Send(0, this.props.t('Error'), this.props.t('DeleteCustomVisualLicenseError'));
				return;
			}
			await this.getLicenses();
		} else {
			util.notificationApiV2Errors(error.errors, t('DeleteCustomVisualLicenseError'), t, 'account', 'customVisualLicenseErr_');
			this.setState({ licenseInfo: [], fileName: null, viewStatus: CVLicenseViewStatus.LicenseList });
		}
	}

	handleLicenseActions = (index: number, value: string) => {
		const lic = this.state.licenseInfo[index];
		switch (value) {
			case 'deactivate':
				this.deleteLicense(lic);
				break;
			default:
		}
	}

	onUploadedSelectChange = (index: number) => {
		const { uploadedLicense } = this.state;
		uploadedLicense[index].checked = !(uploadedLicense[index].checked);
		this.setState({ uploadedLicense });
	}

	onUploadedCellRender = (key: string, row: any, rowIndex: number) => {
		const { uploadedLicense } = this.state;
		if (key === 'checkbox') {
			return <div className='cv-license-grid-checkbox'>
				<Checkbox inline value={rowIndex} disabled={this.isDisabledLicense(uploadedLicense[rowIndex])} checked={row.checked} onChange={this.onUploadedSelectChange} />
			</div>;
		}
	}

	onUploadedSelectAllChange = (value: boolean) => {
		const { uploadedLicense } = this.state;
		for (const license of uploadedLicense) {
			if (!this.isDisabledLicense(license))
				license.checked = !value;
		}
		this.setState({ uploadedLicense });
	}

	renderUploaded() {
		const { t } = this.props;
		const { uploadedLicense } = this.state;
		const licenseDisplayInfo = uploadedLicense.map(info => {
			const res: Row = {
				licenseType: this.getDisplayLicenseType(info.licenseType),
				registrationTime: this.getDisplayRegistrationDate(info.registrationTime),
				licenseKey: info.licenseKey,
				expireDate: this.getDisplayExpirationDate(info.licenseType, info.expirationDate),
				licenseInfoDisplay: this.getDisplayInfo(info),
				validity: this.getDisplayValidity(info),
				checked: info.checked,
			};
			return res;
		});
		const columns: Column[] = [
			{
				key: 'checkbox',
				width: 60,
				component: <div className='cv-license-grid-checkbox'>
					<Checkbox
						value={uploadedLicense.every(info => info.checked)}
						checked={uploadedLicense.every(info => info.checked)}
						onChange={this.onUploadedSelectAllChange}
						disabled={uploadedLicense.every(info => this.isDisabledLicense(info))}
					/>
				</div>,
			},
			{
				key: 'licenseType',
				label: t('LicenseType'),
			},
			{
				key: 'registrationTime',
				label: t('RegistrationDate'),
			},
			{
				key: 'licenseKey',
				label: t('LicenseKey'),
				minWidth: 260,
			},
			{
				key: 'expireDate',
				label: t('ExpiryDate'),
			},
			{
				key: 'licenseInfoDisplay',
				component: <div className='cv-license-grid-license-info'>
					<span title={t('LicenseInfo')}>{t('LicenseInfo')}</span>
					<AriaIcon className='mdi mdi-help-circle-outline license-info-tip' title={t('license-info!title')} />
				</div>,
			},
			{
				key: 'validity',
				width: 40,
			},
		];

		const gridProps: CGridProps = {
			columns,
			rows: licenseDisplayInfo,
			hideGridLine: true,
			rowHeight: 60,
			onRenderCell: this.onUploadedCellRender,
		};

		const importDisabled = !licenseDisplayInfo || licenseDisplayInfo.length === 0 || licenseDisplayInfo.every(m => !m.checked);
		return <React.Fragment>
			<CGrid {...gridProps} />
			<div className='uploaded-footer'>
				<Button onClick={this.onImport} disabled={importDisabled} style='accent' text={t('Import!title')} title={t('Import!title')} inline />
				<Button onClick={this.onCancel} style='default' text={t('ImportCancel!title')} title={t('ImportCancel!title')} inline />
			</div>
		</React.Fragment>;
	}

	onLicenseListCellRender = (key: string, row: any, rowIndex: number) => {
		const { t } = this.props;
		if (key === 'dropdown') {
			const items: DropdownItemProps[] = [
				{ text: t('Deactivate'), value: 'deactivate' },
			];
			const dropdownProps: DropdownProps = {
				icon: 'mdi mdi-dots-vertical',
				items,
				onSelect: (value) => this.handleLicenseActions(rowIndex, value),
				hiddenChevron: true,
				style: 'transparent',
				title: t('cgridMore'),
				offset: true,
				rounded: true,
				menuClassName: 'ef-inverted',
				inline: true,
				className: 'cv-license-grid-dropdown'
			};
			return <Dropdown {...dropdownProps} />;
		}
	}

	renderLicenseList() {
		const { t } = this.props;
		const licenseDisplayInfo = this.state.licenseInfo.map(info => {
			const res: Row = {
				licenseType: t(`LicenseType_${info.licenseType}`),
				registrationTime: this.getDisplayRegistrationDate(info.registrationTime),
				licenseKey: info.licenseKey,
				expireDate: this.getDisplayExpirationDate(info.licenseType, info.expirationDate),
				licenseInfoDisplay: this.getDisplayInfo(info)
			};
			return res;
		});
		const columns: Column[] = [
			{
				key: 'licenseType',
				label: t('LicenseType')
			},
			{
				key: 'registrationTime',
				label: t('RegistrationDate'),
			},
			{
				key: 'licenseKey',
				label: t('LicenseKey'),
				minWidth: 260,
			},
			{
				key: 'expireDate',
				label: t('ExpiryDate'),
			},
			{
				key: 'licenseInfoDisplay',
				component: <div className='cv-license-grid-license-info'>
					<span title={t('LicenseInfo')}>{t('LicenseInfo')}</span>
					<AriaIcon className='mdi mdi-help-circle-outline license-info-tip' title={t('license-info!title')} />
				</div>,
			},
			{
				key: 'dropdown',
				width: 40,
			}
		];

		const gridProps: CGridProps = {
			columns,
			rows: licenseDisplayInfo,
			hideGridLine: true,
			onRenderCell: this.onLicenseListCellRender,
			rowHeight: 60
		};
		return <React.Fragment>
			<CGrid {...gridProps} />
		</React.Fragment>;
	}

	render() {
		const { isBusy, t } = this.props;
		const disableRegister = this.state.viewStatus !== CVLicenseViewStatus.LicenseList;
		const dropZoneProps = {
			className: 'drop-zone',
			accept: '.cvl',
			activeClassName: 'active',
			acceptClassName: 'accept',
			rejectClassName: 'reject',
			disabled: disableRegister,
			onDrop: this.onUpload,
		};

		return <div className='cv-license-management'>
			<div className='import-toolbar'>
				<div className='cv-license-register'>
					<ReactDropZone {...dropZoneProps}>
						<Button className='cv-license-register-btn' icon='mdi mdi-plus' style='accent' text={t('registerCVLicense')} disabled={disableRegister} title={t('registerCVLicense')} />
					</ReactDropZone>
					<span className='import-info'>{this.renderInfo()}</span>
				</div>
			</div>

			{this.state.viewStatus === CVLicenseViewStatus.LicenseList && this.renderLicenseList()}
			{this.state.viewStatus !== CVLicenseViewStatus.LicenseList && this.renderUploaded()}

			{isBusy && <BlockLoader />}

		</div>;
	}
}

const connector = connect(state => {
	return {
		isBusy: state['account-management'].common.isBusy,
	};
});
const translator = translate('account', { withRef: true });

export default translator(connector(CVLicenseManagement));