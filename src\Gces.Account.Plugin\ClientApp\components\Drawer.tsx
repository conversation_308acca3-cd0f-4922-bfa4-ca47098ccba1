import * as React from 'react';
import { translate } from 'react-i18next';
import { AriaIcon } from 'gces-ui';

interface DrawerProps {
	open: boolean;
	anchor?: 'left' | 'right';
	title: string;
	className?: string;
	onDismiss: () => void;
	headerRender?: () => React.Component;
	bodyRender?: () => React.Component;
	footerRender?: () => React.Component;
	closeButtonTitle?: string;
	noMaskLayer?: boolean;
}

class Drawer extends React.Component<DrawerProps> {
	constructor(props, context) {
		super(props, context);
	}

	render() {
		const { open, anchor, title, className, onDismiss, bodyRender, footerRender, closeButtonTitle, noMaskLayer } = this.props;

		if (!open) return null;
		return (
			<React.Fragment>
				<div className={`gc-drawer ${className ? className : ''} ${anchor ? anchor : 'right'}`}>
					<input className='fake-input' />
					<input className='fake-input' type='password' />
					<div className='gc-drawer-header'>
						<span className='title' title={title}>{title}</span>
						<AriaIcon className='mdi mdi-close close-btn' title={closeButtonTitle ? '' : closeButtonTitle} onClick={onDismiss} />
					</div>
					<div className='gc-drawer-body'>
						{bodyRender && bodyRender()}
					</div>
					<div className='gc-drawer-footer'>
						{footerRender && footerRender()}
					</div>
				</div>
				{!noMaskLayer && <div className='gc-drawer-mask-layer' />}
			</React.Fragment>
		);
	}
}

export default translate('account', { withRef: true })(Drawer);
