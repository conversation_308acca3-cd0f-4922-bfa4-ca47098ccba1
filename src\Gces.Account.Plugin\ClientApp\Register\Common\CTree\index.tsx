import * as React from 'react';
import { Scrollbars } from 'gces-react-custom-scrollbars';

export interface CTreeProps {
	items: (CTreeItemProps & any)[];
	displayProp: string;
	childrenProp: string;
	offset?: number;
	iconWidth?: number;
	itemHeight?: number;
	selectedPath?: string;
	className?: string;
	draggable?: boolean;
	onFormatItem?: (item: any, current: JSX.Element) => any;
	onFormatHoverItem?: (item: any, current: JSX.Element) => any;
	onExpand?: (path: string, item: any) => void;
	onCollapse?: (path: string, item: any) => void;
	onSelectedPathChange?: (path: string, item: any) => void;
	onItemClick?: (e: any, path: string, item: any) => void;
	onItemDblClick?: (e: any, path: string, item: any) => void;
	onDragStart?: (e: any, path: string, item: any) => boolean;
	onDragOver?: (e: any, path: string, item: any, inside: boolean, draggingItemPath: string, draggingItem: any) => boolean;
	onDragEnd?: (e: any, path: string, item: any) => void;
	onEditEnded?: (e: any, path: string, item: any) => void;
	onPositionChange?: (from: any, to: any, inside: boolean, order: number) => void;
}

export interface CTreeItemProps {
	loading?: boolean;
	expand?: boolean;
	draggable?: boolean;
	editing?: boolean;
}

interface CTreeState {
	dragOverPath: string;
	inside: boolean;
	order: number;
	hoverId: string;
}

export class CTree extends React.Component<CTreeProps, CTreeState> {
	state = { dragOverPath: '', inside: false, order: 0, hoverId: '' };

	_offset = 20;
	_iconWidth = 25;
	_itemHeight = 30;
	_expandClass = 'mdi-chevron-down';
	_collapseClass = 'mdi-chevron-right';

	_root: any;
	_hasEditingItem: boolean;
	_draggingItem: any;

	_scrollBar: Scrollbars;

	positionToSelectedPath = () => {
		if (!this._scrollBar) {
			return;
		}
		const { itemHeight = this._itemHeight } = this.props;
		const { selectedPathLeft, selectedPathTop } = this.findSelectedPathPosition();
		const { scrollLeft, scrollTop, clientWidth, clientHeight } = this._scrollBar.getValues();
		if (selectedPathLeft < scrollLeft) {
			this._scrollBar.scrollLeft(selectedPathLeft);
		}
		const selectedItem = this._root.querySelector('.selected');
		let selectedItemWidth = 0;
		if (selectedItem) {
			selectedItemWidth = selectedItem.offsetWidth;
		}
		if ((selectedPathLeft + selectedItemWidth) > (scrollLeft + clientWidth)) {
			this._scrollBar.scrollLeft(selectedPathLeft + selectedItemWidth);
		}
		if (selectedPathTop < scrollTop) {
			this._scrollBar.scrollTop(selectedPathTop);
		}
		if ((selectedPathTop + itemHeight) > (scrollTop + clientHeight)) {
			this._scrollBar.scrollTop(selectedPathTop + itemHeight);
		}
	}

	findSelectedPathPosition = () => {
		const { items, selectedPath, childrenProp = 'children', itemHeight = this._itemHeight, offset = this._offset } = this.props;
		const path = selectedPath.split('-');
		const selectedPathLeft = (path.length - 1) * offset + 5;
		let depth = 0;
		for (let k = 0; k < parseInt(path[0]); k++) {
			depth += this.getActualLevelDepth(items[k], childrenProp) + 1;
		}

		let i = 0,
			is = items[path[i]];
		depth += 1;
		while (++i < path.length) {
			depth += (parseInt(path[i]) + 1);
			for (let j = 0; j < parseInt(path[i]); j++) {
				depth += this.getActualLevelDepth(is[childrenProp][j], childrenProp);
			}
			is = is[childrenProp][path[i]];
		}
		const selectedPathTop = (depth - 1) * itemHeight;
		return { selectedPathLeft, selectedPathTop };
	}

	getActualLevelDepth = (item: CTreeItemProps & any, childrenProp: string) => {
		let depth = 0;
		if (item && item.expand && item[childrenProp]) {
			depth += item[childrenProp].length;
			for (const childrenItem of item[childrenProp]) {
				depth += this.getActualLevelDepth(childrenItem, childrenProp);
			}
		}
		return depth;
	}

	onClick = (e) => {
		if (e.target.classList.contains('ctree-name-input')) return;

		const { onExpand, onCollapse, onSelectedPathChange, onItemClick } = this.props;
		const item = this.getTargetItem(e);
		if (item) {
			if (onSelectedPathChange)
				onSelectedPathChange(item.path, item.ref);

			if (e.target.classList.contains('expand-icon')) {
				if (item.ref.expand) {
					if (onCollapse) onCollapse(item.path, item.ref);

				} else {
					if (onExpand) onExpand(item.path, item.ref);
				}
			}

			if (onItemClick) onItemClick(e, item.path, item.ref);
		}
	}
	onDblClick = (e) => {
		const { onItemDblClick } = this.props;
		const item = this.getTargetItem(e);
		if (item && onItemDblClick) onItemDblClick(e, item.path, item.ref);
	}
	getTargetItem = (e) => {
		const { items } = this.props;
		const domItem = parent(e.target, '.c-tree-item', (item1, item2) => {
			return item1.dataset && item2.dataset && item1.dataset.path === item2.dataset.path;
		});
		if (!domItem) return null;

		const item = getItemFromPath(items, domItem.dataset.path);
		return {
			ref: item,
			path: domItem.dataset.path,
			dom: domItem
		};
	}
	onEditEnded = (e) => {
		const item = this.getTargetItem(e);
		if (item) {
			const { onEditEnded } = this.props;
			if (onEditEnded) onEditEnded(e.target.value, item.path, item.ref);
		}
	}
	onDragStart = (e) => {
		const { onDragStart, draggable, onCollapse } = this.props;
		const item = this.getTargetItem(e);
		if (item) {
			if (onDragStart) {
				const stop = onDragStart(e, item.path, item.ref);
				if (stop) return;
			}

			this._draggingItem = item;

			if (draggable && item.ref.expand && onCollapse) onCollapse(item.path, item.ref);
		}
	}
	onDragOver = (e) => {
		const { onExpand } = this.props;
		const item = this.getTargetItem(e);

		if (item) {
			e.preventDefault();
			e.stopPropagation();
			if (this._draggingItem && onExpand && item.path !== this._draggingItem.path) onExpand(item.path, item.ref);
			this.setState({ dragOverPath: item.path, order: item.ref.order });
		}
	}
	onDragEnd = (e) => {
		const { onDragEnd } = this.props;
		if (onDragEnd) {
			const item = this.getTargetItem(e);
			if (item) onDragEnd(e, item.path, item.ref);
		}

		this.setState({ dragOverPath: '', inside: false, order: 0 });
		this._draggingItem = null;
	}
	onMouseOver = (item) => {
		this.setState({ hoverId: item.id });
	}
	onMouseLeave = (item) => {
		if (item.id === this.state.hoverId) {
			this.setState({ hoverId: '' });
		}
	}
	onTreeDragOver = (e) => {
		this.setState({ dragOverPath: '', inside: false });
	}
	onTreeDrop = (e) => {
		const { onPositionChange, items, draggable } = this.props;
		if (draggable && this._draggingItem && onPositionChange && this.state.dragOverPath !== this._draggingItem.path) {
			onPositionChange(this._draggingItem, { path: this.state.dragOverPath, ref: getItemFromPath(items, this.state.dragOverPath) }, this.state.inside, this.state.order);
		}
	}
	updateElements = () => {
		const input = this._root.querySelector('.tree-name-input');
		if (input) {
			input.addEventListener('change', this.onEditEnded);
			input.focus();
			input.setSelectionRange(0, input.value.length);
		}
	}

	renderItems = (items, parentPath?) => {
		const { childrenProp } = this.props;
		let elems = [];
		this._hasEditingItem = false;

		if (items) {
			if (parentPath === undefined)
				parentPath = [];

			items.forEach((item, index) => {
				elems = elems.concat(this.generateItem(item, index, parentPath));

				if (item.expand && item[childrenProp]) {
					const path = parentPath.concat(index);
					elems = elems.concat(this.renderItems(item[childrenProp], path));
				}
			});
		}

		return elems;
	}
	generateItem = (item, index, parentPath) => {
		const { displayProp, onFormatItem, onFormatHoverItem, selectedPath, itemHeight, offset, iconWidth, draggable } = this.props;

		const marginLeft = parentPath.length * (offset ? offset : this._offset) + 5;
		const path = parentPath.length > 0 ? `${parentPath.join('-')}-${index}` : `${index}`;
		const selected = (path === selectedPath || this.state.dragOverPath === path) ? 'selected' : '';
		const dg = item.id !== 'fakeNewItem' && !item.editing && (item.draggable || draggable);
		const height = `${itemHeight ? itemHeight : this._itemHeight}px`;

		const iconClass = item.loading
			? 'mdi mdi-loading mdi-spin'
			: `expand-icon mdi ${item.expand ? this._expandClass : this._collapseClass}`;

		const generateContent = () => {
			let current = null;
			if (item.editing && !this._hasEditingItem) {
				this._hasEditingItem = true;
				current = (
					<input
						key='content'
						className='ctree-name-input'
						style={{ height, lineHeight: height }}
						value={item[displayProp]}
						onChange={this.onEditEnded}
					/>
				);
			}

			const itemStyle = {
				display: 'inline-block',
				height,
				lineHeight: height
			};
			current = (
				<span
					key='content'
					className='item-label'
					style={itemStyle}
					title={item[displayProp]}
				>
					{item[displayProp]}
				</span>
			);

			if (onFormatHoverItem && selected !== this.state.hoverId && this.state.hoverId === item.id) {
				const custom = onFormatHoverItem(item, current);

				if (custom !== undefined) return custom;
			}

			if (onFormatItem) {
				const custom = onFormatItem(item, current);

				if (custom !== undefined) return custom;
			}

			return current;
		};

		const icon = (
			<span
				className={iconClass}
				style={{ flex: `0 0 ${iconWidth ? iconWidth : this._iconWidth}px` }}
			/>
		);

		const style = {
			display: 'flex',
			padding: '3px 5px',
			marginLeft: `${marginLeft}px`,
			with: `calc(100% - ${marginLeft}px)`
		};

		return (
			<div
				className={`c-tree-item ${selected} ${(this.state.inside && this.state.dragOverPath === path) ? 'inside' : ''} `}
				draggable={dg}
				data-path={path}
				style={style}
				onDragStart={this.onDragStart}
				onDragOver={this.onDragOver}
				onDragEnd={this.onDragEnd}
				onMouseOver={() => this.onMouseOver(item)}
				onMouseLeave={() => this.onMouseLeave(item)}
				key={path}
			>
				{icon}
				{generateContent()}
			</div>
		);
	}

	render() {
		const { items, className } = this.props;

		const scrollbarsProps = {
			style: { width: '100%', height: '100%' },
			renderThumbVertical: props => <div {...props} style={{ width: '4px' }} className='thumb-vertical' />,
			autoHide: true
		};

		return (
			<Scrollbars {...scrollbarsProps} ref={(ref) => this._scrollBar = ref}>
				<div
					style={{ width: '100%', height: '100%' }}
					className={`c-tree ${className ? className : ''}`}
					ref={(elem) => this._root = elem}
					onClick={this.onClick}
					onDoubleClick={this.onDblClick}
					onDrop={this.onTreeDrop}
					onDragOver={this.onTreeDragOver}
				>
					{this.renderItems(items)}
				</div>
			</Scrollbars>
		);
	}
}

const parent = (elm, selector, cmp) => {
	const contains = (arr, elem) => {
		for (const current of arr) {
			if (typeof cmp === 'function') {
				if (cmp(current, elem)) return true;
			} else {
				if (current === elem) return true;
			}
		}
		return false;
	};

	const ancestors = document.querySelectorAll(selector);
	while (elm) {
		if (contains(ancestors, elm)) {
			return elm;
		}

		elm = elm.parentNode;
	}

	return null;
};

export function getItemFromPath(items, path, childrenProp?) {
	if (!childrenProp) childrenProp = 'children';
	path = path.split('-');
	let i = -1,
		item = null,
		is = items;
	while (++i < path.length) {
		item = is[path[i]];
		is = item[childrenProp];
	}
	return item;
}