import * as React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';
import Dropzone from 'react-dropzone';
import { Button, AriaIcon } from 'gces-ui';

import Drawer from '../../../../components/Drawer';
import ConfirmDialog, { ConfirmDialogProps } from '../../../../components/ConfirmDialog';
import { userActionCreators, UserState, ImportResult } from '../../store';

interface ConnectedProps {
    isImportingUser: boolean;
    importUsersResult: ImportResult;
    selectedOrganizationId: string;
    dispatch?: any;
    t?: any;
}

interface ImportUsersOwnState {
    importUsersResult: any;
}

@translate('user', { wait: true })
class ImportUsersInner extends React.Component<ConnectedProps, ImportUsersOwnState>{
    state = {
        importUsersResult: null
    };
    importUsers = (files) => {
        const formData = new FormData();
        formData.append('file', files[0]);
        this.props.dispatch(userActionCreators.importUsers(formData));
    };

    showImportUsersResult = () => {
        const { importUsersResult, t } = this.props;
        if (!importUsersResult) {
            return '';
        }
        let msg = '';
        if (importUsersResult.errorMsg) {
            msg = importUsersResult.errorMsg;
        }
        else {
            if (importUsersResult.importedUserCount > 0) {
                this.props.dispatch(userActionCreators.getAllUsers());
                msg += t('ImportUsersSuccessMsg', { count: importUsersResult.importedUserCount });
                if (importUsersResult.notImportedUserCount > 0) {
                    msg += '<br />';
                }
            }
            if (importUsersResult.notImportedUserCount > 0) {
                msg += t('ImportUsersFailMsg', { count: importUsersResult.notImportedUserCount, url: `api/v2/identity/users/template/${importUsersResult.detailKey}${window.token ? `?${window.grapecity?.wyn?.integrationTokenName || 'token'}=${window.token}` : ''}` });
            }
        }

        const dlgProps: ConfirmDialogProps = {
            isOpen: true,
            customClass: 'import-user-result-dlg',
            parentSelector: () => document.querySelector('#portal-app'),
            headerText: t('ImportUsers'),
            contentText: msg,
            yesText: t('Close'),
            closeText: t('Close'),
            onClose: () => this.hideImportUsersResult(importUsersResult.detailKey)
        };

        return <ConfirmDialog {...dlgProps} />;
    };

    hideImportUsersResult = (key) => {
        this.props.dispatch(userActionCreators.setImportResult(null));
    };

    exportTemplate = (e) => {
        const token = window.token ? `?${window.grapecity?.wyn?.integrationTokenName || 'token'}=${window.token}` : '';
        e.stopPropagation();
        const a = document.createElement('a');
        a.href = 'api/v2/identity/users/template' + token;
        a.click();
    };

    onDisMiss = () => {
        this.props.dispatch(userActionCreators.setIsImportingUser(false));
    }

    render() {
        const { isImportingUser, importUsersResult, t } = this.props;
        const dropzoneProps = {
            className: 'users-upload',
            activeClassName: 'users-upload-active',
            acceptClassName: 'users-upload-accept',
            rejectClassName: 'users-upload-reject',
            disabledClassName: 'users-upload-disabled',
            onDrop: this.importUsers,
            disabled: false
        };

        return (
            <div className='import-user-panel'>
                {isImportingUser && <Drawer
                    className='import-users-drawer'
                    open={true}
                    title={t('ImportUsers')}
                    anchor='left'
                    closeButtonTitle={t('Close')}
                    onDismiss={this.onDisMiss}
                    bodyRender={() => {
                        return <Dropzone {...dropzoneProps}>
                            <div className='main-content'>
                                <AriaIcon className='mdi mdi-upload' />
                                <span className='big-font'>{t('DragDropFile')}</span>
                                <span className='small-font'>{t('Or') + ' '}<b>{t('ClickHere')}</b>{' ' + t('SelectFile')}</span>
                                <Button style='transparent' icon='mdi mdi-download' title={t('ExportTemplate')} text={t('ExportTemplate')} onClick={this.exportTemplate} />
                            </div>
                        </Dropzone>;
                    }}
                />}
                {importUsersResult && this.showImportUsersResult()}
            </div>
        );
    }
}

export const ImportUsers = connect(
    (state: { user: UserState }) => ({
        isImportingUser: state.user.isImportingUser,
        importUsersResult: state.user.importResult,
        selectedOrganizationId: state.user.selectedOrganizationId,
    })
)(ImportUsersInner) as React.ComponentClass<{}>;
