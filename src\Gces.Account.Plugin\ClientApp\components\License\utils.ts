export enum KeyTypeUS {
	Trial = 1,
	Perpetual = 2,
	Annual = 3,
}

export type Sa3FeatureBase = {
	enabled: boolean;
};

export type CommonFeature = {
	clp: boolean;
	concurrence: number;
};

export type RptFeature = Sa3FeatureBase & {
	concurrence: number;
	documentCount: number;
};

export type DbdFeature = Sa3FeatureBase & {
	concurrence: number;
	documentCount: number;
	nlp: boolean;
};

export type WynSheetFeature = Sa3FeatureBase & {
	documentCount: number;
};

export type DataFeature = Sa3FeatureBase & {
	datasourceCount: number;
};

export type MultiServerFeature = Sa3FeatureBase & {
	nodeCount: number;
};

export interface Sa3Feature {
	common: CommonFeature;
	rpt: RptFeature;
	dbd: DbdFeature;
	data: DataFeature;
	dataMonitoring: Sa3FeatureBase;
	multiServers: MultiServerFeature;
	wynSheet: WynSheetFeature;
}

export interface Sa3License {
	displayKey: string;
	basedKey: string;
	credential: string;
	status: Sa3Status;
	isTrial: boolean;
	registeredDate: string;
	expireDate: string;
	version: string;
	content: Sa3Feature;
	needMigration: boolean;
	isPreActivated: boolean;
	isTempActivated: boolean;
	allowDeactivation: boolean;
	activatedByIndicator: boolean;
}

export const enum Sa3Status {
	None = 0,
	Expired = 1,
	VersionNotMatch = 2,
	GracePeriod = 3,
	Licensed = 4,
	MismatchDeployment = 5,
}

export const Sa3StatusStrings = {
	[Sa3Status.None]: 'None',
	[Sa3Status.Expired]: 'Expired',
	[Sa3Status.VersionNotMatch]: 'VersionNotMatch',
	[Sa3Status.GracePeriod]: 'GracePeriod',
	[Sa3Status.Licensed]: 'Licensed',
	[Sa3Status.MismatchDeployment]: 'MismatchDeployment'
};