.provider-list {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;

	.provider-list-header {
		padding: 10px 0;
		display: flex;
		justify-content: flex-end;
	}

	.provider-list-body {
		font-size: 12px;
		width: 100%;

		.provider-row {
			display: flex;
			align-items: center;
			height: 42px;
			margin: 5px 0;
			line-height: 40px;
			cursor: pointer;

			.td-provider-name,
			.td-provider-actions {
				display: inline-block;
				max-width: 100%;

				@include gces-truncate;
			}

			.td-provider-name {
				padding: 0 15px;
				width: 80%;
			}

			.td-provider-actions {
				float: right;
				margin-top: 6px;
				margin-right: 5px;
			}

			&.selected {
				background-color: $ef-accent;
				color: $text-contrast;

				.td-provider-actions button {
					color: white !important;
				}
			}
		}
	}

	button {
		>span {
			display: inline-block;
			max-width: 150px;

			@include gces-truncate;
		}
	}
}