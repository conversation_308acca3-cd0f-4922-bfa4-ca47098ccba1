export const tenantZH: LanguageKeyValueMap = {
	// section
	'account-management-organization!title': '组织管理',
	'account-management-organization!description': '组织管理',

	Yes: '确定',
	Close: '关闭',
	tntTenantSchema: '组织模型',
	tntAddTenant: '添加组织',
	tntEditTenant: '编辑组织',
	tntNoItemsTip: '没有记录',
	tntAddProp: '添加属性',
	tntAddTenantProp: '添加组织属性',
	tntEditTenantProp: '编辑组织属性',
	tntPropName: '属性名称',
	tntPropVlue: '属性值',
	tntPropValueType: '属性值类型',
	tntPropValueType_String: '字符串',
	tntPropValueType_Boolean: '布尔',
	tntPropValueType_Integer: '整型',
	tntPropValueType_Float: '浮点型',
	tntPropValueType_Date: '日期',
	tntPropValueType_DateTime: '日期时间',
	tntRequired: '必填项',
	tntMultivalued: '允许多值',
	tntSensitive: '允许隐藏值',
	tntShowValue: '显示值',
	tntHideValue: '隐藏值',
	tntClose: '关闭',
	tntDelete: '永久删除',
	tntName: '名称',
	tntEdit: '编辑',
	tntFromEmail: '邮件地址',
	tntAdd: '添加',
	tntSave: '保存',
	tntCancel: '取消',
	tntTenantMembers: '组织成员',
	tntSelectMembers: '选择成员',
	tntUsername: '用户名',
	tntEmail: '邮件',
	tntProvider: '提供者',
	tntMembers: '{{count}}个成员',
	tntDeleteTenantProp: '删除组织属性',
	tntDeleteTenantPropConfirmMessage: '您要永久删除组织属性"{{tenantPropName}}"吗?',
	tntDeleteTenant: '删除组织',
	tntDeleteTenantConfirmMessage: '您要永久删除组织"{{tenantName}}"吗?',
	tntNoMemberTip: '当前组织下没有用户，如需添加请点击',
	tntMultiLineTip: '每行一个值',
	tntAddMember: '添加',

	tntTenantBasicInformation: '基本信息',
	tntTenantRoles: '组织角色',
	tntSelectRoles: '选择角色',
	tntTenantMoveUp: '上移',
	tntTenantMoveDown: '下移',
	tntExpandAll: '展开所有',
	tntCollapseAll: '收起所有',
	tntActions: '活动',
	tntGlobal: '全局',
	tntRoleName: '角色名',
	tntUsersNumber: '用户数',
	tntNewOrganization: '新组织',
	tntTenantPermissions: '权限',
	tntTenantPermissionScope: '权限范围',
	tntModifyPermissionScope: '修改权限范围',
	tntExpand: '展开',
	tntCollapse: '折叠',
	tntOrganizationName: '组织名',
	tntModifyPermissionScopeMessage: '是否确认修改"{{tenantName}}"组织的权限范围？',
	tntModifyPermissionScopeMessageWithTenant: '本次修改将会同时删除以下子组织的下列权限范围：',
	tntModifyPermissionScopeMessageWithRole: '本次修改将会同时删除以下角色的下列权限：',
	tntNoRolesTip: '当前组织下没有角色',
	tntNoPermissionsTip: '当前组织下没有权限',
	tntPermissionsName: '权限名',
	tntPermissionsDescription: '描述',
	tntInvisible: '不可见',
	tntDisableSubView: '禁止子组织查看值',
	tntDisableSubEdit: '禁止子组织编辑值',

	tntErrorOrganizationNameNull: '组织名不能为空',
	tntErrorOrganizationNameDuplicated: '组织名不能重复',
	tntErrorInvalidCharInName: '组织名不能包含: < > / \\ $',
	tntErrorFromPropNull: '属性值不能为空',

	tntErrorOrganizationPropNameNull: '组织属性名不能为空',
	tntErrorOrganizationPropNameDuplicated: '组织属性名不能重复',

	error_5001: '组织名字不能为空',
	error_5002: '组织"{{TenantName}}"已经存在',
	error_5003: '组织属性名字不能为空',
	error_5004: '组织名字"{{TenantPropName}}"已经存在',
	error_5005: '组织属性"{{TenantPropName}}"为系统保留属性',
	error_5006: '邮件地址"{{FromEmail}}"已经存在',

	changeOrganization: '组织变更',
	organizationDragAbove: '到当前组织前',
	organizationDragBelow: '到当前组织后',
	organizationDragSub: '到当前组织的子组织',
	moveOrganization: '移动组织 "{{tenantName}}"',
	OK: '确定',
	Cancel: '取消',

	rt_organization: '组织',
	'rt_organization property': '组织属性',

	error_V2_007_005_0005: '检测到无效的组织属性值。',
	error_V2_007_005_0012: '子组织的权限不能大于父组织的权限，因此请在进行操作前修改组织权限范围。',

	strictPermissionOrgTip: '组织权限范围限制了其角色和子组织的最大权限。',
	loosePermissionOrgTip: '组织权限代表了组织中“每个人”角色的权限。',
	globalPermissionTip: '全局组织权限不能修改。',
};