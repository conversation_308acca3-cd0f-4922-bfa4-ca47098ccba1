.tfa-settings {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 100%;

	.setting-items {
		display: flex;
		flex-direction: column;

		.setting-item {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			margin: 5px 0;
			font-size: 12px;

			.setting-label {
				width: 35%;
				line-height: 30px;
			}

			.setting-value {
				width: 65%;
			}

			.test-button {
				width: 50%;
				align-self: end;
			}

			.mdi-help-circle-outline {
				color: $accent1;
				font-size: 14px;
			}
		}
	}

	.settings-footer {
		display: flex;
		flex-direction: row;
		justify-content: end;

		button {
			margin: 5px;
		}
	}

	.appsetting-configuration-tooltip {
		border: 1px solid $ef-bg-dk;
		border-radius: 8px;
		background: $ef-bg;
		margin: 5px 5px 0 5px;
		padding: 12px;
		font-size: $ef-font-size-sm;

		.appsetting-configuration-tooltip-text {
			color: $ef-text;

			i {
				margin-right: 8px;
			}
		}

		.appsetting-configuration-tooltip-button {
			background: none;
			color: $ef-accent;
			line-height: 24px;
			height: 24px;

			span {
				padding: 0;
				text-decoration: underline;
			}

			&:hover {
				color: $ef-accent-lt;
			}
		}
	}
}
