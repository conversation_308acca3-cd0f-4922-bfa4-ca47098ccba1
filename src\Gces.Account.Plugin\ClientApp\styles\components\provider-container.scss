.provider-container {
	min-height: 360px;
	height: 100%;
	display: flex;

	.left-part,
	.right-part {
		height: 100%;
		display: flex;
		padding: 10px;
	}

	.left-part {
		border-right: 1px solid $ef-bg-dk;
		flex: 0 0 300px;
		max-width: 20%;
		min-width: 150px;

		.add-security-provider-view {
			position: relative;
			width: 100%;
			height: 100%;

			.view-content {
				position: absolute;
				right: 10px;
				top: 0;
				bottom: 0;
				width: 0;
				height: 100%;
				transition: all 0.2s ease-in-out;

				&.open {
					width: calc(100% - 20px);

					.add-security-provider-no-tip {
						padding: 20px 0;
						text-align: center;
						opacity: .62;
						font-size: $ef-font-size-sm;
						font-style: italic;
					}

					.security-provider-list {
						display: flex;
						flex-direction: column;

						.security-provider {
							height: 40px;
							display: flex;
							align-items: center;
							padding: 0 15px;
							font-size: $ef-font-size-sm;

							&.odd {
								background-color: $ef-bg;
							}

							.efc-checkbox-wrapper {
								>div {
									margin: 0 !important;
								}
							}

							.provider-name {
								margin-left: 10px;
								flex: 1;

								@include gces-truncate;
							}
						}
					}

					.btn-group {
						position: absolute;
						text-align: right;
						left: 0;
						right: 0;
						bottom: 10px;

						button {
							margin: 0 4px;
						}
					}
				}
			}
		}
	}

	.right-part {
		flex: 1;
		max-width: 80%;

		.tab-container .tab-container-header .ef-tab .tab-item {
			width: 150px;
		}
	}
}