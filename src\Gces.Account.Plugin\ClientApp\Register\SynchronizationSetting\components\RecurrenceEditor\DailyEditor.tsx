import * as update from 'immutability-helper';
import * as React from 'react';
import { translate } from 'react-i18next';

import { Dropdown, DropdownItemProps } from 'gces-ui/lib/components/Dropdown';
import { Label } from 'gces-ui/lib/components/Label';
import { DayPicker } from './DayPicker';
import { RecSettingEditor } from './RecSettingEditor';

import { DailyRepeatDetail, ExecutionTimeRange, ScheduledInfo } from '../../store/interface';
import { executionTimeRangeTimeFormat, generateNumbers, isDailyTaskLessThan24Hours } from '../../utils';
import { AddExecutionTimeRange } from './AddExecutionTimeRange';

interface DailyEditorProps {
	scheduleInfo: ScheduledInfo;
	updateScheduleInfo: (scheduleInfo: ScheduledInfo) => void;
	t?: any;
}

const hours = generateNumbers(1, 23);
const minutes = generateNumbers(5, 55, 5);

@translate('synchronization', { wait: true })
export class DailyEditor extends React.PureComponent<DailyEditorProps> {

	onModeSelect = (value: string) => {
		const { scheduleInfo, scheduleInfo: { detail }, updateScheduleInfo } = this.props;
		const { dailyExecutionTimeRange } = detail as DailyRepeatDetail;
		const newDailyExecutionTimeRange = value === 'Once' ? null : dailyExecutionTimeRange;
		const newSchedule = update(scheduleInfo, { detail: { repeatType: { $set: value }, dailyExecutionTimeRange: { $set: newDailyExecutionTimeRange } } });
		updateScheduleInfo(newSchedule);
	}

	onHourSelect = (hour: number) => {
		const { scheduleInfo, updateScheduleInfo } = this.props;
		const newSchedule = update(scheduleInfo, { detail: { hoursRepeatInterval: { $set: hour } } });
		updateScheduleInfo(newSchedule);
	}

	onMinuteSelect = (minute: number) => {
		const { scheduleInfo, updateScheduleInfo } = this.props;
		const newSchedule = update(scheduleInfo, { detail: { minutesRepeatInterval: { $set: minute } } });
		updateScheduleInfo(newSchedule);
	}

	onSecondSelect = (second: number) => {
		const { scheduleInfo, updateScheduleInfo } = this.props;
		const newSchedule = update(scheduleInfo, { detail: { secondsRepeatInterval: { $set: second } } });
		updateScheduleInfo(newSchedule);
	}
	onChangeExecutionTimeRange = (newDailyExecutionTimeRange: ExecutionTimeRange) => {
		const { scheduleInfo, updateScheduleInfo } = this.props;
		const newSchedule = update(scheduleInfo, { detail: { dailyExecutionTimeRange: { $set: newDailyExecutionTimeRange } } });
		updateScheduleInfo(newSchedule);
	}
	whetherCanSetDailyExecutionTimeRange(): boolean {
		const { scheduleInfo, scheduleInfo: { repeatType, detail } } = this.props;
		if (repeatType !== 'Daily') {
			return false;
		}
		if (isDailyTaskLessThan24Hours(scheduleInfo)) {
			return false;
		}
		const { repeatType: dailyRepeatType } = detail as DailyRepeatDetail;
		return dailyRepeatType === 'Seconds' || dailyRepeatType === 'Hours' || dailyRepeatType === 'Minutes';
	}
	renderAddExecutionTimeRange(dailyExecutionTimeRange: ExecutionTimeRange) {
		const { t } = this.props;
		return (<AddExecutionTimeRange
			checkboxName={t('dailyEditorAddExecutionTimeRange')}
			startTimeEditorName={t('dailyEditorExecutionTimeRangeStartTime')}
			endTimeEditorName={t('dailyEditorExecutionTimeRangeEndTime')}
			dailyExecutionTimeRange={dailyExecutionTimeRange}
			timeFormat={executionTimeRangeTimeFormat()}
			onChangeExecutionTimeRange={this.onChangeExecutionTimeRange}
		/>);
	}
	render() {
		const { scheduleInfo, scheduleInfo: { repeatType, detail, startDate }, updateScheduleInfo, t, } = this.props;

		if (repeatType !== 'Daily') return null;

		const { hoursRepeatInterval, minutesRepeatInterval, repeatType: dailyRepeatType, dailyExecutionTimeRange } = detail as DailyRepeatDetail;
		const hoursRepeatItems: DropdownItemProps[] = hours.map(hour => ({ text: `${hour}`, value: hour, selected: hour === hoursRepeatInterval }));
		const minutesRepeatItems: DropdownItemProps[] = minutes.map(minute => ({ text: `${minute}`, value: minute, selected: minute === minutesRepeatInterval }));
		const canSetDailyExecutionTimeRange = this.whetherCanSetDailyExecutionTimeRange();
		return (
			<div className='sc-rec-daily'>
				<Label inverted={window.inverted} labelOnTop>
					<DayPicker
						inverted={window.inverted}
						scheduleInfo={scheduleInfo}
						updateScheduleInfo={updateScheduleInfo}
					/>
				</Label>

				<RecSettingEditor
					text={t('dailyEditorTextAt', { time: startDate.format('LT') })}
					value='Once'
					checked={dailyRepeatType === 'Once'}
					onChange={this.onModeSelect}
					inverted={window.inverted}
				/>

				<RecSettingEditor
					text={t('dailyEditorTextEvery')}
					value='Hours'
					units={t('dailyEditorHoursTextUnits')}
					checked={dailyRepeatType === 'Hours'}
					onChange={this.onModeSelect}
					inverted={window.inverted}
				>
					<Dropdown
						text={`${hoursRepeatInterval}`}
						items={hoursRepeatItems}
						menuWidth='100%'
						offset
						size='small'
						textAlign='left'
						position='center'
						onSelect={this.onHourSelect}
						disabled={dailyRepeatType !== 'Hours'}
						inverted={window.inverted}
					/>
				</RecSettingEditor>

				<RecSettingEditor
					text={t('dailyEditorTextEvery')}
					value='Minutes'
					units={t('dailyEditorMinutesTextUnits')}
					checked={dailyRepeatType === 'Minutes'}
					onChange={this.onModeSelect}
					inverted={window.inverted}
				>
					<Dropdown
						text={`${minutesRepeatInterval}`}
						items={minutesRepeatItems}
						menuWidth='100%'
						offset
						size='small'
						textAlign='left'
						position='center'
						onSelect={this.onMinuteSelect}
						disabled={dailyRepeatType !== 'Minutes'}
						inverted={window.inverted}
					/>
				</RecSettingEditor>
				{canSetDailyExecutionTimeRange && this.renderAddExecutionTimeRange(dailyExecutionTimeRange)}
			</div>
		);
	}
}