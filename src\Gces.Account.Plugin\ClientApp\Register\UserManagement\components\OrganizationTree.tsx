import * as React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';
import { UserState, Organization, userActionCreators } from '../store';
import { OrganizationTreeCommon, OrganizationTreeProps } from '../../Common/OrganizationTreeCommon';
import { GlobalOrganization } from '../../../util';

interface ConnectedProps {
	user: User;
	organizations: Organization[];
	selectedOrganizationId: string;
	isSelectingMembers: boolean;
	dispatch: any;
	t: any;
}

@translate('role', { wait: true })
class OrganizationTreeInner extends React.PureComponent<ConnectedProps> {
	onSelectedPathChange = (id: string) => {
		this.props.dispatch(userActionCreators.setSelectedOrgId(id || GlobalOrganization.Id));
		if (!id) {
			this.props.dispatch(userActionCreators.setIsSelectingMembers(false));
		}
		this.props.dispatch(userActionCreators.setDetailUserId(''));
	}
	render() {
		const { organizations, selectedOrganizationId, user: { orgId } } = this.props;
		if (organizations.length === 0) return null;

		const treeProps: OrganizationTreeProps = {
			items: organizations,
			userTenantId: orgId,
			selectedId: selectedOrganizationId,
			draggable: false,
			onItemSelected: this.onSelectedPathChange,
		};
		return (<div className='org-tree'>
			<OrganizationTreeCommon {...treeProps} />
		</div>);
	}
}

export const OrganizationTree = connect(
	(state: { user: UserState, navApp: { user: User } }) => ({
		user: state.navApp.user,
		organizations: state.user.organizations,
		selectedOrganizationId: state.user.selectedOrganizationId,
	})
)(OrganizationTreeInner) as React.ComponentClass<{}>;