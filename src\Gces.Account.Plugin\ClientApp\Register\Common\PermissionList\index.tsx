import * as React from 'react';
import { Button, Checkbox } from 'gces-ui';
import { translate } from 'react-i18next';
import * as classnames from 'classnames';
import { Permission, PermissionItem } from '../interfaces';
import { calcChangedPermissions } from '../../../utils';
import { Scrollbars } from 'gces-react-custom-scrollbars';
import { hasAssignManageUserPermission } from '../../UserManagement/utils';
import { GlobalOrganization } from '../../../util';

interface PermissionsListProps {
    disabledCheckBox: boolean;
    permissions: Permission[];
    allPermissions: Permission[];
    curUser: User;
    onChange: (permissionNames: string[]) => void;
    t?: any;
    orgId?: string;
    enableStrictPermissionManagement?: boolean;
}

interface LocalState {
    permissionItems: PermissionItem[];
    permissions: Permission[];
    collapsedCategories: string[];
}

@translate('common', { wait: true })
export class PermissionList extends React.PureComponent<PermissionsListProps, LocalState> {
    state: LocalState = {
        permissionItems: [],
        permissions: [],
        collapsedCategories: [],
    };

    componentWillMount() {
        this.initRoleItems(this.props);
    }

    componentWillReceiveProps(nextProps: PermissionsListProps) {
        if (JSON.stringify(nextProps.allPermissions) !== JSON.stringify(this.props.allPermissions) ||
            JSON.stringify(nextProps.permissions) !== JSON.stringify(this.state.permissions) ||
            nextProps.disabledCheckBox !== this.props.disabledCheckBox ||
            nextProps.orgId !== this.props.orgId) {
            this.initRoleItems(nextProps);
        }
    }

    initRoleItems = (props: PermissionsListProps) => {
        const { curUser, allPermissions, permissions, disabledCheckBox, onChange } = props;
        const noEnoughPermission = !hasAssignManageUserPermission(curUser.roles);
        const permissionItems: PermissionItem[] = allPermissions.filter(s =>
            (window.AdminPortal.EnableWebPageDocument || s.name !== 'create-web-page')
            && (window.AdminPortal.Edition === 'zh' || s.name !== 'create-iot-data'))
            .map(p => {
                let disabled = disabledCheckBox;
                if (p.name === 'assign-manage-user' || p.name === 'manage-user') {
                    disabled = disabled || noEnoughPermission;
                }
                return { name: p.name, category: p.category, checked: permissions.some(x => x.name === p.name), disabled };
            });
        this.setState({ permissionItems, permissions });
        onChange(permissions.map(p => p.name));
    }

    renderPermissionItem = (permissionItem: PermissionItem, index: number) => {
        const { permissionItems: originalPermissionItems } = this.state;
        const disabled = permissionItem.name === 'create-document-binder'
            && !originalPermissionItems.some(p => p.name === 'view-dashboard' && p.checked)
            && !originalPermissionItems.some(p => p.name === 'view-report' && p.checked) || permissionItem.disabled;
        const onChange = (name: string) => {
            const permissionItems = calcChangedPermissions(originalPermissionItems, name);

            this.setState({ permissionItems });
            this.props.onChange(permissionItems.filter(p => p.checked).map(p => p.name));
        };

        const generateTips = (permissionName: string) => {
            const { t, disabledCheckBox } = this.props;
            const cssClassName = classnames('permission-item-tips mdi mdi-information-outline', { 'disable': disabledCheckBox });
            switch (permissionName) {
                case ('view-resource-portal'):
                    return <span className={cssClassName} title={t('ViewResourcePortalTip')} />;
                case ('view-dashboard'):
                    return <span className={cssClassName} title={t('ViewDashBoardTip')} />;
                case ('view-report'):
                    return <span className={cssClassName} title={t('ViewReportTip')} />;
                case ('manage-user'):
                    return <span className={cssClassName} title={t('ManageUserTip')} />;
                case ('assign-manage-user'):
                    return <span className={cssClassName} title={t('AssignManageUserTip')} />;
                case ('view-data-monitoring'):
                    return <span className={cssClassName} title={t('ViewDataMonitoringTip')} />;
                case ('create-document-binder'):
                    return <span className={cssClassName} title={t('CreateDocumentBinderTip')} />;
                default:
                    return null;
            }
        };

        return (
            <div
                key={permissionItem.name}
                className={`permission-item ${(index + 1) % 2 !== 0 ? 'odd' : ''}`}
            >
                <Checkbox
                    className='permission-item-check-box'
                    value={permissionItem.name}
                    onChange={onChange}
                    text={this.props.t(permissionItem.name)}
                    checked={permissionItem.checked}
                    disabled={disabled}
                    title={this.props.t([`${permissionItem.name}-description`, permissionItem.name])}
                    inline
                />
                {generateTips(permissionItem.name)}
            </div>
        );
    }

    renderPermissionCategorySection = (permissions: PermissionItem[], index: number, category: string) => {
        const { t } = this.props;
        const { permissionItems: originalPermissionItems, collapsedCategories } = this.state;
        const sectionChecked = permissions.every(p => p.checked);
        const indeterminate = permissions.some(p => p.checked) && !sectionChecked;
        const collapseIndex = collapsedCategories.findIndex(c => c === category);

        const onCategoryCheckedChange = (category: string) => {
            const permissionItems = originalPermissionItems.map(s => { return { ...s }; });
            const oldCategoryChecked = sectionChecked;
            const changedPermissionNames = permissions.filter(p => !p.disabled).map(p => p.name);
            for (const permisssion of permissionItems) {
                if (changedPermissionNames.findIndex(cp => cp === permisssion.name) !== -1) {
                    permisssion.checked = !oldCategoryChecked;
                }
            }

            if ((category === 'data' && oldCategoryChecked === false)
                || (category === 'portal' && oldCategoryChecked === true && permissionItems.some(p => p.category === 'data' && p.checked))) {
                const viewResourcePortalPermission = permissionItems.find(p => p.name === 'view-resource-portal');
                if (viewResourcePortalPermission) {
                    viewResourcePortalPermission.checked = true;
                }
            }

            if ((category === 'datamonitoring' && oldCategoryChecked === false)
                || (category === 'dashboards' && oldCategoryChecked === true && permissionItems.some(p => p.name === 'create-data-monitoring' && p.checked))) {
                const viewDashboardPermission = permissionItems.find(p => p.name === 'view-dashboard');
                if (viewDashboardPermission) {
                    viewDashboardPermission.checked = true;
                }
            }

            if ((category === 'dashboards' && oldCategoryChecked === true && !permissionItems.some(p => p.name === 'view-report' && p.checked))
                || (category === 'reports' && oldCategoryChecked === true && !permissionItems.some(p => p.name === 'view-dashboard' && p.checked))) {
                const createDocumentBinder = permissionItems.find(p => p.name === 'create-document-binder');
                if (createDocumentBinder) {
                    createDocumentBinder.checked = false;
                }
            }

            this.setState({ permissionItems });
            this.props.onChange(permissionItems.filter(p => p.checked).map(p => p.name));
        };

        const onCategoryClick = () => {
            const newCollapsedCategories = [...collapsedCategories];
            if (collapseIndex !== -1) newCollapsedCategories.splice(collapseIndex, 1);
            else newCollapsedCategories.push(category);
            this.setState({ collapsedCategories: newCollapsedCategories });
        };

        return (
            <div
                key={category + index}
                className='permission-category-section'
            >
                <div className='section-header'>
                    <Button
                        className='section-btn'
                        onClick={onCategoryClick}
                        icon={collapseIndex !== -1 ? 'mdi mdi-chevron-right' : 'mdi mdi-chevron-down'}
                        style='transparent'
                        size='small'
                        rounded
                    />
                    <Checkbox
                        className='section-check-box'
                        value={category}
                        onChange={onCategoryCheckedChange}
                        text={t(`${category}!title`)}
                        checked={sectionChecked}
                        disabled={this.props.disabledCheckBox}
                        indeterminate={indeterminate}
                        inline
                    />
                    <p className='section-divider' />
                </div>
                {collapseIndex === -1 && <div className='section-body'>
                    {permissions.map(this.renderPermissionItem)}
                </div>}
            </div>);
    }

    render() {
        const { permissionItems } = this.state;
        const scrollbarsProps = {
            style: { width: '100%', height: '100%' },
            renderThumbVertical: props => <div {...props} style={{ width: '4px' }} className='thumb-vertical' />,
            autoHide: true
        };

        const permissionCategoryDic = {};
        for (const permissionItem of permissionItems) {
            const category = permissionItem.category;
            if (!permissionCategoryDic[category]) {
                permissionCategoryDic[category] = [permissionItem];
            } else {
                permissionCategoryDic[category].push(permissionItem);
            }
        }

        const orderedCategoryKeys = Object.keys(permissionCategoryDic);
        const otherCategoryIndex = orderedCategoryKeys.findIndex(k => k.toUpperCase() === 'others'.toUpperCase());
        if (otherCategoryIndex > -1) {
            const otherCategoryKey = orderedCategoryKeys.splice(otherCategoryIndex, 1);
            orderedCategoryKeys.push(otherCategoryKey[0]);
        }

        const { orgId, enableStrictPermissionManagement, t } = this.props;
        const scrollbars = <Scrollbars {...scrollbarsProps}>
            {orderedCategoryKeys.map((k, index) =>
                this.renderPermissionCategorySection(permissionCategoryDic[k], index, k)
            )}
        </Scrollbars>;
        if (orgId === GlobalOrganization.Id && !enableStrictPermissionManagement) {
            return <div className='permission-list'>
                <React.Fragment>
                    <div className='note-item'>
                        <i className='note-icon mdi mdi-information-outline' />
                        <span className='note-text' title={t('affectEveryone')}>{t('affectEveryone')}</span>
                    </div>
                    <div className='permission-global'>
                        {scrollbars}
                    </div>
                </React.Fragment>
            </div>;
        } else {
            return <div className='permission-list'>
                {scrollbars}
            </div>;
        }
    }
}