.tenant-list {
	padding: 10px 10px 10px 0;
	flex: 0 0 300px;
	position: relative;
	border-right: 1px solid $ef-bg-dk;
	display: flex;
	flex-direction: column;

	.btn-group {
		flex: 0 0 50px;
		display: flex;
		flex-direction: row-reverse;
		align-items: center;

		&.tenant-schema {
			position: absolute;
			top: 20px;
			z-index: 1;

			.tenant-schema-btn {
				max-width: 155px;
			}
		}

		button {
			max-width: 100px;

			@include gces-truncate;
		}
	}

	.search-box {
		width: calc(100% - 5px);
		margin: 10px 0;
		flex: 0 0 30px;
		background: $ef-bg-dk;
		border-radius: 2px;
		display: flex;
		align-items: center;

		.sc-icon {
			flex: 0 0 30px;
			color: $ef-accent;
			font-size: $ef-icon-18;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.sc-input {
			flex: 1;
			background: none !important;
			border: none !important;
			padding-left: 0;
		}
	}

	.no-search-result-in-organization-tree {
		margin-top: 30px;
		text-align: center;
		font-size: $ef-font-size-sm;
		font-style: italic;
	}

	.tree-item-no-action {
		.c-tree-item {
			&:hover {
				background-color: inherit;
				color: inherit;
				opacity: inherit;
			}

			&.selected {
				background-color: $accent1;
				color: $text-contrast;
			}
		}
	}

	.tree-item-actions {
		display: flex;
		align-items: center;
		flex-direction: row-reverse;

		.ef-btn {
			color: $text-contrast;
		}
	}

	.tenant-items {
		flex: 1;

		.tenant-item {
			display: flex;
			align-items: center;
			height: 40px;
			padding-left: 10px;

			.item-name {
				flex: 1;
				font-size: $ef-font-size-sm;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			button {
				margin-right: 5px;
				color: $ef-accent !important;
			}

			&.odd {
				background: $ef-bg;
			}

			&.selected {
				color: white !important;
				background: $ef-accent;

				button {
					color: white !important;
				}
			}
		}
	}

	.no-result-tip {
		padding: 20px 0;
		text-align: center;
		opacity: 0.62;
		font-size: $ef-font-size-sm;
		font-style: italic;
	}

	@import "./tenant-schema-editor.scss";
	@import "./add-tenant.scss";
}
