import { SynchronizationSettings } from './components';
import { synchronizationLocaleData } from './localization';
import { synchronizationReducer } from './store/reducer';
import { watchSynchronization } from './store/saga';

if (window.AdminPortal.Edition === 'zh') {
	window.AdminPortal.Register.Locale(synchronizationLocaleData);
	window.AdminPortal.Register.StoreExtension('synchronization', synchronizationReducer, watchSynchronization);
	window.AdminPortal.Register.SectionItem(
		'account-synchronization-setting',
		'account-management',
		'synchronization',
		'mdi mdi-account-sync',
		'Synchronization Settings',
		'Synchronization Settings',
		true,
		SynchronizationSettings,
		false,
		undefined,
		undefined,
		6,
	);
}