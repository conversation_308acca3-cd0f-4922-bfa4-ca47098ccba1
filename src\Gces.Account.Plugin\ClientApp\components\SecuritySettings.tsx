import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, InputEditor } from 'gces-ui';
import * as update from 'immutability-helper';
import * as React from 'react';
import { Scrollbars } from 'gces-react-custom-scrollbars';
import { translate } from 'react-i18next';
import { connect } from 'react-redux';
import Actions from '../actions/actions';
import * as util from '../util';

interface SecuritySetting {
	passwordPolicy: 0 | 20;
	lockedTime: number;
	allowUserResetPassword: boolean;
}

interface SecuritySettingsProps {
	securitySettings: SecuritySetting;
	securitySettingsModified: boolean;
	busy: boolean;
}

interface ConnectProps {
	dispatch: any;
	t: any;
}

interface LocalState {
	settings: any;
	dirty: boolean;
	validation: {
		validIntegrationClientSecret: boolean;
	};
}

class SecuritySettings extends React.Component<SecuritySettingsProps & ConnectProps, LocalState> {
	constructor(props, context) {
		super(props, context);
		this.state = {
			settings: null,
			dirty: false,
			validation: {
				validIntegrationClientSecret: true
			}
		};
	}

	componentDidMount() {
		this.props.dispatch(Actions.GetSecuritySettings());
	}
	componentWillReceiveProps(nextProps: SecuritySettingsProps & ConnectProps) {
		if (nextProps.securitySettings) {
			this.setState({ settings: nextProps.securitySettings });
			this.setState({ dirty: false });
		}
	}

	validateClientSecret = (secret: string): boolean => {
		if (!secret) return false;
		const regexp = /^[a-zA-Z0-9_\-\.]{16,64}$/gi;
		return regexp.test(secret);
	}

	saveSettings = () => {
		this.props.dispatch(Actions.SetSecuritySettingsSaga(this.state.settings));
	}
	resetSettings = () => {
		this.setState({ settings: this.props.securitySettings, dirty: false });
	}

	render() {
		const { busy, t } = this.props;
		const scrollbarProps = {
			style: { width: '100%', height: '100%' },
			renderThumbVertical: props => <div {...props} style={{ width: '4px' }} className='thumb-vertical' />,
		};

		return <Scrollbars {...scrollbarProps}>
			<div className='security-settings'>
				<div className='setting-items'>
					<div className='setting-item'>
						<div className='setting-label' title={t('EnableStrongPasswordPolicy')}>{t('EnableStrongPasswordPolicy')}</div>
						<div className='setting-value'>
							<BooleanEditor
								value={(this.state.settings && this.state.settings.passwordPolicy === util.PasswordPolicy.StrongPasswordPolicy) ? 'true' : 'false'}
								trueLabel={t('Yes')}
								falseLabel={t('No')}
								onChange={(value) => {
									const passwordPolicy = value ? util.PasswordPolicy.StrongPasswordPolicy : util.PasswordPolicy.WeakPasswordPolicy;
									const settings = update(this.state.settings, { passwordPolicy: { $set: passwordPolicy } });
									this.setState({ settings, dirty: true });
								}}
							/>
						</div>
					</div>
					<pre className='setting-description' title={t('EnableStrongPasswordPolicyDescription')}>{t('EnableStrongPasswordPolicyDescription')}</pre>
					<div className='setting-item'>
						<div className='setting-label' title={t('UserLockedTime')}>{t('UserLockedTime')}</div>
						<div className='setting-value'>
							<NumberEditor
								minValue={0}
								value={(this.state.settings && this.state.settings.lockedTime) || 0}
								onChange={(value, invalid) => {
									if (value !== this.state.settings.lockedTime) {
										let lockedTime = this.state.settings.lockedTime;
										if (!invalid) {
											lockedTime = isNaN(value) ? 0 : value;
										}
										const settings = update(this.state.settings, { lockedTime: { $set: lockedTime } });
										this.setState({ settings, dirty: true });
									}
								}}
							/>
						</div>
					</div>
					<pre className='setting-description' title={t('UserLockedTimeDescription')}>{t('UserLockedTimeDescription')}</pre>
					<div className='setting-item'>
						<div className='setting-label' title={t('AllowUserResetPassword')}>{t('AllowUserResetPassword')}</div>
						<div className='setting-value'>
							<BooleanEditor
								value={(this.state.settings && this.state.settings.allowUserResetPassword) ? 'true' : 'false'}
								trueLabel={t('Yes')}
								falseLabel={t('No')}
								onChange={(value) => {
									const settings = update(this.state.settings, { allowUserResetPassword: { $set: value } });
									this.setState({ settings, dirty: true });
								}}
							/>
						</div>
					</div>
					<pre className='setting-description' title={t('AllowUserResetPasswordDescription')}>{t('AllowUserResetPasswordDescription')}</pre>
					{window.AdminPortal && window.AdminPortal.Edition === 'zh' && <>
						<div className='setting-item'>
							<div className='setting-label' title={t('ForceChangePasswordOnFirstLogin')}>{t('ForceChangePasswordOnFirstLogin')}</div>
							<div className='setting-value'>
								<BooleanEditor
									value={(this.state.settings && this.state.settings.forceChangePasswordOnFirstLogin) ? 'true' : 'false'}
									trueLabel={t('Yes')}
									falseLabel={t('No')}
									onChange={(value) => {
										const settings = update(this.state.settings, { forceChangePasswordOnFirstLogin: { $set: value } });
										this.setState({ settings, dirty: true });
									}}
								/>
							</div>
						</div>
						<pre className='setting-description' title={t('ForceChangePasswordOnFirstLoginDescription')}>{t('ForceChangePasswordOnFirstLoginDescription')}</pre>
						<div className='setting-item'>
							<div className='setting-label' title={t('ForcePasswordExpiration')}>{t('ForcePasswordExpiration')}</div>
							<div className='setting-value'>
								<BooleanEditor
									value={(this.state.settings && this.state.settings.forcePasswordExpiration) ? 'true' : 'false'}
									trueLabel={t('Yes')}
									falseLabel={t('No')}
									onChange={(value) => {
										const settings = update(this.state.settings, { forcePasswordExpiration: { $set: value } });
										this.setState({ settings, dirty: true });
									}}
								/>
							</div>
						</div>
						{this.state.settings && this.state.settings.forcePasswordExpiration && <div className='setting-item'>
							<div className='setting-label' title={t('PasswordExpirationDays')}>{t('PasswordExpirationDays')}</div>
							<div className='setting-value'>
								<NumberEditor
									minValue={15}
									maxValue={365}
									value={(this.state.settings && this.state.settings.passwordExpirationDays) || 90}
									onChange={(value, invalid) => {
										let expirationDays = 90;
										if (!invalid) {
											expirationDays = isNaN(value) ? expirationDays : Math.min(365, Math.max(value, 15));
										}
										const settings = update(this.state.settings, { passwordExpirationDays: { $set: expirationDays } });
										this.setState({ settings, dirty: true });
									}}
								/>
							</div>
						</div>}
						<pre className='setting-description' title={t('ForcePasswordExpirationDescription')}>{t('ForcePasswordExpirationDescription')}</pre>
					</>}
					<div className='setting-item-multiple'>
						<div className='setting-label' title={t('CookieLifetimeSettings')}>{t('CookieLifetimeSettings')}</div>
						<div className='setting-item'>
							<div className='setting-label' title={t('DefaultCookieLifetimeDesc')}>{t('DefaultCookieLifetime')}</div>
							<div className='setting-value'>
								<NumberEditor
									minValue={0}
									maxValue={30}
									value={(this.state.settings && this.state.settings.defaultCookieLifetime) || 0}
									onChange={(value, invalid) => {
										if (value !== this.state.settings.defaultCookieLifetime) {
											let defaultCookieLifetime = this.state.settings.defaultCookieLifetime;
											if (!invalid) {
												defaultCookieLifetime = isNaN(value) ? 0 : Math.min(30, Math.max(value, 0));
											}
											const settings = update(this.state.settings, { defaultCookieLifetime: { $set: defaultCookieLifetime } });
											this.setState({ settings, dirty: true });
										}
									}}
								/>
							</div>
						</div>
						<div className='setting-item'>
							<div className='setting-label' title={t('CookieLifetimeForRememberLoginDesc')}>{t('CookieLifetimeForRememberLogin')}</div>
							<div className='setting-value'>
								<NumberEditor
									minValue={1}
									maxValue={365}
									value={(this.state.settings && this.state.settings.cookieLifetimeForRememberLogin) || 14}
									onChange={(value, invalid) => {
										if (value !== this.state.settings.cookieLifetimeForRememberLogin) {
											let cookieLifetimeForRememberLogin = this.state.settings.cookieLifetimeForRememberLogin;
											if (!invalid) {
												cookieLifetimeForRememberLogin = isNaN(value) ? 1 : Math.min(365, Math.max(value, 1));
											}
											const settings = update(this.state.settings, { cookieLifetimeForRememberLogin: { $set: cookieLifetimeForRememberLogin } });
											this.setState({ settings, dirty: true });
										}
									}}
								/>
							</div>
						</div>
					</div>
					<pre className='setting-description' title={t('CookieLifetimeDescription')}>{t('CookieLifetimeDescription')}</pre>
					<div className='setting-item'>
						<div className='setting-label' title={t('IntegrationClientSecret')}>{t('IntegrationClientSecret')}</div>
						<div className='setting-value'>
							<InputEditor
								value={(this.state.settings && this.state.settings.integrationClientSecret) || ''}
								type='password'
								visibilityToggle
								invalid={!this.state.validation.validIntegrationClientSecret}
								required
								maxLength={64}
								onEveryChange={(value) => {
									const settings = update(this.state.settings, { integrationClientSecret: { $set: value } });
									this.setState({ settings, dirty: true, validation: { validIntegrationClientSecret: this.validateClientSecret(value) } });
								}}
							/>
						</div>
					</div>
					<pre className='setting-description' title={t('IntegrationClientSecretDescription')}>{t('IntegrationClientSecretDescription')}</pre>
				</div>
				<div className='settings-footer'>
					<Button
						style='accent'
						inline={true}
						text={t('Save')}
						title={t('Save')}
						disabled={!this.state.dirty || !this.state.validation.validIntegrationClientSecret}
						icon='mdi mdi-content-save'
						onClick={this.saveSettings}
					/>
					<Button
						inline={true}
						text={t('Cancel')}
						title={t('Cancel')}
						disabled={!this.state.dirty}
						icon='mdi mdi-cancel'
						onClick={this.resetSettings}
					/>
				</div>
			</div>
			{busy && <BlockLoader />}
		</Scrollbars >;
	}
}

export default translate('account', { withRef: true })(connect(state => state['account-management'].ss)(SecuritySettings));
