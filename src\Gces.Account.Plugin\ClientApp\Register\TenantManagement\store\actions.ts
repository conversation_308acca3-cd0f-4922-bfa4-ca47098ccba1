import { TenantProp, UsersDic, User, RolesDic, OrganizationItem, PermissionsDic } from './interfaces';
import { Permission } from '../../Common/interfaces';

// reducer actions
export interface SetBusyAction { type: 'Portal/Tenant/SetBusy'; payload: { busy: boolean }; }
export interface SetTenantItemsAction { type: 'Portal/Tenant/SetTenantItems'; payload: { items: OrganizationItem[] }; }
export interface SetTenantItemsSelectedAction { type: 'Portal/Tenant/SetTenantItemsSelected'; payload: { selected: string }; }
export interface SetShowSchemaEditorAction { type: 'Portal/Tenant/SetShowSchemaEditor'; payload: { showSchemaEditor: boolean }; }
export interface SetShowTenantEditorAction { type: 'Portal/Tenant/SetShowTenantEditor'; payload: { showTenantEditor: boolean }; }
export interface SetShowTenantPropEditorAction { type: 'Portal/Tenant/SetShowTenantPropEditor'; payload: { showTenantPropEditor: boolean }; }
export interface SetShowMembersEditorAction { type: 'Portal/Tenant/SetShowMembersEditor'; payload: { showMembersEditor: boolean }; }
export interface SetTenantPropsAction { type: 'Portal/Tenant/SetTenantProps'; payload: { tenantProps: TenantProp[] }; }
export interface AddUsersToDicAction { type: 'Portal/Tenant/AddUsersToDic'; payload: { dic: UsersDic }; }
export interface AddRolesToDicAction { type: 'Portal/Tenant/AddRolesToDic'; payload: { dic: RolesDic }; }
export interface AddPermissionsToDicAction { type: 'Portal/Tenant/AddPermissionsToDic'; payload: { dic: PermissionsDic }; }
export interface SetAvailablePermissionsAction { type: 'Portal/Tenant/SetAvailablePermissions'; payload: { permissions: Permission[] }; }
export interface SetNoTenantUsersAction { type: 'Portal/Tenant/SetNoTenantUsers'; payload: { noTenantUsers: User[] }; }
export interface SetIsAddingAction { type: 'Portal/Tenant/SetSetIsAdding'; payload: { isAdding: boolean }; }
export interface SetIsEditingAction { type: 'Portal/Tenant/SetIsEditing'; payload: { isEditing: boolean }; }
export interface SetEnableStrictPermissionManagementAction { type: 'Portal/Tenant/SetEnableStrictPermissionManagement'; payload: { enableStrictPermissionManagement: boolean }; }

const setBusy = (busy: boolean): SetBusyAction => ({ type: 'Portal/Tenant/SetBusy', payload: { busy } });
const setTenantItems = (items: OrganizationItem[]): SetTenantItemsAction => ({ type: 'Portal/Tenant/SetTenantItems', payload: { items } });
const setTenantItemsSelected = (selected: string): SetTenantItemsSelectedAction => ({ type: 'Portal/Tenant/SetTenantItemsSelected', payload: { selected } });
const setShowSchemaEditor = (showSchemaEditor: boolean): SetShowSchemaEditorAction => ({ type: 'Portal/Tenant/SetShowSchemaEditor', payload: { showSchemaEditor } });
const setShowTenantEditor = (showTenantEditor: boolean): SetShowTenantEditorAction => ({ type: 'Portal/Tenant/SetShowTenantEditor', payload: { showTenantEditor } });
const setShowTenantPropEditor = (showTenantPropEditor: boolean): SetShowTenantPropEditorAction => ({ type: 'Portal/Tenant/SetShowTenantPropEditor', payload: { showTenantPropEditor } });
const setShowMembersEditor = (showMembersEditor: boolean): SetShowMembersEditorAction => ({ type: 'Portal/Tenant/SetShowMembersEditor', payload: { showMembersEditor } });
const setTenantProps = (tenantProps: TenantProp[]): SetTenantPropsAction => ({ type: 'Portal/Tenant/SetTenantProps', payload: { tenantProps } });
const addUsersToDic = (dic: UsersDic): AddUsersToDicAction => ({ type: 'Portal/Tenant/AddUsersToDic', payload: { dic } });
const addRolesToDic = (dic: RolesDic): AddRolesToDicAction => ({ type: 'Portal/Tenant/AddRolesToDic', payload: { dic } });
const addPermissionsToDic = (dic: PermissionsDic): AddPermissionsToDicAction => ({ type: 'Portal/Tenant/AddPermissionsToDic', payload: { dic } });
const setAvailablePermissions = (permissions: Permission[]): SetAvailablePermissionsAction => ({ type: 'Portal/Tenant/SetAvailablePermissions', payload: { permissions } });
const setNoTenantUsers = (noTenantUsers: User[]): SetNoTenantUsersAction => ({ type: 'Portal/Tenant/SetNoTenantUsers', payload: { noTenantUsers } });
const setIsAdding = (isAdding: boolean): SetIsAddingAction => ({ type: 'Portal/Tenant/SetSetIsAdding', payload: { isAdding } });
const setIsEditing = (isEditing: boolean): SetIsEditingAction => ({ type: 'Portal/Tenant/SetIsEditing', payload: { isEditing } });
const setEnableStrictPermissionManagement = (enableStrictPermissionManagement: boolean): SetEnableStrictPermissionManagementAction => ({ type: 'Portal/Tenant/SetEnableStrictPermissionManagement', payload: { enableStrictPermissionManagement } });

export type TenantReducerActions = SetBusyAction | SetTenantItemsAction | SetTenantItemsSelectedAction | SetShowMembersEditorAction | SetNoTenantUsersAction
	| SetShowSchemaEditorAction | SetShowTenantEditorAction | SetShowTenantPropEditorAction | SetTenantPropsAction | AddUsersToDicAction | AddRolesToDicAction |
	SetIsAddingAction | SetIsEditingAction | AddPermissionsToDicAction | SetAvailablePermissionsAction | SetEnableStrictPermissionManagementAction;

// saga actions
export interface InitAction { type: 'Portal/Tenant/Init'; }
export interface GetTenantUsersAction { type: 'Portal/Tenant/GetTenantUsers'; payload: { tenantId: string }; }
export interface GetTenantRolesAction { type: 'Portal/Tenant/GetTenantRoles'; payload: { tenantId: string }; }
export interface GetAvailablePermissionAction { type: 'Portal/Tenant/GetAvailablePermissions'; payload: { tenantId: string }; }
export interface GetTenantPermissionsAction { type: 'Portal/Tenant/GetTenantPermissions'; payload: { tenantId: string }; }
export interface AddTenantPropAction { type: 'Portal/Tenant/AddTenantProp'; payload: { name: string, required: boolean, multivalued: boolean, valueType: number, sensitive: boolean }; }
export interface EditTenantPropAction { type: 'Portal/Tenant/EditTenantProp'; payload: { id: string, name: string, required: boolean, multivalued: boolean, valueType: number, sensitive: boolean }; }
export interface DeleteTenantPropAction { type: 'Portal/Tenant/DeleteTenantProp'; payload: { id: string }; }
export interface AddTenantAction { type: 'Portal/Tenant/AddTenant'; payload: { tenant: OrganizationItem }; }
export interface EditTenantAction { type: 'Portal/Tenant/EditTenant'; payload: { tenant: OrganizationItem }; }
export interface DeleteTenantAction { type: 'Portal/Tenant/DeleteTenant'; payload: { tenantId: string }; }
export interface UpdateUsersInTenantAction { type: 'Portal/Tenant/UpdateUsersInTenant'; payload: { tenantId: string, userIds: string[] }; }
export interface UpdateTenantPermissionsAction { type: 'Portal/Tenant/UpdateTenantPermissions'; payload: { tenantId: string, permissions: string[] }; }
export interface MoveAction { type: 'Portal/Tenant/Move'; payload: { tenantId: string, offset: number }; }
export interface MigrateAction { type: 'Portal/Tenant/Migrate'; payload: { id: string, ParentOrgId: string, Order: number }; }

const init = (): InitAction => ({ type: 'Portal/Tenant/Init' });
const getTenantUsers = (tenantId: string): GetTenantUsersAction => ({ type: 'Portal/Tenant/GetTenantUsers', payload: { tenantId } });
const getTenantRoles = (tenantId: string): GetTenantRolesAction => ({ type: 'Portal/Tenant/GetTenantRoles', payload: { tenantId } });
const getAvailablePermissions = (tenantId: string): GetAvailablePermissionAction => ({ type: 'Portal/Tenant/GetAvailablePermissions', payload: { tenantId } });
const getTenantPermissions = (tenantId: string): GetTenantPermissionsAction => ({ type: 'Portal/Tenant/GetTenantPermissions', payload: { tenantId } });
const addTenantProp = (name: string, required: boolean, multivalued: boolean, valueType: number, sensitive: boolean): AddTenantPropAction => ({ type: 'Portal/Tenant/AddTenantProp', payload: { name, required, multivalued, valueType, sensitive } });
const editTenantProp = (id: string, name: string, required: boolean, multivalued: boolean, valueType: number, sensitive: boolean): EditTenantPropAction => ({ type: 'Portal/Tenant/EditTenantProp', payload: { id, name, required, multivalued, valueType, sensitive } });
const deleteTenantProp = (id: string): DeleteTenantPropAction => ({ type: 'Portal/Tenant/DeleteTenantProp', payload: { id } });
const addTenant = (tenant: OrganizationItem): AddTenantAction => ({ type: 'Portal/Tenant/AddTenant', payload: { tenant } });
const editTenant = (tenant: OrganizationItem): EditTenantAction => ({ type: 'Portal/Tenant/EditTenant', payload: { tenant } });
const deleteTenant = (tenantId: string): DeleteTenantAction => ({ type: 'Portal/Tenant/DeleteTenant', payload: { tenantId } });
const updateUsersInTenant = (tenantId: string, userIds: string[]): UpdateUsersInTenantAction => ({ type: 'Portal/Tenant/UpdateUsersInTenant', payload: { tenantId, userIds } });
const updatePermissionsInTenant = (tenantId: string, permissions: string[]): UpdateTenantPermissionsAction => ({ type: 'Portal/Tenant/UpdateTenantPermissions', payload: { tenantId, permissions } });
const move = (tenantId: string, offset: number): MoveAction => ({ type: 'Portal/Tenant/Move', payload: { tenantId, offset } });
const migrate = (id: string, ParentOrgId: string, Order: number): MigrateAction => ({ type: 'Portal/Tenant/Migrate', payload: { id, ParentOrgId, Order } });

export const SagaActionTypes = {
	Init: 'Portal/Tenant/Init',
	GetTenantUsers: 'Portal/Tenant/GetTenantUsers',
	GetTenantRoles: 'Portal/Tenant/GetTenantRoles',
	GetAvailablePermissions: 'Portal/Tenant/GetAvailablePermissions',
	GetTenantPermissions: 'Portal/Tenant/GetTenantPermissions',
	GetNoTenantUsers: 'Portal/Tenant/GetNoTenantUsers',
	UpdateUsersInTenant: 'Portal/Tenant/UpdateUsersInTenant',
	AddTenantProp: 'Portal/Tenant/AddTenantProp',
	EditTenantProp: 'Portal/Tenant/EditTenantProp',
	DeleteTenantProp: 'Portal/Tenant/DeleteTenantProp',
	AddTenant: 'Portal/Tenant/AddTenant',
	EditTenant: 'Portal/Tenant/EditTenant',
	DeleteTenant: 'Portal/Tenant/DeleteTenant',
	UpdateTenantPermissions: 'Portal/Tenant/UpdateTenantPermissions',
	Move: 'Portal/Tenant/Move',
	Migrate: 'Portal/Tenant/Migrate',
};

// actions creators
export const tenantActionCreators = {
	init,
	getTenantUsers,
	getTenantRoles,
	getAvailablePermissions,
	getTenantPermissions,
	updateUsersInTenant,
	addTenantProp,
	editTenantProp,
	deleteTenantProp,
	addTenant,
	editTenant,
	deleteTenant,
	updatePermissionsInTenant,
	move,
	migrate,

	setBusy,
	setTenantItems,
	setTenantItemsSelected,
	setShowSchemaEditor,
	setShowTenantEditor,
	setShowTenantPropEditor,
	setShowMembersEditor,
	setTenantProps,
	addUsersToDic,
	addRolesToDic,
	addPermissionsToDic,
	setNoTenantUsers,
	setIsAdding,
	setIsEditing,
	setAvailablePermissions,
	setEnableStrictPermissionManagement,
};