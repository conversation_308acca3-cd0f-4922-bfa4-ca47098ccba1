import { Management } from './components/Management';
import { roleLocaleData } from './localization';
import { roleReducer, watchRole } from './store';

window.AdminPortal.Register.Locale(roleLocaleData);
window.AdminPortal.Register.StoreExtension('role', roleReducer, watchRole);
window.AdminPortal.Register.SectionItem(
	'account-management-role',
	'account-management',
	'role',
	'mdi mdi-account-group',
	'Role',
	'Role management.',
	true,
	Management,
	true,
	undefined,
	undefined,
	2
);
