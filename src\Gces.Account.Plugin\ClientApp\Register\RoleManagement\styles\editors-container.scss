.editors-container {
	display: flex;
	flex-direction: column;
	padding: 10px;
	position: relative;
	max-width: 60%;

	.ec-header {
		flex: 0 0 40px;
		display: flex;
		align-items: center;
		font-weight: bold;

		.role-name {
			display: inline-block;
			max-width: 50%;

			@include gces-truncate;

			&.support-edit {
				display: flex;
				align-items: center;
				flex: 0 0 30%;
				font-size: $ef-font-size-lg;

				.role-name-editor {
					background: none !important;
					padding: 0 !important;
					font-weight: bold;
					font-size: $ef-font-size-lg !important;

					@include gces-truncate;
				}

				>span {
					max-width: 210px;
					font-weight: bold;

					@include gces-truncate;
				}

				.edit-role-name-btn {
					margin-left: 5px;
					color: $ef-accent;
				}
			}
		}

		.select-members-btn {
			margin-left: 20px;
		}
	}

	.editors-tab-container {
		flex: 0 0 60px;
		position: relative;

		.ef-tab {
			border-bottom: 1px solid $accent1;
			flex-wrap: nowrap;
		}
	}

	.ec-body {
		flex: 1;

		@import '~gces-react-grid/styles/grid/index.scss';

		.no-users-in-role {
			padding-top: 70px;
			text-align: center;
			font-size: 12px;
			font-style: italic;
		}

		@import '../../Common/PermissionList/permission-list.scss';

		.cg-row.odd {
			background-color: transparentize(#000, .95);
		}

		.del-user-btn {
			margin-top: 5px;
			margin-left: auto;
		}

		.role-column-page {
			height: 100%;
			display: flex;
			flex-direction: column;

			.role-column-container {
				height: 100%;
				flex: 1;
			}
		}
	}

	.ec-footer {
		flex: 0 0 40px;
		display: flex;
		align-items: center;

		.role-column-hint {
			flex: 1;
			font-size: 12px;
			margin: 15px;

			.tip-icon {
				color: $ef-accent;
				margin-right: 5px;
			}
		}

		button {
			>span {
				display: inline-block;
				max-width: 100px;

				@include gces-truncate;
			}
		}
	}

	.members-editor {
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
		padding: 20px 10px 10px 10px;
		z-index: 100;

		h3 {
			display: block;

			@include gces-truncate;
		}
	}
}