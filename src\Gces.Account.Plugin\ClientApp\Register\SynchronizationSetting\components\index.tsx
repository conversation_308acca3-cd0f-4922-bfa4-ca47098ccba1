import { Divider } from 'gces-ui';
import * as React from 'react';
import { connect } from 'react-redux';
import { synchronizationSettingsActionCreator } from '../store/action';
import { Settings } from './Settings';
import { View } from './View';
import { heartbeat } from '../utils/heartbeat';

interface SynchronizationSettingsProps {
	dispatch?: any;
}

class SynchronizationSettingsBase extends React.PureComponent<SynchronizationSettingsProps> {
	UNSAFE_componentWillMount(): void {
		const { dispatch } = this.props;
		heartbeat.start();
		dispatch(synchronizationSettingsActionCreator.init());
	}

	componentWillUnmount(): void {
		heartbeat.stop();
	}

	render() {
		return (
			<div className='synchronization-settings'>
				<Settings />
				<Divider className='synchronization-settings-divider' />
				<View />
			</div>
		);
	}
}

export const SynchronizationSettings = connect((state) => ({}))(SynchronizationSettingsBase);