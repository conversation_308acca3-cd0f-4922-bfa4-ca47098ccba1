﻿import * as React from 'react';
import Modal from 'react-modal';
import { Button } from 'gces-ui';

export interface ConfirmDialogProps {
	isOpen: boolean;
	parentSelector: () => HTMLElement | Element;
	headerText: string;
	contentText: any;
	yesText?: string;
	noText?: string;
	cancelText?: string;
	closeText?: string;
	customClass?: string;
	onYes?: () => void;
	onNo?: () => void;
	onCancel?: () => void;
	onClose?: () => void;
}
export default class ConfirmDialog extends React.Component<ConfirmDialogProps> {
	private _dlgClassName = 'gc-confirm-dialog confirm-dialog';
	render() {
		const { isOpen, parentSelector, headerText, contentText, yesText, noText, cancelText, closeText, customClass, onYes, onNo, onCancel, onClose } = this.props;

		const modalProps = {
			isOpen,
			parentSelector,
			className: {
				base: customClass ? this._dlgClassName + ' ' + customClass : this._dlgClassName,
				afterOpen: '',
				beforeClose: ''
			},
			style: {
				overlay: {
					display: 'block'
				}
			},
			ariaHideApp: false
		};

		return <Modal {...modalProps}>
			<div className='gc-confirm-dialog-header dialog-header'>
				<span>{headerText}</span>
				<Button style='transparent' icon='mdi mdi-close dlg-close-btn' size='small' rounded inverted onClick={onClose} title={closeText} />
			</div>
			<div className='gc-confirm-dialog-body dialog-body' dangerouslySetInnerHTML={{ __html: contentText }} />
			<div className='gc-confirm-dialog-footer dialog-footer'>
				{yesText && <Button inline text={yesText} size='small' style='accent' onClick={onYes || onClose} />}
				{noText && <Button inline text={noText} size='small' onClick={onNo || onClose} />}
				{cancelText && <Button inline text={cancelText} size='small' onClick={onCancel || onClose} />}
			</div>
		</Modal>;
	}
}
