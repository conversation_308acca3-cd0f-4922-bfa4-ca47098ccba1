export interface SecurityProvider extends Provider {
	checked?: boolean;
}

export type ProviderType = 'SecurityProvider' | 'ExternalLoginProvider';

export interface ProviderSetting {
	description: string;
	displayName: string;
	name: string;
	restriction: number;
	value: any;
	valueType: number;
}

export interface Provider {
	type: ProviderType;
	description: string;
	providerName: string;
	settings: ProviderSetting[];
	enabled?: boolean;
	ordinal?: number;
}

// Custom visual license info
export type CVLicenseType = 'Trial' | 'Perpetual';
export type CVLicenseValidityType = 'Valid' | 'Invalid' | 'Disabled' | 'Existed' | 'CVNotFound' | 'Expired' | 'UnmatchedCVVersion';
export interface CVLicenseInfo {
	id: string;
	cvId: string;
	name: string;
	version: string;
	licenseKey: string;
	licenseType: CVLicenseType;
	expirationDate: string;
	registrationTime: string;
	createTime: string;  // imported time
	modifyTime: string;
	validity: CVLicenseValidityType;
	existedLicense: CVLicenseInfo;
	checked?: boolean;
}
export enum CVLicenseViewStatus {
	LicenseList,
	Uploading,
	Uploaded,
	Importing
}
export interface CVLicense {
	licenseInfo: CVLicenseInfo[];
}

export interface CustomVisualInfo {
	info: {
		visualId: string;
		visualVersion: string;
	};
}