import * as React from 'react';
import { connect } from 'react-redux';
import { Scrollbars } from 'gces-react-custom-scrollbars';
import { translate } from 'react-i18next';
import { InputEditor, Label, Button, BlockLoader, DateTimeEditor, Switch, DropdownEditor, Toolbar, ToolbarProps, DropdownProps, Dropdown } from 'gces-ui';
import { fetchActions } from '../actions/fetchActions';
import CGrid, { CGridProps, Row } from 'gces-react-grid';
import { TokenManagementState, HistoryToken } from '../interfaces/generate-token-interfaces';
import Actions from './../actions/actions';
import * as moment from 'moment';
import * as util from './../util';
import * as util2 from './../util/generateTokenUtil';
import * as Modal from 'react-modal';
import ConfirmDialog from './ConfirmDialog';
import { formatOrgPath } from '../utils';

interface ConnectedProps {
	tokenManagement: TokenManagementState;
	dispatch: any;
	t: any;
}
interface GenerateTokenState {
	user: string;
	password: string;
	expireMoment: moment.Moment;
	title: string;
	description: string;
	organizationUser?: string;
	organizationPath?: string;
	allOrganizationPaths?: string[];
	hideAvatarMenu: boolean;
	showRevokeConfirm: boolean;
	hideWelcomeScreen: boolean;
	revokeToken: HistoryToken;
	showGenerateUrlDialog: boolean;
	generateUrlToken: HistoryToken;
	searchToken: string;
}

@translate('account', { wait: true })
class GenerateToken extends React.PureComponent<ConnectedProps, GenerateTokenState>{
	static MinExpireValue: number = 2;
	static MaxExpireValue: number = 6307200; // 12 years
	static DefaultExpireValue: number = 5256000; // 10 years
	state: GenerateTokenState = {
		user: '',
		password: '',
		title: '',
		description: '',
		organizationPath: '/',
		hideAvatarMenu: false,
		hideWelcomeScreen: false,
		showRevokeConfirm: false,
		revokeToken: undefined,
		showGenerateUrlDialog: false,
		generateUrlToken: undefined,
		expireMoment: moment().add(10, 'year'),
		searchToken: '',
	};

	componentWillMount() {
		const { dispatch } = this.props;
		dispatch(Actions.GetTokens('all'));
	}

	generateToken = async () => {
		const { dispatch, t } = this.props;
		const { expireMoment } = this.state;
		const expireIn = expireMoment.diff(this.getNow(), 'minutes');
		if (expireIn <= 0) {
			window.AdminPortal.Notifications.Send(0, t('gtExpireTimeInvalid'), t('gtExpireTimeLessThanNow'));
			return;
		}
		dispatch(Actions.SetBusy(true));
		const { result, error } = await fetchActions.generateToken(this.state.user.trim(), this.state.password.trim(), expireMoment.second(0), this.state.organizationPath);
		if (result) {
			window.AdminPortal.Notifications.Send(2, t('gtGenerateTokenSuccess'), t('gtGenerateTokenSuccess'), 5000);
			const { title, description, user, organizationPath } = this.state;
			dispatch(Actions.SaveToken(title, description, user, result.accessToken, result.expiresIn, result.tokenType, organizationPath));
		}
		else {
			const msg = t(error ? `error_${error.code}` : 'error_description_default', error.context);
			window.AdminPortal.Notifications.Send(0, t('Error'), msg);
		}
		dispatch(Actions.SetBusy(false));
	}

	generateUrl = () => {
		const { dispatch } = this.props;
		dispatch(Actions.SetUrls('', '', ''));
		const { generateUrlToken, hideAvatarMenu, hideWelcomeScreen } = this.state;
		dispatch(Actions.GenerateUrl(generateUrlToken.id, window.AdminPortal.Edition, hideAvatarMenu, hideWelcomeScreen));
	}

	updateAvatarMenu = (checked: boolean) => {
		this.setState({ hideAvatarMenu: checked });
	}

	updateWelcomeScreen = (checked: boolean) => {
		this.setState({ hideWelcomeScreen: checked });
	}

	checkExpireTime() {
		const { expireMoment } = this.state;
		if (!expireMoment || !expireMoment.isValid()) {
			return false;
		}
		return true;
	}

	getNow(): moment.Moment {
		const now = moment();
		return now.set({ second: 0, millisecond: 0 });
	}

	validateDate = (currentDate: moment.Moment, selectedDate: moment.Moment) => {
		if (!currentDate || !currentDate.isValid()) {
			return false;
		}
		const now = this.getNow();
		return now.isSameOrBefore(currentDate, 'day') && currentDate.diff(now, 'minutes') < GenerateToken.MaxExpireValue;
	}

	onActionExecute = (idx: number, key: string) => {
		const { tokenManagement: { tokens }, dispatch } = this.props;
		const token = tokens[idx];
		if (!token) {
			return;
		}
		if (key === 'revoke') {
			this.setState({ showRevokeConfirm: true, revokeToken: token });
		} else if (key === 'generateUrl') {
			this.setState({ showGenerateUrlDialog: true, generateUrlToken: token });
		}
		else if (key === 'getToken') {
			dispatch(Actions.GetTokens(token.id));
		}
	}

	onGenerateUrlClose = () => {
		const { dispatch } = this.props;
		dispatch(Actions.SetUrls('', '', ''));
		this.setState({ hideAvatarMenu: false, hideWelcomeScreen: false, showGenerateUrlDialog: false, generateUrlToken: undefined });
	}

	onExpireValueChange = (value: moment.Moment) => {
		if (!moment.isMoment(value)) {
			this.forceUpdate();
			return;
		}
		if (!value.isValid()) {
			this.forceUpdate();
			return;
		}
		const now = this.getNow();
		const diffInMinutes = value.diff(now, 'minutes');
		if (diffInMinutes < GenerateToken.MinExpireValue) {
			value = now.add(GenerateToken.MinExpireValue, 'minutes');
		}
		else if (diffInMinutes > GenerateToken.MaxExpireValue) {
			value = now.add(GenerateToken.MaxExpireValue, 'minutes');
		}
		if (this.state.expireMoment.isSame(value)) {
			this.forceUpdate();
			return;
		}
		this.setState({ expireMoment: value });
	}

	onTitleChange = (value: string) => {
		let title = (value || '').trim();
		if (title.length > 30) {
			title = title.substr(0, 30);
		}
		if (title === this.state.title) {
			this.forceUpdate();
			return;
		}
		this.setState({ title });
	}

	renderRequiredLabel(title: string, content: JSX.Element) {
		return <div className='ef-control'>
			<div className='efc-label' title={title}>
				<span className='required'>{title}</span>
			</div>
			<div className='efc-content'>
				{content}
			</div>
		</div>;
	}

	onOrgSelectorToggle = async (open: boolean, target?: HTMLElement) => {
		const { user, organizationUser } = this.state;
		if (open && user && user !== organizationUser) {
			const result = await fetchActions.getUserOrgPaths(this.state.user.trim(), this.state.password.trim());
			if (result) {
				this.setState({ allOrganizationPaths: result.map(s => s.path), organizationUser: user });
			}
		}
	}

	renderGenerateToken() {
		const { t } = this.props;
		const { allOrganizationPaths, organizationPath } = this.state;
		const dropdownItems = allOrganizationPaths ? allOrganizationPaths.map(s => ({ value: s, text: formatOrgPath(s) })) : [{ value: '/', text: formatOrgPath('/') }];
		return <React.Fragment>
			<input className='fake-input' />
			<input className='fake-input' type='password' />
			<div className='generate-token'>
				{this.renderRequiredLabel(t('gtTitle'), <InputEditor
					value={this.state.title}
					onChange={this.onTitleChange}
				/>)}
				<Label text={t('gtDescription')}>
					<InputEditor
						value={this.state.description}
						onEveryChange={description => this.setState({ description })}
					/>
				</Label>
				{this.renderRequiredLabel(t('gtExpiryTime'),
					<DateTimeEditor
						mode='dateTime'
						placeholder='YYYY-MM-DD HH:mm:ss'
						dateFormat='YYYY-MM-DD'
						timeFormat='HH:mm:ss'
						value={this.state.expireMoment}
						onChange={this.onExpireValueChange}
						validateDate={this.validateDate}
					/>
				)}
				{this.renderRequiredLabel(t('gtUser'), <InputEditor
					value={this.state.user}
					onEveryChange={user => this.setState({ user, allOrganizationPaths: null, organizationUser: null, organizationPath: '/' })}
				/>)}
				{this.renderRequiredLabel(t('gtPassword'), <InputEditor
					type='password'
					value={this.state.password}
					onEveryChange={password => this.setState({ password })}
				/>)}
				<Label text={t('gtOrgPath')}>
					<DropdownEditor
						items={dropdownItems}
						text={formatOrgPath(organizationPath)}
						disabled={!this.state.user || !this.state.password}
						onChange={organizationPath => this.setState({ organizationPath })}
						onToggle={(e, v) => { this.onOrgSelectorToggle(e, v); }}
					/>
				</Label>
				<Label text=''>
					<Button
						disabled={!this.state.user || !this.state.password || !this.state.title || !this.checkExpireTime()}
						style='accent'
						size='small'
						icon='mdi mdi-shield-lock'
						text={t('gtGenerateToken')}
						title={t('gtGenerateToken')}
						onClick={this.generateToken}
						maxWidth='160px'
					/>
				</Label>
			</div>
		</React.Fragment >;
	}

	renderToolbar() {
		const { t } = this.props;
		const { searchToken } = this.state;
		const toolbarProps: ToolbarProps = {
			className: 'search-token-input',
			items: [
				{
					kind: 'input',
					title: t('SearchText'),
					type: 'text',
					icon: 'mdi mdi-magnify',
					value: searchToken,
					onChange: (e) => this.setState({ searchToken: e.target.value }),
				}
			],
			style: 'transparent',
			size: 'small',
			rounded: true,
			align: 'right',
			ariaLabel: 'Search Token'
		};
		return <Toolbar {...toolbarProps} />;
	}

	renderCell = (key: string, row: Row) => {
		const { t } = this.props;
		if (key === 'actions') {
			const dropdownProps: DropdownProps = {
				items: [
					{
						icon: 'mdi mdi-delete-forever',
						value: 'revoke',
						text: t('gtRevoke')
					},
					{
						icon: 'mdi mdi-web',
						value: 'generateUrl',
						text: t('gtGenerateUrl')
					},
					{
						icon: 'mdi mdi-shield-lock',
						value: 'getToken',
						text: t('gtGetToken')
					}
				],
				className: 'token-actions',
				icon: 'mdi mdi-dots-vertical',
				style: 'transparent',
				size: 'small',
				rounded: true,
				inline: true,
				onSelect: (value) => this.onActionExecute(row.idx, value),
				hiddenChevron: true,
				offset: true
			};

			return <div className='grid-actions'>
				<Dropdown {...dropdownProps} />
			</div>;
		}
	}

	renderTokenList() {
		const { tokenManagement: { tokens }, t } = this.props;
		const { searchToken } = this.state;
		const now = this.getNow();
		let rows = (tokens || []).map((r, idx) => ({
			id: r.id,
			user: r.user,
			title: r.title,
			description: r.description ?? '',
			state: moment(r.expiredTime).millisecond(0).isAfter(now) ? t('gtValid') : t('gtExpired'),
			createdTime: moment(r.createdTime).format('YYYY-MM-DD HH:mm:ss'),
			expiredTime: moment(r.expiredTime).format('YYYY-MM-DD HH:mm'),
			organizationPath: formatOrgPath(r.organizationPath),
			idx
		}));
		if (searchToken) {
			rows = rows.filter(r => r.user.toLowerCase().indexOf(searchToken.toLowerCase()) > -1);
		}
		const gridProps: CGridProps = {
			columns: [
				{ key: 'title', label: t('gtTitle') },
				{ key: 'description', label: t('gtDescription') },
				{ key: 'user', label: t('gtUser') },
				{ key: 'state', label: t('gtState') },
				{ key: 'createdTime', label: t('gtCreatedTime') },
				{ key: 'expiredTime', label: t('gtExpiryTime') },
				{ key: 'organizationPath', label: t('gtOrgPath') },
				{ key: 'actions', width: 40 }
			],
			rows,
			onRenderCell: this.renderCell,
			hideGridLine: true,
			rowHeight: 40,
		};

		return <div className='token-list'>
			<div className='toolbar-container'>
				<div className='title'>
					<span title={t('gtTokenList')}>{t('gtTokenList')}</span>
				</div>
				{this.renderToolbar()}
			</div>
			<div className='token-list-container'>
				<div className='token-list-content'>
					<CGrid {...gridProps} />
				</div>
			</div>
		</div>;
	}
	renderRevokeConfirm() {
		const { t, dispatch } = this.props;
		const { revokeToken } = this.state;
		const dlgProps = {
			isOpen: true,
			parentSelector: () => document.querySelector(util.portalAppId),
			headerText: t('gtRevokeToken'),
			contentText: t('gtRevokeTokenConfirmMessage', { title: revokeToken.title }),
			yesText: t('gtRevoke!btn'),
			closeText: t('Close'),
			cancelText: t('Cancel'),
			onYes: () => {
				this.setState({ showRevokeConfirm: false, revokeToken: undefined });
				dispatch(Actions.RevokeToken(revokeToken.id));
			},
			onCancel: () => { this.setState({ showRevokeConfirm: false, revokeToken: undefined }); },
			onClose: () => { this.setState({ showRevokeConfirm: false, revokeToken: undefined }); },
		};
		return <ConfirmDialog {...dlgProps} />;
	}

	renderGenerateUrl() {
		const { tokenManagement: { portalUrl, resourcePortalUrl, adminPortalUrl }, t } = this.props;
		const modalProps = {
			isOpen: true,
			parentSelector: () => document.querySelector(util.portalAppId),
			className: {
				base: 'gt-generateUrl-dialog',
				afterOpen: '',
				beforeClose: ''
			},
			style: {
				overlay: {
					display: 'block'
				}
			},
			ariaHideApp: false
		};
		return <Modal {...modalProps}>
			<div className='gt-generateUrl-dialog-header'>
				<span title={t('gtGenerateUrlTitle')}>{t('gtGenerateUrlTitle') + ': ' + this.state.generateUrlToken.title}</span>
				<Button style='transparent' icon='mdi mdi-close dlg-close-btn' size='small' rounded inverted onClick={this.onGenerateUrlClose} title={t('Close')} />
			</div>
			<div className='gt-generateUrl-dialog-body'>

				<Label text={t('gtAvatarMenu')} controlClass='hide-avatar-control'>
					<Switch trueString={t('Yes')} falseString={t('No')} value={this.state.hideAvatarMenu} onChange={this.updateAvatarMenu} />
				</Label>
				{
					window.AdminPortal?.Edition === 'en' && <Label text={t('gtWelcomeScreen')} controlClass='hide-welcome-screen-control'>
						<Switch trueString={t('Yes')} falseString={t('No')} value={this.state.hideWelcomeScreen} onChange={this.updateWelcomeScreen} />
					</Label>
				}
				<div className='generate-url-button'>
					<Button
						style='accent'
						size='small'
						icon='mdi mdi-web'
						text={t('gtGenerateUrlTitle')}
						title={t('gtGenerateUrlTitle')}
						onClick={this.generateUrl}
					/>
				</div>
				{portalUrl &&
					<Label text={t('gtIntegratePortalUrl')}>
						<div className='item'>
							<InputEditor
								disabled
								value={portalUrl}
							/>
							<Button
								disabled={!portalUrl}
								style='accent'
								size='small'
								icon='mdi mdi-content-copy'
								title={t('gtCopyUrl')}
								onClick={() => { util2.copyTextToClipboard(portalUrl); }}
							/>
						</div>
					</Label>
				}
				{resourcePortalUrl && window.AdminPortal.Edition === 'en' &&
					<Label text={t('gtIntegrateResourcePortalUrl')}>
						<div className='item'>
							<InputEditor
								disabled
								value={resourcePortalUrl}
							/>
							<Button
								disabled={!resourcePortalUrl}
								style='accent'
								size='small'
								icon='mdi mdi-content-copy'
								title={t('gtCopyUrl')}
								onClick={() => { util2.copyTextToClipboard(resourcePortalUrl); }}
							/>
						</div>
					</Label>
				}
				{adminPortalUrl &&
					<Label text={t('gtIntegrateAdminPortalUrl')}>
						<div className='item'>
							<InputEditor
								disabled
								value={adminPortalUrl}
							/>
							<Button
								disabled={!adminPortalUrl}
								style='accent'
								size='small'
								icon='mdi mdi-content-copy'
								title={t('gtCopyUrl')}
								onClick={() => { util2.copyTextToClipboard(adminPortalUrl); }}
							/>
						</div>
					</Label>
				}
			</div>
			<div className='gt-generateUrl-dialog-footer'>
				{<Button inline text={t('Close')} title={t('Close')} size='small' onClick={this.onGenerateUrlClose} />}
			</div>
		</Modal>;
	}
	render() {
		const scrollbarProps = {
			style: { width: '100%', height: '100%' },
			renderThumbVertical: props => <div {...props} style={{ width: '4px' }} className='thumb-vertical' />,
		};
		const containerScrollbarsProps = {
			style: { width: '100%', height: '100%' },
			renderThumbHorizontal: props => <div {...props} style={{ height: '4px' }} className='thumb-horizontal' />,
			autoHide: true
		};
		return (
			<Scrollbars {...containerScrollbarsProps}>
				<div className='generate-token-container'>
					<div className='left-panel'>
						<Scrollbars {...scrollbarProps}>
							{this.renderGenerateToken()}
						</Scrollbars>
					</div>
					<div className='right-panel'>
						{this.renderTokenList()}
					</div>
					{this.props.tokenManagement.busy && <BlockLoader />}
					{this.state.showRevokeConfirm && this.renderRevokeConfirm()}
					{this.state.showGenerateUrlDialog && this.renderGenerateUrl()}
				</div>
			</Scrollbars>
		);
	}
}

export default connect((state: any) => ({
	tokenManagement: state['account-management'].token
}))(GenerateToken as any) as React.ComponentClass<{}>;