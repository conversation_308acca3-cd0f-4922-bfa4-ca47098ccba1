import * as moment from 'moment';
import * as util from '../../../util';
import { DailyRepeatDetail, ExecutionTimeRange, IntervalWeeklyRepeatDetail, MonthlyRepeatDetail, ScheduledInfo, WeeklyRepeatDetail } from '../store/interface';
import { EntityType, SyncEntity } from '../store/viewModel';

export async function sendRequestV2(url: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET', data: any = null) {
	return util.sendRequestV2(url, method, data, 'synchronization');
}

export const generateCronExpression = (info: ScheduledInfo): string => {
	if (!info) return '';
	const currentYear = moment().year();
	let year = '*';
	if (info.endDate) {
		const endDateYear = info.endDate.year();
		year = endDateYear > currentYear ? `${currentYear}-${endDateYear}` : `${currentYear}`;
	}

	if (info.repeatType === 'Daily') {
		const dailyRepeatDetail = info.detail as DailyRepeatDetail;
		const dayOfWeek = [...dailyRepeatDetail.days].sort().join(',');
		let seconds = '0', minutes = `${info.startDate.minute()}`, hours = `${info.startDate.hour()}`;

		if (dailyRepeatDetail.repeatType === 'Hours') {
			hours = `*/${dailyRepeatDetail.hoursRepeatInterval}`;
		}
		else if (dailyRepeatDetail.repeatType === 'Minutes') {
			minutes = `*/${dailyRepeatDetail.minutesRepeatInterval}`;
			hours = '*';
		} else if (dailyRepeatDetail.repeatType === 'Seconds') {
			seconds = `*/${dailyRepeatDetail.secondsRepeatInterval}`;
			minutes = '*';
			hours = '*';
		}
		const dailyExecutionTimeRange = dailyRepeatDetail.dailyExecutionTimeRange;
		if (dailyExecutionTimeRange && dailyExecutionTimeRange.startTime) {
			const dailyExecutionTimeRangeInCron = generateDailyExecutionTimeRangeInCron(dailyExecutionTimeRange);
			hours = `${hours}${dailyExecutionTimeRangeInCron}`;
		}
		return combineExpression(seconds, minutes, hours, '?', '*', dayOfWeek, year);
	}
	else if (info.repeatType === 'Weekly') {
		const weeklyRepeatDetail = info.detail as WeeklyRepeatDetail;
		const dayOfMonth = `*/${7 * weeklyRepeatDetail.repeatInterval}`;

		return combineExpression(0, info.startDate.minute(), info.startDate.hour(), dayOfMonth, '*', '?', year);
	}
	else if (info.repeatType === 'Monthly') {
		const monthlyRepeatDetail = info.detail as MonthlyRepeatDetail;
		const month = `*/${monthlyRepeatDetail.repeatInterval}`;

		if (monthlyRepeatDetail.type === 'OnDay') {
			return combineExpression(0, info.startDate.minute(), info.startDate.hour(), monthlyRepeatDetail.onDay, month, '?', year);
		}
		else if (monthlyRepeatDetail.type === 'OnTheLast') {
			let dayOfMonth = '?', dayOfWeek = '?';
			if (monthlyRepeatDetail.onTheLast.startsWith('L')) dayOfMonth = monthlyRepeatDetail.onTheLast;
			else dayOfWeek = `${monthlyRepeatDetail.onTheLast}L`;

			return combineExpression(0, info.startDate.minute(), info.startDate.hour(), dayOfMonth, month, dayOfWeek, year);
		}
		else if (monthlyRepeatDetail.type === 'OnTheFirst') {
			const dayOfWeek = `${monthlyRepeatDetail.onTheFirst}#1`;
			const dayOfMonth = '?';
			return combineExpression(0, info.startDate.minute(), info.startDate.hour(), dayOfMonth, month, dayOfWeek, year);
		}
	}
	else if (info.repeatType === 'IntervalWeekly') {
		const intervalWeeklyRepeatDetail = info.detail as IntervalWeeklyRepeatDetail;
		const weeksRepeatInterval = intervalWeeklyRepeatDetail.weeksRepeatInterval;
		const dayOfWeek = [...intervalWeeklyRepeatDetail.days].sort().join(',');
		const seconds = '0', minutes = `${info.startDate.minute()}`, hours = `${info.startDate.hour()}`;
		return combineExpression(seconds, minutes, hours, '?', '1-12', `${dayOfWeek}/${weeksRepeatInterval}`, year);
	}

	return '';
};

export const generateDailyExecutionTimeRangeInCron = (dailyExecutionTimeRange: ExecutionTimeRange): string => {
	const { valid, value } = getValidDailyExecutionTimeRange(dailyExecutionTimeRange);
	if (valid) {
		if (value.startTime && moment.isMoment(value.startTime) && moment.isMoment(value.endTime)) {
			return `in${value.startTime.hour()}:${value.startTime.minute()}-${value.endTime.hour()}:${value.endTime.minute()}`;
		}
	}
	return '';
};

const combineExpression = (
	seconds: string | number,
	minutes: string | number,
	hours: string | number,
	dayOfMonth: string | number,
	month: string | number,
	dayOfWeek: string | number,
	year: string | number
): string => {
	return `${seconds} ${minutes} ${hours} ${dayOfMonth} ${month} ${dayOfWeek} ${year}`;
};

export const getValidDailyExecutionTimeRange = (dailyExecutionTimeRange: ExecutionTimeRange): { valid: boolean, value: ExecutionTimeRange } => {
	if (isNull(dailyExecutionTimeRange)) {
		return { valid: true, value: null };
	}
	const { startTimeValid, startTimeValue, endTimeValid, endTimeValue } = getValidStartTimeAndEndTime(dailyExecutionTimeRange.startTime, dailyExecutionTimeRange.endTime);
	return { valid: startTimeValid && endTimeValid, value: { startTime: startTimeValue, endTime: endTimeValue } };
};

export const getValidStartTimeAndEndTime = (startTime: moment.Moment | string, endTime: moment.Moment | string): { startTimeValid: boolean, startTimeValue: moment.Moment | string, endTimeValid: boolean, endTimeValue: moment.Moment | string } => {
	const startValidTimeMoment = getValidTimeMoment(startTime, false);
	const endValidTimeMoment = getValidTimeMoment(endTime, false);

	if (startValidTimeMoment.valid && endValidTimeMoment.valid && isNotNull(endValidTimeMoment.value)) {
		const valid = isStartTimeBeforeEndTime(startValidTimeMoment.value as moment.Moment, endValidTimeMoment.value as moment.Moment);
		return {
			startTimeValid: valid,
			startTimeValue: startValidTimeMoment.value,
			endTimeValid: valid,
			endTimeValue: endValidTimeMoment.value
		};
	}
	return {
		startTimeValid: startValidTimeMoment.valid,
		startTimeValue: startValidTimeMoment.value,
		endTimeValid: endValidTimeMoment.valid,
		endTimeValue: endValidTimeMoment.value,
	};
};

export const isNotNull = (obj: any): boolean => {
	return !isNull(obj);
};

export const isNull = (obj: any): boolean => {
	return obj === null || obj === undefined;
};

const isStartTimeBeforeEndTime = (startTime: moment.Moment, endTime: moment.Moment): boolean => {
	const tempStartTime = moment();
	tempStartTime.hour(startTime.hour());
	tempStartTime.minute(startTime.minute());

	const tempEndTime = moment();
	tempEndTime.hour(endTime.hour());
	tempEndTime.minute(endTime.minute());
	return tempStartTime.isBefore(tempEndTime);
};

const getValidTimeMoment = (time: moment.Moment | string, canBeNull: boolean): { valid: boolean, value: moment.Moment | string } => {
	if (isNull(time) || time === '') {
		if (canBeNull) {
			return { valid: true, value: null };
		} else {
			return { valid: false, value: null };
		}
	}
	if (moment.isMoment(time)) {
		return { valid: time.isValid(), value: moment(time, executionTimeRangeTimeFormat()) };
	}
	return { valid: false, value: time };
};

export const executionTimeRangeTimeFormat = (): string => {
	return window.AdminPortal.i18n.language && (window.AdminPortal.i18n.language as string).startsWith('en') ? 'hh:mm A' : 'HH:mm';
};

export const getCronstrueLanguage = (): string => {
	const i18nLng = window.AdminPortal?.i18n.language;
	if (!i18nLng) return window.AdminPortal?.Edition === 'en' ? 'en' : 'zh';
	if (i18nLng === 'zh' || i18nLng === 'zh-CN' || i18nLng === 'zh-TW') {
		return i18nLng;
	}
	return i18nLng.split('-')[0];
};

export const parseCronAndHoursCronAndDailyExecutionTimeRange = (cronExpression: string): { realCronExpression: string, realHoursCron: string, dailyExecutionTimeRange: ExecutionTimeRange } => {
	const parts = cronExpression.trim().split(' ');
	if (parts.length < 3) {
		return { realCronExpression: cronExpression, realHoursCron: null, dailyExecutionTimeRange: null };
	}
	const hoursCron = parts[2];
	const { realHoursCron, dailyExecutionTimeRange } = parseHoursCronAndDailyExecutionTimeRange(hoursCron);
	const realCronExpression = cronExpression.replace(hoursCron, realHoursCron);
	return { realCronExpression, realHoursCron, dailyExecutionTimeRange };
};

export const parseHoursCronAndDailyExecutionTimeRange = (hoursCronExpression: string): { realHoursCron: string, dailyExecutionTimeRange: ExecutionTimeRange } => {
	if (!hoursCronExpression.includes('in')) {
		return { realHoursCron: hoursCronExpression, dailyExecutionTimeRange: null };
	}
	const pattern: RegExp = /(?<realHourPartStr>\S+)in(?<startTimeStr>\d{1,2}:\d{1,2})-(?<endTimeStr>\d{1,2}:\d{1,2})/;
	const hoursMatch: RegExpMatchArray | null = hoursCronExpression.match(pattern);
	if (hoursMatch) {
		const { realHourPartStr, startTimeStr, endTimeStr } = hoursMatch.groups;
		const realHoursCron = realHourPartStr;
		const startTime = getExecutionTimeFromStr(startTimeStr);
		const endTime = getExecutionTimeFromStr(endTimeStr);
		const dailyExecutionTimeRange = { startTime, endTime };
		return { realHoursCron, dailyExecutionTimeRange };
	}
	const hoursArray = hoursCronExpression.split('in');
	return { realHoursCron: hoursArray[0], dailyExecutionTimeRange: null };
};

export const getExecutionTimeFromStr = (timeStr: string): moment.Moment => {
	const timeArray = timeStr.split(':');
	const hour = parseInt(timeArray[0]);
	const minute = parseInt(timeArray[1]);
	const executionTime = moment();
	executionTime.hour(hour);
	executionTime.minute(minute);
	return executionTime;
};

export function convertToMomentValue(val: any): moment.Moment {
	if (moment.isMoment(val)) {
		return val;
	}
	return moment(val);
}

// daysOfWeek: 1~7 ,moment.Moment.day(): 0~6
export const findIntervalWeeklyStartTime = (daysOfWeek: number[]): moment.Moment => {
	const currentDateMoment: moment.Moment = moment();
	const currentDateMomentDay = currentDateMoment.day();
	daysOfWeek.sort();
	for (const day of daysOfWeek) {
		if ((day - 1) >= currentDateMomentDay) {
			return moment().day(day - 1);
		}
	}

	// Next week, first day of daysOfWeek: moment().day(daysOfWeek[0] - 1 + 7)
	return moment().day(daysOfWeek[0] + 6);
};

export const startTimeMatchDaysOfWeek = (moment: moment.Moment, schedule: ScheduledInfo): boolean => {
	if ('IntervalWeekly' === schedule.repeatType) {
		const detail = schedule.detail as IntervalWeeklyRepeatDetail;
		const daysOfWeek: number[] = detail.days;
		const startDayOfWeek = moment.day() + 1;
		return daysOfWeek.findIndex(day => day === startDayOfWeek) > -1;
	}
	return true;
};

export const isNeedToRemoveExecutionTimeRange = (schedule: ScheduledInfo): boolean => {

	if (schedule.repeatType !== 'Daily') {
		return false;
	}
	const daily = schedule.detail as DailyRepeatDetail;
	if (!daily.dailyExecutionTimeRange) {
		return false;
	}
	if (isDailyTaskLessThan24Hours(schedule)) {
		return true;
	}
	return false;
};

export const isDailyTaskLessThan24Hours = (scheduledInfo: ScheduledInfo): boolean => {
	if (moment.isMoment(scheduledInfo.startDate) && moment.isMoment(scheduledInfo.endDate)) {
		const minEndDate = scheduledInfo.startDate.clone().add(1, 'day');
		if (scheduledInfo.endDate.isBefore(minEndDate)) {
			return true;
		}
	}
	return false;
};

export const generateNumbers = (start: number, end: number, step?: number): number[] => {
	const result: number[] = [];
	let currentValue = start;
	while (currentValue <= end) {
		result.push(currentValue);
		currentValue += step || 1;
	}
	return result;
};

export const generateScheduleInfo = (cronExpression: string, startDate: Date, endDate: Date, timeZoneId: string): ScheduledInfo => {
	if (!cronExpression) return null;

	const parts = cronExpression.trim().split(' ');
	const seconds = parts[0], minutes = parts[1], dayOfMonth = parts[3], month = parts[4], dayOfWeek = parts[5];
	let hours = parts[2];
	const isDailyRepeat = dayOfMonth === '?' && month === '*',
		isWeeklyRepeat = dayOfMonth.indexOf('/') !== -1,
		isIntervalWeeklyRepeat = dayOfWeek.indexOf('/') !== -1;
	const info: ScheduledInfo = {
		startDate: startDate ? moment(startDate) : null,
		endDate: endDate ? moment(endDate) : null,
		timeZoneId,
		repeatType: null,
		detail: null,
	};

	if (isDailyRepeat) {
		let dailyRepeatType: 'Once' | 'Seconds' | 'Hours' | 'Minutes' = 'Once', hoursRepeatInterval = 1, minutesRepeatInterval = 5, secondsRepeatInterval = 5;
		const { realHoursCron, dailyExecutionTimeRange } = parseHoursCronAndDailyExecutionTimeRange(hours);
		hours = realHoursCron;
		if (hours.indexOf('*/') !== -1) {
			dailyRepeatType = 'Hours';
			hoursRepeatInterval = parseInt(hours.replace('*/', ''));
		}
		else if (minutes.indexOf('*/') !== -1) {
			dailyRepeatType = 'Minutes';
			minutesRepeatInterval = parseInt(minutes.replace('*/', ''));
		}
		else if (seconds.indexOf('*/') !== -1) {
			dailyRepeatType = 'Seconds';
			secondsRepeatInterval = parseInt(seconds.replace('*/', ''));
		}

		const dailyRepeatDetail: DailyRepeatDetail = {
			days: dayOfWeek.split(',').map(d => parseInt(d)),
			repeatType: dailyRepeatType,
			hoursRepeatInterval,
			minutesRepeatInterval,
			secondsRepeatInterval,
			dailyExecutionTimeRange,
		};

		info.repeatType = 'Daily';
		info.detail = dailyRepeatDetail;
	}
	else if (isWeeklyRepeat) {
		const weeklyRepeatDetail: WeeklyRepeatDetail = {
			repeatInterval: parseInt(dayOfMonth.replace('*/', '')) / 7
		};

		info.repeatType = 'Weekly';
		info.detail = weeklyRepeatDetail;
	}
	else if (isIntervalWeeklyRepeat) {
		const weekPartArray = dayOfWeek.trim().split('/');
		const intervalWeeklyRepeatDetail: IntervalWeeklyRepeatDetail = {
			weeksRepeatInterval: parseInt(weekPartArray[1]),
			days: weekPartArray[0].split(',').map(d => parseInt(d)),
		};

		info.repeatType = 'IntervalWeekly';
		info.detail = intervalWeeklyRepeatDetail;
	}
	else {
		const dayNumber = parseInt(dayOfMonth);
		const isRunOnDay = !isNaN(dayNumber) && dayNumber >= 1 && dayNumber <= 31;
		const isRunFirstWeekday = dayOfWeek.endsWith('#1');
		const monthlyRepeatDetail: MonthlyRepeatDetail = {
			type: isRunOnDay ? 'OnDay' : isRunFirstWeekday ? 'OnTheFirst' : 'OnTheLast',
			repeatInterval: parseInt(month.replace('*/', '')),
			onDay: isRunOnDay ? parseInt(dayOfMonth) : 1,
			onTheLast: isRunOnDay || isRunFirstWeekday ? 'L' : (dayOfMonth.startsWith('L') ? dayOfMonth : dayOfWeek.replace('L', '')),
			onTheFirst: !isRunFirstWeekday ? '1' : dayOfWeek.endsWith('#1') ? dayOfWeek.replace('#1', '') : dayOfWeek,
		};

		info.repeatType = 'Monthly';
		info.detail = monthlyRepeatDetail;
	}

	return info;
};

export const convertEntityTypeToDatasetType = (entityType: EntityType) => {
	switch (entityType) {
		case EntityType.User:
			return 'user';
		case EntityType.Organization:
			return 'organization';
		case EntityType.Role:
			return 'role';
		case EntityType.UserRole:
			return 'userRoleRelation';
		case EntityType.UserOrg:
			return 'userOrgRelation';
	}
};

export const findEntityName = (errorEntity: SyncEntity) => {
	const { type, id, name, relations } = errorEntity;
	if (type === EntityType.User || type === EntityType.Organization || type === EntityType.Role) {
		return name || id;
	} else if (type === EntityType.UserRole || type === EntityType.UserOrg) {
		return `${relations[0]}-${relations[1]}`;
	}
};

export const defaultPageSize = 20;

export const beautifyMinutes = (minutes: number): number => Math.ceil(minutes / 5.0) * 5;
