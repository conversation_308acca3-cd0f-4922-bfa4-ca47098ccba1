import { Reducer } from 'redux';
import * as update from 'immutability-helper';
import { OrganizationState } from './interfaces';
import { TenantReducerActions } from './actions';
import { nameSorter } from '../../../utils';

const defaultState: OrganizationState = {
	busy: false,
	tenantProps: [],
	items: [],
	selected: null,
	showSchemaEditor: false,
	showTenantEditor: false,
	showTenantPropEditor: false,
	showMembersEditor: false,
	usersDic: {},
	rolesDic: {},
	permissionsDic: {},
	noTenantUsers: [],
	availablePermissions: [],
	enableStrictPermissionManagement: false,
};

export const tenantReducer: Reducer<OrganizationState> = (state: OrganizationState = defaultState, action: TenantReducerActions) => {
	switch (action.type) {
		case 'Portal/Tenant/SetBusy':
			return update(state, { busy: { $set: action.payload.busy } });
		case 'Portal/Tenant/SetTenantItems':
			const sortedItems = [...action.payload.items];
			sortedItems.sort(nameSorter);
			return update(state, { items: { $set: sortedItems } });
		case 'Portal/Tenant/SetTenantItemsSelected':
			return update(state, { selected: { $set: action.payload.selected } });
		case 'Portal/Tenant/SetShowSchemaEditor':
			return update(state, { showSchemaEditor: { $set: action.payload.showSchemaEditor } });
		case 'Portal/Tenant/SetShowTenantEditor':
			return update(state, { showTenantEditor: { $set: action.payload.showTenantEditor } });
		case 'Portal/Tenant/SetShowTenantPropEditor':
			return update(state, { showTenantPropEditor: { $set: action.payload.showTenantPropEditor } });
		case 'Portal/Tenant/SetShowMembersEditor':
			return update(state, { showMembersEditor: { $set: action.payload.showMembersEditor } });
		case 'Portal/Tenant/SetTenantProps':
			const sortedTenantProps = [...action.payload.tenantProps];
			sortedTenantProps.sort(nameSorter);
			return update(state, { tenantProps: { $set: sortedTenantProps } });
		case 'Portal/Tenant/AddUsersToDic':
			return update(state, { usersDic: { $merge: action.payload.dic } });
		case 'Portal/Tenant/AddRolesToDic':
			return update(state, { rolesDic: { $merge: action.payload.dic } });
		case 'Portal/Tenant/AddPermissionsToDic':
			return update(state, { permissionsDic: { $merge: action.payload.dic } });
		case 'Portal/Tenant/SetSetIsAdding':
			return update(state, { isAdding: { $set: action.payload.isAdding } });
		case 'Portal/Tenant/SetIsEditing':
			return update(state, { isEditing: { $set: action.payload.isEditing } });
		case 'Portal/Tenant/SetNoTenantUsers':
			return update(state, { noTenantUsers: { $set: action.payload.noTenantUsers } });
		case 'Portal/Tenant/SetAvailablePermissions':
			return update(state, { availablePermissions: { $set: action.payload.permissions.sort((s, v) => s.order - v.order) } });
		case 'Portal/Tenant/SetEnableStrictPermissionManagement':
			return update(state, { enableStrictPermissionManagement: { $set: action.payload.enableStrictPermissionManagement } });

		default: return state;
	}
};
