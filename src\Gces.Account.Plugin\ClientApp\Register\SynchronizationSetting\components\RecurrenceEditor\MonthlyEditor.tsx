import * as update from 'immutability-helper';
import * as moment from 'moment';
import * as React from 'react';
import { translate } from 'react-i18next';

import { Dropdown, DropdownItemProps } from 'gces-ui/lib/components/Dropdown';
import { MonthlyRepeatDetail, ScheduledInfo } from '../../store/interface';
import { generateNumbers } from '../../utils';
import { RecSettingEditor } from './RecSettingEditor';

const monthsRepeat = generateNumbers(1, 12);
const days = generateNumbers(1, 31);

interface MonthlyEditorProps {
	scheduleInfo: ScheduledInfo;
	updateScheduleInfo: (scheduleInfo: ScheduledInfo) => void;
	t?: any;
}

@translate('synchronization', { wait: true })
export class MonthlyEditor extends React.PureComponent<MonthlyEditorProps> {

	onMonthSelect = (month: number) => {
		const { scheduleInfo, updateScheduleInfo } = this.props;
		const newSchedule = update(scheduleInfo, { detail: { repeatInterval: { $set: month } } });
		updateScheduleInfo(newSchedule);
	}

	onModeSelect = (mode: string) => {
		if (mode) {
			const { scheduleInfo, updateScheduleInfo } = this.props;
			const newSchedule = update(scheduleInfo, { detail: { type: { $set: mode } } });
			updateScheduleInfo(newSchedule);
		}
	}

	onDaySelect = (day: number) => {
		const { scheduleInfo, updateScheduleInfo } = this.props;
		const newSchedule = update(scheduleInfo, { detail: { onDay: { $set: day } } });
		updateScheduleInfo(newSchedule);
	}

	onLastSelect = (lastType: string) => {
		const { scheduleInfo, updateScheduleInfo } = this.props;
		const newSchedule = update(scheduleInfo, { detail: { onTheLast: { $set: lastType } } });
		updateScheduleInfo(newSchedule);
	}

	onFirstSelect = (firstType: string) => {
		const { scheduleInfo, updateScheduleInfo } = this.props;
		const newSchedule = update(scheduleInfo, { detail: { onTheFirst: { $set: firstType } } });
		updateScheduleInfo(newSchedule);
	}

	render() {
		const { t, scheduleInfo: { repeatType, detail } } = this.props;

		if (repeatType !== 'Monthly') return null;

		const { repeatInterval, onDay, onTheLast, onTheFirst, type } = detail as MonthlyRepeatDetail;

		const localeData = moment.localeData();
		const weekdays = localeData.weekdays();
		const weekdayItems = [
			{ name: weekdays[0], value: '1' },
			{ name: weekdays[1], value: '2' },
			{ name: weekdays[2], value: '3' },
			{ name: weekdays[3], value: '4' },
			{ name: weekdays[4], value: '5' },
			{ name: weekdays[5], value: '6' },
			{ name: weekdays[6], value: '7' },
		];
		const lastItems = [
			{ name: t('monthlyEditorTextLastDay'), value: 'L' },
			{ name: t('monthlyEditorTextLastWeekday'), value: 'LW' },
			...weekdayItems,
		];
		const firstItems = [
			...weekdayItems
		];

		const repeatItems: DropdownItemProps[] = monthsRepeat.map(month => ({ text: `${month}`, value: month, selected: month === repeatInterval }));
		const dayItems: DropdownItemProps[] = days.map(day => ({ text: `${day}`, value: day, selected: day === onDay }));
		const lastDDItems: DropdownItemProps[] = lastItems.map(li => ({ text: li.name, value: li.value, selected: li.value === onTheLast }));
		const firstDDItems: DropdownItemProps[] = firstItems.map(li => ({ text: li.name, value: li.value, selected: li.value === onTheFirst }));
		const selectedLast = lastItems.find(li => li.value === onTheLast);
		const selectedFirst = firstItems.find(li => li.value === onTheFirst);

		return (
			<div className='sc-rec-monthly'>
				<RecSettingEditor text={t('monthlyEditorTextEvery')} units={t('monthlyEditorTextUnits')} value='' noRadio inverted={window.inverted}>
					<Dropdown inverted={window.inverted} text={`${repeatInterval}`} items={repeatItems} menuWidth='100%' offset size='small' textAlign='left' position='center' onSelect={this.onMonthSelect} />
				</RecSettingEditor>
				<RecSettingEditor text={t('monthlyEditorTextOnDay')} units={t('monthlyEditorTextOnDayUnits')} value='OnDay' checked={type === 'OnDay'} onChange={this.onModeSelect} inverted={window.inverted}>
					<Dropdown inverted={window.inverted} text={`${onDay}`} items={dayItems} menuWidth='100%' offset size='small' textAlign='left' position='center' onSelect={this.onDaySelect} disabled={type !== 'OnDay'} />
				</RecSettingEditor>
				<RecSettingEditor text={t('monthlyEditorTextOnTheLast')} value='OnTheLast' checked={type === 'OnTheLast'} onChange={this.onModeSelect} inverted={window.inverted}>
					<Dropdown inverted={window.inverted} text={`${selectedLast && selectedLast.name || ''}`} items={lastDDItems} menuWidth='100%' offset size='small' textAlign='left' position='center' onSelect={this.onLastSelect} disabled={type !== 'OnTheLast'} />
				</RecSettingEditor>
				<RecSettingEditor text={t('monthlyEditorTextOnTheFirst')} value='OnTheFirst' checked={type === 'OnTheFirst'} onChange={this.onModeSelect} inverted={window.inverted}>
					<Dropdown inverted={window.inverted} text={`${selectedFirst && selectedFirst.name || ''}`} items={firstDDItems} menuWidth='100%' offset size='small' textAlign='left' position='center' onSelect={this.onFirstSelect} disabled={type !== 'OnTheFirst'} />
				</RecSettingEditor>
			</div>
		);
	}
}