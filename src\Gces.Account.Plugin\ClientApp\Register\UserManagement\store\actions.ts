import { UserInfo, ImportResult, Organization } from './interfaces';
// reducer actions
export interface SetBusyAction { type: 'Portal/User/SetBusy'; payload: { busy: boolean }; }
export interface SetAllUsersAction { type: 'Portal/User/SetAllUsers'; payload: { allUsers: UserInfo[] }; }
export interface SetRolesAction { type: 'Portal/User/SetRoles'; payload: { roles: any[] }; }
export interface SetPropertiesAction { type: 'Portal/User/SetProperties'; payload: { Properties: any[] }; }
export interface SetPasswordPolicyAction { type: 'Portal/User/SetPasswordPolicy'; payload: { passwordPolicy: number }; }
export interface SetIsAddingUserAction { type: 'Portal/User/SetIsAddingUser'; payload: { isAddingUser: boolean }; }
export interface SetIsImportingUserAction { type: 'Portal/User/SetIsImportingUser'; payload: { isImportingUser: boolean }; }
export interface SetEditingUserAction { type: 'Portal/User/SetEditingUser'; payload: { editingUser: UserInfo }; }
export interface SetSearchTextAction { type: 'Portal/User/SetSearchText'; payload: { searchText: string }; }
export interface SetImportResultAction { type: 'Portal/User/SetImportResult'; payload: { importResult: ImportResult }; }
export interface SetOrganizationsAction { type: 'Portal/User/SetOrganizations'; payload: { organizations: Organization[] }; }
export interface SetSelectedOrganizationIdAction { type: 'Portal/User/SetSelectedOrganizationId'; payload: { selectedOrganizationId: string }; }
export interface SetIsSelectingMembersAction { type: 'Portal/User/SetIsSelectingMembers'; payload: { isSelectingMembers: boolean }; }
export interface SetDetailUserIdAction { type: 'Portal/User/SetDetailUserId'; payload: { detailUserId: string }; }
export interface SetShowMembersOfSubOrgAction { type: 'Portal/User/SetShowMembersOfSubOrg'; payload: { showMembersOfSubOrg: boolean }; }
export interface ResetStateAction { type: 'Portal/user/ResetState'; }

const setBusy = (busy: boolean): SetBusyAction => ({ type: 'Portal/User/SetBusy', payload: { busy } });
const setAllUsers = (allUsers: UserInfo[]): SetAllUsersAction => ({ type: 'Portal/User/SetAllUsers', payload: { allUsers } });
const setRoles = (roles: any[]): SetRolesAction => ({ type: 'Portal/User/SetRoles', payload: { roles } });
const setProperties = (Properties: any[]): SetPropertiesAction => ({ type: 'Portal/User/SetProperties', payload: { Properties } });
const setPasswordPolicy = (passwordPolicy: number): SetPasswordPolicyAction => ({ type: 'Portal/User/SetPasswordPolicy', payload: { passwordPolicy } });
const setIsAddingUser = (isAddingUser: boolean): SetIsAddingUserAction => ({ type: 'Portal/User/SetIsAddingUser', payload: { isAddingUser } });
const setIsImportingUser = (isImportingUser: boolean): SetIsImportingUserAction => ({ type: 'Portal/User/SetIsImportingUser', payload: { isImportingUser } });
const setEditingUser = (editingUser: UserInfo): SetEditingUserAction => ({ type: 'Portal/User/SetEditingUser', payload: { editingUser } });
const setSearchText = (searchText: string): SetSearchTextAction => ({ type: 'Portal/User/SetSearchText', payload: { searchText } });
const setImportResult = (importResult: ImportResult): SetImportResultAction => ({ type: 'Portal/User/SetImportResult', payload: { importResult } });
const setOrganizations = (organizations: Organization[]): SetOrganizationsAction => ({ type: 'Portal/User/SetOrganizations', payload: { organizations } });
const setSelectedOrgId = (selectedOrganizationId: string): SetSelectedOrganizationIdAction => ({ type: 'Portal/User/SetSelectedOrganizationId', payload: { selectedOrganizationId } });
const setIsSelectingMembers = (isSelectingMembers: boolean): SetIsSelectingMembersAction => ({ type: 'Portal/User/SetIsSelectingMembers', payload: { isSelectingMembers } });
const setDetailUserId = (detailUserId: string): SetDetailUserIdAction => ({ type: 'Portal/User/SetDetailUserId', payload: { detailUserId } });
const setShowMembersOfSubOrg = (showMembersOfSubOrg: boolean): SetShowMembersOfSubOrgAction => ({ type: 'Portal/User/SetShowMembersOfSubOrg', payload: { showMembersOfSubOrg } });
const resetState = (): ResetStateAction => ({ type: 'Portal/user/ResetState' });

export type UserReducerActions = SetBusyAction | SetAllUsersAction | SetRolesAction
    | SetPropertiesAction | SetPasswordPolicyAction | SetEditingUserAction | SetIsAddingUserAction
    | SetIsImportingUserAction | SetSearchTextAction | SetImportResultAction | SetOrganizationsAction
    | SetSelectedOrganizationIdAction | SetIsSelectingMembersAction | SetDetailUserIdAction | SetShowMembersOfSubOrgAction
    | ResetStateAction;

// saga actions
export interface InitAction { type: 'Portal/User/Init'; }
export interface GetAllUsersAction { type: 'Portal/User/GetAllUsers'; }
export interface CreateUserAction { type: 'Portal/User/CreateUser'; payload: { newUser: UserInfo }; }
export interface UpdateUserAction { type: 'Portal/User/UpdateUser'; payload: { newUser: UserInfo }; }
export interface DeleteUserAction { type: 'Portal/User/DeleteUser'; payload: { userId: string }; }
export interface ToggleUserStatusAction { type: 'Portal/User/ToggleUserStatus'; payload: { userId: string, enable: boolean }; }
export interface ImportUsersAction { type: 'Portal/User/ImportUsers'; payload: { users: FormData }; }
export interface ExportTemplateAction { type: 'Portal/User/ExportTemplate'; }
export interface UpdateOrganizationUsersAction { type: 'Portal/User/UpdateOrganizationUsers'; payload: { selectedOrganizationId: string, userIds: string[] }; }
export interface RemoveUserFromOrgnizationAction { type: 'Portal/User/RemoveUserFromOrganization'; payload: { selectedOrganizationId: string, userId: string }; }
export interface GetRolesOfCurOrganizationAction { type: 'Portal/User/GetRolesOfCurOrganization'; payload: { selectedOrganizationId: string }; }

const init = (): InitAction => ({ type: 'Portal/User/Init' });
const getAllUsers = (): GetAllUsersAction => ({ type: 'Portal/User/GetAllUsers' });
const createUser = (newUser: UserInfo): CreateUserAction => ({ type: 'Portal/User/CreateUser', payload: { newUser } });
const updateUser = (newUser: UserInfo): UpdateUserAction => ({ type: 'Portal/User/UpdateUser', payload: { newUser } });
const deleteUser = (userId: string): DeleteUserAction => ({ type: 'Portal/User/DeleteUser', payload: { userId } });
const toggleUserStatus = (userId: string, enable: boolean): ToggleUserStatusAction => ({ type: 'Portal/User/ToggleUserStatus', payload: { userId, enable } });
const importUsers = (users: FormData): ImportUsersAction => ({ type: 'Portal/User/ImportUsers', payload: { users } });
const exportTemplate = (): ExportTemplateAction => ({ type: 'Portal/User/ExportTemplate' });
const updateOrganizationUsers = (selectedOrganizationId: string, userIds: string[]): UpdateOrganizationUsersAction => ({ type: 'Portal/User/UpdateOrganizationUsers', payload: { selectedOrganizationId, userIds } });
const removeUserFromOrganization = (selectedOrganizationId: string, userId: string): RemoveUserFromOrgnizationAction => ({ type: 'Portal/User/RemoveUserFromOrganization', payload: { selectedOrganizationId, userId } });
const getRolesOfCurOrganization = (selectedOrganizationId: string): GetRolesOfCurOrganizationAction => ({ type: 'Portal/User/GetRolesOfCurOrganization', payload: { selectedOrganizationId } });

export const SagaActionTypes = {
    Init: 'Portal/User/Init',
    GetAllUsers: 'Portal/User/GetAllUsers',
    CreateUser: 'Portal/User/CreateUser',
    UpdateUser: 'Portal/User/UpdateUser',
    DeleteUser: 'Portal/User/DeleteUser',
    ToggleUserStatus: 'Portal/User/ToggleUserStatus',
    ImportUsers: 'Portal/User/ImportUsers',
    ExportTemplate: 'Portal/User/ExportTemplate',
    UpdateOrganizationUsers: 'Portal/User/UpdateOrganizationUsers',
    RemoveUserFromOrganization: 'Portal/User/RemoveUserFromOrganization',
    GetRolesOfCurOrganization: 'Portal/User/GetRolesOfCurOrganization',
};

// actions creators
export const userActionCreators = {
    init,
    getAllUsers,
    createUser,
    updateUser,
    deleteUser,
    toggleUserStatus,
    importUsers,
    exportTemplate,
    setImportResult,
    updateOrganizationUsers,
    resetState,

    setBusy,
    setAllUsers,
    setRoles,
    setProperties,
    setPasswordPolicy,
    setOrganizations,
    setSelectedOrgId,
    setIsAddingUser,
    setIsImportingUser,
    setEditingUser,
    setSearchText,
    setIsSelectingMembers,
    setDetailUserId,
    setShowMembersOfSubOrg,
    removeUserFromOrganization,
    getRolesOfCurOrganization,
};