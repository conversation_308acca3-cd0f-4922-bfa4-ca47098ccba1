@import '~gces-ellipsis/styles/index.scss';
@import "./components/drawer.scss";
@import "./components/account-management.scss";
@import "./components/tab-container.scss";
@import "./components/provider-container.scss";
@import "./components/provider-list.scss";
@import "./components/provider-settings.scss";
@import "./components/confirm-dialog.scss";
@import "./components/locked-user-management.scss";
@import "./components/security-settings.scss";
@import "./components/empty-page.scss";
@import "./components/claim-mappings.scss";
@import "./components/generate-token.scss";
@import "./components/tfa-settings.scss";
@import "./components/inactive-session-settings.scss";
@import "~gces-ui/styles/components/simpleLoader.scss";
@import "~gces-ui/styles/components/confirmDialog.scss";
@import '~gces-ui/styles/components/messageBox.scss';
@import '~gces-ui/styles/components/button.scss';

// Attention: additional.scss should be the last one when importing styles of gces-ui.
@import '~gces-ui/styles/components/additional.scss';
@import "../Register/all.scss";
@import "./components/switch.scss";
@import "./components/document-selector.scss";
@import './grid-common.scss';
@import "./components/c-dropdownCheckList.scss";

.user-management .thumb-vertical {
	background-color: rgba(0, 0, 0, .1);
}
