import { Notification<PERSON>rovider, SecretInfo, SyncInfo, SynchronizationView, TaskStatus } from './interface';
import { TaskStatusViewModel } from './viewModel';

export interface InitAction { type: 'SYNCHRONIZATION-SETTINGS/Init'; }
const init = (): InitAction => ({ type: 'SYNCHRONIZATION-SETTINGS/Init' });
export const synchronizationSettingsActionCreator = {
	init,
};

export interface SetSyncInfoAction { type: 'SYNCHRONIZATION-SETTINGS/SS/SetSyncInfo'; payload: { syncInfo: SyncInfo }; }
export interface SetFailureNotifications { type: 'SYNCHRONIZATION-SETTINGS/SS/SetFailureNotifications'; payload: { failureNotifications: Record<string, NotificationProvider>; }; }
export interface SetSecretAction { type: 'SYNCHRONIZATION-SETTINGS/SS/SetSecret'; payload: { enable: boolean; secret: string; }; }
export interface ManualSyncAction { type: 'SYNCHRONIZATION-SETTINGS/SS/ManualSync'; }
export interface SaveAction { type: 'SYNCHRONIZATION-SETTINGS/SS/Save'; payload: { syncInfo: SyncInfo; secretInfo: SecretInfo; }; }
export interface SetViewAction { type: 'SYNCHRONIZATION-SETTINGS/SV/SetView'; payload: { view: SynchronizationView }; }
export interface UpdateTaskStatusAction { type: 'SYNCHRONIZATION-SETTINGS/SV/UpdateTaskStatus'; payload: { status: TaskStatusViewModel; }; }
export interface SetNextRuntimeAction { type: 'SYNCHRONIZATION-SETTINGS/SV/SetNextRuntime'; payload: { nextRuntime: moment.Moment; }; }
export interface SetTaskStatusAction { type: 'SYNCHRONIZATION-SETTINGS/SV/SetTaskStatus'; payload: { status: TaskStatus; }; }
export interface SetManualSyncingAction { type: 'SYNCHRONIZATION-SETTINGS/SV/SetManualSyncing'; payload: { status: boolean; }; }
export interface SetExecutionIdAction { type: 'SYNCHRONIZATION-SETTINGS/SV/SetExecutionId'; payload: { id: string; }; }
export interface SetBusyAction { type: 'SYNCHRONIZATION-SETTINGS/SS/SetBusy'; payload: { busy: boolean }; }
export interface SetGettingHistoryAction { type: 'SYNCHRONIZATION-SETTINGS/SS/SetGettingHistory'; payload: { getting: boolean }; }

export type SynchronizationSettingsAction = SetSyncInfoAction | SetFailureNotifications | SetSecretAction | ManualSyncAction | SetViewAction | SetNextRuntimeAction | SetTaskStatusAction | SetManualSyncingAction | SetExecutionIdAction | SetBusyAction | SetGettingHistoryAction;

const setSyncInfo = (syncInfo: SyncInfo): SetSyncInfoAction => ({ type: 'SYNCHRONIZATION-SETTINGS/SS/SetSyncInfo', payload: { syncInfo } });
const setFailureNotifications = (failureNotifications: Record<string, NotificationProvider>): SetFailureNotifications => ({ type: 'SYNCHRONIZATION-SETTINGS/SS/SetFailureNotifications', payload: { failureNotifications } });
const setSecret = (enable: boolean, secret: string): SetSecretAction => ({ type: 'SYNCHRONIZATION-SETTINGS/SS/SetSecret', payload: { enable, secret } });
const manualSync = (): ManualSyncAction => ({ type: 'SYNCHRONIZATION-SETTINGS/SS/ManualSync' });
const save = (syncInfo: SyncInfo, secretInfo?: SecretInfo): SaveAction => ({ type: 'SYNCHRONIZATION-SETTINGS/SS/Save', payload: { syncInfo, secretInfo } });
const setView = (view: SynchronizationView): SetViewAction => ({ type: 'SYNCHRONIZATION-SETTINGS/SV/SetView', payload: { view } });
const updateTaskStatus = (status: TaskStatusViewModel): UpdateTaskStatusAction => ({ type: 'SYNCHRONIZATION-SETTINGS/SV/UpdateTaskStatus', payload: { status } });
const setNextRuntime = (nextRuntime: moment.Moment): SetNextRuntimeAction => ({ type: 'SYNCHRONIZATION-SETTINGS/SV/SetNextRuntime', payload: { nextRuntime } });
const setTaskStatus = (status: TaskStatus): SetTaskStatusAction => ({ type: 'SYNCHRONIZATION-SETTINGS/SV/SetTaskStatus', payload: { status } });
const setManualSyncing = (status: boolean): SetManualSyncingAction => ({ type: 'SYNCHRONIZATION-SETTINGS/SV/SetManualSyncing', payload: { status } });
const setExecutionId = (id: string): SetExecutionIdAction => ({ type: 'SYNCHRONIZATION-SETTINGS/SV/SetExecutionId', payload: { id } });
const setBusy = (busy: boolean): SetBusyAction => ({ type: 'SYNCHRONIZATION-SETTINGS/SS/SetBusy', payload: { busy } });
const setGettingHistory = (getting: boolean): SetGettingHistoryAction => ({ type: 'SYNCHRONIZATION-SETTINGS/SS/SetGettingHistory', payload: { getting } });

export const syncSettingActionCreator = {
	setSyncInfo,
	setFailureNotifications,
	setSecret,
	manualSync,
	save,
	setView,
	updateTaskStatus,
	setNextRuntime,
	setTaskStatus,
	setManualSyncing,
	setExecutionId,
	setBusy,
	setGettingHistory
};

export const SagaActionTypes = {
	init: 'SYNCHRONIZATION-SETTINGS/Init',
	sync: 'SYNCHRONIZATION-SETTINGS/SS/ManualSync',
	save: 'SYNCHRONIZATION-SETTINGS/SS/Save',
	updateTaskState: 'SYNCHRONIZATION-SETTINGS/SV/UpdateTaskStatus',
};