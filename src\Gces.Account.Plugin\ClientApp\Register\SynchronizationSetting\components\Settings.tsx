import { Scrollbars } from 'gces-react-custom-scrollbars';
import { <PERSON><PERSON><PERSON>, BlockLoader, Button, Checkbox, Divider, DropdownEditor, DropdownItemProps, InputEditor, Label, NumberEditor, Switch, debounce } from 'gces-ui';
import * as update from 'immutability-helper';
import * as React from 'react';
import { translate } from 'react-i18next';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import { syncSettingActionCreator } from '../store/action';
import { Dataset, DatasetSynchronizationType, NotificationInfo, NotificationProvider, ScheduledInfo, SecretInfo, SettingsEditor, SyncDataset, SyncInfo, SynchronizationScheduling, SynchronizationSettingsState, Timeout, TimeoutUnit, UserSyncProcess } from '../store/interface';
import { sendRequestV2 } from '../utils';
import { CPasswordInput } from './Common/c-password-input';
import DatasetSelector, { DatasetSelectorProps } from './DatasetSelector';
import { RecurrenceEditor } from './RecurrenceEditor';

export interface ConnectedProps {
	synchronizationSetting: SettingsEditor;
	running: boolean;
	busy: boolean;
	dispatch?: any;
	t?: any;
}

interface SettingsStates {
	datasets: Dataset[];
	datasetsLoaded: boolean;

	notifications: Record<string, NotificationInfo[]>;
	availableNotifications: Record<string, NotificationInfo[]>;

	synchronizationEnable: boolean;
	syncDatasets: Record<DatasetSynchronizationType, SyncDataset>;
	synchronizationScheduling: SynchronizationScheduling;
	failureNotifications: Record<string, NotificationProvider>;
	applicationSecretEnable: boolean;
	applicationSecret: string;
}

@translate('synchronization', { wait: true })
export class SettingsBase extends React.PureComponent<ConnectedProps, SettingsStates> {

	state: SettingsStates = {
		datasets: [],
		datasetsLoaded: false,

		notifications: {},
		availableNotifications: {},

		synchronizationEnable: false,
		syncDatasets: null,
		synchronizationScheduling: null,
		failureNotifications: null,

		applicationSecretEnable: false,
		applicationSecret: '',
	};

	UNSAFE_componentWillMount(): void {
		const { synchronizationSetting } = this.props;
		this.findEdit(synchronizationSetting);
	}

	UNSAFE_componentWillReceiveProps(nextProps: Readonly<ConnectedProps>): void {
		if (this.props.synchronizationSetting !== nextProps.synchronizationSetting) {
			this.findEdit({ ...nextProps.synchronizationSetting });
		}
	}

	findEdit = (synchronizationSetting: SettingsEditor) => {
		const notifications = {};
		if (synchronizationSetting.syncInfo.failureNotifications) {
			Object.keys(synchronizationSetting.syncInfo.failureNotifications).forEach(key => {
				notifications[key] = synchronizationSetting.syncInfo.failureNotifications[key]?.notifications || [];
			});
		}
		this.setState({
			synchronizationEnable: synchronizationSetting.syncInfo.synchronizationEnable,
			syncDatasets: synchronizationSetting.syncInfo.syncDatasets,
			synchronizationScheduling: synchronizationSetting.syncInfo.synchronizationScheduling,
			failureNotifications: synchronizationSetting.syncInfo.failureNotifications,
			applicationSecretEnable: synchronizationSetting.secretInfo.applicationSecretEnable,
			applicationSecret: synchronizationSetting.secretInfo.applicationSecret,
			notifications
		});
	}

	setSynchronizationState = () => {
		const { synchronizationEnable } = this.state;
		this.setState({ synchronizationEnable: !synchronizationEnable });
	}

	setSchedulingState = (value: boolean) => {
		const { synchronizationScheduling } = this.state;
		this.setState({ synchronizationScheduling: { ...synchronizationScheduling, enabled: value } });
	}

	setApplicationSecretState = () => {
		const { applicationSecretEnable } = this.state;
		this.setState({ applicationSecretEnable: !applicationSecretEnable });
	}

	generateApplicationSecret = () => {
		const validChars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!@#$%^&*()-_=+[]{}|;:,.<>?';
		const length = 32;

		let secret = '';
		for (let i = 0; i < length; i++) {
			const rng = Math.floor(Math.random() * validChars.length);
			secret += validChars[rng % validChars.length];
		}
		this.setState({ applicationSecret: secret });
	}

	compareSettings = () => {
		const { synchronizationSetting } = this.props;
		const {
			synchronizationEnable,
			syncDatasets,
			synchronizationScheduling,
			failureNotifications,
			applicationSecretEnable,
			applicationSecret,
		} = this.state;
		const {
			syncInfo: {
				synchronizationEnable: synchronizationEnableOld,
				syncDatasets: syncDatasetsOld,
				synchronizationScheduling: synchronizationSchedulingOld,
				failureNotifications: failureNotificationsOld,
			},
			secretInfo: {
				applicationSecretEnable: applicationSecretEnableOld,
				applicationSecret: applicationSecretOld,
			},
		} = synchronizationSetting;
		return (
			synchronizationEnable === synchronizationEnableOld &&
			JSON.stringify(syncDatasets) === JSON.stringify(syncDatasetsOld) &&
			JSON.stringify(synchronizationScheduling) === JSON.stringify(synchronizationSchedulingOld) &&
			JSON.stringify(failureNotifications) === JSON.stringify(failureNotificationsOld) &&
			applicationSecretEnable === applicationSecretEnableOld &&
			applicationSecret === applicationSecretOld
		);
	}

	updateScheduleInfo = (scheduleInfo: ScheduledInfo) => {
		const { synchronizationScheduling } = this.state;
		this.setState({ synchronizationScheduling: { ...synchronizationScheduling, scheduleInfo } });
	}

	loadDirectDatasets = async () => {
		const { result } = await sendRequestV2('/api/v2/common/documents?types=dataset&orderBy=+displayName');
		if (result) {
			const datasets: Dataset[] = result?.documents?.filter((doc: any) => doc.ext === 'dst' || doc.ext === '.dst').map((doc: any) => ({ id: doc.id, name: doc.displayName || doc.title }));
			this.setState({ datasets, datasetsLoaded: true });
		}
	}

	updateTimeout = (value: Timeout) => {
		const { synchronizationScheduling } = this.state;
		this.setState({ synchronizationScheduling: { ...synchronizationScheduling, timeout: value } });
	}

	validateSettings = (): { sync: boolean, save: boolean } => {
		const { syncDatasets, synchronizationEnable } = this.state;
		if (!synchronizationEnable) return { sync: false, save: true };
		let selectOne = false;
		let inputValid = true;
		Object.keys(syncDatasets).forEach(key => {
			const dataset = syncDatasets[key];
			if (dataset.selected) {
				if (!dataset.dataset.id) {
					inputValid = false;
					return;
				}
				selectOne = true;
			}
		});
		return { sync: inputValid && selectOne, save: inputValid };
	}

	saveValidate = () => {
		const { syncDatasets } = this.state;
		Object.keys(syncDatasets).forEach(key => {
			const dataset = syncDatasets[key];
			if (dataset.selected) {
				if (!dataset.dataset.id) {
					return false;
				}
			}
		});
		return true;
	}

	onCheckDataset = (type: DatasetSynchronizationType) => {
		const { syncDatasets } = this.state;
		return (value: boolean) => {
			const newSyncDatasets = update(syncDatasets, { [type]: { selected: { $set: !value } } });
			this.setState({ syncDatasets: newSyncDatasets });
		};
	}

	onSelectDataset = (type: DatasetSynchronizationType) => {
		const { syncDatasets } = this.state;
		return (value: Dataset) => {
			const newSyncDatasets = update(syncDatasets, { [type]: { dataset: { $set: value } } });
			this.setState({ syncDatasets: newSyncDatasets });
		};
	}

	onCheckNotification = (value: string) => {
		const { failureNotifications, failureNotifications: { [value]: { selected } } } = this.state;
		const newSelectedNotifications = update(failureNotifications, { [value]: { selected: { $set: !selected } } });
		this.setState({ failureNotifications: newSelectedNotifications });
	}

	onChangeEmailNotificationContact = (type: string) => {
		const { failureNotifications } = this.state;
		return (value: string) => {
			if (type.toLowerCase() !== 'email') return;
			const newSelectedNotifications = update(failureNotifications, { [type]: { notifications: { $set: value } } });
			this.setState({ failureNotifications: newSelectedNotifications });
		};
	}

	onChangeDropdownNotificationContact = (type: string) => {
		const { failureNotifications } = this.state;
		return (value: string) => {
			if (type.toLowerCase() === 'email') return;
			const index = (failureNotifications[type].notifications as NotificationInfo[]).findIndex(n => n.id === value);
			if (index > -1) {
				const newSelectedNotifications = update(failureNotifications, { [type]: { notifications: { $splice: [[index, 1]] } } });
				this.setState({ failureNotifications: newSelectedNotifications });
			} else {
				const name = this.state.availableNotifications[type].find(n => n.id === value)?.label;
				const newSelectedNotifications = update(failureNotifications, { [type]: { notifications: { $push: [{ id: value, label: name }] } } });
				this.setState({ failureNotifications: newSelectedNotifications });
			}
		};
	}

	onDropdownToggle = (open: boolean, appName: string) => {
		const { t } = this.props;
		if (!open) return;
		const notifications = this.state.availableNotifications[appName];
		if (!notifications || notifications.length === 0) {
			this.props.dispatch(syncSettingActionCreator.setBusy(true));
			sendRequestV2(`/api/notification/contacts/${appName}`).then(rst => {
				this.props.dispatch(syncSettingActionCreator.setBusy(false));
				if (rst.result) {
					if (rst.result.success) {
						const contacts = (rst.result.data?.users || []).map((n: any) => ({ id: n.id, label: n.name }));
						const availableNotifications = update(this.state.availableNotifications, { [appName]: { $set: contacts } });
						this.setState({ availableNotifications });
					} else {
						window.AdminPortal.Notifications.Send(0, t('errorGetAppContacts'), rst.result.errorMsg || t('errorGetAppContacts'));
					}
				}
			});
		}
	}

	copyInner = null;

	copyAppSecret = (appSecret: string) => {
		if (!appSecret) {
			return;
		}
		if (this.copyInner) return this.copyInner(appSecret);
		if (navigator.clipboard) {
			this.copyInner = (text: string) => navigator.clipboard.writeText(text);
		} else {
			this.copyInner = (appSecret: string) => {
				const textArea = document.createElement('textarea');
				textArea.style.position = 'fixed';
				textArea.style.top = '-500px';
				textArea.style.left = '-500px';
				textArea.style.background = 'transparent';
				textArea.value = appSecret;
				document.body.appendChild(textArea);
				textArea.focus();
				textArea.select();
				try {
					document.execCommand('copy');
				} catch (err) {
					console.warn('sorry,unable to copy');
				}
				document.body.removeChild(textArea);
			};
		}
		return this.copyInner(appSecret);
	}

	onClickCopySecret = () => {
		const { applicationSecret } = this.state;
		this.copyAppSecret(applicationSecret);
	}

	onManualSync = () => {
		const { dispatch } = this.props;
		dispatch(syncSettingActionCreator.manualSync());
	}

	onSave = () => {
		const { synchronizationSetting: { secretInfo: { applicationSecretEnable: oldSecretEnable, applicationSecret: oldSecret } }, dispatch } = this.props;
		const {
			syncDatasets,
			synchronizationScheduling,
			synchronizationEnable,
			failureNotifications,
			applicationSecretEnable,
			applicationSecret,
		} = this.state;
		const syncInfo: SyncInfo = {
			synchronizationEnable,
			syncDatasets,
			synchronizationScheduling,
			failureNotifications,
		};
		if (oldSecretEnable !== applicationSecretEnable || oldSecret !== applicationSecret) {
			const secretInfo: SecretInfo = {
				applicationSecretEnable,
				applicationSecret,
			};
			dispatch(syncSettingActionCreator.save(syncInfo, secretInfo));
		} else {
			dispatch(syncSettingActionCreator.save(syncInfo));
		}
	}

	onCancel = () => {
		const { synchronizationSetting } = this.props;
		this.findEdit(synchronizationSetting);
	}

	renderSynchronizeDataset = (type: DatasetSynchronizationType) => {
		const { t } = this.props;
		const { datasetsLoaded, datasets, syncDatasets: { [type]: { selected: checked, dataset } } } = this.state;

		const datasetSelectorProps: DatasetSelectorProps = {
			disabled: !checked,
			datasets,
			currentDataset: dataset,
			datasetsLoaded,
			onSelectDataset: this.onSelectDataset(type),
			loadDirectDatasets: this.loadDirectDatasets,
		};

		return (
			<div className='synchronize-settings-row'>
				<Checkbox text={t(`synchronizationDatasets!${type}`)} value={checked} checked={checked} onChange={this.onCheckDataset(type)} />
				<DatasetSelector {...datasetSelectorProps} />
			</div>
		);
	}

	renderSynchronizeDatasets = () => {
		const { t } = this.props;
		return (
			<div className='synchronize-datasets'>
				<div className='synchronize-settings-header'>
					<span className='synchronize-settings-title'>{t('synchronizationDatasets!title')}</span>
					<AriaIcon className='mdi mdi-information-outline' title={t('synchronizationDatasets!tip')} />
					<Divider className='synchronize-settings-header-divider' />
				</div>
				{this.renderSynchronizeDataset('user')}
				{this.renderSynchronizeDataset('organization')}
				{this.renderSynchronizeDataset('role')}
				{this.renderSynchronizeDataset('userRoleRelation')}
				{this.renderSynchronizeDataset('userOrganization')}
			</div>
		);
	}

	renderSchedulingSettings = () => {
		const { t } = this.props;
		const { synchronizationScheduling: { enabled, scheduleInfo } } = this.state;
		return (
			<div className='scheduling-settings'>
				<div className='synchronize-settings-header'>
					<span className='synchronize-settings-title'>{t('schedulingSettings')}</span>
					<Divider className='synchronize-settings-header-divider' />
				</div>

				<Label controlClass='synchronize-switch' text={t('schedulingSync')} title={t('schedulingSync')}>
					<Switch
						value={enabled}
						trueString={t('Enable')}
						falseString={t('Disable')}
						onChange={this.setSchedulingState}
					/>
				</Label>

				{enabled && <RecurrenceEditor scheduleInfo={scheduleInfo} updateScheduleInfo={this.updateScheduleInfo} />}
			</div>
		);
	}

	renderTimeoutSetting = () => {
		const { synchronizationScheduling: { timeout: { value, unit } } } = this.state;
		const { t } = this.props;
		return (
			<div className='timeout-setting'>
				<div className='synchronize-settings-header'>
					<span className='synchronize-settings-title'>{t('timeoutSettings')}</span>
					<Divider className='synchronize-settings-header-divider' />
				</div>

				<Label inverted={window.inverted} text={t('timeout')} labelOnTop controlClass='timeout-control'>
					<NumberEditor
						minValue={1}
						maxValue={10000}
						value={value}
						onChange={(newValue) => {
							const patchedValue = isNaN(newValue) ? 1 : Math.min(10000, Math.max(newValue, 1));
							this.updateTimeout({ value: patchedValue, unit });
						}}
					/>
					<DropdownEditor
						items={[
							{ value: TimeoutUnit.Minutes, text: t('minute'), selected: unit === TimeoutUnit.Minutes },
							{ value: TimeoutUnit.Hours, text: t('hour'), selected: unit === TimeoutUnit.Hours }
						]}
						text={t(unit === TimeoutUnit.Minutes ? 'minute' : 'hour')}
						onChange={(newValue: TimeoutUnit) => { this.updateTimeout({ value, unit: newValue }); }}
						inverted={window.inverted}
					/>
				</Label>
			</div>
		);
	}

	onAppContectClick = (appName: string, item: DropdownItemProps) => {
		const notifications = this.state.notifications[appName];
		const idx = notifications.findIndex(n => n.id === item.value);
		if (idx > -1) {
			const newNotifications = update(this.state.notifications, { [appName]: { $splice: [[idx, 1]] } });
			this.setState({ notifications: newNotifications });
		} else {
			const newNotifications = update(this.state.notifications, { [appName]: { $push: [{ id: item.value, label: item.text }] } });
			this.setState({ notifications: newNotifications });
		}
	}
	renderAppContactDropdownItem = (type: string) => {
		return (item: DropdownItemProps) => {
			return (
				<div className='app-contact-dropdown-item'>
					<AriaIcon className={item.icon} style={{ margin: '0 5px 0 0' }} onClick={_ => this.onAppContectClick(type, item)} />
					<span>{item.text}</span>
				</div>
			);
		};
	}
	renderFailureNotification = (type: string, info: NotificationProvider, key: string) => {
		const { t } = this.props;
		const { disabled, selected: checked } = info;
		if (type.toLowerCase() === 'email') {
			const selectedNotifications = info.notifications as string;
			return (
				<div className='synchronize-settings-row' key={key}>
					<Checkbox text={t(type)} value={type} disabled={disabled} checked={checked} onChange={this.onCheckNotification} />
					<InputEditor
						value={selectedNotifications}
						onEveryChange={this.onChangeEmailNotificationContact(type)}
						disabled={disabled || !checked}
					/>
				</div>
			);
		}
		const selectedNotifications = this.state.notifications[type] || [];
		const set = new Set(selectedNotifications.map(n => n.id));
		const items: DropdownItemProps[] = this.state.availableNotifications[type]?.map(n => ({
			icon: set.has(n.id) ? 'mdi mdi-check-circle' : 'mdi mdi-checkbox-blank-circle-outline',
			value: n.id,
			text: n.label,
			onRenderCustomDropdownItem: this.renderAppContactDropdownItem(type),
		})) || [];
		const label = selectedNotifications.map(i => i.label).join(';');
		return (
			<div className='synchronize-settings-row' key={key}>
				<Checkbox text={t(type)} value={type} disabled={disabled} checked={checked} onChange={this.onCheckNotification} />
				<DropdownEditor
					noCloseOnSelect
					text={label}
					items={items}
					onToggle={(open) => this.onDropdownToggle(open, type)}
					onChange={this.onChangeDropdownNotificationContact(type)}
					disabled={disabled || !checked}
				/>
			</div>
		);
	}

	renderFailureNotifications = () => {
		const { t } = this.props;
		const { failureNotifications } = this.state;
		const noNotification = Object.keys(failureNotifications).length === 0;
		return (
			<div className='failure-notification'>
				<div className='synchronize-settings-header'>
					<span className='synchronize-settings-title'>{t('failureNotification')}</span>
					<Divider className='synchronize-settings-header-divider' />
				</div>
				{noNotification && <Link to='/admin/configuration/notification'>{t('noFailureNotification')}</Link>}
				{!noNotification && Object.keys(failureNotifications).map((type) => this.renderFailureNotification(type, failureNotifications[type], `notification-${type}`))}
			</div>
		);
	}

	renderApplicationSecret = () => {
		const { t } = this.props;
		const { applicationSecretEnable, applicationSecret } = this.state;
		return (
			<div className='application-secret'>
				<Label controlClass='synchronize-switch' text={t('applicationSecret!label')} title={t('applicationSecret!title')} tip={{ text: t('applicationSecret!tip'), className: 'synchronize-application-secret-tip mdi mdi-information-outline' }}>
					<Switch
						value={applicationSecretEnable}
						trueString={t('Enable')}
						falseString={t('Disable')}
						onChange={this.setApplicationSecretState}
					/>
				</Label>

				{
					applicationSecretEnable &&
					<div className='application-secret-content'>
						<div className='synchronize-settings-row application-secret-row'>
							<Button text={t('generate')} style='accent' onClick={this.generateApplicationSecret} size='small' />
						</div>
						<div className='synchronize-settings-row'>
							<CPasswordInput aid='password-input' value={applicationSecret} inputDisabled={true} />
							<Button text={t('copy')} style='accent' onClick={this.onClickCopySecret} size='small' className='password-copy' disabled={!applicationSecret} />
						</div>
					</div>
				}
			</div>
		);

	}

	renderSynchronizationFooter = () => {
		const { running, t } = this.props;
		const { synchronizationEnable } = this.state;
		const same = this.compareSettings();
		const { sync: syncValid, save: saveValid } = this.validateSettings();
		return (
			<div className='synchronize-settings-footer'>
				<div className='left-button-list'>
					<Button text={t('manualSync')} style='accent' onClick={debounce(this.onManualSync, 800, true)} disabled={!same || !syncValid || !synchronizationEnable || running} />
				</div>
				<div className='right-button-list'>
					<Button text={t('saveChanges')} style='accent' onClick={this.onSave} disabled={same || !saveValid} />
					<Button text={t('cancel')} onClick={this.onCancel} className='btn-cancel' />
				</div>
			</div>
		);

	}

	render() {
		const { t, busy } = this.props;
		const { synchronizationEnable } = this.state;
		const scrollbarsProps = {
			style: { width: '100%', height: '100%' },
			renderThumbHorizontal: props => <div {...props} style={{ display: 'none' }} className='thumb-horizontal' />,
			renderThumbVertical: props => <div {...props} className='thumb-vertical' style={{ width: '4px' }} />,
			renderTrackVertical: props => <div {...props} className='track-vertical' style={{ width: '4px' }} />,
		};
		return (
			<div className='settings-panel'>
				<div className='synchronize-content'>
					<Scrollbars {...scrollbarsProps} >
						<Label controlClass='synchronize-switch' text={t('synchronizeStateTip')} title={t('synchronizeStateTip')}>
							<Switch
								value={synchronizationEnable}
								trueString={t('Enable')}
								falseString={t('Disable')}
								onChange={this.setSynchronizationState}
							/>
						</Label>

						{synchronizationEnable && this.renderSynchronizeDatasets()}

						{synchronizationEnable && this.renderSchedulingSettings()}

						{synchronizationEnable && this.renderTimeoutSetting()}

						{synchronizationEnable && this.renderFailureNotifications()}

						<Divider className='synchronize-settings-divider' />

						{this.renderApplicationSecret()}

						{busy && <BlockLoader />}
					</Scrollbars>
				</div>

				{this.renderSynchronizationFooter()}
			</div>
		);
	}
}

export const Settings = connect((state: any) => {
	const synchronization = state.synchronization as SynchronizationSettingsState;
	const taskStatus = synchronization.syncView.taskStatus.process;
	return {
		synchronizationSetting: synchronization.settingsEditor,
		running: taskStatus !== UserSyncProcess.None && taskStatus !== UserSyncProcess.Completed,
		busy: state.synchronization.busy,
	};
})(SettingsBase) as React.ComponentClass<{}>;