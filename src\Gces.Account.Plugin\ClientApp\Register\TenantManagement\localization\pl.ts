export const tenantPL: LanguageKeyValueMap = {
	// section
	'account-management-organization!title': 'Organizacje', // Organizations
	'account-management-organization!description': 'Zarządzanie Organizacjami', // Organization Management

	Yes: 'Dodaj', // Yes
	Close: 'Zamknij', // Close
	tntTenantSchema: 'Schemat Organizacji', // Organization Schema
	tntAddTenant: 'Dodaj Organizację', // Add Organization
	tntEditTenant: 'Edytuj Organizację', // Edit Organization
	tntNoItemsTip: 'brak elementów', // no items
	tntAddProp: 'Dodaj Atrybut', // Add Property
	tntAddTenantProp: 'Dodaj Atrybut Organizacji', // Add Organization Property
	tntEditTenantProp: 'Edytuj Atrybut Organizacji', // Edit Organization Property
	tntPropName: 'Nazwa Atrybutu', // Property Name
	tntPropVlue: 'Wartość Atrybutu', // Property Value
	tntPropValueType: 'Property Value Type',
	tntPropValueType_String: 'String',
	tntPropValueType_Boolean: 'Boolean',
	tntPropValueType_Integer: 'Integer',
	tntPropValueType_Float: 'Float',
	tntPropValueType_Date: 'Date',
	tntPropValueType_DateTime: 'DateTime',
	tntRequired: 'Wymagana', // Required
	tntMultivalued: 'Wielowartościowa', // Multivalued
	tntClose: 'Zamknij', // Close
	tntDelete: 'Usuń Trwale', // Delete Forever
	tntEdit: 'Edytuj', // Edit
	tntName: 'Nazwa', // Name
	tntFromEmail: 'Z adresu email', // From Email
	tntMultiLineTip: 'Jedna wartość na linię', // One value per line
	tntSensitive: 'Sensitive',
	tntShowValue: 'Show value',
	tntHideValue: 'Hide value',
	tntAdd: 'Dodaj', // Add
	tntSave: 'Zapisz', // Save
	tntCancel: 'Anuluj', // Cancel
	tntTenantMembers: 'Członkowie Organizacji', // Organization Members
	tntSelectMembers: 'Wybierz Członków', // Select Members
	tntUsername: 'Nazwa użytkownika', // Username
	tntEmail: 'Email', // Email
	tntProvider: 'Dostawca', // Provider
	tntMembers: '{{count}} użytkowników', // {{count}} member(s)
	tntDeleteTenantProp: 'Usuń Atrybut Organizacji', // Delete Organization Property
	tntDeleteTenantPropConfirmMessage: 'Czy chcesz usunąć trwale atrybut organizacji "{{tenantPropName}}"?', // Do you want to delete organization property "{{tenantPropName}}" permanently?
	tntDeleteTenant: 'Usuń organizację', // Delete Organization
	tntDeleteTenantConfirmMessage: 'Czy chcesz trwale usunąć organizację "{{tenantName}}"?', // Do you want to delete organization "{{tenantName}}" permanently?
	tntNoMemberTip: 'Brak Użykowników w organizacji.', // No Users in the organization. Please click
	tntAddMember: 'Dodaj.', // Add

	tntTenantBasicInformation: 'Podstawowe Informacje', // Basic Information
	tntTenantRoles: 'Role Organizacji', // Organization Roles
	tntSelectRoles: 'Wybierz Role', // Select Roles
	tntTenantMoveUp: 'W Górę', // Move Up
	tntTenantMoveDown: 'W Dół', // Move Down
	tntExpandAll: 'Rozwiń Wszystkie', // Expand All
	tntCollapseAll: 'Zwiń wszystkie', // Collapse All
	tntActions: 'Akcje', // Actions
	tntGlobal: 'Ogólny', // Global
	tntRoleName: 'Nazwa Roli', // Role Name
	tntUsersNumber: 'Numer Użytkownika', // Users Number
	tntNewOrganization: 'NowaOrganizacja', // NewOrganization
	tntTenantPermissions: 'Uprawnienia', // Permissions
	tntTenantPermissionScope: 'Permission Scope',
	tntModifyPermissionScope: 'Modify Permission Scope',
	tntExpand: 'Rozwiń', // Expand
	tntCollapse: 'Zwiń', // Collapse
	tntOrganizationName: 'Organization Name',
	tntModifyPermissionScopeMessage: 'Are you sure you want to modify the permission scope of the "{{tenantName}}" organization?',
	tntModifyPermissionScopeMessageWithTenant: 'This modification will also delete the following permission scopes for the following sub-organizations:',
	tntModifyPermissionScopeMessageWithRole: 'This modification will also delete the following permissions for the following roles:',
	tntNoRolesTip: 'Brak Ról w organizacji.', // No Roles in the organization.
	tntNoPermissionsTip: 'Brak Ról w organizacji.', // No Roles in the organization.
	tntPermissionsName: 'Nazwa', // Name
	tntPermissionsDescription: 'Opis', // Description
	tntInvisible: 'Niewidoczny', // Invisible
	tntDisableSubView: 'Wyłącz wartość widoku organizacji podrzędnej', // Disable sub-organization view　value
	tntDisableSubEdit: 'Wyłącz wartość edycji organizacji podrzędnej', // Disable Sub-organization edit　value

	tntErrorOrganizationNameNull: 'Nazwa organizacji jest wymagana.', // Organization name is required.
	tntErrorOrganizationNameDuplicated: 'Nazwa organizacji powtarza się.', // Organization name is duplicated.
	tntErrorInvalidCharInName: 'Nazwa organizacji nie może zawierać znaków: < > / \\ $', // Organization name could not contain characters: < > / \\ $
	tntErrorFromPropNull: 'Wartość atrybutu jest wymagana.', // Property value is required.

	tntErrorOrganizationPropNameNull: 'Nazwa właściwości organizacji jest wymagana.', // Organization property name is required.
	tntErrorOrganizationPropNameDuplicated: 'Nazwa właściwości organizacji jest zduplikowana.', // Organization property name is duplicated.

	error_5001: 'Nazwa organizacji nie może być pusta.', // Organization name can not be empty.
	error_5002: 'Organizacja o nazwie "{{TenantName}}" już istnieje.', // Organization with name "{{TenantName}}" already exists.
	error_5003: 'Atrybut organizacji nie może być pusty', // Organization property name can not be empty.
	error_5004: 'Atrybut organizacji o nazwie "{{TenantPropName}}" już istnieje', // Organization property with name "{{TenantPropName}}" already exists
	error_5005: 'Nazwa atrybutu "{{TenantPropName}}" jest zarezerwowana', // The tenant property name "{{TenantPropName}}" is reserved.
	error_5006: 'Wartość z adresu email już istnieje', // The from email "{{FromEmail}}" already exists.

	changeOrganization: 'Move Organization',
	organizationDragAbove: 'Above the current organization',
	organizationDragBelow: 'Below the current organization',
	organizationDragSub: 'Into a sub-organization of the current organization',
	moveOrganization: 'Move Organization "{{tenantName}}"',
	OK: 'OK',
	Cancel: 'Cancel',

	rt_organization: 'organization',
	'rt_organization property': 'organization property',

	error_V2_007_005_0005: 'Invalid organization property values detected.',
	error_V2_007_005_0012: 'The permissions of the child organization cannot be larger than those of the parent organization, so please modify the organization permission scope before proceeding with the operation.',

	strictPermissionOrgTip: 'Organization permission scope limits the maximum permissions of its roles and sub-organizations.',
	loosePermissionOrgTip: 'Organization permission represents the permissions of the Everyone role in the organization.',
	globalPermissionTip: 'Permission for the Global organization cannot be changed.',
};