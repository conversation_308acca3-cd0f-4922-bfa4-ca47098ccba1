export const accountTW = {
	// common
	Add: '添加',
	Edit: '編輯',
	Save: '保存',
	Cancel: '取消',
	Home: '主頁',
	Delete: '刪除',
	Remove: '移除',
	DeleteConfirm: '確認刪除',
	Yes: '是',
	No: '否',
	All: '全部',
	MoveUp: '上移',
	MoveDown: '下移',
	SearchText: '搜索內容',
	Enabled: '啟用',
	Error: '錯誤',
	Close: '關閉',
	More: '更多',
	Created: '創建日期',

	Username: '用戶名',
	Roles: '角色',
	Organizations: '組織',
	Locked: '被鎖定',
	Users: '用戶',
	SelectValue: '選擇值',
	UnlockUser: '解除鎖定',
	UnlockUserConfirm: '確定解除鎖定用戶“{{user}}”嗎？',
	Settings: '設置',
	cgridMore: '更多',

	// Generate token
	gtUser: '用戶',
	gtPassword: '密碼',
	gtAvatarMenu: '隱藏頭像菜單',
	gtWelcomeScreen: '隱藏歡迎屏幕',
	gtGenerateToken: '生成令牌',
	gtToken: '令牌',
	gtTitle: '名稱',
	gtDescription: '描述',
	gtOrgPath: '組織路徑',
	gtCreatedTime: '創建時間',
	gtTokenList: '令牌',
	gtIntegratePortalUrl: '集成門戶的地址',
	gtIntegrateAdminPortalUrl: '集成系統管理的地址',
	gtIntegrateResourcePortalUrl: '集成資源門戶的地址',
	invalid_username_or_password: '無效的用戶名或密碼',
	error_description_default: '獲取令牌失敗，可能是用戶被禁用或刪除。',
	gtRevoke: '撤銷令牌',
	'gtRevoke!btn': '撤銷',
	gtGenerateTokenSuccess: '令牌生成成功',
	gtRevokeTokenSuccess: '令牌撤銷成功',
	gtGenerateUrl: '生成集成地址...',
	gtGenerateUrlTitle: '生成地址',
	gtGetToken: '獲取令牌',
	gtClipboardError: '無法寫入數據到剪貼板',
	gtClipboardSuccess: '成功複製到剪貼板',
	gtRevokeToken: '撤銷令牌',
	gtRevokeTokenConfirmMessage: "確定撤銷令牌“{{title}}”嗎？",
	gtCopyUrl: '複製地址',
	gtCopyOfflineLicenseString: '複製文本',
	gtGenerateQRCode: '生成二維碼',
	gtQRCodeGetOfflineLicenseString: '掃描二維碼，獲取{{operationCode}}',
	gtExpiryTime: '過期時間',
	error_100001: '無效的參數: {{name}}',
	error_100002: '未知錯誤: {{message}}',
	error_100003: '找不到令牌 {{id}}',
	error_100004: '撤銷令牌發生錯誤: {{message}}',
	error_100005: '請確保在“生成集成地址”操作之前，“系統外觀”中的“門戶網站地址”已經設置。',
	error_100006: '門戶網站地址“{{url}}”認證失敗。請求用戶上下文信息失敗。',
	gtExpireTimeInvalid: '過期時間不合法',
	gtExpireTimeLessThanNow: '過期時間不能小於或等於當前時間',
	gtState: '狀態',
	gtValid: '有效',
	gtExpired: '已過期',

	// Customize properties
	AddProperty: '添加屬性',
	EditProperty: '編輯屬性',
	DeleteProperty: '刪除屬性',
	PropertyName: '屬性名稱',
	PropertyValueType: '屬性值類型',
	PropertyValueType_String: '字符串',
	PropertyValueType_Boolean: '布爾',
	PropertyValueType_Integer: '整型',
	PropertyValueType_Float: '浮點型',
	PropertyValueType_Date: '日期',
	PropertyValueType_DateTime: '日期時間',
	AvailableValues: '可選值',
	AvailableValuesDesc: '可選值（每行一個值）',
	ShowInList: '在用戶列表畫面中顯示',
	DeletePropertyConfirmMessage: '確定刪除屬性“{{property}}”嗎？ ',
	PropertyNameRequirement: "屬性值的長度為1-64位，並且不能包含字符'<'，'>'，'$'，'/'和'\\'",
	ContinueUpdate: '繼續修改？ ',
	UpdatePropertyConfirm: '屬性值“{{propValues}}”正在被使用，如果確定修改的話這些值將被清除，需要繼續嗎？ ',
	NewPropertyName: '新屬性名稱',
	NoCustomizePropertiesTip: '當前系統中沒有擴展屬性，如需添加請點擊',
	AllowEdit: '允許用戶編輯',
	Multivalued: '允許多值',
	ShowInProfile: '在用戶信息頁面中顯示',
	MultivaluedChangedWarning: '修改擴展屬性的多值屬性將會導致系統中所有角色的該擴展屬性值發生變化，請謹慎操作！',
	Sensitive: '允許隱藏值',

	// Locked user management
	NoLockedUserTip: '當前系統中沒有鎖定用戶',

	// Concurrence management
	Ban: '斷開會話',
	IpAddress: 'IP地址',
	BrowserCount: '瀏覽器數量',
	UserAgent: '瀏覽器信息',
	loginDate: '上線時間',
	NoLoginUserTip: '當前沒有登錄系統的用戶',
	BanDesc: '斷開當前會話並禁止該用戶在3分鐘之内繼續訪問站點',

	// License
	RegistrationDate: '註冊日期',
	LicenseKey: '產品序列號',
	ServerGeneratedInfo: '離綫授權信息',
	ExpiryDate: '過期日期',
	LicenseStatus: '狀態',
	LicenseInfo: '授權信息',

	Activate: '激活授權',
	Deactivate: '反激活授權',
	Refresh: '刷新',
	RefreshOnline: '在線刷新',
	RefreshOffline: '離線刷新',
	ActivateOffline: '離線激活',
	ActivateOnline: '在線激活',
	DeactivateOffline: '離線反激活',
	ImportLicense: '導入離綫授權信息',
	MigrateOffline: '離線刷新',
	Upgrade: '正式激活',
	RefreshInputLicense: '請輸入完整的產品序列號進行離線刷新',
	UpgradeInputLicense: '請輸入產品序列號進行正式激活',

	DeactivateConfirm: '反激活確認',
	DeactivateConfirmMessage: '確定反激活產品授權“{{license}}”嗎？',
	OfflineDeactivateConfirmMessage: '確定離線反激活產品授權“{{license}}”嗎？\n該操作不可撤銷，確認後將立刻從系統中移除許可證，並產生離線反激活憑證。',
	Succeed: '成功',
	RefreshConfirm: '刷新確認',
	RefreshConfirmMessage: '確定刷新產品授權“{{license}}”嗎？ ',
	getOfflineLicenseDataTips: '點擊“$t(getOfflineLicenseData)“按鈕獲取授權數據，然後在下方輸入獲取到的信息以完成{{action}}。如果您已有有效的授權數據，可直接輸入內容並{{action}}。',
	obtainOfflineLicenseDataTips: '請複製或者生成二維碼，將{{offlineCode}}發送給售後或者銷售人員，為您提供離線授權信息。',
	inputOfflineLicenseData: '請將離線授權信息拷貝至此處',
	getOfflineLicenseData: '獲取離線授權信息',
	offlineLicenseData: '離線授權信息',
	offlineActivationCode: '離線激活憑證',
	offlineRefreshCode: '離線刷新憑證',
	offlineDeactivationCode: '離線反激活憑證',

	ActivateSuccess: '激活成功',
	ActivateSuccessMessage: '產品授權激活成功。',
	RefreshSuccess: '刷新成功',
	RefreshSuccessMessage: '產品授權刷新成功。',
	DeactivateSuccess: '反激活成功',
	DeactivateSuccessMessage: '產品授權反激活成功。',
	OfflineDeactivateCloseConfirm: '請確認您已保存離綫反激活憑證。關閉對話框後，您將無法重新生成用於反激活授權的信息',
	OK: '確定',

	Included: '包含',
	Unlimited: '不限',
	NoExpire: '永不過期',

	KeyType: '授權類型',
	KeyTypeTrial: '試用',
	KeyTypePerpetual: '永久',
	KeyTypeAnnual: '年度',
	Expired: '已過期',
	VersionNotMatch: '版本不符合',
	MismatchDeployment: '部署方式不符合',
	GracePeriod: '缓冲期',
	Licensed: '有效',
	needMigrate: '需要刷新',
	invalid: '无效',
	tempActivated: "臨時",

	rptModule: '報表模塊',
	rptDocCount: '報表文檔數',
	rptConcurrence: '報表並發用戶',
	dbdModule: 'BI模塊',
	dbdDocCount: 'BI文檔數',
	dbdConcurrence: 'BI並發用戶',
	dataMonitoringModule: '數據監控',
	wynSheetModule: 'Spread Sheet',
	wynSheetDocCount: 'Spread Sheet Documents Count',
	dcsDocCount: '數據源個數',
	concurrence: '並發用戶數',
	clpFeature: '自定義語言包',
	nlpFeature: 'AI對話分析',
	ServerCount: '節點數',
	IsTrial: '是否試用',
	True: '是',
	False: '否',
	NoLicenseTip: '當前環境無產品授權',
	BuiltinTrialLicenseTip: '產品試用將於 {{expireDate}} 過期',
	BuiltinTrialHasExpiredTip: '產品試用已過期',
	OldLicenseShouldBeDeactivated: '已經激活了新版授權祕鑰,請確保舊版授權祕鑰被反激活',
	MigrateByTheActionBtn: '請通過右方的按鈕刷新當前授權',
	ContactToMarketToMigrate: '當前系統上激活了多箇舊版授權密鑰。請聯繫我們合併授權密鑰',
	InvalidLicenseCannotMigrate: '狀態爲"{{status}}"的舊版授權祕鑰不支持遷移，請直接反激活',
	hasFormalKeyToMigrate: '檢測到有正式的舊版授權祕鑰等待遷移，試用授權請直接反激活',
	deactivateStep: '要在離線模式下反激活授權，請按照以下步驟操作。',
	deactivateStepEn1: '1. 在一臺可以上網的機器上打開我們的離線授權反激活頁面，並使用上面顯示的離線反激活憑證來反激活授權。',
	deactivateStepCn1: '1. 請將離線反激活憑證發送給我們。我們將幫助您反激活授權。',
	deactivateStep2: '2. 當授權成功反激活時，請關閉當前窗口。否則，請妥善保存離線反激活憑證並與我們聯繫。',
	deactivateOfflinePage: '離線授權反激活頁面',
	activateStep: '要在離線模式下激活授權，請按照以下步驟操作。',
	activateStepEn1: '1. 在一臺可以上網的機器上打開我們的離線授權激活頁面，並使用上面顯示的離線激活憑證獲取離線授權信息。',
	activateStepCn1: '1. 請將離線激活憑證提供給我們，我們將回復給您離線授權信息。',
	activateStep2: '2. 在獲取離線授權信息後，點擊導入離綫授權信息按鈕。',
	activateOfflinePage: '離線授權激活頁面',
	licenseKeyInvalid: '產品序列號無效',
	detail: '查看詳情',
	licenseInfoDetail: '授權明細',

	// Custom visual license
	registerCVLicense: '添加正式授權',
	'Import!title': '導入',
	'ImportCancel!title': '取消',
	uploadingInfo: "'{{fileName}}' 正在上傳...",
	uploadedInfo: "'{{fileName}}' 已上傳。",
	importingInfo: "'{{fileName}}' 正在導入...",
	LicenseType: '類型',
	LicenseType_Trial: '試用版',
	LicenseType_Perpetual: '正式版',
	'license-info!title': '該授權只能用於主版本相同的視覺化元件。',
	'validCVLicense!title': '有效的視覺化元件授權',
	'invalidCVLicense!title': '無效的視覺化元件授權',
	'disabledCVLicense!title': '該視覺化元件授權和Wyn授權不匹配。',
	'cvNotFoundLicense!title': '視覺化元件{{name}} ({{version}})未找到，請先上傳該視覺化元件。',
	'existedCVLicense!title': '已經存在一個視覺化元件授權，如果你想覆蓋已有授權，請手動勾選。\n\n類型: {{licenseType}}\n註冊日期: {{registerDate}}\n過期日期: {{expirationDate}}\n授權明細: {{licenseInfo}}',
	'expiredCVLicense!title': '該視覺化元件授權已過期。',
	'unmatchedCVVersionLicense!title': '視覺化元件{{name}}已存在，但主版本不匹配，請先上傳對應版本的視覺化元件。',

	// System configurations
	AddProvider: '添加用戶提供程序',
	ProviderName: '用戶提供程序名稱',
	Description: '描述',
	AddSecurityProvider: '添加用戶安全提供程序',
	RemoveSecurityProvider: '移除用戶安全提供程序',
	RemoveSecurityProviderConfirm: '確定移除用戶安全提供程“{{provider}}”嗎？ ',

	// Client Management
	AllowedGrantTypes: '允許的授權方式',
	AllowedScopes: '允許的訪問範圍',
	AllowedUris: '允許的訪問地址',
	AccessTokenLifetime: '訪問令牌的生命週期',

	// Security Providers and External Providers
	'account_provider_local': '內置賬戶',
	'account_provider_local_description': '由本地身份認證服務提供的內置賬戶管理系統。 ',
	'account_provider_WeChat4Work': '企業微信',
	'account_provider_WeChat4Work_description': '由企業微信提供的賬戶管理系統。 ',
	'account_provider_AD Security Provider': '活動目錄',
	'account_provider_AD Security Provider_description': '活動目錄用戶安全提供程序。 ',
	'account_provider_DingTalk': '釘釘',
	'account_provider_DingTalk_description': '由釘釘提供的賬戶管理系統',

	NoSecurityProviderTip: '您當前使用的是內建賬戶模式進行登錄',
	NoExternalProviderTip: '當前未添加企業用戶',
	NoAvailableSecurityProvidersTip: '當前沒有咳喲可用的用戶安全提供程序',

	'ShowPassword': '顯示密碼',
	'HidePassword': '隱藏密碼',

	EnableExternalLoginProviderExplainText: '要啟用外部用戶提供程序，你需要正確地填寫這些帶星號的配置項，然後你就可以同步該應用程序中的數據並且啟用其它的一些相關功能，比如單點登錄，掃碼登錄和自動數據同步。',
	DataSyncingExplainText: "數據同步功能會同步外部用戶提供程序授權的所有組織，角色和用戶信息，數據同步功能會覆蓋原先已經存在的數據，並刪除組織名稱和角色名稱中的特殊字符（'/'，'\\' ，'<'，'>'和'$'）。",
	DataSyncingFailsExplainText: '注意：如果存在同名的角色，或者在組織結構的同一層級中存在相同的組織名稱，那麼數據同步會失敗。重複的用戶名將會被格式化為類似“用戶名[用戶ID]”這樣的格式。',

	SyncData: '同步數據',
	SyncingData: '同步數據中...',
	SyncDataSuccessTitle: "数据同步成功",
	SyncDataSuccess: "外部用戶提供者的數據同步成功。",
	SyncDataDesc: "數據同步之後，原先的數據將會被覆蓋，組織和角色名稱中的特殊字符（‘/’，‘\\’，‘<’，‘>’和‘$’）將被刪除。",

	SPTestDefaultResult: '沒有測試結果。',
	SPTestSuccessResult: '登錄測試成功。',
	SPTestFailResult: '登錄測試失敗。',
	LoginTest: '登錄測試',
	UserId: '用戶ID',
	UserName: '用戶名',
	UserContext: '用戶上下文',
	Exception: '異常信息',
	ErrorMessage: '錯誤消息',
	SPShowDetail: '顯示詳細信息',
	SPHideDetail: '隱藏詳細信息',
	Password: '密碼',
	Test: '測試',
	Testing: '測試中...',
	CustomParam: '自定義參數',
	CustomParamDescribe: '自定義參數由一些鍵值對組成，每行一個鍵值對，鍵與值之間使用“:”分割。',

	'setting_item_name!ad security provider!server url': '服務器地址',
	'setting_item_desc!ad security provider!server url': '服務器地址',
	'setting_item_name!ad security provider!admin user': '管理員用戶名',
	'setting_item_desc!ad security provider!admin user': '管理員用戶名',
	'setting_item_name!ad security provider!admin password': '管理員密碼',
	'setting_item_desc!ad security provider!admin password': '管理員密碼',
	'setting_item_name!ad security provider!admin groups': '管理員組名稱',
	'setting_item_desc!ad security provider!admin groups': '管理員組名稱',
	'setting_item_name!ad security provider!use ssl/tls': '使用安全連接',
	'setting_item_desc!ad security provider!use ssl/tls': '使用安全連接',
	'setting_item_name!ad security provider!user context': '用戶上下文屬性',
	'setting_item_desc!ad security provider!user context': '用戶上下文屬性',

	'setting_item_name!open ldap security provider!server url': '服務器地址',
	'setting_item_desc!open ldap security provider!server url': '服務器地址',
	'setting_item_desc!open ldap security provider!admin user': '管理員用戶名',
	'setting_item_name!open ldap security provider!admin user': '管理員用戶名',
	'setting_item_desc!open ldap security provider!admin password': '管理員密碼',
	'setting_item_name!open ldap security provider!admin password': '管理員密碼',
	'setting_item_desc!open ldap security provider!admin groups': '管理員組名稱',
	'setting_item_name!open ldap security provider!admin groups': '管理員組名稱',
	'setting_item_desc!open ldap security provider!user name': '用戶名屬性',
	'setting_item_name!open ldap security provider!user name': '用戶名屬性',
	'setting_item_desc!open ldap security provider!user display name': '用戶顯示名稱屬性',
	'setting_item_name!open ldap security provider!user display name': '用戶顯示名稱屬性',
	'setting_item_desc!open ldap security provider!use member chain rule group search': '使用成員鏈規則組搜索',
	'setting_item_name!open ldap security provider!use member chain rule group search': '使用成員鏈規則組搜索',
	'setting_item_desc!open ldap security provider!use ssl/tls': '使用安全連接',
	'setting_item_name!open ldap security provider!use ssl/tls': '使用安全連接',
	'setting_item_desc!open ldap security provider!user context': '用戶上下文屬性',
	'setting_item_name!open ldap security provider!user context': '用戶上下文屬性',

	'setting_item_name!dingtalk!corpid': 'CorpId',
	'setting_item_desc!dingtalk!corpid': 'CorpId',
	'setting_item_name!dingtalk!appkey': 'AppKey',
	'setting_item_desc!dingtalk!appkey': 'AppKey',
	'setting_item_name!dingtalk!appsecret': 'AppSecret',
	'setting_item_desc!dingtalk!appsecret': 'AppSecret',
	'setting_item_name!dingtalk!agentid': 'AgentId',
	'setting_item_desc!dingtalk!agentid': 'AgentId',
	'setting_item_name!dingtalk!maxconcurrentrequests': '最大並發請求數',
	'setting_item_desc!dingtalk!maxconcurrentrequests': '數據同步時允許的最大並發請求數',
	'setting_item_name!dingtalk!qrcodeappid': '掃碼登錄AppId',
	'setting_item_desc!dingtalk!qrcodeappid': '掃碼登錄程序的Id',
	'setting_item_name!dingtalk!qrcodeappsecret': '掃碼登錄AppSecret',
	'setting_item_desc!dingtalk!qrcodeappsecret': '掃碼登錄程序的密碼',
	'setting_item_name!dingtalk!enableqrcodelogin': '啟用掃碼登錄',
	'setting_item_desc!dingtalk!enableqrcodelogin': '允許通過掃描釘釘的二維碼登錄來Wyn',
	'setting_item_name!dingtalk!enablesendingmessage': '允許發送消息',
	'setting_item_desc!dingtalk!enablesendingmessage': '允許Wyn發送消息給釘釘',
	'setting_item_name!dingtalk!enableautomaticsynchronization': '啟用自動同步',
	'setting_item_desc!dingtalk!enableautomaticsynchronization': '啟用自動同步釘釘的數據到Wyn的功能',
	'setting_item_name!dingtalk!automaticsynchronizationinterval': '同步時間間隔',
	'setting_item_desc!dingtalk!automaticsynchronizationinterval': '自動同步釘釘數據的時間間隔，以小時為單位，最小間隔為一個小時',
	'setting_item_name!dingtalk!hiderootorganizationinloginpage': '在登錄頁面隱藏根組織',
	'setting_item_desc!dingtalk!hiderootorganizationinloginpage': '在登錄頁面中隱藏虛擬根組織（钉钉）',

	'setting_item_name!wechat4work!corpid': '企業ID',
	'setting_item_desc!wechat4work!corpid': '企業ID',
	'setting_item_name!wechat4work!secret': 'Secret',
	'setting_item_desc!wechat4work!secret': 'Secret',
	'setting_item_name!wechat4work!agentid': 'AgentId',
	'setting_item_desc!wechat4work!agentid': 'AgentId',
	'setting_item_name!wechat4work!maxconcurrentrequests': '最大並發請求數',
	'setting_item_desc!wechat4work!maxconcurrentrequests': '數據同步時允許的最大並發請求數',
	'setting_item_name!wechat4work!enableqrcodelogin': '啟用掃碼登錄',
	'setting_item_desc!wechat4work!enableqrcodelogin': '允許通過掃描企業微信的二維碼來登錄Wyn',
	'setting_item_name!wechat4work!enablesendingmessage': '允許發送消息',
	'setting_item_desc!wechat4work!enablesendingmessage': '允許Wyn發送消息給企業微信',
	'setting_item_name!wechat4work!enableautomaticsynchronization': '啟用自動同步',
	'setting_item_desc!wechat4work!enableautomaticsynchronization': '啟用自動同步企業微信的數據到Wyn的功能',
	'setting_item_name!wechat4work!automaticsynchronizationinterval': '同步時間間隔',
	'setting_item_desc!wechat4work!automaticsynchronizationinterval': '自動同步企業微信數據的時間間隔，以小時為單位，最小間隔為一個小時',
	'setting_item_name!wechat4work!hiderootorganizationinloginpage': '在登錄頁面隱藏根組織',
	'setting_item_desc!wechat4work!hiderootorganizationinloginpage': '在登錄頁面中隱藏虛擬根組織（企业微信）',

	// Security Settings
	EnableStrongPasswordPolicy: '啟用強密碼策略',
	EnableStrongPasswordPolicyDescription: '強密碼策略要求用戶的密碼必須同時包含大寫字母，小寫字母和數字，長度在8-150位之間。默認不啟用強密碼策略，只要求密碼長度在1-150位之間，可以包含任何字符。 ',
	UserLockedTime: '用戶被鎖定時長（分鐘）',
	UserLockedTimeDescription: '用戶連續輸入錯誤的密碼超過5次將被鎖定，在被鎖定的時間段內即使用戶輸入正確的密碼也無法登錄，直到鎖定時間結束。如果想要禁用鎖定功能，將鎖定時間設為0即可。 ',
	AllowUserResetPassword: '允許用戶重置密碼',
	AllowUserResetPasswordDescription: '啟用這個選項以允許用戶可以重置自己的密碼。',
	CookieLifetimeSettings: 'Cookie過期時間設置（天）',
	DefaultCookieLifetime: '默認時間',
	CookieLifetimeForRememberLogin: '記住登錄時間',
	DefaultCookieLifetimeDesc: '默認cookie有效期應該介於0-30天之間。',
	CookieLifetimeForRememberLoginDesc: '記住登錄時間的cookie有效期應該介於1-365天之間。',
	CookieLifetimeDescription: '如果用戶在登錄的時候勾選了“記住登錄”選項，那麽生成的cookie的過期時間就是“記住登錄時間”，否則，cookie的過期時間就是“默認時間”，0天表示生成的cookie的過期時間為會話時間（瀏覽器關閉則會話結束）。默認cookie有效期應該介於0-30天之間，記住登錄時間的cookie有效期應該介於1-365天之間。',
	ForceChangePasswordOnFirstLogin: '首次登錄修改密碼',
	ForceChangePasswordOnFirstLoginDescription: '開啓該選項后，用戶在首次登錄時需要修改密碼。',
	ForcePasswordExpiration: '强制密碼過期',
	ForcePasswordExpirationDescription: '開啓該選項后，用戶的密碼會在指定的時間之後過期，過期後用戶需要更改密碼。密碼有效期的範圍為15-365天，默認有效期為90天。',
	PasswordExpirationDays: '密码有效期（天）',
	IntegrationClientSecret: '集成客戶端密鑰',
	IntegrationClientSecretDescription: '你可以在這裏修改集成客戶端的密鑰，這個密鑰會在生成集成令牌的時候使用。密鑰只能包含字母，數字和符號\'-\'，\'_\'，\'.\'，長度應該在16-64位之間。',

	// Claim Mappings
	ClaimName: '名稱',
	UserPropName: '用戶屬性',
	AddClaim: '添加',
	DeleteClaim: '刪除',
	DeleteClaimConfirmMessage: "確定刪除用戶上下文屬性'{{claim}}'嗎？",
	Create: '新建',

	// Two Factor Authentication
	defaultSubject: 'Wyn登錄驗證碼',
	defaultBodyTemplate: '您的登錄驗證碼是 {@code}，有效期為5分鐘，請勿告知他人。',
	SMS: '短信',
	EMAIL: '郵箱',
	EnableTFA: '啓用',
	Lifetime: '有效期（分鐘）',
	TFAType: '驗證方式',
	accessKeyId: 'AccessKeyId',
	accessKeySecret: 'AccessKeySecret',
	signName: '簽名',
	templateCode: '模板代碼',
	subject: '郵件主題',
	bodyTemplate: '郵件正文',
	SendVerificationCodeFailed: '發送驗證碼失敗，原始錯誤信息是：{{error}}',
	SendVerificationCodeSuccess: '發送驗證碼成功，請確保您已經收到驗證碼，然後再保存設置。',
	ShowSecret: '顯示密碼',
	HideSecret: '隱藏密碼',
	TestTFA: '測試設置',
	EnableTFADescription: '啓用雙因子認證后，所有本地用戶（用戶提供者為"local"的用戶）在登錄的時候都需要提供驗證碼，所以請務必保證所有的本地用戶都設置了正確的電話號碼或郵箱地址。',
	BodyTemplateDescription: '郵件正文中必須包含代碼“{@code}”，在發送郵件的時候它將會被替換爲真實的驗證碼。',
	Configure: '配置',
	NoEmailSettingsWarning: '注意：您還沒有在通知中心完成您的郵箱信息配置。',
	TFA_EmptyMobile: '用戶電話號碼為空。',
	TFA_EmptyEmail: '用戶郵箱地址爲空。',
	TFA_InvalidSettings: '檢測到非法的雙因子認證設置。',
	TFA_InvalidEmailSettings: '檢測到非法的郵箱設置。',
	TFA_NoAvailableEmailSender: '沒有可用的郵件發送者。',

	// Inactive Session Settings
	deleteUser: '刪除用戶',
	providerPlaceText: '提供者',
	namePlaceText: '用戶名',
	UserName: '用戶名',
	Provider: '提供者',
	LoginPage: '登錄頁面',
	SessionDisconnectedPage: '會話斷開頁面',
	CustomAddress: '自定義地址',
	Enable: '啓用',
	EnableInactiveSessionDescription: '此功能開啓後，不活動的用戶會話將會在指定時間之後被斷開。',
	Timeout: '超時時間（分鐘）',
	RedirectURL: '重定向地址',
	UnrestrictedUsers: '白名單用戶',
	addUser: '添加外部用戶',
	selectUsers: '添加内部用戶',
	AvailableUsers: '用戶列表',
	unlock: '解鎖',

	// Identity Server User Controller Error Code Range 1001 ~ 1999
	error_1001: '無效的PageSize或者PageNumber。',
	error_1002: '用戶名不能為空。',
	error_1003: '郵件地址不能為空。',
	error_1004: '密碼必須同時包括字母和數字，長度限制8～150。',
	error_1005: '用戶名已經存在。',
	error_1006: '郵件地址已經存在。',
	error_1007: '角色 \'{{role}}\' 不存在。',
	error_1008: '無效的參數。',
	error_1009: '用戶沒有找到。',
	error_1010: '用戶已經存在。',
	error_1011: '擴展屬性 \'{{prop}}\' 不存在。',
	error_1012: '\'{{value}}\' 不是擴展屬性 \'{{prop}}\'的合法值。',
	error_1013: '保存數據到數據庫失敗。',
	error_1014: '用戶名不能修改。',
	error_1015: '沒有上傳文件。',
	error_1016: '沒有可見的Sheet。',
	error_1017: '模版格式不正確。',
	error_1018: '用戶找不到。',
	error_1019: '用戶 \'{{id}}\' 沒有被鎖定。',
	error_1020: '無效的密碼。',
	error_1021: '無效的安全碼。',
	error_1022: '請求來自被禁止的IP。',
	error_1023: '語言不支持。',
	error_1024: '用戶手機號碼已經存在。',
	error_1025: '非法的郵箱地址。',

	// Import users error
	error_1100: "行 [{{rowIndex}}]: 用戶ID '{{userId}}' 已經存在。",
	error_1101: "行 [{{rowIndex}}]: 用戶名不能為空。",
	error_1102: "行 [{{rowIndex}}]: 用戶名 '{{userName}}' 已經存在。",
	error_1103: "行 [{{rowIndex}}]: 用戶郵箱不能為空。",
	error_1104: "行 [{{rowIndex}}]: 用戶郵箱 '{{userEmail}}' 已經存在。",
	error_1105: "行 [{{rowIndex}}]: 用戶手機號碼 '{{userMobile}}' 已經存在。",
	error_1106: "行 [{{rowIndex}}]: 用戶密碼不能為空。",
	error_1107: "行 [{{rowIndex}}]: 非法的密碼格式。密碼應該同時包含大寫字母，小寫字母和數字，長度在8-150位之間。",
	error_1108: "行 [{{rowIndex}}]: 非法的創建時間 '{{creationTime}}'。",
	error_1109: "行 [{{rowIndex}}]: 字段“是否啟用”指定了非法值 '{{value}}'。",
	error_1110: "行 [{{rowIndex}}]: 角色 '{{role}}' 不存在。",
	error_1111: "行 [{{rowIndex}}]: 非法的郵箱地址 '{{userEmail}}'。",

	// Identity Server Role Controller Error Code Range 2001 ~ 2999
	error_2001: '角色 \'{{name}}\' 已經存在',
	error_2002: '角色名稱和權限名稱衝突',
	error_2003: '內置角色不能被刪除。 ',
	error_2004: "不能修改角色 'everyone' 的成員。",
	error_2005: "不能把用戶‘admin’從角色‘administrator’中刪除。",
	error_2006: "不能修改角色‘administrator’的權限。",
	// Identity Server Claim Mapping Errors
	error_3001: "用戶上下文屬性'{{claimName}}'已經存在。",
	error_3002: "Id為'{{claimId}}'的用戶上下文屬性不存在。",
	error_3003: "用戶自定義屬性'{{propName}}'不存在。",
	error_3004: "內置的用戶上下文屬性不能修改。",
	error_3005: "內置的用戶上下文屬性不能刪除。",
	error_3006: "非法的用戶上下文屬性名。用戶上下文屬性的長度應該在1-64位之間，並且不能包含字符'<'，'>'，'/'，'\\'，'$'和一些非法的關鍵字。",
	// Identity Server Custom Property Errors
	error_4001: "擴展屬性‘{{propName}}’已經存在。",
	error_4002: "擴展屬性‘{{propName}}’不存在。",
	error_4003: "Id為‘{{propId}}’的擴展屬性不存在。",
	error_4004: "非法的擴展屬性名。擴展屬性名的長度應該在1-64位之間，並且不能包含字符'<'，'>'，'/'，'\\'，'$'和一些非法關鍵字。",
	// Identity Server Tenant Errors
	error_5001: "組織名不能為空。",
	error_5002: "組織‘{{tenantName}}’已經存在。",
	error_5003: "組織屬性名不能為空。",
	error_5004: "組織屬性‘{{tenantPropName}}’不存在。",
	error_5005: "組織屬性‘{{tenantPropName}}’為保留屬性。",
	error_5006: "組織發件人郵箱‘{{fromEmail}}’已經被佔用。",
	// External Login Provider Errors
	error_6001: "外部用戶提供程序“{{providerName}}”不存在。",
	error_6002: "外部用戶提供程序“{{providerName}}”的消息發送功能沒有啟用。",
	error_6003: "發送消息給外部用戶提供程序“{{providerName}}”失敗了, 錯誤碼: {{errCode}}, 錯誤描述: {{errMsg}}",
	error_6004: "沒有檢測到合法的消息接收者。",
	error_6005: "檢測到有重複的用戶名稱“{{userName}}”。",
	error_6006: "檢測到有重複的角色名稱“{{roleName}}”。",
	error_6007: "檢測到同一層級下有重複的組織名稱“{{organizationName}}”。",
	error_6008: "數據同步失敗，原始的錯誤描述是: {{errMsg}}",
	// Security Provider Errors
	error_7001: '安全提供程序“{{providerName}}”未啟用。',
	error_7002: '加載安全提供程序“{{providerName}}”失敗。',
	error_7003: '錯誤的用戶或者密碼。',
	error_7004: '用戶名和密碼不能為空。',
	error_7005: '傳遞的參數無效。',
	error_7006: '使用安全提供程序登錄失敗。',
	// System Config Errors
	error_9001: '非法的cookie有效期。默認cookie有效期應該介於0-30天之間，記住登錄時間的cookie有效期應該介於1-365天之間。',

	//LicenseActiveResult
	ActivateError: '激活授權失敗',
	OfflineActivateError: '離綫激活授權失敗',
	RefreshError: '刷新授權失敗',
	OfflineRefreshError: '離綫刷新授權失敗',
	DeactivateError: '反激活授權失敗',
	OfflineDeactivateError: '離綫反激活授權失敗',
	ImportLicenseError: '導入授權信息失敗',
	GetLicenseError: '獲取授權信息失敗',
	UpgradeLicenseError: '正式激活失敗',
	GetClientIdentityCodeError: '獲取客戶識別碼錯誤',

	V2_007_009_0001: "激活授權錯誤",
	V2_007_009_0002: "離綫激活授權錯誤",
	V2_007_009_0003: "反激活授權錯誤",
	V2_007_009_0004: "離綫反激活授權錯誤",
	V2_007_009_0005: "刷新授權錯誤",
	V2_007_009_0006: "導入授權信息錯誤",
	V2_007_009_0007: "舊授權不存在",
	V2_007_009_0008: "刷新授權錯誤(迁移)",
	V2_007_009_0009: "離綫刷新授權錯錯誤(迁移)",
	V2_007_009_0010: "獲取授權信息错误",
	V2_007_009_0011: '離綫刷新授權錯錯誤',
	V2_007_009_0012: '臨時激活錯誤',
	V2_007_009_0013: '正式激活錯誤',
	V2_007_009_0014: '獲取客戶識別碼錯誤',

	licenseErr_1000: '請求錯誤。',
	licenseErr_1001: '無效的數據格式(1001)。',
	licenseErr_1002: '無效的數據格式(1002)。',
	licenseErr_1003: '無效日期(1003)。請檢查您的系統時間並重試。',
	licenseErr_1004: '無效的授權密鑰(1004)。請檢查您的授權密鑰並重試。',
	licenseErr_1005: '無效的版本信息(1005)。',
	licenseErr_1006: '無效的機器信息(1006)。',
	licenseErr_1007: '無效的激活信息(1007)。客戶端激活狀態和授權激活服務器記錄的信息不一致。請嘗試反激活原有的授權，然後重新激活。',
	licenseErr_1008: '無效的數據格式(1008)。',
	licenseErr_1009: '無效的授權密鑰(1009)。請嘗試反激活原有的授權，然後重新激活。',
	licenseErr_2000: '授權信息錯誤(2000)。',
	licenseErr_2001: '授權信息錯誤(2001)。',
	licenseErr_2002: '授權信息錯誤(2002)。',
	licenseErr_2003: '授權信息錯誤(2003)。授權信息失效，請嘗試重新獲取。',
	licenseErr_2004: '授權信息錯誤(2004)。授權信息失效，請嘗試重新獲取。',
	licenseErr_3000: '授權信息錯誤(3000)。',
	licenseErr_3001: '授權密鑰已被禁用(3001)。',
	licenseErr_3002: '授權密鑰已過期(3002)。',
	licenseErr_3003: '授權信息錯誤(3003)。該授權碼不可用於產品當前版本。',
	licenseErr_3004: '授權信息錯誤(3004)。該授權碼不可用於產品當前版本。',
	licenseErr_4000: '操作失敗(4000)。',
	licenseErr_4001: '當前授權密鑰不支持離綫激活。',
	licenseErr_4002: '當前授權密鑰不支持反激活。',
	licenseErr_4003: '當前的授權密鑰已超過可用數量。',
	licenseErr_4004: '當前授權碼操作過於頻繁。',
	licenseErr_5000: '服務器錯誤(5000)。',
	licenseErr_5001: '服務器錯誤(5001)。',
	licenseErr_5002: '服務器錯誤(5002)。',
	licenseErr_6000: '當前授權密鑰無法遷移到當前版本(6000)。請聯係我們。',
	licenseErr_6001: '當前授權密鑰無法遷移到當前版本(6001)。請聯係我們。',
	licenseErr_6002: '當前授權密鑰無法遷移到當前版本(6002)。請聯係我們。',
	licenseErr_6003: '當前授權密鑰無法遷移到當前版本(6003)。請聯係我們。',
	licenseErr_6004: '當前授權密鑰無法遷移到當前版本(6004)。請聯係我們。',
	licenseErr_7001: '臨時激活失敗，授權文件無效(7001)。請聯繫我們。',
	licenseErr_7002: '臨時激活失敗，加密方式無效(7002)。請聯繫我們。',
	licenseErr_7003: '臨時激活失敗，授權密鑰無效(7003)。請聯繫我們。',
	licenseErr_7004: '臨時激活失敗，授權信息無效(7004)。請聯繫我們。',
	licenseErr_7005: '臨時激活失敗，授權信息過期(7005)。請聯繫我們。',
	licenseErr_7006: '臨時激活失敗，簽名校驗失敗(7006)。請聯繫我們。',
	licenseErr_65535: '未知錯誤，請聯係我們。',

	licenseErr_0000_0001: '已經添加了一個授權。',
	licenseErr_0000_0002: '無效的授權碼。',
	licenseErr_0000_0003: '解密授權內容失敗。',
	licenseErr_0000_0004: '未找到授權。',
	licenseErr_0001_0001: '添加授權失敗，請查看日志獲取更多信息。',
	licenseErr_0001_0002: '生成離綫激活信息失敗。',
	licenseErr_0002_0001: '反激活失敗， 請查看日志獲取更多信息。',
	licenseErr_0002_0002: '生成離綫反激活信息失敗。',
	licenseErr_0003_0001: '刷新授權失敗，請查看日志獲取更多信息。',
	licenseErr_0003_0002: '刷新授權失敗，沒有找到授權碼用於刷新。',
	licenseErr_0003_0003: '刷新授權失敗，生成離線刷新信息失敗。',
	licenseErr_0003_0004: '刷新授權失敗，輸入的序列號不匹配。',
	licenseErr_0004_0001: '導入授權信息失敗，請查看日志獲取更多信息。',
	licenseErr_0005_0001: '找不到對應的授權信息。',
	licenseErr_0005_0002: '當前操作需要一個舊版本授權信息，但是沒有找到符合條件的條目。',
	licenseErr_0006_0001: '遷移授權失敗，請查看日志獲取更多信息。',
	licenseErr_0006_0002: '生成離綫操作憑證失敗。',
	licenseErr_0007_0001: '獲取授權信息失敗，請查看日志獲取更多信息。',
	licenseErr_0008_0001: '臨時激活失敗，請查看日志獲取更多信息。',
	licenseErr_0009_0001: '正式激活失敗，請查看日志獲取更多信息。',
	licenseErr_0010_0001: '獲取客戶識別碼失敗，輸入的序列號不匹配。',
	LicenseErr_ImportLicenseError: '導入授權信息失敗，授權信息錯誤。',

	licenseWarn: '授權操作警告',

	licenseWarn_DeactivationFailed: '反激活失敗。',
	licenseWarn_AlmostlyOverFrequency: '您的操作過於頻繁。',
	licenseWarn_AlmostlyOverMaxInstanceCount: '接近授權的最大激活數',

	// Custom Visual License Errors
	GetCustomVisualLicenseError: "獲取視覺化元件授權列表失敗。",
	AnalysisCustomVisualLicenseFileError: "上傳視覺化元件授權文件失敗。",
	GetCustomVisualListError: "獲取視覺化元件列表失敗。",
	ImportCustomVisualLicenseFileError: "導入視覺化元件授權失敗。",
	ImportCustomVisualLicenseFileSuccess: "導入視覺化元件授權成功。",
	DeleteCustomVisualLicenseError: "刪除視覺化元件授權失敗。",

	'rt_claim mapping': '用戶上下文',
	'rt_custom property': '擴展屬性',

	error_V2_007_000_0001: '{{resourceType}}“{{resourceIdentifier}}”未找到。',
	error_V2_007_000_0002: '{{resourceType}}“{{resourceIdentifier}}”不存在或已被刪除。',
	error_V2_007_000_0003: '{{resourceType}}“{{resourceIdentifier}}”已經存在。',
	error_V2_007_000_0006: '您沒有足夠的權限來執行此操作。',
	error_V2_007_000_0010: '發生了未知錯誤“{{message}}”，請查看日志來獲取錯誤的詳細信息。',
	error_V2_007_003_0003: "非法的用戶上下文屬性名。用戶上下文屬性的長度應該在1-64位之間，並且不能包含字符'<'，'>'，'/'，'\\'，'$'和一些非法的關鍵字。",
	error_V2_007_004_0001: "非法的擴展屬性名。擴展屬性名的長度應該在1-64位之間，並且不能包含字符'<'，'>'，'/'，'\\'，'$'和一些非法關鍵字。",
	error_V2_007_004_0004: '提供的可選值"{{InvalidAvailableValue}}"和指定的屬性值類型或者格式不匹配。',

	error_V2_007_006_0004: '檢測到有重複的用戶名稱“{{UserName}}”。',
	error_V2_007_006_0005: '檢測到有重複的角色名稱“{{RoleName}}”。',
	error_V2_007_006_0006: '檢測到同一層級下有重複的組織名稱“{{OrganizationName}}”。',
	error_V2_007_006_0007: '數據同步失敗，原始的錯誤描述是: {{ErrMsg}}',
	error_V2_007_010_0002: '會話過期時間應該在1到120分鐘之間。',
	error_V2_007_010_0003: '重定向地址應該以“/”開頭，或者必須是一個以“http://”或者“https://”開頭的網址。',
	error_V2_007_010_0004: '用戶名和用戶提供者都不能爲空。',

	error_V2_008_002_0001: '請確保在“生成集成地址”操作之前，“系統外觀”中的“門戶網站地址”已經設置。',
	error_V2_008_001_0003: '令牌生成失敗。原始的錯誤信息是：{{error_description}}。',
	error_V2_008_001_0004: '令牌已過期。',

	error_V2_008_003_0001: '使用令牌獲取用戶信息失敗，也許令牌已過期或者被刪除了。',

	customVisualLicenseErr_V2_007_011_0001: '視覺化元件授權文件內容為空。',
	customVisualLicenseErr_V2_007_011_0002: '上傳視覺化元件授權文件失敗。',
	customVisualLicenseErr_V2_007_011_0003: '參數為空或要匯入的視覺化原件授權清單為空。',
	customVisualLicenseErr_V2_007_011_0004: '導入視覺化元件授權失敗。',
	customVisualLicenseErr_V2_007_011_0005: '獲取視覺化元件授權列表失敗。',
	customVisualLicenseErr_V2_007_011_0006: '刪除視覺化元件授權失敗。',
}