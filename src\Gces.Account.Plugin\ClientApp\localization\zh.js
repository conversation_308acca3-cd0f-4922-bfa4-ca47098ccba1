﻿export const accountZH = {
	// common
	Add: '添加',
	Edit: '编辑',
	Save: '保存',
	Cancel: '取消',
	Home: '主页',
	Delete: '删除',
	Remove: '移除',
	DeleteConfirm: '确认删除',
	Yes: '是',
	No: '否',
	All: '全部',
	MoveUp: '上移',
	MoveDown: '下移',
	SearchText: '搜索内容',
	Enabled: '启用',
	Error: '错误',
	Close: '关闭',
	More: '更多',
	Created: '创建日期',

	Username: '用户名',
	Roles: '角色',
	Organizations: '组织',
	Locked: '被锁定',
	Users: '用户',
	SelectValue: '选择值',
	UnlockUser: '解除锁定',
	UnlockUserConfirm: '确定解除锁定用户“{{user}}”吗？',
	Settings: '设置',
	cgridMore: '更多',

	// Generate token
	gtUser: '用户',
	gtPassword: '密码',
	gtAvatarMenu: '隐藏头像菜单',
	gtWelcomeScreen: '隐藏欢迎屏幕',
	gtGenerateToken: '生成令牌',
	gtToken: '令牌',
	gtTitle: '名称',
	gtDescription: '描述',
	gtOrgPath: '组织路径',
	gtCreatedTime: '创建时间',
	gtTokenList: '令牌',
	gtIntegratePortalUrl: '集成门户的地址',
	gtIntegrateAdminPortalUrl: '集成系统管理的地址',
	gtIntegrateResourcePortalUrl: '集成资源门户的地址',
	invalid_username_or_password: '无效的用户名或密码',
	error_description_default: '获取令牌失败，可能是用户被禁用或删除。',
	gtRevoke: '撤销令牌',
	'gtRevoke!btn': '撤销',
	gtGenerateTokenSuccess: '令牌生成成功',
	gtRevokeTokenSuccess: '令牌撤销成功',
	gtGenerateUrl: '生成集成地址...',
	gtGenerateUrlTitle: '生成地址',
	gtGetToken: '获取令牌',
	gtClipboardError: '无法写入数据到剪贴板',
	gtClipboardSuccess: '成功复制到剪贴板',
	gtRevokeToken: '撤销令牌',
	gtRevokeTokenConfirmMessage: "确定撤销令牌“{{title}}”吗？",
	gtCopyUrl: '复制地址',
	gtCopyOfflineLicenseString: '复制文本',
	gtQRCodeGetOfflineLicenseString: '扫描二维码，获取{{operationCode}}',
	gtGenerateQRCode: '生成二维码',
	gtExpiryTime: '过期时间',
	error_100001: '无效的参数: {{name}}',
	error_100002: '未知错误: {{message}}',
	error_100003: '找不到令牌 {{id}}',
	error_100004: '撤销令牌发生错误: {{message}}',
	error_100005: '请确保在“生成集成地址”操作之前，“系统外观”中的“门户网站地址”已经设置。',
	error_100006: '门户网站地址“{{url}}”认证失败。请求用户上下文信息失败。',
	gtExpireTimeInvalid: '过期时间不合法',
	gtExpireTimeLessThanNow: '过期时间不能小于或等于当前时间',
	gtState: '状态',
	gtValid: '有效',
	gtExpired: '已过期',

	// Customize properties
	AddProperty: '添加属性',
	EditProperty: '编辑属性',
	DeleteProperty: '删除属性',
	PropertyName: '属性名称',
	PropertyValueType: '属性值类型',
	PropertyValueType_String: '字符串',
	PropertyValueType_Boolean: '布尔',
	PropertyValueType_Integer: '整型',
	PropertyValueType_Float: '浮点型',
	PropertyValueType_Date: '日期',
	PropertyValueType_DateTime: '日期时间',
	AvailableValues: '可选值',
	AvailableValuesDesc: '可选值（每行一个值）',
	ShowInList: '在用户列表画面中显示',
	DeletePropertyConfirmMessage: '确定删除属性“{{property}}”吗？',
	PropertyNameRequirement: "属性值的长度为1-64位，并且不能包含字符'<'，'>'，'$'，'/'和'\\'",
	ContinueUpdate: '继续修改？',
	UpdatePropertyConfirm: '属性值“{{propValues}}”正在被使用，如果确定修改的话这些值将被清除，需要继续吗？',
	NewPropertyName: '新属性名称',
	NoCustomizePropertiesTip: '当前系统中没有扩展属性，如需添加请点击',
	AllowEdit: '允许用户编辑',
	Multivalued: '允许多值',
	ShowInProfile: '在用户信息页面中显示',
	MultivaluedChangedWarning: '修改扩展属性的多值属性将会导致系统中所有角色的该扩展属性值发生变化，请谨慎操作！',
	Sensitive: '允许隐藏值',

	// Locked  user management
	NoLockedUserTip: '当前系统中没有锁定用户',

	// Concurrence management
	Ban: '断开会话',
	IpAddress: 'IP地址',
	BrowserCount: '浏览器数量',
	UserAgent: '浏览器信息',
	loginDate: '上线时间',
	NoLoginUserTip: '当前没有登录系统的用户',
	BanDesc: '断开当前会话并禁止该用户在3分钟之内继续访问站点',

	// License
	RegistrationDate: '激活日期',
	LicenseKey: '产品序列号',
	ServerGeneratedInfo: '离线授权信息',
	ExpiryDate: '过期日期',
	LicenseStatus: '状态',
	LicenseInfo: '授权信息',

	Activate: '激活',
	Deactivate: '反激活授权',
	Refresh: '刷新',
	RefreshOnline: '在线刷新',
	RefreshOffline: '离线刷新',
	ActivateOffline: '离线激活',
	ActivateOnline: '在线激活',
	DeactivateOffline: '离线反激活授权',
	ImportLicense: '导入离线授权信息',
	MigrateOffline: '离线刷新',
	Upgrade: '正式激活',
	RefreshInputLicense: '请输入完整的产品序列号进行离线刷新',
	UpgradeInputLicense: '请输入产品序列号进行正式激活',

	DeactivateConfirm: '反激活确认',
	DeactivateConfirmMessage: '确定反激活产品授权“{{license}}”吗？',
	OfflineDeactivateConfirmMessage: '确定离线反激活产品授权“{{license}}”吗？\n该操作不可撤销，确认后将立刻从系统中移除许可证，并生成离线反激活凭证。',
	Succeed: '成功',
	RefreshConfirm: '刷新确认',
	RefreshConfirmMessage: '确定刷新产品授权“{{license}}”吗？',
	getOfflineLicenseDataTips: '点击“$t(getOfflineLicenseData)“按钮获取授权数据，然后在下方输入获取到的信息以完成{{action}}。如果您已有有效的授权数据，可直接输入内容并{{action}}。',
	obtainOfflineLicenseDataTips: '请复制或者生成二维码，将{{offlineCode}}发送给售后或者销售人员，为您提供离线授权信息。',
	inputOfflineLicenseData: '请将离线授权信息拷贝至此处',
	getOfflineLicenseData: '获取离线授权信息',
	offlineLicenseData: '离线授权信息',
	offlineActivationCode: '离线激活凭证',
	offlineRefreshCode: '离线刷新凭证',
	offlineDeactivationCode: '离线反激活凭证',

	ActivateSuccess: '激活成功',
	ActivateSuccessMessage: '产品授权激活成功。',
	RefreshSuccess: '刷新成功',
	RefreshSuccessMessage: '产品授权刷新成功。',
	DeactivateSuccess: '反激活成功',
	DeactivateSuccessMessage: '产品授权反激活成功。',
	OfflineDeactivateCloseConfirm: '请确认您已保存离线反激活凭证。关闭对话框后，您将无法重新生成用于反激活授权的信息',
	OK: '确定',

	Included: '包含',
	Unlimited: '不限',
	NoExpire: '永不过期',

	KeyType: '授权类型',
	KeyTypeTrial: '试用',
	KeyTypePerpetual: '永久',
	KeyTypeAnnual: '年度',
	Expired: '已过期',
	VersionNotMatch: '版本不符合',
	MismatchDeployment: '部署方式不符合',
	GracePeriod: '緩衝期',
	Licensed: '有效',
	needMigrate: '需要刷新',
	invalid: '无效',
	tempActivated: "临时",

	rptModule: '报表模块',
	rptDocCount: '报表文档数',
	rptConcurrence: '报表并发用户',
	dbdModule: 'BI模块',
	dbdDocCount: 'BI文档数',
	dbdConcurrence: 'BI并发用户',
	dataMonitoringModule: '数据监控',
	wynSheetModule: 'Spread Sheet',
	wynSheetDocCount: 'Spread Sheet Documents Count',
	dcsDocCount: '数据源个数',
	concurrence: '并发用户数',
	clpFeature: '自定义语言包',
	nlpFeature: 'AI对话分析',
	ServerCount: '节点数',
	IsTrial: '是否试用',
	True: '是',
	False: '否',
	NoLicenseTip: '当前环境无产品授权',
	BuiltinTrialLicenseTip: '产品试用将于 {{expireDate}} 过期',
	BuiltinTrialHasExpiredTip: '产品试用已过期',
	OldLicenseShouldBeDeactivated: '已经激活了新版授权秘钥,请确保旧版授权秘钥被反激活',
	MigrateByTheActionBtn: '请通过右方的按钮刷新当前授权',
	ContactToMarketToMigrate: '当前系统上激活了多个旧版授权密钥。请联系我们合并授权密钥',
	InvalidLicenseCannotMigrate: '状态为"{{status}}"的旧版授权秘钥不支持迁移，请直接反激活',
	hasFormalKeyToMigrate: '检测到有正式的旧版授权秘钥等待迁移，试用授权请直接反激活',
	deactivateStep: '要在离线模式下反激活授权，请按照以下步骤操作。',
	deactivateStepEn1: '1. 在一台可以上网的机器上打开我们的离线授权反激活页面，并使用上面显示的离线反激活凭证来反激活授权。',
	deactivateStepCn1: '1. 请将离线反激活凭证发送给我们。我们将帮助您反激活授权。',
	deactivateStep2: '2. 当授权成功反激活时，请关闭当前窗口。否则，请妥善保存离线反激活凭证并与我们联系。',
	deactivateOfflinePage: '离线授权反激活页面',
	activateStep: '要在离线模式下激活授权，请按照以下步骤操作。',
	activateStepEn1: '1. 在一台可以上网的机器上打开我们的离线授权激活页面，并使用上面显示的离线激活凭证获取离线授权信息。',
	activateStepCn1: '1. 请将离线激活凭证提供给我们，我们将回复给您离线授权信息。',
	activateStep2: '2. 在获取离线授权信息后，点击导入离线授权信息按钮。',
	activateOfflinePage: '离线授权激活页面',
	licenseKeyInvalid: '产品序列号无效',
	detail: '查看详情',
	licenseInfoDetail: '授权明细',

	// Custom visual license
	registerCVLicense: '添加正式授权',
	'Import!title': '导入',
	'ImportCancel!title': '取消',
	uploadingInfo: "'{{fileName}}' 正在上传...",
	uploadedInfo: "'{{fileName}}' 已上传。",
	importingInfo: "'{{fileName}}' 正在导入...",
	LicenseType: '类型',
	LicenseType_Trial: '试用版',
	LicenseType_Perpetual: '正式版',
	'license-info!title': '该授权只能用于主版本相同的可视化插件。',
	'validCVLicense!title': '有效的可视化插件授权',
	'invalidCVLicense!title': '无效的可视化插件授权',
	'disabledCVLicense!title': '该可视化插件授权和Wyn授权不匹配。',
	'cvNotFoundLicense!title': '可视化插件{{name}} ({{version}})未找到，请先上传该可视化插件。',
	'existedCVLicense!title': '已经存在一个可视化插件授权，如果你想覆盖已有授权，请手动勾选。\n\n类型: {{licenseType}}\n注册日期: {{registerDate}}\n过期日期: {{expirationDate}}\n授权明细: {{licenseInfo}}',
	'expiredCVLicense!title': '该可视化插件授权已过期。',
	'unmatchedCVVersionLicense!title': '可视化插件{{name}}已存在，但主版本不匹配，请先上传对应版本的可视化插件。',

	// System configurations
	AddProvider: '添加用户提供程序',
	ProviderName: '用户提供程序名称',
	Description: '描述',
	AddSecurityProvider: '添加用户安全提供程序',
	RemoveSecurityProvider: '移除用户安全提供程序',
	RemoveSecurityProviderConfirm: '确定移除用户安全提供程“{{provider}}”吗？',

	// Client Management
	AllowedGrantTypes: '允许的授权方式',
	AllowedScopes: '允许的访问范围',
	AllowedUris: '允许的访问地址',
	AccessTokenLifetime: '访问令牌的生命周期',

	// Security Providers and External Providers
	'account_provider_local': '内置账户',
	'account_provider_local_description': '由本地身份认证服务提供的内置账户管理系统。',
	'account_provider_WeChat4Work': '企业微信',
	'account_provider_WeChat4Work_description': '由企业微信提供的账户管理系统。',
	'account_provider_AD Security Provider': '活动目录',
	'account_provider_AD Security Provider_description': '活动目录用户安全提供程序。',
	'account_provider_DingTalk': '钉钉',
	'account_provider_DingTalk_description': '由钉钉提供的账户管理系统',

	NoSecurityProviderTip: '您当前使用的是内建账户模式进行登录',
	NoExternalProviderTip: '当前未添加企业用户',
	NoAvailableSecurityProvidersTip: '当前没有可用的用户安全提供程序',

	'ShowPassword': '显示密码',
	'HidePassword': '隐藏密码',

	EnableExternalLoginProviderExplainText: '要启用外部用户提供程序，你需要正确地填写这些带星号的配置项，然后你就可以同步该应用程序中的数据并且启用其它的一些相关功能，比如单点登录，扫码登录和自动数据同步。',
	DataSyncingExplainText: "数据同步功能会同步外部用户提供程序授权的所有组织，角色和用户信息，数据同步功能会覆盖原先已经存在的数据，并删除组织名称和角色名称中的特殊字符（‘/’，‘\\’，‘<’，‘>’和‘$’）。",
	DataSyncingFailsExplainText: '注意：如果存在同名的角色，或者在组织结构的同一层级中存在相同的组织名称，那么数据同步会失败。重复的用户名将会被格式化为类似“用户名[用户ID]”这样的格式。',

	SyncData: '同步数据',
	SyncingData: '同步数据中...',
	SyncDataSuccessTitle: "数据同步成功",
	SyncDataSuccess: "外部用户提供者的数据同步成功。",
	SyncDataDesc: "数据同步之后，原先的数据将会被覆盖，组织和角色名称中的特殊字符（‘/’，‘\\’，‘<’，‘>’和‘$’）将被删除。",

	SPTestDefaultResult: '没有测试结果。',
	SPTestSuccessResult: '登录测试成功。',
	SPTestFailResult: '登录测试失败。',
	LoginTest: '登录测试',
	UserId: '用户ID',
	UserName: '用户名',
	UserContext: '用户上下文',
	Exception: '异常信息',
	ErrorMessage: '错误消息',
	SPShowDetail: '显示详细信息',
	SPHideDetail: '隐藏详细信息',
	Password: '密码',
	Test: '测试',
	Testing: '测试中...',
	CustomParam: '自定义参数',
	CustomParamDescribe: '自定义参数由一些键值对组成，每行一个键值对，键与值之间使用“:”分割。',

	'setting_item_name!ad security provider!server url': '服务器地址',
	'setting_item_desc!ad security provider!server url': '服务器地址',
	'setting_item_name!ad security provider!admin user': '管理员用户名',
	'setting_item_desc!ad security provider!admin user': '管理员用户名',
	'setting_item_name!ad security provider!admin password': '管理员密码',
	'setting_item_desc!ad security provider!admin password': '管理员密码',
	'setting_item_name!ad security provider!admin groups': '管理员组名称',
	'setting_item_desc!ad security provider!admin groups': '管理员组名称',
	'setting_item_name!ad security provider!use ssl/tls': '使用安全连接',
	'setting_item_desc!ad security provider!use ssl/tls': '使用安全连接',
	'setting_item_name!ad security provider!user context': '用户上下文属性',
	'setting_item_desc!ad security provider!user context': '用户上下文属性',

	'setting_item_name!open ldap security provider!server url': '服务器地址',
	'setting_item_desc!open ldap security provider!server url': '服务器地址',
	'setting_item_desc!open ldap security provider!admin user': '管理员用户名',
	'setting_item_name!open ldap security provider!admin user': '管理员用户名',
	'setting_item_desc!open ldap security provider!admin password': '管理员密码',
	'setting_item_name!open ldap security provider!admin password': '管理员密码',
	'setting_item_desc!open ldap security provider!admin groups': '管理员组名称',
	'setting_item_name!open ldap security provider!admin groups': '管理员组名称',
	'setting_item_desc!open ldap security provider!user name': '用户名属性',
	'setting_item_name!open ldap security provider!user name': '用户名属性',
	'setting_item_desc!open ldap security provider!user display name': '用户显示名称属性',
	'setting_item_name!open ldap security provider!user display name': '用户显示名称属性',
	'setting_item_desc!open ldap security provider!use member chain rule group search': '使用成员链规则组搜索',
	'setting_item_name!open ldap security provider!use member chain rule group search': '使用成员链规则组搜索',
	'setting_item_desc!open ldap security provider!use ssl/tls': '使用安全连接',
	'setting_item_name!open ldap security provider!use ssl/tls': '使用安全连接',
	'setting_item_desc!open ldap security provider!user context': '用户上下文属性',
	'setting_item_name!open ldap security provider!user context': '用户上下文属性',

	'setting_item_name!dingtalk!corpid': 'CorpId',
	'setting_item_desc!dingtalk!corpid': 'CorpId',
	'setting_item_name!dingtalk!appkey': 'AppKey',
	'setting_item_desc!dingtalk!appkey': 'AppKey',
	'setting_item_name!dingtalk!appsecret': 'AppSecret',
	'setting_item_desc!dingtalk!appsecret': 'AppSecret',
	'setting_item_name!dingtalk!agentid': 'AgentId',
	'setting_item_desc!dingtalk!agentid': 'AgentId',
	'setting_item_name!dingtalk!maxconcurrentrequests': '最大并发请求数',
	'setting_item_desc!dingtalk!maxconcurrentrequests': '数据同步时允许的最大并发请求数',
	'setting_item_name!dingtalk!qrcodeappid': '扫码登录AppId',
	'setting_item_desc!dingtalk!qrcodeappid': '扫码登录程序的Id',
	'setting_item_name!dingtalk!qrcodeappsecret': '扫码登录AppSecret',
	'setting_item_desc!dingtalk!qrcodeappsecret': '扫码登录程序的密码',
	'setting_item_name!dingtalk!enableqrcodelogin': '启用扫码登录',
	'setting_item_desc!dingtalk!enableqrcodelogin': '允许通过扫描钉钉的二维码登录来Wyn',
	'setting_item_name!dingtalk!enablesendingmessage': '允许发送消息',
	'setting_item_desc!dingtalk!enablesendingmessage': '允许Wyn发送消息给钉钉',
	'setting_item_name!dingtalk!enableautomaticsynchronization': '启用自动同步',
	'setting_item_desc!dingtalk!enableautomaticsynchronization': '启用自动同步钉钉的数据到Wyn的功能',
	'setting_item_name!dingtalk!automaticsynchronizationinterval': '同步时间间隔',
	'setting_item_desc!dingtalk!automaticsynchronizationinterval': '自动同步钉钉数据的时间间隔，以小时为单位，最小间隔为一个小时',
	'setting_item_name!dingtalk!hiderootorganizationinloginpage': '在登录页面隐藏根组织',
	'setting_item_desc!dingtalk!hiderootorganizationinloginpage': '在登录页面中隐藏虚拟根组织（钉钉）',

	'setting_item_name!wechat4work!corpid': '企业ID',
	'setting_item_desc!wechat4work!corpid': '企业ID',
	'setting_item_name!wechat4work!secret': 'Secret',
	'setting_item_desc!wechat4work!secret': 'Secret',
	'setting_item_name!wechat4work!agentid': 'AgentId',
	'setting_item_desc!wechat4work!agentid': 'AgentId',
	'setting_item_name!wechat4work!maxconcurrentrequests': '最大并发请求数',
	'setting_item_desc!wechat4work!maxconcurrentrequests': '数据同步时允许的最大并发请求数',
	'setting_item_name!wechat4work!enableqrcodelogin': '启用扫码登录',
	'setting_item_desc!wechat4work!enableqrcodelogin': '允许通过扫描企业微信的二维码来登录Wyn',
	'setting_item_name!wechat4work!enablesendingmessage': '允许发送消息',
	'setting_item_desc!wechat4work!enablesendingmessage': '允许Wyn发送消息给企业微信',
	'setting_item_name!wechat4work!enableautomaticsynchronization': '启用自动同步',
	'setting_item_desc!wechat4work!enableautomaticsynchronization': '启用自动同步企业微信的数据到Wyn的功能',
	'setting_item_name!wechat4work!automaticsynchronizationinterval': '同步时间间隔',
	'setting_item_desc!wechat4work!automaticsynchronizationinterval': '自动同步企业微信数据的时间间隔，以小时为单位，最小间隔为一个小时',
	'setting_item_name!wechat4work!hiderootorganizationinloginpage': '在登录页面隐藏根组织',
	'setting_item_desc!wechat4work!hiderootorganizationinloginpage': '在登录页面中隐藏虚拟根组织（企业微信）',

	// Security Settings
	EnableStrongPasswordPolicy: '启用强密码策略',
	EnableStrongPasswordPolicyDescription: '强密码策略要求用户的密码必须同时包含大写字母，小写字母和数字，长度在8-150位之间。默认不启用强密码策略，只要求密码长度在1-150位之间，可以包含任何字符。',
	UserLockedTime: '用户被锁定时长（分钟）',
	UserLockedTimeDescription: '用户连续输入错误的密码超过5次将被锁定，在被锁定的时间段内即使用户输入正确的密码也无法登录，直到锁定时间结束。如果想要禁用锁定功能，将锁定时间设为0即可。',
	AllowUserResetPassword: '允许用户重置密码',
	AllowUserResetPasswordDescription: '启用这个选项以允许用户可以重置自己的密码。',
	CookieLifetimeSettings: 'Cookie过期时间设置（天）',
	DefaultCookieLifetime: '默认时间',
	CookieLifetimeForRememberLogin: '记住登录时间',
	DefaultCookieLifetimeDesc: '默认cookie有效期应该介于0-30天之间。',
	CookieLifetimeForRememberLoginDesc: '记住登录时间的cookie有效期应该介于1-365天之间。',
	CookieLifetimeDescription: '如果用户在登录的时候勾选了“记住登录”选项，那么生成的cookie的过期时间就是“记住登录时间”，否则，cookie的过期时间就是“默认时间”，0天表示生成的cookie的过期时间为会话时间（浏览器关闭则会话结束）。默认cookie有效期应该介于0-30天之间，记住登录时间的cookie有效期应该介于1-365天之间。',
	ForceChangePasswordOnFirstLogin: '首次登录修改密码',
	ForceChangePasswordOnFirstLoginDescription: '开启该选项后，用户在首次登录时需要修改密码。',
	ForcePasswordExpiration: '强制密码过期',
	ForcePasswordExpirationDescription: '开启该选项后，用户的密码会在指定的时间之后过期，过期后用户需要更改密码。密码有效期的范围为15-365天，默认有效期为90天。',
	PasswordExpirationDays: '密码有效期（天）',
	IntegrationClientSecret: '集成客户端密钥',
	IntegrationClientSecretDescription: '你可以在这里修改集成客户端的密钥，这个密钥会在生成集成令牌的时候使用。密钥只能包含字母，数字，和符号\'-\'，\'_\'，\'.\'，长度应该在16-64位之间。',

	// Claim Mappings
	ClaimName: '名称',
	UserPropName: '用户属性',
	AddClaim: '添加',
	DeleteClaim: '删除',
	DeleteClaimConfirmMessage: "确定删除用户上下文属性'{{claim}}'吗？",
	Create: '新建',

	// Two Factor Authentication
	defaultSubject: 'Wyn登录验证码',
	defaultBodyTemplate: '您的登录验证码是 {@code}，有效期为5分钟，请勿告知他人。',
	SMS: '短信',
	EMAIL: '邮箱',
	EnableTFA: '启用',
	Lifetime: '有效期（分钟）',
	TFAType: '验证方式',
	accessKeyId: 'AccessKeyId',
	accessKeySecret: 'AccessKeySecret',
	signName: '签名',
	templateCode: '模板代码',
	subject: '邮件主题',
	bodyTemplate: '邮件正文',
	SendVerificationCodeFailed: '发送验证码失败，原始错误信息是：{{error}}',
	SendVerificationCodeSuccess: '发送验证码成功，请确保您已经收到验证码，然后再保存设置。',
	ShowSecret: '显示密码',
	HideSecret: '隐藏密码',
	TestTFA: '测试设置',
	EnableTFADescription: '启用双因子认证后，所有本地用户（用户提供者为"local"的用户）在登录的时候都需要提供验证码，所以请务必保证所有的本地用户都设置了正确的电话号码或邮箱地址。',
	BodyTemplateDescription: '邮件正文中必须包含代码“{@code}”，在发送邮件的时候它将会被替换为真实的验证码。',
	Configure: '配置',
	NoEmailSettingsWarning: '注意：您还没有在通知中心完成您的邮箱信息配置。',
	TFA_EmptyMobile: '用户电话号码为空。',
	TFA_EmptyEmail: '用户邮箱地址为空。',
	TFA_InvalidSettings: '检测到非法的双因子认证设置。',
	TFA_InvalidEmailSettings: '检测到非法的邮箱设置。',
	TFA_NoAvailableEmailSender: '没有可用的邮件发送者。',

	// Inactive Session Settings
	deleteUser: '删除用户',
	providerPlaceText: '提供者',
	namePlaceText: '用户名',
	UserName: '用户名',
	Provider: '提供者',
	LoginPage: '登录页面',
	SessionDisconnectedPage: '会话断开页面',
	CustomAddress: '自定义地址',
	Enable: '启用',
	EnableInactiveSessionDescription: '此功能开启后，不活动的用户会话将会在指定时间之后被断开。',
	Timeout: '超时时间（分钟）',
	RedirectURL: '重定向地址',
	UnrestrictedUsers: '白名单用户',
	addUser: '添加外部用户',
	selectUsers: '添加内部用户',
	AvailableUsers: '用户列表',
	unlock: '解锁',

	// Identity Server User Controller Error Code Range 1001 ~ 1999
	error_1001: '无效的PageSize或者PageNumber。',
	error_1002: '用户名不能为空。',
	error_1003: '邮件地址不能为空。',
	error_1004: '密码必须同时包括字母和数字，长度限制8～150。',
	error_1005: '用户名已经存在。',
	error_1006: '邮件地址已经存在。',
	error_1007: '角色 \'{{role}}\' 不存在。',
	error_1008: '无效的参数。',
	error_1009: '用户没有找到。',
	error_1010: '用户已经存在。',
	error_1011: '扩展属性 \'{{prop}}\' 不存在。',
	error_1012: '\'{{value}}\' 不是扩展属性 \'{{prop}}\'的合法值。',
	error_1013: '保存数据到数据库失败。',
	error_1014: '用户名不能修改。',
	error_1015: '没有上传文件。',
	error_1016: '没有可见的Sheet。',
	error_1017: '模版格式不正确。',
	error_1018: '用户找不到。',
	error_1019: '用户 \'{{id}}\' 没有被锁定。',
	error_1020: '无效的密码。',
	error_1021: '无效的安全码。',
	error_1022: '请求来自被禁止的IP。',
	error_1023: '语言不支持。',
	error_1024: '用户手机号码已经存在。',
	error_1025: '非法的邮箱地址。',

	// Import users error
	error_1100: "行 [{{rowIndex}}]: 用户ID '{{userId}}' 已经存在。",
	error_1101: "行 [{{rowIndex}}]: 用户名不能为空。",
	error_1102: "行 [{{rowIndex}}]: 用户名 '{{userName}}' 已经存在。",
	error_1103: "行 [{{rowIndex}}]: 用户邮箱不能为空。",
	error_1104: "行 [{{rowIndex}}]: 用户邮箱 '{{userEmail}}' 已经存在。",
	error_1105: "行 [{{rowIndex}}]: 用户手机号码 '{{userMobile}}' 已经存在。",
	error_1106: "行 [{{rowIndex}}]: 用户密码不能为空。",
	error_1107: "行 [{{rowIndex}}]: 非法的密码格式。密码应该同时包含大写字母，小写字母和数字，长度在8-150位之间。",
	error_1108: "行 [{{rowIndex}}]: 非法的创建时间 '{{creationTime}}'。",
	error_1109: "行 [{{rowIndex}}]: 字段“是否启用”指定了非法值 '{{value}}'。",
	error_1110: "行 [{{rowIndex}}]: 角色 '{{role}}' 不存在。",
	error_1111: "行 [{{rowIndex}}]: 非法的邮箱地址 '{{userEmail}}'。",

	// Identity Server Role Controller Error Code Range 2001 ~ 2999
	error_2001: '角色 \'{{name}}\' 已经存在',
	error_2002: '角色名称和权限名称冲突',
	error_2003: '内置角色不能被删除。',
	error_2004: "不能修改角色 'everyone' 的成员。",
	error_2005: "不能把用户‘admin’从角色‘administrator’中删除。",
	error_2006: "不能修改角色‘administrator’的权限。",
	// Identity Server Claim Mapping Errors
	error_3001: "用户上下文属性'{{claimName}}'已经存在。",
	error_3002: "Id为'{{claimId}}'的用户上下文属性不存在。",
	error_3003: "用户自定义属性'{{propName}}'不存在。",
	error_3004: "内置的用户上下文属性不能修改。",
	error_3005: "内置的用户上下文属性不能删除。",
	error_3006: "非法的用户上下文属性名。用户上下文属性的长度应该在1-64位之间，并且不能包含字符‘<’，‘>’，‘/’，‘\\’，‘$’和一些非法的关键字。",
	// Identity Server Custom Property Errors
	error_4001: "扩展属性‘{{propName}}’已经存在。",
	error_4002: "扩展属性‘{{propName}}’不存在。",
	error_4003: "Id为‘{{propId}}’的扩展属性不存在。",
	error_4004: "非法的扩展属性名。扩展属性名的长度应该在1-64位之间，并且不能包含字符‘<’，‘>’，‘/’，‘\\’，‘$’和一些非法关键字。",
	// Identity Server Tenant Errors
	error_5001: "组织名不能为空。",
	error_5002: "组织‘{{tenantName}}’已经存在。",
	error_5003: "组织属性名不能为空。",
	error_5004: "组织属性‘{{tenantPropName}}’不存在。",
	error_5005: "组织属性‘{{tenantPropName}}’为保留属性。",
	error_5006: "组织发件人邮箱‘{{fromEmail}}’已经被占用。",
	// External Login Provider Errors
	error_6001: "外部用户提供程序“{{providerName}}”不存在。",
	error_6002: "外部用户提供程序“{{providerName}}”的消息发送功能没有启用。",
	error_6003: "发送消息给外部用户提供程序“{{providerName}}”失败了, 错误码: {{errCode}}, 错误描述: {{errMsg}}",
	error_6004: "没有检测到合法的消息接收者。",
	error_6005: "检测到有重复的用户名称“{{userName}}”。",
	error_6006: "检测到有重复的角色名称“{{roleName}}”。",
	error_6007: "检测到同一层级下有重复的组织名称“{{organizationName}}”。",
	error_6008: "数据同步失败，原始的错误描述是: {{errMsg}}",
	// Security Provider Errors
	error_7001: '安全提供程序“{{providerName}}”未启用。',
	error_7002: '加载安全提供程序“{{providerName}}”失败。',
	error_7003: '错误的用户或者密码。',
	error_7004: '用户名和密码不能为空。',
	error_7005: '传递的参数无效。',
	error_7006: '使用安全提供程序登录失败。',
	// System Config Errors
	error_9001: '非法的cookie有效期。默认cookie有效期应该介于0-30天之间，记住登录时间的cookie有效期应该介于1-365天之间。',

	//LicenseActiveResult
	ActivateError: '激活授权失败',
	OfflineActivateError: '离线激活授权失败',
	RefreshError: '刷新授权失败',
	OfflineRefreshError: '离线刷新授权失败',
	DeactivateError: '反激活授权失败',
	OfflineDeactivateError: '离线反激活授权失败',
	ImportLicenseError: '导入授权信息失败',
	GetLicenseError: '获取授权信息失败',
	UpgradeLicenseError: '正式激活失败',
	GetClientIdentityCodeError: '获取客户识别码失败',

	V2_007_009_0001: "激活授权错误",
	V2_007_009_0002: "离线激活授权错误",
	V2_007_009_0003: "反激活授权错误",
	V2_007_009_0004: "离线反激活授权错误",
	V2_007_009_0005: "刷新授权错误",
	V2_007_009_0006: "导入授权信息错误",
	V2_007_009_0007: "旧授权不存在",
	V2_007_009_0008: "刷新授权错误(迁移)",
	V2_007_009_0009: "离线刷新授权错误(迁移)",
	V2_007_009_0010: "获取授权信息错误",
	V2_007_009_0011: '离线刷新授权错误',
	V2_007_009_0012: '临时激活错误',
	V2_007_009_0013: '正式激活错误',
	V2_007_009_0014: '获取客户识别码错误',

	licenseErr_1000: '请求错误。',
	licenseErr_1001: '无效的数据格式(1001)。',
	licenseErr_1002: '无效的数据格式(1002)。',
	licenseErr_1003: '无效日期(1003)。请检查您的系统时间并重试。',
	licenseErr_1004: '无效的授权密钥(1004)。请检查您的授权密钥并重试。',
	licenseErr_1005: '无效的数据格式 (1005)。',
	licenseErr_1006: '无效的数据格式 (1006)。',
	licenseErr_1007: '无效的激活信息(1007)。客户端激活状态和授权激活服务器记录的信息不一致。请尝试反激活原有的授权，然后重新激活。',
	licenseErr_1008: '无效的数据格式(1008)。',
	licenseErr_1009: '无效的授权密钥(1009)。请尝试反激活原有的授权，然后重新激活。',
	licenseErr_2000: '授权信息错误(2000)。',
	licenseErr_2001: '授权信息错误(2001)。',
	licenseErr_2002: '授权信息错误(2002)。',
	licenseErr_2003: '授权信息错误(2003)。授权信息失效，请尝试重新获取。',
	licenseErr_2004: '授权信息错误(2004)。授权信息失效，请尝试重新获取。',
	licenseErr_3000: '授权信息错误(3000)。',
	licenseErr_3001: '授权密钥已被禁用(3001)。',
	licenseErr_3002: '授权密钥已过期(3002)。',
	licenseErr_3003: '授权信息错误(3003)。该授权码不可用于产品当前版本。',
	licenseErr_3004: '授权信息错误(3004)。该授权码不可用于产品当前版本。',
	licenseErr_4000: '操作失败(4000)。',
	licenseErr_4001: '当前授权密钥不支持离线激活。',
	licenseErr_4002: '当前授权密钥不支持反激活。',
	licenseErr_4003: '当前的授权密钥已超过可用数量。',
	licenseErr_4004: '当前授权码操作过于频繁。',
	licenseErr_5000: '服务器错误(5000)。',
	licenseErr_5001: '服务器错误(5001)。',
	licenseErr_5002: '服务器错误(5002)。',
	licenseErr_6000: '当前授权密钥无法迁移到当前版本(6000)。请联系我们。',
	licenseErr_6001: '当前授权密钥无法迁移到当前版本(6001)。请联系我们。',
	licenseErr_6002: '当前授权密钥无法迁移到当前版本(6002)。请联系我们。',
	licenseErr_6003: '当前授权密钥无法迁移到当前版本(6003)。请联系我们。',
	licenseErr_6004: '当前授权密钥无法迁移到当前版本(6004)。请联系我们。',
	licenseErr_7001: '临时激活失败，授权文件无效(7001)。请联系我们。',
	licenseErr_7002: '临时激活失败，加密方式无效(7002)。请联系我们。',
	licenseErr_7003: '临时激活失败，授权密钥无效(7003)。请联系我们。',
	licenseErr_7004: '临时激活失败，授权信息无效(7004)。请联系我们。',
	licenseErr_7005: '临时激活失败，授权信息过期(7005)。请联系我们。',
	licenseErr_7006: '临时激活失败，签名校验失败(7006)。请联系我们。',
	licenseErr_65535: '未知错误，请联系我们。',

	licenseErr_0000_0001: '已经激活了一个授权。',
	licenseErr_0000_0002: '无效的授权码。',
	licenseErr_0000_0003: '解密授权内容失败。',
	licenseErr_0000_0004: '未找到授权。',
	licenseErr_0001_0001: '激活授权失败，请查看日志获取更多信息。',
	licenseErr_0001_0002: '生成离线激活信息失败。',
	licenseErr_0002_0001: '反激活失败，请查看日志获取更多信息。',
	licenseErr_0002_0002: '生成离线反激活信息失败。',
	licenseErr_0003_0001: '刷新授权失败，请查看日志获取更多信息。',
	licenseErr_0003_0002: '刷新授权失败，没有找到授权码用于刷新。',
	licenseErr_0003_0003: '刷新授权失败，生成离线刷新信息失败。',
	licenseErr_0003_0004: '刷新授权失败，输入的序列号不匹配。',
	licenseErr_0004_0001: '导入授权信息失败. 请查看日志获取更多信息。',
	licenseErr_0005_0001: '找不到对应的授权信息。',
	licenseErr_0005_0002: '当前操作需要一个旧版本授权信息，但是没有找到符合条件的条目。',
	licenseErr_0006_0001: '迁移授权失败. 请查看日志获取更多信息。',
	licenseErr_0006_0002: '生成离线操作凭证失败。',
	licenseErr_0007_0001: '获取授权信息失败，请查看日志获取更多信息。',
	licenseErr_0008_0001: '临时激活失败，请查看日志获取更多信息。',
	licenseErr_0009_0001: '正式激活失败，请查看日志获取更多信息。',
	licenseErr_0010_0001: '获取客户识别码失败，输入的序列号不匹配。',
	LicenseErr_ImportLicenseError: '导入授权信息失败，授权信息错误。',

	licenseWarn: '授权操作警告',

	licenseWarn_DeactivationFailed: '反激活失败。',
	licenseWarn_AlmostlyOverFrequency: '您的操作过于频繁。',
	licenseWarn_AlmostlyOverMaxInstanceCount: '接近授权的最大激活数',

	// Custom Visual License Errors
	GetCustomVisualLicenseError: "获取可视化插件授权列表失败。",
	AnalysisCustomVisualLicenseFileError: "上传可视化插件授权文件失败。",
	GetCustomVisualListError: "获取可视化插件列表失败。",
	ImportCustomVisualLicenseFileError: "导入可视化插件授权失败。",
	ImportCustomVisualLicenseFileSuccess: "导入可视化插件授权成功。",
	DeleteCustomVisualLicenseError: "删除可视化插件授权失败。",

	'rt_claim mapping': '用户上下文',
	'rt_custom property': '扩展属性',

	error_V2_007_000_0001: '{{resourceType}}“{{resourceIdentifier}}”未找到。',
	error_V2_007_000_0002: '{{resourceType}}“{{resourceIdentifier}}”不存在或已被删除。',
	error_V2_007_000_0003: '{{resourceType}}“{{resourceIdentifier}}”已经存在。',
	error_V2_007_000_0006: '您没有足够的权限来执行此操作。',
	error_V2_007_000_0010: '发生了未知错误“{{message}}”，请查看日志来获取错误的详细信息。',
	error_V2_007_003_0003: "非法的用户上下文属性名。用户上下文属性的长度应该在1-64位之间，并且不能包含字符‘<’，‘>’，‘/’，‘\\’，‘$’和一些非法的关键字。",
	error_V2_007_004_0001: "非法的扩展属性名。扩展属性名的长度应该在1-64位之间，并且不能包含字符‘<’，‘>’，‘/’，‘\\’，‘$’和一些非法关键字。",
	error_V2_007_004_0004: '提供的可选值"{{InvalidAvailableValue}}"和指定的属性值类型或者格式不匹配。',

	error_V2_007_006_0004: '检测到有重复的用户名称“{{UserName}}”。',
	error_V2_007_006_0005: '检测到有重复的角色名称“{{RoleName}}”。',
	error_V2_007_006_0006: '检测到同一层级下有重复的组织名称“{{OrganizationName}}”。',
	error_V2_007_006_0007: '数据同步失败，原始的错误描述是: {{ErrMsg}}',
	error_V2_007_010_0002: '会话过期时间应该在1到120分钟之间。',
	error_V2_007_010_0003: '重定向地址应该以“/”开头，或者必须是一个以“http://”或者“https://”开头的网址。',
	error_V2_007_010_0004: '用户名和用户提供者都不能为空。',

	error_V2_008_002_0001: '请确保在“生成集成地址”操作之前，“系统外观”中的“门户网站地址”已经设置。',
	error_V2_008_001_0003: '令牌生成失败。原始的错误信息是：{{error_description}}。',
	error_V2_008_001_0004: '令牌已过期。',

	error_V2_008_003_0001: '使用令牌获取用户信息失败，也许令牌已过期或者被删除了。',

	customVisualLicenseErr_V2_007_011_0001: '可视化插件授权文件内容为空。',
	customVisualLicenseErr_V2_007_011_0002: '上传可视化插件授权文件失败。',
	customVisualLicenseErr_V2_007_011_0003: '参数为空或者要导入的可视化插件授权列表为空',
	customVisualLicenseErr_V2_007_011_0004: '导入可视化插件授权失败。',
	customVisualLicenseErr_V2_007_011_0005: '获取可视化插件授权列表失败。',
	customVisualLicenseErr_V2_007_011_0006: '删除可视化插件授权失败。',
}