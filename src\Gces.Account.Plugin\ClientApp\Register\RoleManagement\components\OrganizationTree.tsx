import * as React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';
import { RoleState, Organization, roleActionCreators } from '../store';
import { OrganizationTreeCommon } from '../../Common/OrganizationTreeCommon';
import { GlobalOrganization } from '../../../util';

interface ConnectedProps {
	user: User;
	organizations: Organization[];
	selectedOrganizationId: string;
	enableStrictPermissionManagement: boolean;
	dispatch: any;
	t: any;
}

@translate('role', { wait: true })
class OrganizationTreeInner extends React.PureComponent<ConnectedProps> {
	onSelectedPathChange = (id: string) => {
		this.props.dispatch(roleActionCreators.selectOrg(id || GlobalOrganization.Id, this.props.enableStrictPermissionManagement));
	}
	render() {
		const { organizations, selectedOrganizationId, user: { orgId } } = this.props;
		if (organizations.length === 0) return null;

		const treeProps = {
			items: organizations,
			userTenantId: orgId,
			selectedId: selectedOrganizationId,
			onItemSelected: this.onSelectedPathChange,
			draggable: false,
		};
		return (<div className='org-tree'>
			<OrganizationTreeCommon {...treeProps} />
		</div>);
	}
}

export const OrganizationTree = connect(
	(state: { role: RoleState, navApp: { user: User } }) => ({
		user: state.navApp.user,
		organizations: state.role.organizations,
		selectedOrganizationId: state.role.selectedOrganizationId,
		enableStrictPermissionManagement: state.role.enableStrictPermissionManagement,
	})
)(OrganizationTreeInner) as React.ComponentClass<{}>;