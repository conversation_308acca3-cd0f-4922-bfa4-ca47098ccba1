import { RoleConsts } from '../Register/Common';
import { PermissionItem } from '../Register/Common/interfaces';
import { GlobalOrganization } from '../util';

export function groupBy(arr: any[], prop: string) {
	return arr.reduce((rv, x) => {
		(rv[x[prop]] = rv[x[prop]] || []).push(x);
		return rv;
	}, {});
}

export function calcChangedPermissions(originalPermissionItems: PermissionItem[], name: string) {
	const permissionItems = originalPermissionItems.map(s => { return { ...s }; });
	const changingItem = permissionItems.find(p => p.name === name);
	changingItem.checked = !changingItem.checked;
	if (changingItem.checked && changingItem.name.startsWith('create')) {
		const viewItem = permissionItems.find(x => x.name === changingItem.name.replace('create', 'view'));
		if (viewItem) viewItem.checked = true;
	}
	if (!changingItem.checked && changingItem.name.startsWith('view')) {
		const createItem = permissionItems.find(x => x.name === changingItem.name.replace('view', 'create'));
		if (createItem && createItem.checked) changingItem.checked = true;
	}
	if (changingItem.checked && ['create-data-source', 'create-semantic-model', 'create-dataset', 'create-iot-data'].indexOf(changingItem.name) !== -1) {
		const viewResourcePortalItem = permissionItems.find(x => x.name === 'view-resource-portal');
		if (viewResourcePortalItem) viewResourcePortalItem.checked = true;
	}
	if (!changingItem.checked && changingItem.name === 'view-resource-portal') {
		if (permissionItems.some(x => x.checked && ['create-data-source', 'create-semantic-model', 'create-dataset', 'create-iot-data'].indexOf(x.name) !== -1)) {
			changingItem.checked = true;
		}
	}

	if (changingItem.checked && changingItem.name.startsWith('assign')) {
		const viewItem = permissionItems.find(x => x.name === changingItem.name.replace('assign-', ''));
		if (viewItem) viewItem.checked = true;
	}

	if (!changingItem.checked && changingItem.name === 'manage-user') {
		const assignItem = permissionItems.find(x => x.name === 'assign-manage-user');
		if (assignItem && assignItem.checked) changingItem.checked = true;
	}

	if (changingItem.checked && changingItem.name === 'create-data-monitoring') {
		const viewDashboard = permissionItems.find(x => x.name === 'view-dashboard');
		if (viewDashboard) viewDashboard.checked = true;
	}

	if (!changingItem.checked && changingItem.name === 'view-dashboard') {
		const createDataMonitoring = permissionItems.find(x => x.name === 'create-data-monitoring');
		if (createDataMonitoring && createDataMonitoring.checked) changingItem.checked = true;

		if (!permissionItems.some(x => x.checked && x.name === 'view-report')) {
			const createDocumentBinder = permissionItems.find(x => x.name === 'create-document-binder');
			if (createDocumentBinder && createDocumentBinder.checked) createDocumentBinder.checked = false;
		}
	}

	if (!changingItem.checked && changingItem.name === 'view-report' && !permissionItems.some(x => x.checked && x.name === 'view-dashboard')) {
		const createDocumentBinder = permissionItems.find(x => x.name === 'create-document-binder');
		if (createDocumentBinder && createDocumentBinder.checked) createDocumentBinder.checked = false;
	}

	return permissionItems;
}

export const hasManagePermissionForOrgsAndRoles = (roles: string[] = []) => {
	return roles.some(role => 'manage-permission' === role.toLowerCase());
};

export const UUID = {
	isUUID: (uuid: string) => {
		return uuid && uuid.trim() && /^[0-9a-zA-Z\-]{36}$/.test(uuid.trim());
	}
};

export function getRoleDisplayName(name: string) {
	const nameSpan = name.split('$');
	if (nameSpan && 2 === nameSpan.length && UUID.isUUID(nameSpan[0]) && (RoleConsts.orgadmin.name === nameSpan[1] || RoleConsts.orgapprover.name === nameSpan[1])) {
		name = nameSpan[1];
	}
	switch (name) {
		case RoleConsts.orgadmin.name:
		case RoleConsts.admin.name:
		case RoleConsts.orgeveryone.name:
		case RoleConsts.everyone.name:
		case RoleConsts.approver.name:
		case RoleConsts.orgapprover.name:
			return window.AdminPortal.i18n.t(`common:rn_${name}`);
		default:
			return name;
	}
}

export function getDefaultSelectedOrgId(items: { id: string, parentTenantId?: string, order?: number }[], tenantId: string) {
	if (tenantId) {
		const rootItem = items.find(s => s.id === tenantId);
		if (rootItem) {
			return rootItem.id;
		}
	} else {
		const idx = items.findIndex(i => i.id === GlobalOrganization.Id);
		if (idx > -1) {
			return items[idx].id;
		}
		const directChildren = items.filter(s => !s.parentTenantId);
		const minOrder = Math.min(...directChildren.map(s => s.order));
		const firstOrgItem = directChildren.find(s => s.order === minOrder);
		if (firstOrgItem) {
			return firstOrgItem.id;
		} else {
			return null;
		}
	}
}

export const formatOrgPath = (orgPath: string) => {
	if (!orgPath) {
		return orgPath;
	}

	let formatPath = orgPath;
	if (orgPath === '/') {
		formatPath = window.AdminPortal.i18n.t('common:globalOrgName');
	} else {
		formatPath = orgPath.split('/').filter(p => p).join(' / ');
	}
	return formatPath;
};

export const getManagerOrgId = (orgIdPath: string) => {
	if (!orgIdPath) return 'global';
	const idArr = orgIdPath.split('/');
	return idArr[idArr.length - 1];
};

export const nameSorter = (o1: { name: string }, o2: { name: string }) => o1.name.toLowerCase().localeCompare(o2.name.toLowerCase());