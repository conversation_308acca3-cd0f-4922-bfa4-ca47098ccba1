{"version": "1.0.0", "name": "user-management", "private": true, "engines": {"node": ">=14.15.4 <21.0.0"}, "scripts": {"dev": "cross-env NODE_OPTIONS='--max-http-header-size=90000000' webpack-dev-server", "build": "webpack --progress --env prod=true"}, "dependencies": {"classnames": "2.2.5", "gces-react-custom-scrollbars": "0.0.3", "gces-react-grid": "8.0.3", "gces-cronstrue": "0.0.3", "gces-ui": "0.8.16", "gces-ellipsis": "7.1.3", "wyn-utils": "6.1.2", "i18next": "^20.6.1", "i18next-browser-languagedetector": "^2.2.0", "immutability-helper": "2.6.4", "interactjs": "^1.5.1", "lodash": "^4.17.5", "moment": "^2.30.1", "qrcode.react": "^1.0.1", "json5-loader": "^4.0.1", "react": "16.13.1", "react-dom": "16.13.1", "react-dropzone": "^4.2.9", "react-i18next": "^7.6.1", "react-md": "^1.0.19", "react-modal": "^3.3.1", "react-redux": "6.0.1", "react-router-dom": "4.2.2", "react-toolbox": "^2.0.0-beta.12", "react-tooltip": "5.11.1", "redux": "3.7.2", "redux-saga": "0.16.0", "redux-thunk": "2.2.0"}, "devDependencies": {"@babel/core": "7.16.7", "@babel/plugin-proposal-class-properties": "7.16.7", "@babel/plugin-proposal-decorators": "7.16.7", "@babel/preset-env": "7.16.7", "@babel/preset-react": "7.16.7", "@babel/preset-typescript": "7.16.7", "@types/node": "^11.11.3", "@types/react": "16.7.17", "@types/react-codemirror": "^1.0.2", "@types/react-dom": "16.0.11", "@types/react-redux": "^6.0.8", "@types/wyn-portal": "7.1.1", "babel-loader": "8.2.3", "cross-env": "7.0.3", "css-loader": "6.6.0", "css-minimizer-webpack-plugin": "5.0.0", "fork-ts-checker-webpack-plugin": "^7.2.1", "mini-css-extract-plugin": "2.7.5", "node-glob": "^1.2.0", "sass": "^1.26.3", "sass-loader": "12.6.0", "source-map-loader": "3.0.1", "style-loader": "3.3.1", "terser-webpack-plugin": "5.3.1", "ts-loader": "9.2.6", "typescript": "3.9.2", "webpack": "^5.80.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.7.4", "webpack-env": "^0.8.0", "webpack-remove-empty-scripts": "0.7.3", "wyn-portal-types": "7.1.1"}}