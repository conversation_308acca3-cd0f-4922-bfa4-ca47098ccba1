import * as React from 'react';
import { translate } from 'react-i18next';
import { connect } from 'react-redux';
import { tenantActionCreators, TenantProp, OrganizationState, OrganizationItem, TenantItemProp } from '../store';
import CGrid, { CGridProps, Column } from 'gces-react-grid';
import { Button, Checkbox } from 'gces-ui';
import { ContextValueTypes, DataType, DateFormat, DateTimeFormat, GlobalOrganization, isValidValue } from '../../../util';
import { CNumberPicker } from '../../../components/c-numberPicker';
import { CMultiValueInput } from '../../../components/c-multivalue-input';
import { CDropdownEditor } from '../../../components/c-dropdown';
import { CDateTimePicker } from '../../../components/c-datetimePicker';
import { CInputEditor } from '../../../components/c-input';
import * as moment from 'moment';
import * as classnames from 'classnames';

interface PropViewValue {
	values: string[];
	editable: boolean;
	viewable: boolean;
	isMultiValued: boolean;
	required: boolean;
	valueType: number;
	sensitive: boolean;
}

interface PropertyGridRow {
	propertyName: string;
	value: PropViewValue;
	disableToEdit: boolean;
	disableToView: boolean;
	builtin?: boolean;
	height?: number;
}

interface ConnectedProps {
	tenantProps: TenantProp[];
	items: OrganizationItem[];
	isAdding?: boolean;
	isEditing?: boolean;
	dispatch: any;
	selected: string;
	t: any;
}

interface LocalState {
	tenantItem: OrganizationItem;
	hasSensitiveProps: boolean;
}

const defaultHeight = 50;

@translate('organization', { wait: true })
class OrganizationPropEditorInner extends React.PureComponent<ConnectedProps, LocalState> {
	state: LocalState = {
		tenantItem: { id: '', name: '', props: [] },
		hasSensitiveProps: false,
	};

	componentWillMount() {
		this.updateStateIfNeed(this.props);
	}
	componentWillReceiveProps(nextProps: ConnectedProps) {
		this.updateStateIfNeed(nextProps);
	}
	updateStateIfNeed = (newProps: ConnectedProps) => {
		const { selected, isAdding } = newProps;
		const editingId = isAdding ? null : selected;
		if (editingId !== this.state.tenantItem.id) {
			this.updateStateByProps(newProps);
		}
	}

	updateStateByProps = (props: ConnectedProps) => {
		const { selected, items, tenantProps, isAdding } = props;
		const editingId = isAdding ? null : selected;
		if (editingId) {
			const item = items.find(it => it.id === editingId);
			if (item) {
				const hasSensitiveProps = item.props?.some(p => p.sensitive);
				this.setState({ tenantItem: JSON.parse(JSON.stringify(item)), hasSensitiveProps });
			}
		} else {
			const parentTenantId = selected;
			const parentTenant = items.find(s => s.id === parentTenantId);
			const parentProps = parentTenant && parentTenant.props || [];
			const tenantItem: OrganizationItem = {
				id: '', name: '', parentTenantId, props: tenantProps.map(p => {
					const parentProp = parentProps.find(s => s.id === p.id);
					const { allowSubTenantEditing, allowSubTenantViewing } = parentProp || { allowSubTenantEditing: true, allowSubTenantViewing: true };
					return {
						id: p.id,
						name: p.name,
						values: [],
						allowSubTenantEditing,
						allowSubTenantViewing,
						editable: allowSubTenantEditing,
						viewable: allowSubTenantViewing,
						valueType: p.valueType,
						sensitive: p.sensitive,
					};
				})
			};
			const hasSensitiveProps = tenantItem.props?.some(p => p.sensitive);
			this.setState({ tenantItem, hasSensitiveProps });
		}
	}

	onOKClick = () => {
		if (!this.props.isAdding) {
			this.props.dispatch(tenantActionCreators.editTenant(this.state.tenantItem));
		} else {
			this.props.dispatch(tenantActionCreators.addTenant({ ...this.state.tenantItem, ...{ parentTenantId: this.props.selected } }));
		}
	}

	onCancelClick = () => {
		const { isAdding } = this.props;
		if (isAdding) {
			this.props.dispatch(tenantActionCreators.setIsAdding(false));
		}
		else {
			this.updateStateByProps(this.props);
			this.props.dispatch(tenantActionCreators.setIsEditing(false));
		}
	}

	onNameChange = (name: string) => {
		this.changeTenantItem({ ...this.state.tenantItem, name });
	}
	onPropItemPropChange = (name: string, value: string, changeFunc: (value: string, tenantPropValue: TenantItemProp, tenantProp: TenantProp) => void) => {
		const { tenantProps } = this.props;
		const tenantItem: OrganizationItem = JSON.parse(JSON.stringify(this.state.tenantItem));
		const tenantPropValue = (tenantItem.props || []).find(p => p.name === name);
		if (tenantPropValue) {
			const tenantProp = tenantProps.find(p => p.name === name);
			if (tenantProp) {
				changeFunc(value, tenantPropValue, tenantProp);
				this.changeTenantItem(tenantItem);
			}
		}
	}

	onPropValueChange = (name: string, value: string) => this.onPropItemPropChange(name, value, this.onPropValueChangeFunc);
	onPropValueChangeFunc = (value: string, tenantPropValue: TenantItemProp, tenantProp: TenantProp) => {
		if (tenantProp.multivalued) {
			tenantPropValue.values = value.split(/\r?\n/);
		} else {
			switch (tenantPropValue.valueType) {
				case 4: // date
					tenantPropValue.values = [moment(value).isValid() ? moment(value).format(DateFormat) : value]; break;
				case 5: // datetime
					tenantPropValue.values = [moment(value).isValid() ? moment(value).format(DateTimeFormat) : value]; break;
				default:
					tenantPropValue.values = [value];
			}
		}
	}

	onDisableSubViewChange = (name: string) => this.onPropItemPropChange(name, null, this.onDisableSubViewChangeFunc);
	onDisableSubViewChangeFunc = (value: string, tenantPropValue: TenantItemProp, tenantProp: TenantProp) => {
		const targetValue = !tenantPropValue.allowSubTenantViewing;
		if (!targetValue) {
			tenantPropValue.allowSubTenantEditing = false;
		}
		tenantPropValue.allowSubTenantViewing = targetValue;
	}

	onDisableSubEditChange = (name: string) => this.onPropItemPropChange(name, null, this.onDisableSubEditChangeFunc);
	onDisableSubEditChangeFunc = (value: string, tenantPropValue: TenantItemProp, tenantProp: TenantProp) => {
		const targetValue = !tenantPropValue.allowSubTenantEditing;
		if (targetValue && !tenantPropValue.allowSubTenantViewing) {
			return;
		}
		tenantPropValue.allowSubTenantEditing = targetValue;
	}

	changeTenantItem = (tenantItem: OrganizationItem) => {
		const { isAdding, selected: editingId, items, dispatch } = this.props;
		if (!isAdding && editingId) {
			const item = items.find(it => it.id === editingId);
			const isEditing = item && JSON.stringify(item) !== JSON.stringify(tenantItem);
			dispatch(tenantActionCreators.setIsEditing(isEditing));
		}
		this.setState({ tenantItem });
	}

	validTenantProps = (tenantItem: OrganizationItem) => {
		const props = tenantItem.props || [];

		for (const p of props) {
			if (!p.viewable || !p.editable) continue;

			const tp = this.props.tenantProps.find(tp => tp.id === p.id);
			const invalidRequired = this.invalidRequiredPropValue(tp.required, p.values);
			if (invalidRequired) {
				return false;
			}
			if ((tp.required || !this.isNullOrEmptyArray(p.values)) && !isValidValue(p.values.filter(v => !!v), ContextValueTypes[tp.valueType])) {
				return false;
			}
		}

		return true;
	}
	validTenantName = (tenantItem: OrganizationItem) => {
		const { items } = this.props;
		if (!tenantItem.name) return false;
		const name = tenantItem.name.trim();
		if (-1 !== items.findIndex(i => i.id !== tenantItem.id && i.parentTenantId === tenantItem.parentTenantId && i.name.toUpperCase() === name.toUpperCase())) return false;
		return true;
	}
	validTenantNameErrorMessage = (tenantItem: OrganizationItem): string => {
		const { items, t } = this.props;
		if (!tenantItem.name)
			return t('tntErrorOrganizationNameNull');
		const name = tenantItem.name.trim();
		if (-1 !== items.findIndex(i => i.id !== tenantItem.id && i.parentTenantId === tenantItem.parentTenantId && i.name.toUpperCase() === name.toUpperCase())) {
			return t('tntErrorOrganizationNameDuplicated');
		}
		if (/[<>\/\\$]/.test(name)) {
			return t('tntErrorInvalidCharInName');
		}
		return '';
	}

	generatePropertyRows = (tenantItem: OrganizationItem, isAdding: boolean, tenantProps: TenantProp[]): PropertyGridRow[] => {

		const isMultiValued = (prop: TenantItemProp | TenantProp) => tenantProps.find(tp => tp.id === prop.id).multivalued;
		const isRequired = (prop: TenantItemProp | TenantProp) => tenantProps.find(tp => tp.id === prop.id).required;
		const buildPropViewValue = (prop: TenantItemProp): PropViewValue => (
			{
				values: prop.values,
				editable: prop.editable,
				viewable: prop.viewable,
				isMultiValued: isMultiValued(prop),
				required: isRequired(prop),
				valueType: prop.valueType,
				sensitive: prop.sensitive,
			}
		);
		const calcRowHeight = (multiValued: boolean, required: boolean, values: string[]) => {
			const height = multiValued ? 105 : defaultHeight;
			const isInvalid = this.invalidRequiredPropValue(required, values);
			return isInvalid ? height + 20 : height;
		};

		const propRows: PropertyGridRow[] = tenantItem.props
			.sort((p1, p2) => {
				if (isMultiValued(p1) === isMultiValued(p2)) {
					return p1.name.toUpperCase().localeCompare(p2.name.toUpperCase());
				} else {
					return isMultiValued(p1) ? 1 : -1;
				}
			})
			.map(tenantProp =>
			({
				propertyName: tenantProp.name,
				value: buildPropViewValue(tenantProp),
				disableToEdit: tenantProp.allowSubTenantEditing,
				disableToView: tenantProp.allowSubTenantViewing,
				height: calcRowHeight(isMultiValued(tenantProp), isRequired(tenantProp), tenantProp.values),
			}));

		propRows.unshift({
			propertyName: this.props.t('tntName'),
			value: { values: [tenantItem.name], editable: true, viewable: true, isMultiValued: false, required: true, valueType: 0, sensitive: false },
			disableToEdit: null,
			disableToView: null,
			builtin: true,
			height: calcRowHeight(false, true, [tenantItem.name])
		});

		return propRows;
	}

	getPropertyDefaultValue = (valueType: number, propName: string, isMultiValued: boolean) => {
		if (isMultiValued) {
			return this.props.t('tntMultiLineTip');
		}

		switch (ContextValueTypes[valueType]) {
			case DataType.String: return propName;
			case DataType.Boolean: return 'true';
			case DataType.Integer: return '0';
			case DataType.Float: return '0.0';
			case DataType.Date: return '1990-01-01';
			case DataType.DateTime: return '1990-01-01 00:00:00';
		}
	}

	buildPropertyValueEditor = (propName: string, propValue: PropViewValue, isInvalid: boolean) => {
		const { isMultiValued, values, valueType, sensitive } = propValue;
		const { hasSensitiveProps } = this.state;
		const { t } = this.props;

		if (isMultiValued) {
			return (
				<CMultiValueInput
					className='multi-valued'
					value={values.join('\n')}
					valueType={ContextValueTypes[valueType]}
					rows={2}
					invalid={isInvalid}
					placeHolder={this.getPropertyDefaultValue(valueType, propName, true)}
					onChange={(value) => this.onPropValueChange(propName, value)}
					visibilityToggle={sensitive}
					noVisibilityToggle={!hasSensitiveProps}
					showEyeTitle={t('tntShowValue')}
					hideEyeTitle={t('tntHideValue')}
				/>
			);
		}

		const value = values.join('\r\n');
		switch (ContextValueTypes[valueType]) {
			case DataType.String:
				return (
					<CInputEditor
						invalid={isInvalid}
						placeholder={propName}
						value={value}
						onEveryChange={(value: string) => this.onPropValueChange(propName, value)}
						visibilityToggle={sensitive}
						noVisibilityToggle={!hasSensitiveProps}
						showEyeTitle={t('tntShowValue')}
						hideEyeTitle={t('tntHideValue')}
					/>
				);
			case DataType.Boolean:
				{
					const items = [{ value: true, text: 'True' }, { value: false, text: 'False' }];
					const selectedItem = items.find(s => s.value.toString() === value?.toLowerCase());
					return (
						<CDropdownEditor
							invalid={isInvalid}
							items={items}
							text={selectedItem && selectedItem.text}
							onChange={(value: boolean) => this.onPropValueChange(propName, value.toString())}
							visibilityToggle={sensitive}
							noVisibilityToggle={!hasSensitiveProps}
							showEyeTitle={t('tntShowValue')}
							hideEyeTitle={t('tntHideValue')}
						/>
					);
				}
			case DataType.Integer:
				return (
					<CNumberPicker
						type='integer'
						value={value}
						placeholder={this.getPropertyDefaultValue(valueType, propName, false)}
						invalid={isInvalid}
						onEveryChange={(value) => this.onPropValueChange(propName, !!value ? value : '')}
						visibilityToggle={sensitive}
						noVisibilityToggle={!hasSensitiveProps}
						showEyeTitle={t('tntShowValue')}
						hideEyeTitle={t('tntHideValue')}
					/>
				);
			case DataType.Float:
				return (
					<CNumberPicker
						type='float'
						value={value}
						placeholder={this.getPropertyDefaultValue(valueType, propName, false)}
						invalid={isInvalid}
						onEveryChange={(value) => this.onPropValueChange(propName, !!value ? value : '')}
						visibilityToggle={sensitive}
						noVisibilityToggle={!hasSensitiveProps}
						showEyeTitle={t('tntShowValue')}
						hideEyeTitle={t('tntHideValue')}
					/>
				);
			case DataType.Date:
				return (
					<CDateTimePicker
						placeholder={DateFormat}
						value={!!value ? moment(value) : undefined}
						mode='date'
						onChange={(value) => this.onPropValueChange(propName, moment.isMoment(value) ? value.format(DateFormat) : value)}
						onBlur={(value) => this.onPropValueChange(propName, moment.isMoment(value) ? value.format(DateFormat) : value)}
						dateFormat={DateFormat}
						invalid={isInvalid}
						visibilityToggle={sensitive}
						noVisibilityToggle={!hasSensitiveProps}
						showEyeTitle={t('tntShowValue')}
						hideEyeTitle={t('tntHideValue')}
					/>
				);
			case DataType.DateTime:
				return (
					<CDateTimePicker
						placeholder={DateTimeFormat}
						value={!!value ? moment(value) : undefined}
						mode='dateTime'
						onChange={(value) => this.onPropValueChange(propName, moment.isMoment(value) ? value.format(DateTimeFormat) : value)}
						onBlur={(value) => this.onPropValueChange(propName, moment.isMoment(value) ? value.format(DateTimeFormat) : value)}
						dateFormat={DateFormat}
						timeFormat='HH:mm:ss'
						invalid={isInvalid}
						visibilityToggle={sensitive}
						noVisibilityToggle={!hasSensitiveProps}
						showEyeTitle={t('tntShowValue')}
						hideEyeTitle={t('tntHideValue')}
					/>
				);
			default: return null;
		}
	}

	invalidRequiredPropValue = (required: boolean, values: string[]) => required && this.isNullOrEmptyArray(values);

	isNullOrEmptyArray = (values: string[]) => !values || values.length === 0 || values.every(v => !v || v.trim() === '');

	onRenderPropertyValue = (row: PropertyGridRow) => {
		const { t } = this.props;
		const { hasSensitiveProps: hasSensitiveProps } = this.state;
		const { editable, viewable, required, isMultiValued, values, valueType, sensitive } = row.value;

		if (row.builtin) {
			const { tenantItem } = this.state;
			const nameErrorMessage = this.validTenantNameErrorMessage(tenantItem);
			const isNameInvalid = !!nameErrorMessage;
			const tenantItemName = tenantItem.name === 'Global' ? t('tntGlobal') : tenantItem.name;
			return (
				<div className={classnames('property-value', { 'no-sensitive-property-value': !hasSensitiveProps })}>
					<CInputEditor
						maxLength={64}
						invalid={isNameInvalid}
						placeholder={t('tntName')}
						value={tenantItemName}
						readOnly={tenantItem.id === GlobalOrganization.Id}
						disabled={tenantItem.id === GlobalOrganization.Id}
						onEveryChange={this.onNameChange}
						noVisibilityToggle={!hasSensitiveProps}
						showEyeTitle={t('tntShowValue')}
						hideEyeTitle={t('tntHideValue')}
					/>
					{isNameInvalid && <span className='error-text' title={nameErrorMessage}>{nameErrorMessage}</span>}
				</div>
			);
		}

		if (!viewable || !editable) {
			return (
				<div className={classnames('property-value', { 'no-sensitive-property-value': !hasSensitiveProps })}>
					{isMultiValued ?
						<CMultiValueInput
							className='multi-valued'
							value={viewable ? values.join('\n') : ''}
							valueType={ContextValueTypes[valueType]}
							rows={2}
							invalid={false}
							placeHolder={viewable ? this.getPropertyDefaultValue(valueType, row.propertyName, isMultiValued) : t('tntInvisible')}
							disabled={!editable}
							onChange={undefined}
							visibilityToggle={viewable && sensitive}
							noVisibilityToggle={!hasSensitiveProps}
							showEyeTitle={t('tntShowValue')}
							hideEyeTitle={t('tntHideValue')}
						/>
						:
						<CInputEditor
							invalid={false}
							placeholder={viewable ? this.getPropertyDefaultValue(valueType, row.propertyName, isMultiValued) : t('tntInvisible')}
							disabled={!editable}
							value={viewable ? values.join('\r\n') : ''}
							visibilityToggle={viewable && sensitive}
							noVisibilityToggle={!hasSensitiveProps}
							showEyeTitle={t('tntShowValue')}
							hideEyeTitle={t('tntHideValue')}
						/>
					}
				</div>
			);
		}

		const isInvalid = this.invalidRequiredPropValue(required, values);
		return (
			<div className={classnames('property-value', { 'no-sensitive-property-value': !hasSensitiveProps })}>
				{this.buildPropertyValueEditor(row.propertyName, row.value, isInvalid)}
				{isInvalid && <span className='error-text' title={t('tntErrorFromPropNull')}>{t('tntErrorFromPropNull')}</span>}
			</div>
		);
	}

	onRenderColumn = (columnName: string) => {
		return <div className='cell-content' title={columnName}>{columnName}</div>;
	}

	onRenderCell = (key: string, row: PropertyGridRow) => {
		const { t } = this.props;
		const { editable, viewable, required } = row.value;
		if (key === 'propertyName') {
			return (
				<div className='property-name'>
					<div className='property-name-inner' title={row.propertyName} >{row.propertyName}</div>
					{required && <div className='required-property-star'>*</div>}
				</div>
			);
		}
		if (key === 'valueType') {
			return (
				<div className='property-value-type'>
					<span title={t(`tntPropValueType_${ContextValueTypes[row.value.valueType]}`)}>{t(`tntPropValueType_${ContextValueTypes[row.value.valueType]}`)}</span>
				</div>
			);
		}
		if (key === 'value') {
			return this.onRenderPropertyValue(row);
		}
		if (key === 'disableToEdit') {
			if (row.builtin) {
				return null;
			}
			return (
				<Checkbox
					className='disable-to-edit'
					disabled={!editable || !row.disableToView}
					checked={!row.disableToEdit}
					onChange={s => this.onDisableSubEditChange(row.propertyName)}
					value={!row.disableToEdit}
					title={t('tntDisableSubEdit')}
					text=' '
				/>
			);
		}
		if (key === 'disableToView') {
			if (row.builtin) {
				return null;
			}
			return (
				<Checkbox
					className='disable-to-view'
					disabled={!viewable}
					checked={!row.disableToView}
					onChange={s => this.onDisableSubViewChange(row.propertyName)}
					value={!row.disableToView}
					title={t('tntDisableSubView')}
					text=' '
				/>
			);

		}
	}

	renderTenantPropsEditorGrid = () => {
		const { tenantProps, isAdding } = this.props;
		const { tenantItem } = this.state;

		const columns: Column[] = [
			{ key: 'propertyName', component: this.onRenderColumn(this.props.t('tntPropName')) },
			{ key: 'valueType', width: 150, component: this.onRenderColumn(this.props.t('tntPropValueType')) },
			{ key: 'value', component: this.onRenderColumn(this.props.t('tntPropVlue')) },
			{ key: 'disableToEdit', component: this.onRenderColumn(this.props.t('tntDisableSubEdit')) },
			{ key: 'disableToView', component: this.onRenderColumn(this.props.t('tntDisableSubView')) },
		];
		const propRows = this.generatePropertyRows(tenantItem, isAdding, tenantProps);

		const gridProps: CGridProps = {
			columns,
			rows: propRows || [],
			onRenderCell: this.onRenderCell,
			hideGridLine: true,
			rowHeight: defaultHeight,
		};

		return (<CGrid {...gridProps} />);
	}

	render() {
		const { t, isAdding, isEditing } = this.props;
		const { tenantItem } = this.state;
		const showActionButton = isAdding || isEditing;
		const nameErrorMessage = this.validTenantNameErrorMessage(tenantItem);
		const isNameInvalid = !!nameErrorMessage;

		return (
			<div className='basic-info' >
				{this.renderTenantPropsEditorGrid()}
				{showActionButton &&
					<div className='at-footer'>
						<Button disabled={isNameInvalid || !this.validTenantProps(tenantItem)} text={t(!isAdding ? 'common:cmSaveChanges' : 'tntAdd')} title={t(!isAdding ? 'common:cmSaveChanges' : 'tntAdd')} onClick={this.onOKClick} style='accent' width='100px' />
						<Button text={t('tntCancel')} title={t('tntCancel')} onClick={this.onCancelClick} width='100px' />
					</div>
				}
			</div>
		);
	}
}

export const OrganizationPropEditor = connect(
	(state: { tenant: OrganizationState }) => ({
		tenantProps: state.tenant.tenantProps,
		items: state.tenant.items,
		isAdding: state.tenant.isAdding,
		isEditing: state.tenant.isEditing,
		selected: state.tenant.selected,
	})
)(OrganizationPropEditorInner) as React.ComponentClass<{}>;