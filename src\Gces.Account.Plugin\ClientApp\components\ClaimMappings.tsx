import * as React from 'react';
import { translate } from 'react-i18next';
import { connect } from 'react-redux';
import { Button, DropdownEditor, InputEditor } from 'gces-ui';
import CGrid, { CGridProps } from 'gces-react-grid';
import Actions from './../actions/actions';
import * as util from './../util';
import ConfirmDialog from './ConfirmDialog';

type CustomizePropertyItem = {
	id: string;
	name: string;
	availableValues: string[];
};

type ClaimMappingItem = {
	id: string;
	name: string;
	propName: string;
	isBuiltIn: boolean;
};

type ClaimMappingsProps = {
	claimMappings: ClaimMappingItem[];
	properties: CustomizePropertyItem[];
	claimMappingsViewMode: 'claimMappingsList' | 'addClaimMapping';
	deletingClaimMapping: ClaimMappingItem;
};

type ClaimMappingsState = {
	newClaimName: string;
	newPropName: string;
};

type ClaimMappingsConnectedProps = {
	dispatch: any;
	t: any;
};

@translate('account', { wait: true })
export class ClaimMappingsBase extends React.Component<ClaimMappingsConnectedProps & ClaimMappingsProps, ClaimMappingsState> {
	constructor(props: ClaimMappingsConnectedProps & ClaimMappingsProps) {
		super(props);
		this.state = {
			newClaimName: '',
			newPropName: ''
		};
	}

	componentDidMount() {
		this.props.dispatch(Actions.GetClaimMappings());
		this.props.dispatch(Actions.GetProperties());
	}

	componentWillUnmount() {
		this.props.dispatch(Actions.SetClaimMappingsViewMode('claimMappingsList'));
	}

	addClaim = () => {
		this.props.dispatch(Actions.AddClaimMapping(this.state.newClaimName, this.state.newPropName));
		this.setState({ newClaimName: '', newPropName: '' });
	}
	deleteClaim = () => {
		this.props.dispatch(Actions.DeleteClaimMapping(this.props.deletingClaimMapping.id));
	}

	onCancelClick = () => {
		this.props.dispatch(Actions.SetClaimMappingsViewMode('claimMappingsList'));

		this.setState({
			newClaimName: '',
			newPropName: '',
		});
	}

	buildDeleteClaimMappingDialog = () => {
		const { t, deletingClaimMapping, dispatch } = this.props;
		const dlgProps = {
			isOpen: true,
			parentSelector: () => document.querySelector(util.portalAppId),
			headerText: t('DeleteClaim'),
			contentText: t('DeleteClaimConfirmMessage', { claim: deletingClaimMapping.name }),
			yesText: t('Yes'),
			closeText: t('Close'),
			cancelText: t('Cancel'),
			onYes: this.deleteClaim,
			onCancel: () => dispatch(Actions.SetDeletingClaimMapping(null)),
			onClose: () => dispatch(Actions.SetDeletingClaimMapping(null))
		};
		return <ConfirmDialog {...dlgProps} />;
	}

	renderCell = (key: string, row: ClaimMappingItem) => {
		const { t, dispatch } = this.props;
		if (key === 'actions') {
			if (row.isBuiltIn) return <></>;
			return <div className='grid-actions'>
				<Button
					className='claim-action-btn'
					icon='mdi mdi-delete-forever'
					title={t('Delete')}
					onClick={() => dispatch(Actions.SetDeletingClaimMapping(row))}
					style='transparent'
					rounded
					size='small'
				/>
			</div>;
		}
	}

	render() {
		const { claimMappings, properties, claimMappingsViewMode, deletingClaimMapping, dispatch, t } = this.props;

		const gridProps: CGridProps = {
			columns: [{ key: 'name', label: t('ClaimName') }, { key: 'propName', label: t('UserPropName') }, { key: 'actions', width: 40 }],
			rows: claimMappings || [],
			onRenderCell: this.renderCell,
			hideGridLine: true,
			rowHeight: 40,
		};

		return <div className='claim-mappings-container'>
			{(claimMappingsViewMode !== 'addClaimMapping') && <div className='claim-mappings-list'>
				<div className='btn-add-claim'><Button icon='mdi mdi-plus' title={t('AddClaim')} text={t('AddClaim')} style='accent' onClick={() => dispatch(Actions.SetClaimMappingsViewMode('addClaimMapping'))} /></div>
				<CGrid {...gridProps} />
			</div>}
			{(claimMappingsViewMode === 'addClaimMapping') && <div className='new-claim-mapping-container'>
				<div className='setting-items'>
					<div className='setting-item'>
						<label title={t('ClaimName')}>{t('ClaimName')}</label>
						<InputEditor
							value={this.state.newClaimName}
							placeholder={t('ClaimName')}
							onChange={(value) => this.setState({ newClaimName: value })}
						/>
					</div>
					<div className='setting-item'>
						<label title={t('UserPropName')}>{t('UserPropName')}</label>
						<DropdownEditor
							text={this.state.newPropName || t('SelectValue')}
							items={properties.map((p) => { return { text: p.name, value: p.name, title: p.name, selected: false }; })}
							onChange={(value) => this.setState({ newPropName: value })}
						/>
					</div>
				</div>
				<div className='footer'>
					<Button
						inline
						style='accent'
						title={t('Create')}
						text={t('Create')}
						disabled={!(this.state.newClaimName && this.state.newClaimName.trim() !== '' && this.state.newPropName)}
						onClick={this.addClaim}
					/>
					<Button inline title={t('Cancel')} text={t('Cancel')} onClick={this.onCancelClick} />
				</div>
			</div>}
			{deletingClaimMapping && this.buildDeleteClaimMappingDialog()}
		</div>;
	}
}

export default connect((state: any) => {
	return state['account-management'].claimMappings;
})(ClaimMappingsBase);
