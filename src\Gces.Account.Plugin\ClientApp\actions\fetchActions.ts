import { safeFetchV2 } from '../utils/safeFetchV2';
import { accountIntegrationTokenV2Url } from '../util';
import * as moment from 'moment';
import { ErrorResponseModelV2, GenerateTokenResponseModelV2 } from '../interfaces/generate-token-interfaces';

async function generateToken(User: string, Password: string, ExpireIn: moment.Moment, OrganizationPath: string): Promise<{ result?: GenerateTokenResponseModelV2; error?: ErrorResponseModelV2; }> {
	try {
		const response = await safeFetchV2(`${accountIntegrationTokenV2Url}/generate`, {
			credentials: 'same-origin',
			method: 'POST',
			headers: { 'Content-type': 'application/json', 'Accept': 'application/json' },
			body: JSON.stringify({ User, Password, ExpireIn: ExpireIn.toISOString(), OrganizationPath })
		});
		if (response) return response;
	} catch {
		return {
			result: null,
			error: null
		};
	}
}

async function getUserOrgPaths(user: string, password: string) {
	try {
		const result = await safeFetchV2('/api/v2/identity/users/organizations', {
			credentials: 'same-origin',
			method: 'POST',
			headers: { 'Content-type': 'application/json', 'Accept': 'application/json' },
			body: JSON.stringify({ userName: user, password })
		});
		if (result && result.result instanceof Array) {
			return result.result;
		}
	} catch { return null; }
}

export const fetchActions = {
	generateToken,
	getUserOrgPaths,
};