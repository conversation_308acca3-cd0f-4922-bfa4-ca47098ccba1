﻿import ActionTypes from '../actions/action-types';
import update from 'immutability-helper';

const defaultTFASettings = {
	enabled: false,
	lifetime: 5,
	type: 'SMS',
	settings: null
};

export default (state = { tfaSettings: defaultTFASettings, busy: false, validated: false, saved: false, hasEmailSettings: false }, action) => {
	switch (action.type) {
		case ActionTypes.SetTFASettings:
			return update(state, { tfaSettings: { $set: action.tfaSettings } });
		case ActionTypes.SetTFASettingsBusy:
			return update(state, { busy: { $set: action.busy } });
		case ActionTypes.SetTFASettingsValidated:
			return update(state, { validated: { $set: action.validated } });
		case ActionTypes.SetTFASettingsSaved:
			return update(state, { saved: { $set: action.saved } });
		case ActionTypes.SetEmailSettings:
			return update(state, { hasEmailSettings: { $set: action.hasEmailSettings } });
		default:
			return state;
	}
}