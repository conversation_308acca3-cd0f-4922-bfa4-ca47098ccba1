import * as React from 'react';
import * as classnames from 'classnames';
import { <PERSON><PERSON>, CheckList, DropdownItemProps } from 'gces-ui';

export interface CCheckListProps {
	items: DropdownItemProps[];
	text: string;
	onSelect: (values: any) => void;
	aid?: string;
	className?: string;
	disabled?: boolean;
	visibilityToggle?: boolean;
	noVisibilityToggle?: boolean;
	showEyeTitle?: string;
	hideEyeTitle?: string;
}

interface LocalState {
	showEncryptedText: boolean;
}

export class CCheckList extends React.PureComponent<CCheckListProps, LocalState> {

	constructor(props: CCheckListProps, context) {
		super(props, context);

		this.state = {
			showEncryptedText: false,
		};
	}

	toggleShowEncryptedText = () => {
		this.setState({ showEncryptedText: !this.state.showEncryptedText });
	}

	render() {
		const { items, text, className, visibilityToggle, noVisibilityToggle, disabled, hideEyeTitle, showEyeTitle, onSelect } = this.props;

		const enableTextSecurity = visibilityToggle && !this.state.showEncryptedText;

		return (
			<div className={classnames(className, 'efc-sensitive-wrapper', 'efc-checklist-wrapper', { 'efc-input-password': visibilityToggle })}>
				<CheckList
					className={classnames({ 'efc-text-security': enableTextSecurity })}
					text={text}
					items={items}
					onSelect={onSelect}
					disabled={disabled || enableTextSecurity}
				/>
				<Button
					className={classnames('efc-input-visible-toggle', { 'efc-input-visible-hidden': !visibilityToggle }, { 'efc-input-visible-none': noVisibilityToggle })}
					icon={this.state.showEncryptedText ? 'mdi mdi-eye' : 'mdi mdi-eye-off'}
					size='small'
					style='transparent'
					rounded
					onClick={this.toggleShowEncryptedText}
					title={this.state.showEncryptedText ? hideEyeTitle : showEyeTitle}
				/>
			</div>
		);
	}
}