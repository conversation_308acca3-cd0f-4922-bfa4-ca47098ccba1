import CGrid, { CGridProps, Row } from 'gces-react-grid';
import { <PERSON><PERSON>, CheckboxWrapper, ConfirmDialog, ConfirmDialogProps, InputEditor, Tab, TabItem } from 'gces-ui';
import * as React from 'react';
import { translate } from 'react-i18next';
import { connect } from 'react-redux';
import { Permission } from '../../../Common/interfaces';
import { PermissionList } from '../../../Common/PermissionList';
import { Role, roleActionCreators, RoleState, UserInfo } from '../../store';
import { MembersEditor } from './MembersEditor';
import EmptyPage from '../../../../components/EmptyPage';
import { RoleConsts } from '../../../../Register/Common';
import * as util from '../../../../util';
import { getRole } from '../../utils';
import { hasManagePermissionForOrgsAndRoles } from '../../../../utils';

interface ConnectedProps {
	user: User;
	roles: Role[];
	users: UserInfo[];
	permissions: Permission[];
	availablePermissions: Permission[];
	selectedRoleId: string;
	selectedOrganizationId: string;
	isAddingMembers: boolean;
	isAddingRole: boolean;
	documentColumns: string[];
	originalDocumentColumns: string[];
	enableStrictPermissionManagement: boolean;
	dispatch: any;
	t: any;
}

interface LocalState {
	selectedIndex: number;
	permissionNames: string[];
	permissionsChanged: boolean;
	deletingUser: UserInfo;
	roleName: string;
	renaming: boolean;
}

enum DocumentColumnType {
	type = 'type',
	updateBy = 'updateBy',
	updated = 'updated',
	createBy = 'createBy',
	created = 'created',
	description = 'description',
	references = 'references',
	permissions = 'permissions',
	categories = 'categories',
}

@translate('role', { wait: true })
class EditorsContainerInner extends React.PureComponent<ConnectedProps, LocalState> {
	state: LocalState = {
		selectedIndex: 0,
		permissionsChanged: false,
		permissionNames: [],
		deletingUser: null,
		roleName: '',
		renaming: false,
	};
	_columns = [
		{ key: 'username', label: this.props.t('ecUsername') },
		{ key: 'email', label: this.props.t('ecEmail') },
		{ key: 'provider', label: this.props.t('ecProvider') },
		{ key: 'deleteAction', label: '', width: 100 },
	];
	_items: TabItem[] = [
		{ title: this.props.t('ecRoleMembers') },
		{ title: this.props.t('ecPermissions') },
	];

	componentWillMount() {
		if (window.AdminPortal.AllowRoleControlColumn) {
			this._items.push({ title: this.props.t('ecRoleColumn') });
		}
	}

	componentWillReceiveProps(nextProps: ConnectedProps) {
		const { selectedOrganizationId, selectedRoleId, roles } = nextProps;
		if (this.props.selectedOrganizationId !== selectedOrganizationId || this.props.selectedRoleId !== selectedRoleId) {
			const role = roles.find(r => r.id === selectedRoleId);
			if (role) {
				this.setState({ roleName: role.displayName, renaming: false });
			}
		}
	}

	onSelectedChanged = (selectedIndex: number) => {
		this.setState({ selectedIndex });
	}
	onSavePermissionsCLick = () => {
		const { permissionsChanged, permissionNames } = this.state;
		if (permissionsChanged) {
			const { selectedOrganizationId, selectedRoleId, dispatch } = this.props;
			dispatch(
				roleActionCreators.updateRolePermissions(
					selectedOrganizationId,
					selectedRoleId,
					permissionNames
				)
			);
		}
	}

	onRenderCell = (key: string, row: UserInfo) => {
		const { user: { id: currentUserId }, roles, selectedOrganizationId, selectedRoleId } = this.props;
		if (key === 'deleteAction') {
			if (this.isEveryone(selectedOrganizationId, selectedRoleId)) {
				return null;
			}
			if (row.id === 'user_id_for_admin' && this.isAdministrator(selectedOrganizationId, selectedRoleId)) {
				return null;
			}
			if (row.id === currentUserId && roles.findIndex(r => r.id === selectedRoleId && r.name === 'orgadmin') > -1) {
				return null;
			}
			return (
				<Button
					rounded
					size='small'
					style='transparent'
					icon='mdi mdi-close'
					className='del-user-btn'
					onClick={() => this.setState({ deletingUser: row })}
				/>
			);
		}
	};

	isEveryone = (orgId: string, roleId: string) => {
		return (orgId === util.GlobalOrganization.Id && roleId === RoleConsts.everyone.id) || (orgId === roleId);
	}

	isAdministrator = (orgId: string, roleId: string) => {
		return orgId === util.GlobalOrganization.Id && roleId === RoleConsts.admin.id;
	}

	buildDeletingUserConfirmDialog = () => {
		const { deletingUser } = this.state;
		if (!deletingUser) return null;

		const { selectedOrganizationId: orgId, selectedRoleId: roleId, enableStrictPermissionManagement, dispatch, t } = this.props;
		const dlgProps: ConfirmDialogProps = {
			parentSelector: (): HTMLElement => document.querySelector(util.portalAppId),
			title: t('ecDeleteUser'),
			yesText: t('cmDelete'),
			noText: t('cmCancel'),
			portalClassName: '',
			yesCallback: () => {
				this.setState({ deletingUser: null });
				dispatch(roleActionCreators.deleteRoleUser(orgId, roleId, deletingUser.id, enableStrictPermissionManagement));
			},
			noCallback: () => this.setState({ deletingUser: null }),
		};
		return <ConfirmDialog {...dlgProps} >{t('ecDeleteUserConfirmMessage', { UserName: deletingUser.username })}</ConfirmDialog>;
	}

	renderNoUsers = () => {
		return (
			<div className='no-users-in-role'>
				{this.props.t('cmEmptyResult')}
			</div>
		);
	}

	renderUserList = () => {
		const { selectedOrganizationId, selectedRoleId } = this.props;
		const showAddLink = selectedOrganizationId !== selectedRoleId && (selectedOrganizationId !== util.GlobalOrganization.Id || selectedRoleId !== RoleConsts.everyone.id);
		return (
			<React.Fragment>
				{this.props.users.length ?
					<CGrid
						rowHeight={40}
						rows={this.props.users.sort((a, b) => {
							if (a.username === 'admin') return -1;
							if (b.username === 'admin') return 1;
							return a.username.toUpperCase().localeCompare(b.username.toUpperCase());
						})}
						hideGridLine={true}
						columns={this._columns}
						useSmallScrollbars={true}
						columnResizing={false}
						onRenderCell={this.onRenderCell}
						renderNoRows={this.renderNoUsers}
					/>
					:
					<EmptyPage
						imageName='locked-user-management'
						tip={this.props.t('ecNoMemberTip')}
						buttonText={this.props.t('ecSelectMembers')}
						onclick={() => this.props.dispatch(roleActionCreators.setIsAddingMembers(true))}
						showTip={showAddLink}
					/>
				}
				{this.buildDeletingUserConfirmDialog()}
			</React.Fragment>
		);
	}
	renderPermissionsList = () => {
		const { selectedRoleId, permissions, user, roles, availablePermissions } = this.props;

		let allRolePermissions = [...availablePermissions];
		const selectRole = roles.find(r => r.id === selectedRoleId);
		if (selectRole?.name !== 'orgadmin' && selectRole?.name !== 'administrator') {
			allRolePermissions = allRolePermissions.filter(p => p.name !== 'assign-manage-user' && p.name !== 'manage-user' && p.name !== 'manage-permission');
		}

		const onPermissionsChange = (permissionNames: string[]) => {
			const { permissions } = this.props;
			const originPermissionNames = permissions.map(p => p.name).sort();
			const permissionsChanged = JSON.stringify(permissionNames.sort()) !== JSON.stringify(originPermissionNames);
			this.setState({ permissionsChanged, permissionNames });
		};

		return (
			<PermissionList
				allPermissions={allRolePermissions}
				permissions={[...permissions]}
				onChange={onPermissionsChange}
				disabledCheckBox={selectedRoleId === RoleConsts.admin.id || !hasManagePermissionForOrgsAndRoles(user.roles)}
				curUser={user}
			/>
		);
	}

	onCellChange = (colName: DocumentColumnType, value: boolean) => {
		const { dispatch, t } = this.props;
		if (value && (colName === DocumentColumnType.references || colName === DocumentColumnType.permissions || colName === DocumentColumnType.categories)) {
			dispatch(roleActionCreators.alertMessage([t('msgPerformance')]));
		}

		this.selectRoleColumn(colName);
	}

	renderRoleDocListColGridCell = (key: string, row: Row) => {
		if (key === 'ShowInList') {
			return <div className='grid-actions'>
				<CheckboxWrapper checked={row.ShowInList} onChange={(value: boolean) => this.onCellChange(row.colName, value)} />
			</div>;
		}
	}

	renderDocListColumnConfig = () => {
		const { documentColumns, t } = this.props;

		const rowData: Row[] = [
			{ ColumnName: t('dlType'), colName: DocumentColumnType.type, ShowInList: documentColumns.indexOf(DocumentColumnType.type) !== -1 },
			{ ColumnName: t('dlUpdateBy'), colName: DocumentColumnType.updateBy, ShowInList: documentColumns.indexOf(DocumentColumnType.updateBy) !== -1 },
			{ ColumnName: t('dlUpdated'), colName: DocumentColumnType.updated, ShowInList: documentColumns.indexOf(DocumentColumnType.updated) !== -1 },
			{ ColumnName: t('dlCreatedBy'), colName: DocumentColumnType.createBy, ShowInList: documentColumns.indexOf(DocumentColumnType.createBy) !== -1 },
			{ ColumnName: t('dlCreated'), colName: DocumentColumnType.created, ShowInList: documentColumns.indexOf(DocumentColumnType.created) !== -1 },
			{ ColumnName: t('dlDescription'), colName: DocumentColumnType.description, ShowInList: documentColumns.indexOf(DocumentColumnType.description) !== -1 },
		];
		if (window.AdminPortal.Edition === 'en') {
			rowData.push({ ColumnName: t('dlReferences'), colName: DocumentColumnType.references, ShowInList: documentColumns.indexOf(DocumentColumnType.references) !== -1 });
			rowData.push({ ColumnName: t('dlPermissions'), colName: DocumentColumnType.permissions, ShowInList: documentColumns.indexOf(DocumentColumnType.permissions) !== -1 });
			rowData.push({ ColumnName: t('dlCategories'), colName: DocumentColumnType.categories, ShowInList: documentColumns.indexOf(DocumentColumnType.categories) !== -1 });
		}
		const gridProps: CGridProps = {
			rows: rowData,
			columns: [
				{ key: 'ColumnName', label: t('ColumnName') },
				{ key: 'ShowInList', label: t('ShowInList') },
			],
			onCellChange: this.onCellChange,
			onRenderCell: this.renderRoleDocListColGridCell,
			hideGridLine: true,
			rowHeight: 40,
		};
		return (
			<div className='role-column-page'>
				<div className='role-column-container'>
					<CGrid {...gridProps} />
				</div>
			</div>
		);
	}

	selectRoleColumn = (columnType: string) => {
		const { documentColumns, dispatch } = this.props;
		const selectedColumn = [...documentColumns];
		const columnIndex = selectedColumn.indexOf(columnType);
		if (columnIndex !== -1) {
			selectedColumn.splice(columnIndex, 1);
		} else {
			selectedColumn.push(columnType);
		}
		dispatch(roleActionCreators.setDocumentColumns(selectedColumn));
	}

	onSaveRoleControlColumnClick = () => {
		const { documentColumns, selectedOrganizationId, selectedRoleId, roles, dispatch } = this.props;
		const role = getRole(selectedOrganizationId, roles, selectedRoleId);
		dispatch(roleActionCreators.updateDocumentColumns(role, documentColumns));
	}

	isSelectedColumnChanged = () => {
		const { originalDocumentColumns, documentColumns } = this.props;
		if (originalDocumentColumns.length !== documentColumns.length) {
			return false;
		}
		for (const originalColumn of originalDocumentColumns) {
			if (documentColumns.indexOf(originalColumn) === -1) {
				return false;
			}
		}
		return true;
	}

	onSaveRoleName = () => {
		const { selectedOrganizationId, selectedRoleId, roles, enableStrictPermissionManagement, dispatch } = this.props;
		const role = roles.find(r => r.id === selectedRoleId);
		if (role?.displayName === this.state.roleName) {
			this.setState({ renaming: false });
			return;
		}
		dispatch(roleActionCreators.updateRoleName(selectedOrganizationId, selectedRoleId, this.state.roleName, enableStrictPermissionManagement,
			(success) => {
				if (!success) {
					this.setState({ roleName: role.displayName });
				}
				this.setState({ renaming: false });
			}));
	}

	renderRoleName = (role: Role) => {
		return (
			<div className='role-name'>{role.displayName}</div>
		);
	}

	renderRoleNameWithEditor = (role: Role) => {
		const { t } = this.props;
		const { roleName } = this.state;
		return (
			<div className='role-name support-edit'>
				{this.state.renaming &&
					<InputEditor
						autoFocus
						className='role-name-editor'
						value={roleName}
						onEveryChange={val => this.setState({ roleName: val })}
						onBlur={() => this.onSaveRoleName()}
					/>
				}
				{!this.state.renaming &&
					<React.Fragment>
						<span title={roleName}>{roleName}</span>
						{!role.isBuiltin &&
							<Button
								className='edit-role-name-btn'
								rounded
								icon='mdi mdi-pencil'
								size='small'
								style='transparent'
								title={t('rcEditRoleName')}
								onClick={() => this.setState({ renaming: true })}
							/>
						}
					</React.Fragment>
				}
			</div>
		);
	};

	render() {
		const { roles, selectedOrganizationId, selectedRoleId, isAddingMembers, isAddingRole, dispatch, t } = this.props;
		const role = roles.find(r => r.id === selectedRoleId);
		if (!role || isAddingRole) return null;
		return (
			<div className='editors-container'>
				{!isAddingMembers &&
					<>
						<div className='ec-header'>
							{window.AdminPortal.Edition === 'zh' ? this.renderRoleNameWithEditor(role) : this.renderRoleName(role)}
							{this.state.selectedIndex === 0 && selectedOrganizationId !== selectedRoleId && (selectedOrganizationId !== util.GlobalOrganization.Id || selectedRoleId !== RoleConsts.everyone.id) &&
								<Button
									size='small'
									style='accent'
									className='select-members-btn'
									icon='mdi mdi-plus'
									text={t('ecSelectMembers')}
									title={t('ecSelectMembers')}
									onClick={() => dispatch(roleActionCreators.setIsAddingMembers(true))}
								/>
							}
						</div>

						<div className='editors-tab-container'>
							<Tab
								items={this._items}
								selected={this.state.selectedIndex}
								onSelectedChanged={this.onSelectedChanged}
							/>
						</div>
						<div className='ec-body'>
							{this.state.selectedIndex === 0 && this.renderUserList()}
							{this.state.selectedIndex === 1 && this.renderPermissionsList()}
							{this.state.selectedIndex === 2 && this.renderDocListColumnConfig()}
						</div>
						{this.state.selectedIndex === 1 &&
							<div className='ec-footer'>
								<Button
									size='small'
									style='accent'
									className='save-btn'
									text={t('cmSave')}
									title={t('cmSave')}
									disabled={!this.state.permissionsChanged}
									onClick={this.onSavePermissionsCLick}
								/>
							</div>
						}
						{this.state.selectedIndex === 2 &&
							<div className='ec-footer'>
								<Button
									size='small'
									style='accent'
									className='save-btn'
									text={t('cmSave')}
									title={t('cmSave')}
									disabled={this.isSelectedColumnChanged()}
									onClick={this.onSaveRoleControlColumnClick}
								/>
							</div>
						}
					</>}
				{isAddingMembers && <MembersEditor />}
			</div>
		);
	}
}

export const EditorsContainer = connect(
	(state: { role: RoleState, navApp: { user: User } }) => ({
		user: state.navApp.user,
		users: state.role.users,
		roles: state.role.roles,
		permissions: state.role.permissions,
		availablePermissions: state.role.availablePermissions,
		selectedRoleId: state.role.selectedRoleId,
		selectedOrganizationId: state.role.selectedOrganizationId,
		isAddingMembers: state.role.isAddingMembers,
		isAddingRole: state.role.isAddingRole,
		documentColumns: state.role.documentColumns,
		originalDocumentColumns: state.role.originalDocumentColumns,
		enableStrictPermissionManagement: state.role.enableStrictPermissionManagement,
	})
)(EditorsContainerInner) as React.ComponentClass<{}>;