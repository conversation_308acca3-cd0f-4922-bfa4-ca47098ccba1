import { Checkbox } from 'gces-ui';
import * as moment from 'moment';
import * as React from 'react';
import { ExecutionTimeRange } from '../../store/interface';
import { getValidStartTimeAndEndTime, isNotNull } from '../../utils';
import { ExecutionTimeEditor } from './ExecutionTimeEditor';

interface AddExecutionTimeRangeProps {
	checkboxName: string;
	startTimeEditorName: string;
	endTimeEditorName: string;
	dailyExecutionTimeRange: ExecutionTimeRange;
	timeFormat: string;
	onChangeExecutionTimeRange: (newDailyExecutionTimeRange: ExecutionTimeRange) => void;
}
export class AddExecutionTimeRange extends React.PureComponent<AddExecutionTimeRangeProps> {
	onCheck = () => {
		if (this.props.dailyExecutionTimeRange) {
			this.deleteExecutionTimeRange();
			return;
		}
		const startTime = moment('00:00', this.props.timeFormat);
		const endTime = moment('23:59', this.props.timeFormat);
		this.onChangeExecutionTimeRange(startTime, endTime);
	}
	onStartTimeChange = (value: moment.Moment | string) => {
		this.onChangeExecutionTimeRange(value, this.props.dailyExecutionTimeRange.endTime);
	}
	onEndTimeChange = (value: moment.Moment | string) => {
		this.onChangeExecutionTimeRange(this.props.dailyExecutionTimeRange.startTime, value);
	}
	deleteExecutionTimeRange() {
		const { onChangeExecutionTimeRange } = this.props;
		onChangeExecutionTimeRange && onChangeExecutionTimeRange(null);
	}
	cloneMoment(m: moment.Moment | string): moment.Moment | string {
		if (moment.isMoment(m)) {
			return m.clone();
		}
		return m;
	}
	onChangeExecutionTimeRange = (startTime: moment.Moment | string, endTime: moment.Moment | string) => {
		const { dailyExecutionTimeRange } = this.props;
		const { startTimeValid, startTimeValue, endTimeValid, endTimeValue } = getValidStartTimeAndEndTime(startTime, endTime);
		const newDailyExecutionTimeRange: ExecutionTimeRange = { startTime: startTimeValid ? startTimeValue : this.cloneMoment(dailyExecutionTimeRange?.startTime), endTime: endTimeValid ? endTimeValue : this.cloneMoment(dailyExecutionTimeRange?.endTime) };
		const { onChangeExecutionTimeRange } = this.props;
		onChangeExecutionTimeRange && onChangeExecutionTimeRange(newDailyExecutionTimeRange);
	}
	renderExecutionTimeEditors(startTimeEditorName: string, endTimeEditorName: string, timeFormat: string, dailyExecutionTimeRange: ExecutionTimeRange) {
		const startTime = dailyExecutionTimeRange?.startTime;
		const endTime = dailyExecutionTimeRange?.endTime;
		const { startTimeValid, endTimeValid } = getValidStartTimeAndEndTime(startTime, endTime);
		return (
			<div className='execution-time-editors'>
				<ExecutionTimeEditor time={startTime} title={startTimeEditorName} invalid={!startTimeValid} timeFormat={timeFormat} onTimeChange={this.onStartTimeChange} />
				<ExecutionTimeEditor time={endTime} title={endTimeEditorName} invalid={!endTimeValid} timeFormat={timeFormat} onTimeChange={this.onEndTimeChange} />
			</div>
		);
	}
	render() {
		const { checkboxName, startTimeEditorName, endTimeEditorName, timeFormat, dailyExecutionTimeRange } = this.props;
		const checked = isNotNull(dailyExecutionTimeRange);
		return (
			<div className='add-execution-time-range'>
				<Checkbox
					inverted={window.inverted}
					inline
					value={checkboxName}
					text={checkboxName}
					checked={checked}
					onChange={this.onCheck}
				/>
				{checked && this.renderExecutionTimeEditors(startTimeEditorName, endTimeEditorName, timeFormat, dailyExecutionTimeRange)}
			</div>
		);
	}
}
