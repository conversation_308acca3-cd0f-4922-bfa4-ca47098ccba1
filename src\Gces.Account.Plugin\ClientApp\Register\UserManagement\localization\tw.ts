export const userTW: LanguageKeyValueMap = {
	// common
	cmNoMembersInOrg: '當前組織沒有用戶',

	UserManagement: '用戶管理',
	Yes: '確定',
	Edit: '編輯',
	Delete: '刪除',
	Save: '保存', // Save
	Cancel: '取消',
	Close: '關閉',
	AddUser: '創建用戶',
	SelectMembers: '選擇成員',
	EditUser: '編輯用戶',
	SearchText: '搜索內容',
	DeleteUser: '刪除用戶',
	RemoveUserFromOrg: '從組織中移除用戶',
	Username: '用戶名',
	Email: '郵箱',
	Mobile: '手機號碼',
	FirstName: '名字',
	LastName: '姓',
	FullName: '全名',
	Password: '密碼',
	ConfirmPassword: '確認密碼',
	PasswordNeverExpire: '密碼永不過期',
	Roles: '角色',
	HomePage: '主頁',
	ManagedBy: '管理者',
	Provider: '提供者',
	Organizations: '組織',
	Status: '狀態',
	Enabled: '啟用',
	Disabled: '禁用',
	Enable: '啟用',
	Disable: '禁用',
	Locked: '被鎖定',
	Actions: '操作',
	GetUsersError: '獲取用戶錯誤',
	UsernameIsRequired: '用戶名是必填項',
	UsernameAlreadyExists: '用戶名已經存在',
	EmailIsRequired: '郵箱是必填項',
	EmailIsInvalid: '非法的郵箱地址',
	EmailAlreadyExists: '郵箱地址已經存在',
	MobileAlreadyExists: '電話號碼已經存在',
	WeakPasswordRequirement: '密碼不能為空，且長度應該在1-150位之間',
	StrongPasswordRequirement: '密碼的長度應該在8-150位之間，且必須包含一個大寫字母，一個小寫字母和一個數字',
	PasswordNotMatch: '密碼和確認密碼不匹配',
	PasswordIsBlank: '密碼不能僅包含空格字符',
	DeleteUserConfirmMessage: '確定要刪除用戶“{{user}}”嗎？ ',
	RemoveUserConfirmMessage: '確定要從組織"{{organization}}"移除用戶“{{user}}”嗎?',
	OneRolePerLine: ' （一行一個角色）',
	OneValuePerLine: ' （每行一個值）',
	Import: '導入用戶',
	Export: '導出',
	Users: '用戶',
	Template: '模板',
	NewUserName: '新用戶名',
	NewUserEmail: '新用戶郵箱',
	SelectValue: '選擇值',
	NullValue: '<空>',
	ImportUsers: '導入用戶',
	DragDropFile: '拖拽文件到這裡',
	Or: '或者',
	ClickHere: '點擊這裡',
	SelectFile: '選擇文件',
	ExportTemplate: '導出模板',
	UnlockUser: '解除鎖定',
	UnlockUserConfirm: '確定解除鎖定用戶“{{user}}”嗎？ ',
	UserTemplate: '用戶模板',
	ErrorRemoveAdminFromAdministratorRole: "不能把用戶 '{{userName}}' 從用戶組 '{{roleName}}' 中刪除。",
	ImportUsersSuccessMsg: '成功導入 {{count}} 個用戶。',
	ImportUsersFailMsg: '{{count}} 個用戶導入失敗, <a href="{{url}}" target="_blank">點擊查看詳情</a>。 ',
	ImportUsersFailed: '導入用戶失敗。 ',
	UsernameLengthExceedLimit: '用戶名不能爲空，且長度應該在1-150位之間',
	ShowPropertyValue: '顯示值',
	HidePropertyValue: '隱藏值',

	// homePageSelector
	loadingDocuments: '正在加載文檔...',
	noSearchResultTip: '沒有匹配的搜索結果',
	searchPlaceHolder: '在此處輸入搜索文本...',

	// select members
	smSelectMembers: '選擇成員',
	smEmptyResult: '沒有符合條件的條目',
	smMembers: '({{count}} 個用戶)',
	smAddUser: '添加',

	UserDetail: '用戶詳情',
	ShowSubOrg: '顯示子組織的用戶',
	NotShowSubOrg: '不顯示子組織的用戶',
	RemoveUser: '從當前組織中移除用戶',

	udBasicInfo: '基礎信息',
	udEmpty: '[未填寫]',
	NoOrganizations: '當前用戶不屬於任何組織',
	NoRoles: '未在組織中分配角色',
	onlyNoMemberOfSubOrg: '從組織中移除用戶只能在不顯示子組織用戶時使用',
	ecNoMemberTip: '當前組織下沒有用戶，如需添加請點擊',

	Error: '錯誤',
	umError_1024: '手機號碼已存在',
	umError_1028: '"admin"用戶不能被刪除',
	umError_1039: '系統管理員的管理者只能爲全局',
	umError_1040: '權限不足',
	umError_5010: '組織管理員不能將自己從當前組織中移除',

	rt_user: '用戶',
	rt_role: '角色',
	'rt_organization user': '組織用戶',

	error_V2_007_001_0002: '用戶名不能爲空。',
	error_V2_007_001_0003: '郵箱地址不能爲空。',
	error_V2_007_001_0004: '密碼不能爲空。',
	error_V2_007_001_0005: '用戶名“{{UserName}}”已經存在。',
	error_V2_007_001_0006: '郵箱地址“{{Email}}”已經存在。',
	error_V2_007_001_0015: '非法的郵箱地址“{{Email}}”。',
	error_V2_007_001_0028: '系統管理員的管理者只能爲全局組織。',
	error_V2_007_001_0030: '電話號碼“{{Mobile}}”已經存在。',
	error_V2_007_004_0003: '自定義屬性“{{CustomProperty}}”的值“{{Value}}”不合法。',
};