.license-management-container {
	.grid-cell-editor {
		position: relative;
	}

	.license-item-key {
		display: flex;
		flex-direction: column;
		justify-content: center;
		width: 100%;
		position: relative;
		line-height: normal;

		.display-key {
			display: flex;
			align-items: center;

			.license-item-key-tips {
				font-size: $ef-font-size-lg;

				&:hover {
					.need-migration-tip {
						pointer-events: all;
						opacity: 1;
					}
				}

				&.normal {
					display: none;
				}

				&.warn {
					color: $warning;

					&:hover {
						color: var(--gces-warning-dk-15);
					}
				}

				&.error {
					color: $danger;

					&:hover {
						color: var(--gces-warning-dk-15);
					}
				}

				.need-migration-tip {
					position: absolute;
					z-index: $zindex-popover;
					bottom: calc(50% + 15px);
					left: 0;
					min-width: 200px;
					max-width: 260px;
					padding: 5px 10px;
					word-wrap: break-word;
					white-space: pre-line;
					transition: opacity .2s ease-in-out;
					text-align: left;
					pointer-events: none;
					opacity: 0;
					color: $ef-text;
					border-radius: $ef-border-rad;
					background-color: $ef-bg-dk;
					box-shadow: $ef-shadow-border;
					font-size: $ef-font-size-sm;
					line-height: 20px;
				}
			}
		}
	}

	.license-item-detail-content {
		display: flex;
		align-items: center;
		width: 100%;

		.detail-inner {
			width: 100%;

			.detail-item {
				display: flex;
				line-height: normal;

				.item-title {
					flex: 1 0 50%;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				.item-content {
					flex: 1;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
		}
	}

	.license-item-detail {
		display: flex;
		flex-direction: column;
		justify-content: center;
		padding: 10px 0 10px 0;
		line-height: 25px;
		width: 100%;

		.license-summary {
			display: flex;
			flex-direction: column;

			.detail-item {
				display: flex;

				.item-title {
					margin: 0 15px 0 0;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				.item-content {
					flex: 1;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
		}

		.license-item-detail-btn {
			color: blue;
		}

		.license-item-detail-btn:hover {
			text-decoration: underline;
			cursor: pointer;
		}
	}

	.grid-actions {
		.license-action-btn {
			font-size: var(--gces-ef-font-size);
			margin-left: 20px;

			.ef-abs-icon {
				color: var(--gces-ef-accent);
			}
		}
	}
}