import * as React from 'react';
import { translate } from 'react-i18next';

import { Button, Radio } from 'gces-ui';
import * as Modal from 'react-modal';

export interface OrganizationDragDialogProps {
    from: any;
    to: any;
    order?: number;
    title: string;
    yesText: string;
    noText?: string;
    portalClassName: string;
    yesCallback?: () => void;
    noCallback?: () => void;
    parentSelector?: () => HTMLElement;
    buttonSize?: 'default' | 'small' | 'large';
    yesDisabled?: boolean;
    onPositionChange?: (from: any, to: any, inside: boolean, order: number) => void;

}

interface OrganizationDragDialogState {
    radioVal: string;
}

interface ConnectedProps {
    t?: any;
}

@translate('organization', { wait: true })
export class OrganizationDragDialog extends React.PureComponent<OrganizationDragDialogProps & ConnectedProps, OrganizationDragDialogState> {
    _header: HTMLDivElement = null;

    constructor(props) {
        super(props);
        this.state = {
            radioVal: '0',
        };
    }

    componentDidMount(): void {
        this.getRadioValue('1');
    }

    getRadioValue = (value: string) => {
        const { onPositionChange, from, to } = this.props;
        this.setState({
            radioVal: value,
        });
        switch (value) {
            case '1':
                if (from.ref.parentTenantId === to.ref.parentTenantId && from.ref.order < to.ref.order) {
                    onPositionChange(from, to, false, to.ref.order);
                } else {
                    onPositionChange(from, to, false, to.ref.order + 1);
                }
                break;
            case '2':
                if (from.ref.parentTenantId === to.ref.parentTenantId && from.ref.order < to.ref.order) {
                    onPositionChange(from, to, false, to.ref.order + 1);
                } else {
                    onPositionChange(from, to, false, to.ref.order + 2);
                }
                break;
            case '3':
                onPositionChange(from, to, true, 0);
                break;
        }
    }

    render() {
        const { t, from, title, yesText, noText, portalClassName, yesCallback, noCallback, yesDisabled, parentSelector, buttonSize } = this.props;
        const modalProps = {
            isOpen: true,
            parentSelector,
            portalClassName,
            className: { base: 'confirm-dialog organization-drag', afterOpen: '', beforeClose: '' },
            overlayClassName: { base: 'modal fade confirm-dialog-overlay', afterOpen: 'show', beforeClose: '' },
            style: { overlay: { display: 'block' } },
            ariaHideApp: false,
        };
        return (
            <Modal {...modalProps}>
                <div className='dialog-header tab-group' ref={r => this._header = r}>
                    <span>{title}</span>
                    <Button
                        className='dlg-close-btn'
                        aid='close-btn'
                        size='small'
                        icon='mdi mdi-close'
                        style='transparent'
                        rounded
                        onClick={() => noCallback
                            ? noCallback()
                            : (yesCallback && yesCallback())
                        }
                    />
                </div>
                <div className='dialog-body tab-group '>
                    <p title={t('moveOrganization', { tenantName: from.ref.name })}>{t('moveOrganization', { tenantName: from.ref.name })}</p>
                    <div id='radiogroup' className='text-content' role='radiogroup'>
                        <div className='drag-radio-group drag-front'>
                            <svg className='svg-style' viewBox='0 0 66 66' version='1.1' xmlns='http://www.w3.org/2000/svg' >
                                <title>In front of</title>
                                <g stroke='none' strokeWidth='1' fill='none' fillRule='evenodd'>
                                    <g transform='translate(2.000000, 14.000000)'>
                                        <rect fill='#C2C2C2' x='0' y='14' width='50' height='10' />
                                        <rect className='svg-rect-style' fill='#FFFFFF' x='34.5' y='0.5' width='27' height='9' />
                                        <polygon fill='#5E5E5E' points='23 6 23 9 12 4.5 23 0 23 3 32 3 32 6' />
                                    </g>
                                </g>
                            </svg>
                            <div className='radio-show'>
                                <Radio value='1' text='' checked={this.state.radioVal === '1'} onChange={this.getRadioValue} />
                                <p title={t('organizationDragAbove')}>{t('organizationDragAbove')}</p>
                            </div>
                        </div>
                        <div className='drag-radio-group drag-behind'>
                            <svg className='svg-style' viewBox='0 0 66 66' version='1.1' xmlns='http://www.w3.org/2000/svg' >
                                <title>Behind</title>
                                <g stroke='none' strokeWidth='1' fill='none' fillRule='evenodd'>
                                    <g transform='translate(2.000000, 28.000000)'>
                                        <rect fill='#C2C2C2' x='0' y='0' width='50' height='10' />
                                        <polygon fill='#5E5E5E' points='23 21 23 24 12 19.5 23 15 23 18 32 18 32 21' />
                                        <rect className='svg-rect-style' fill='#FFFFFF' x='34.5' y='14.5' width='27' height='9' />
                                    </g>
                                </g>
                            </svg>
                            <div className='radio-show'>
                                <Radio value='2' text='' checked={this.state.radioVal === '2'} onChange={this.getRadioValue} />
                                <p title={t('organizationDragBelow')}>{t('organizationDragBelow')}</p>
                            </div>
                        </div>
                        <div className='drag-radio-group drag-sub'>
                            <svg className='svg-style' viewBox='0 0 66 66' version='1.1' xmlns='http://www.w3.org/2000/svg' >
                                <title>Sub</title>
                                <g stroke='none' strokeWidth='1' fill='none' fillRule='evenodd'>
                                    <g transform='translate(0.000000, 23.000000)'>
                                        <rect fill='#C2C2C2' x='0' y='5' width='50' height='10' />
                                        <rect className='svg-rect-style' fill='#FFFFFF' x='26.5' y='21.5' width='39' height='9' />
                                        <path d='M14,15 L15,15 L15,26 L26,26 L26,27 L14,27 L14,15 Z M61.5,8 L64.5,8 L60,19 L55.5,8 L58.5,8 L58.5,0 L61.5,0 L61.5,8 Z' fill='#5E5E5E' />
                                    </g>
                                </g>
                            </svg>
                            <div className='radio-show'>
                                <Radio value='3' text='' checked={this.state.radioVal === '3'} onChange={this.getRadioValue} />
                                <p title={t('organizationDragSub')}>{t('organizationDragSub')}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div className='dialog-footer tab-group'>
                    {yesText && <Button inline text={yesText} title={yesText} disabled={yesDisabled !== undefined ? yesDisabled : false} size={buttonSize || 'small'} style='accent' onClick={() => (yesCallback && yesCallback())} />}
                    {noText && <Button inline text={noText} title={noText} size={buttonSize || 'small'} onClick={() => (noCallback && noCallback())} />}
                </div>

            </Modal>
        );
    }
}