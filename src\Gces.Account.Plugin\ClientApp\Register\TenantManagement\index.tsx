import { watchTenant, tenantReducer } from './store';
import { organizationLocaleData } from './localization';
import { OrganizationManagement } from './components/OrganizationManagement';

window.AdminPortal.Register.Locale(organizationLocaleData);
window.AdminPortal.Register.StoreExtension('tenant', tenantReducer, watchTenant);
window.AdminPortal.Register.SectionItem(
	'account-management-organization',
	'account-management',
	'organization',
	'mdi mdi-lan',
	'Organization',
	'Organization management.',
	true,
	OrganizationManagement,
	true,
	undefined,
	undefined,
	1,
	organizationLocaleData.namespace
);
const defaultMiddleware: Middleware = api => next => action => AppContext.internalMiddleware ? AppContext.internalMiddleware(api, next, action) : next(action);
AppContext.registerMiddleware = AppContext.registerMiddleware ?? (middleware => AppContext.internalMiddleware = middleware);
AppContext.unregisterMiddleware = AppContext.unregisterMiddleware ?? (middleware => { if (AppContext.internalMiddleware === middleware) AppContext.internalMiddleware = null; });
AppContext.reduxMiddleware = AppContext.reduxMiddleware ?? defaultMiddleware;
(window.AdminPortal as any).ReduxMiddlewares.push(AppContext.reduxMiddleware);