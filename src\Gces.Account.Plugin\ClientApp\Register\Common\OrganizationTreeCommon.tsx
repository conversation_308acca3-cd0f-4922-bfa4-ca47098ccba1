import * as React from 'react';
import { translate } from 'react-i18next';
import { CTreeProps, CTree, CTreeItemProps } from './CTree';
import { InputEditor, Button, Dropdown, DropdownProps, DropdownItemProps, AriaIcon } from 'gces-ui';
import { OrganizationItem } from '../TenantManagement/store';
import * as _ from 'lodash';

type TreeItem = OrganizationItem & CTreeItemProps & {
	children?: TreeItem[],
};

export type OrganizationTreeProps = {
	items: OrganizationItem[];
	userTenantId: string;
	isAdding?: boolean;
	isEditing?: boolean;
	draggable?: boolean;
	onItemSelected?: (id: string) => void;
	enableEdit?: boolean,
	hideExpandCollapseAll?: boolean,
	selectedId?: string,
	onAdd?: (selectedId: string) => void,
	onMoveUp?: (selectedId: string) => void,
	onMoveDown?: (selectedId: string) => void,
	onMoveDelete?: (selectedId: string) => void,
	onDragStart?: (e: any, path: string, item: any) => boolean,
	onDragOver?: (e: any, path: string, item: any, inside: boolean, draggingItemPath: string, draggingItem: any) => boolean,
	onPositionChange?: (from: any, to: any, inside: boolean, order: number) => void,
	t?: any;
};

export type OrganizationTreeState = {
	search: string,
	treeRoot?: TreeItem,
	allExpanded?: boolean,
	allCollapsed?: boolean,
};

@translate('organization', { wait: true })
export class OrganizationTreeCommon extends React.PureComponent<OrganizationTreeProps, OrganizationTreeState>{
	_cTree?: CTree;
	public static defaultProps = {
		enableEdit: false,
	};

	state: OrganizationTreeState = {
		search: '',
	};

	findItemIndex = (items: TreeItem[], targetId: string, stack: number[]): boolean => {
		for (let i = 0; items && i < items.length; i++) {
			stack.push(i);

			if (items[i].id === targetId) {
				return true;
			}
			else if (this.findItemIndex(items[i].children, targetId, stack)) {
				return true;
			}
			else {
				stack.pop();
			}
		}
		return false;
	};

	findPath = (root: TreeItem, targetId: string) => {
		const items = root ? [root] : [];
		const stack = [];
		if (this.findItemIndex(items, targetId, stack)) {
			return stack.join('-');
		} else {
			return '';
		}
	}

	componentWillMount() {
		this.updateStateByProps(this.props);
	}

	componentWillReceiveProps(nextProps: OrganizationTreeProps, nextState) {
		this.updateStateByProps(nextProps);
	}

	componentDidUpdate() {
		const { isAdding } = this.props;
		if (isAdding) {
			this._cTree && this._cTree.positionToSelectedPath();
		}
	}

	updateStateByProps = (props: OrganizationTreeProps) => {
		const { treeRoot: currentRoot } = this.state;
		const { items, userTenantId, isAdding, selectedId, t } = props;
		const allItems: OrganizationItem[] = isAdding ? [...items, {
			id: 'fakeNewItem',
			name: t('tntNewOrganization'),
			parentTenantId: selectedId,
			order: 100000,
			props: [],
		}] : items;
		const rootNode = this.buildTree(allItems, userTenantId);

		const flattenTree = (node: TreeItem) => {
			let res = node ? [node] : [];
			if (node && node.children && node.children.length) {
				node.children.forEach(child => res = res.concat(flattenTree(child)));
			}
			return res;
		};
		const currentItems = flattenTree(currentRoot);
		const newItems = flattenTree(rootNode);
		newItems.forEach(s => {
			const oldItem = currentItems.find(v => v.id === s.id);
			if (oldItem && typeof (oldItem.expand) === 'boolean') {
				s.expand = oldItem.expand;
			}
			else {
				s.expand = true;
			}
		});
		const targetState = { treeRoot: rootNode };
		this.setState(targetState);
	}

	expandAllClick = () => {
		const { treeRoot } = this.state;
		if (treeRoot) {
			const expandNode = (node: TreeItem) => {
				node.expand = true;
				if (node.children && node.children.length) {
					node.children.forEach(s => expandNode(s));
				}
			};
			const clonedTreeRoot = _.cloneDeep(treeRoot);
			expandNode(clonedTreeRoot);
			clonedTreeRoot.expand = true;
			this.setState({ treeRoot: clonedTreeRoot, allCollapsed: false, allExpanded: true });
		}
	}

	collapseAllClick = () => {
		const { treeRoot } = this.state;
		if (treeRoot) {
			const collapseNode = (node: TreeItem) => {
				node.expand = false;
				if (node.children && node.children.length) {
					node.children.forEach(s => collapseNode(s));
				}
			};
			const clonedTreeRoot = _.cloneDeep(treeRoot);
			collapseNode(clonedTreeRoot);
			clonedTreeRoot.expand = false;
			this.setState({ treeRoot: clonedTreeRoot, allCollapsed: true, allExpanded: false });
		}
	}

	onSearchChange = (search: string) => {
		search = search || '';
		this.setState({ search });
	}

	buildTree = (items: OrganizationItem[], userTenantId: string) => {
		const { t } = this.props;
		const clonedItems = items.map(s => { return { ...s }; });
		const rootItem = (userTenantId ? clonedItems.find(s => s.id === userTenantId) : { id: userTenantId, name: t('tntGlobal') }) as TreeItem;
		this.buildTreeNodes(rootItem, clonedItems);
		return rootItem;
	}

	buildTreeNodes = (parentNode: TreeItem, items: OrganizationItem[]) => {
		const allSubItems = items.filter(s => !parentNode.id ? !s.parentTenantId : s.parentTenantId === parentNode.id).sort((a, b) => a.order - b.order);
		if (allSubItems.length) {
			parentNode.children = [...allSubItems];
			parentNode.children.forEach(s => this.buildTreeNodes(s, items));
		}
	}

	applyFilter = (node: TreeItem, filterStr: string): TreeItem => {
		const currentMatched = node.name.toLowerCase().indexOf(filterStr.toLowerCase()) !== -1;
		const newChildren = (node.children || []).map(s => this.applyFilter(s, filterStr)).filter(s => !!s);
		const childrenMatched = !!newChildren.length;
		if (currentMatched === false && childrenMatched === false) {
			return null;
		}
		node.children = childrenMatched ? newChildren : null;
		return node;
	}

	onSelectOrganization = (item: OrganizationItem) => {
		const { onItemSelected, selectedId, isAdding, isEditing } = this.props;
		if (!item || item.id === selectedId || isAdding || isEditing) return;

		onItemSelected && onItemSelected(item.id);
	}

	onContextItemSelected = (currentId: string, value: string) => {
		const { onMoveUp, onMoveDown, onMoveDelete } = this.props;

		switch (value) {
			case 'up':
				onMoveUp && onMoveUp(currentId);
				break;
			case 'down':
				onMoveDown && onMoveDown(currentId);
				break;
			case 'delete':
				onMoveDelete && onMoveDelete(currentId);
				break;
		}
	}
	onMoreOptionToggle = (item: any, open: boolean, target?: HTMLElement) => {
		if (open && item) {
			this.onSelectOrganization(item);
		}
	}
	formatGroupItem = (item, current, isHover: boolean = false) => {
		const { selectedId, onAdd, t, enableEdit, isAdding, isEditing } = this.props;
		const currentId = isHover ? item.id : selectedId;
		const { treeRoot } = this.state;
		const isRoot = treeRoot && treeRoot.id === currentId;
		let itemOrderTag = '';
		if (!isRoot) {
			const findItemInList = (item: TreeItem, list: TreeItem[]): TreeItem[] => {
				if (list) {
					if (list.some(s => s.id === item.id)) {
						return list;
					}
					for (const subItem of list) {
						const subList = findItemInList(item, subItem.children);
						if (subList) {
							return subList;
						}
					}
				}
				return null;
			};
			const itemList = findItemInList(item, treeRoot.children);
			if (itemList) {
				if (itemList.length === 1) {
					itemOrderTag = 'only';
				} else if (itemList[0].id === item.id) {
					itemOrderTag = 'first';
				}
				else if (itemList[itemList.length - 1].id === item.id) {
					itemOrderTag = 'last';
				}
			}
		}

		let dropDownItems: DropdownItemProps[] = [
			{
				value: 'up',
				text: t('tntTenantMoveUp'),
				title: t('tntTenantMoveUp'),
				icon: 'mdi mdi-arrow-up',
			},
			{
				value: 'down',
				text: t('tntTenantMoveDown'),
				title: t('tntTenantMoveDown'),
				icon: 'mdi mdi-arrow-down',
			},
			{
				value: 'delete',
				text: t('tntDeleteTenant'),
				title: t('tntDeleteTenant'),
				icon: 'mdi mdi-delete-forever',
			}
		];

		switch (itemOrderTag) {
			case 'only':
				dropDownItems = dropDownItems.filter(s => s.value === 'delete');
				break;
			case 'first':
				dropDownItems = dropDownItems.filter(s => s.value !== 'up');
				break;
			case 'last':
				dropDownItems = dropDownItems.filter(s => s.value !== 'down');
				break;
		}

		const addDropdownProps: DropdownProps = {
			icon: 'mdi mdi-dots-vertical',
			items: dropDownItems,
			onSelect: (value) => this.onContextItemSelected(currentId, value),
			onToggle: (open, target) => this.onMoreOptionToggle(item, open, target),
			hiddenChevron: true,
			style: 'transparent',
			title: t('tntActions'),
			size: 'small',
			offset: true,
			rounded: true,
			menuClassName: 'ef-inverted',
			inline: true,
		};

		return (
			<React.Fragment>
				<i className='tree-item-icon mdi mdi-lan' />
				<span title={item.name} className='tree-item-name' style={{ textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap', flexShrink: 5 }}>{item.name}</span>
				{enableEdit && currentId === item.id && !isAdding && !isEditing && (
					<div className='tree-item-actions'>
						{!isRoot && <Dropdown {...addDropdownProps} />}
						<div className='group-actions'>
							<Button icon='mdi mdi-plus' onClick={() => { this.singleItemExpandChange(item, true); onAdd && onAdd(currentId); }} title={t('tntAddTenant')} size='small' style='transparent' rounded />
						</div>
					</div>
				)
				}
			</React.Fragment>
		);
	}
	formatHoverGroupItem = (item, current) => {
		return this.formatGroupItem(item, current, true);
	}
	singleItemExpandChange = (item, val: boolean) => {
		const { treeRoot } = this.state;
		const targetItem = this.findSingleItem(treeRoot, item.id);
		targetItem.expand = val;
		if (val) {
			this.setState({ allCollapsed: false });
			if (targetItem === treeRoot) {
				this.setState({ allExpanded: true });
			}
		} else {
			this.setState({ allExpanded: false });
			if (targetItem === treeRoot) {
				this.setState({ allCollapsed: true });
			}
		}
		this.forceUpdate();
	}

	findSingleItem = (item: TreeItem, targetId: string): TreeItem => {
		if (item.id === targetId) {
			return item;
		}
		if (item.children && item.children.length) {
			for (const child of item.children) {
				const subItem = this.findSingleItem(child, targetId);
				if (subItem) {
					return subItem;
				}
			}
		}
	}

	render() {
		const { t, hideExpandCollapseAll, isAdding, isEditing, selectedId, onPositionChange, onDragStart, onDragOver } = this.props;
		const { search, treeRoot } = this.state;
		if (!treeRoot) {
			return (<React.Fragment />);
		}
		let clonedTree = _.cloneDeep(treeRoot);
		if (clonedTree.name === 'Global') {
			clonedTree.name = t('tntGlobal');
		}
		clonedTree = this.applyFilter(clonedTree, search);

		const treePath = this.findPath(clonedTree, isAdding ? 'fakeNewItem' : selectedId);

		const treeProps: CTreeProps = {
			items: clonedTree ? [clonedTree] : [],
			displayProp: 'name',
			childrenProp: 'children',
			selectedPath: treePath,
			draggable: this.props.draggable,
			onSelectedPathChange: (path, ref) => this.onSelectOrganization(ref),
			onExpand: (path, item) => this.singleItemExpandChange(item, true),
			onCollapse: (path, item) => this.singleItemExpandChange(item, false),
			onFormatItem: this.formatGroupItem,
			onFormatHoverItem: this.formatHoverGroupItem,
			onPositionChange,
			onDragStart,
			onDragOver,
			className: isAdding || isEditing ? 'tree-item-no-action' : '',
		};

		return (
			<React.Fragment>
				{!hideExpandCollapseAll &&
					<div className='btn-group'>
						{treeRoot.expand === false
							? <Button
								style='accent'
								size='small'
								icon='mdi mdi-arrow-down-drop-circle-outline'
								text={t('tntExpandAll')}
								title={t('tntExpandAll')}
								onClick={this.expandAllClick}
								maxWidth='120px'
							/>
							: <Button
								style='accent'
								size='small'
								icon='mdi mdi-arrow-right-drop-circle-outline'
								text={t('tntCollapseAll')}
								title={t('tntCollapseAll')}
								onClick={this.collapseAllClick}
								maxWidth='120px'
							/>}
					</div>
				}
				<div className='search-box'>
					<AriaIcon type='span' className='mdi mdi-magnify sc-icon' />
					<InputEditor value={this.state.search} className='sc-input' onEveryChange={this.onSearchChange} />
				</div>
				{!clonedTree &&
					<div className='no-search-result-in-organization-tree' title={t('cmEmptyResult', { ns: 'common' })}>
						{t('cmEmptyResult', { ns: 'common' })}
					</div>
				}
				<CTree {...treeProps} ref={(ref) => this._cTree = ref} />
			</React.Fragment>
		);
	}
}
