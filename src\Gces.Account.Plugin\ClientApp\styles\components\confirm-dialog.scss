@at-root [class*='theme-'].light-mode {
	.ReactModal__Overlay--after-open {
		.gc-confirm-dialog {
			.gc-confirm-dialog-footer {
				--gces-transformed-color: var(--gces-panels-bg-dk-5);
				--gces-btn-bg: var(--gces-panels-bg-dk-10);
				--gces-btn-hover-bg: var(--gces-panels-bg-dk-10);
				--gces-btn-active-bg: var(--gces-panels-bg-dk-15);
			}
		}
	}
}

@at-root [class*='theme-'].dark-mode {
	.ReactModal__Overlay--after-open {
		.gc-confirm-dialog {
			.gc-confirm-dialog-footer {
				--gces-transformed-color: var(--gces-panels-bg-lt-5);
				--gces-btn-bg: var(--gces-panels-bg-lt-10);
				--gces-btn-hover-bg: var(--gces-panels-bg-lt-10);
				--gces-btn-active-bg: var(--gces-panels-bg-lt-15);
			}
		}
	}
}

.ReactModal__Overlay--after-open {
	z-index: 9999;
	display: flex !important;
	align-items: center;
	justify-content: center;

	.gc-confirm-dialog {
		display: flex;
		flex-direction: column;
		box-shadow: $ef-shadow-border;
		font-size: 14px;
		width: 526px;
		min-width: 500px;

		.gc-confirm-dialog-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color: $ef-accent;
			height: 50px;
			padding: 10px 15px !important;

			span {
				color: $text-contrast;
				font-size: 15px !important;
				line-height: 30px;
			}

			button {
				color: white !important;
				background-color: $ef-accent !important;
			}
		}

		.gc-confirm-dialog-body {
			padding: 20px 15px !important;
			min-height: 60px;
			background-color: $ef-body-bg;
			color: $ef-text;
			word-break: break-word;
		}

		.gc-confirm-dialog-footer {
			display: flex;
			justify-content: flex-end;
			background: var(--gces-transformed-color) !important;
			height: 50px;
			padding: 10px 15px !important;

			span {
				font-size: 12px !important;
			}

			.ef-btn {
				margin-left: 15px !important;
				height: 30px;
				border-radius: 2px;
				min-width: 100px;
				background-color: var(--gces-btn-bg);

				&:not([disabled]):not(.disabled):hover {
					background-color: var(--gces-btn-hover-bg);
				}

				&:not([disabled]):not(.disabled):active {
					background-color: var(--gces-btn-active-bg);
				}
			}

			.ef-btn.ef-btn-accent {
				color: $text-contrast;
				background-color: $ef-accent;

				&:not([disabled]):not(.disabled):hover {
					background-color: var(--gces-accent1-dk-5);
				}

				&:not([disabled]):not(.disabled):active {
					background-color: var(--gces-accent1-dk-10);
				}
			}
		}
	}
}

.confirm-dialog.organization-drag {
	.dialog-body .tab-group .radio-group {
		width: 100%;
	}

	.text-content {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;

		.drag-radio-group {
			display: flex;
			flex: 1;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			height: 100%;

			.svg-style {
				width: 66px;
				height: 66px;

				.svg-rect-style {
					stroke: $ef-accent;
				}
			}

			.radio-show {
				height: 100%;
				text-align: center;
				display: flex;
				flex-direction: column;
				align-items: center;

				.efc-radio {
					width: 20px;
					height: 30px;
					padding-top: 10px;
				}

				>p {
					height: 50px;
				}
			}
		}
	}
}