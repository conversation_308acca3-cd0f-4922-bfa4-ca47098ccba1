.inactive-session-settings {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 100%;

	.setting-items {
		display: flex;
		flex-direction: column;
		height: 90%;

		.setting-item {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			margin: 5px 0;
			font-size: 12px;

			.setting-label {
				width: 35%;
				line-height: 30px;
			}

			.setting-value {
				width: 65%;
			}

			.test-button {
				width: 50%;
				align-self: end;
			}

			.mdi-help-circle-outline {
				color: $accent1;
				font-size: 14px;
			}

			.btn-group {
				display: flex;
				flex-direction: row;
				justify-content: start;

				button {
					margin-right: 5px;
				}
			}
		}

		.unrestricted-users-list {
			width: 100%;
			flex: 1;
			font-size: $ef-font-size-sm;
			background-color: $ef-bg-dk;
			margin-top: 5px;
			margin-right: 7px;

			--gces-row-height: 40px;
			--gces-cell-text-lt: var(--gces-content-text-lt-5);
			--gces-cell-text: var(--gces-content-text);

			.cell-input {
				background-color: inherit;
				margin-left: -2px;
				padding: 0;
			}

			.c-grid-wrapper {
				.c-grid {
					border: none;
					background: transparent;

					.cg-header {
						.cg-row {
							border-bottom: none;
							background: $content-bg;
						}

						.cg-h-cell {
							font-weight: normal;
						}
					}

					.cg-body {
						.cg-row {
							background: transparent;
						}
					}
				}
			}

			.dl-cell {
				flex: 1;
				padding-left: 5px;
				color: var(--gces-cell-text-lt);
				cursor: default;
				height: 100%;
				overflow: hidden;

				.ef-toolbar {
					button {
						color: $content-text;
					}
				}

				.dl-cell-inner {
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}

			.dl-header {
				flex: 0 0 var(--gces-row-height);
				display: flex;
				border-bottom: 1px solid var(--gces-content-bg-dk-5);

				.dl-cell {
					.hc-icon,
					.hc-text .hc-sort-indicator {
						cursor: pointer;
					}

					.hc-text {
						max-width: 100%;
						display: inline-block;
					}

					.hc-sort-indicator {
						margin-left: 5px;
						font-size: $ef-font-size;

						.sort-button {
							display: none;
							transition: $ef-transition;
						}

						.asc {
							display: flex;
						}

						.desc {
							display: flex;
							transform: rotate(180deg);
						}
					}

					&:hover {
						.default {
							display: flex;
							opacity: 0.62;
						}
					}
				}
			}

			.dl-body {
				flex: 1;

				.dl-row {
					border-bottom: 1px solid var(--gces-content-bg-dk-5) !important;
					height: var(--gces-row-height);
					display: flex;

					&:hover {
						background: var(--gces-content-bg-dk-5) !important;
					}

					&.selected {
						background: $accent1 !important;

						.dl-cell {
							color: $text-contrast;
						}
					}
				}
			}
		}
	}

	.settings-footer {
		display: flex;
		flex-direction: row;
		justify-content: end;

		button {
			margin: 5px;
		}
	}
}

.users-container {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 100%;

	.main-content {
		display: flex;
		flex-direction: column;
		height: 90%;

		.header {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			width: 100%;
			margin: 10px 0;

			.ef-control {
				min-height: 0;

				.efc-label {
					margin: 0;
					width: 100%;

					.label-text {
						font-size: 14px;
					}
				}
			}
		}

		.body {
			width: 100%;
			height: 100%;
			font-size: $ef-font-size-sm;
			margin-top: 5px;
			margin-right: 7px;

			.c-grid-wrapper {
				.c-grid {
					border: none;
					background: transparent;

					.cg-header {
						.cg-row {
							border-bottom: none;
						}

						.cg-h-cell {
							font-weight: normal;
						}
					}

					.cg-body {
						.cg-row {
							.odd {
								background: transparent;
							}

							.even {
								background: lightgrey;
							}
						}

						.efc-checkbox > div {
							background-color: rgba(0, 0, 0, 0.15);
						}
					}
				}
			}
		}
	}

	.footer {
		display: flex;
		flex-direction: row;
		justify-content: end;

		button {
			margin: 5px;
		}
	}
}
