import { put, takeEvery, call, select } from 'redux-saga/effects';
import { isAdministrator, hasManageUserPermission, updateUserInfo, sendRequestV2 } from '../utils';
import { getDefaultSelectedOrgId } from '../../../utils';
import {
	userActionCreators,
	SagaActionTypes,
	ToggleUserStatusAction,
	UpdateUserAction,
	ImportUsersAction,
	CreateUserAction,
	DeleteUserAction,
	UpdateOrganizationUsersAction,
	RemoveUserFromOrgnizationAction,
	GetRolesOfCurOrganizationAction,
} from './actions';
import { UserInfo, Organization } from './interfaces';
import { GlobalOrganization, isGlobalOrg } from '../../../util';
import { safeFetchV2 } from '../../../utils/safeFetchV2';

function* init() {
	yield put(userActionCreators.setBusy(true));
	const organizations = (yield* getAllOrganizations()) as Organization[];
	yield put(userActionCreators.setOrganizations(organizations));
	const { tenantId } = (yield select()).navApp.user;
	const defaultId = getDefaultSelectedOrgId(organizations, tenantId);
	if (defaultId) {
		yield put(userActionCreators.setSelectedOrgId(defaultId));
	} else {
		yield put(userActionCreators.setSelectedOrgId('root'));
	}

	const allPropertiesUrl = '/api/v2/identity/custom-properties';
	const passwordPolicyUrl = '/api/v2/identity/sys-config/password-policy';
	const allProperties = yield call(sendRequestV2, allPropertiesUrl);
	if (!allProperties.error && allProperties.result && Array.isArray(allProperties.result)) {
		const sorter = (p1, p2) => p1.name.toLowerCase().localeCompare(p2.name.toLowerCase());
		const properties = allProperties.result.sort(sorter);
		yield put(userActionCreators.setProperties(properties));
	}
	const user = (yield select()).navApp.user as User;
	const canManageUser = hasManageUserPermission(user.roles);
	if (canManageUser) {
		const passwordPolicy = yield call(sendRequestV2, passwordPolicyUrl);
		if (passwordPolicy.result) {
			yield put(userActionCreators.setPasswordPolicy(passwordPolicy.result.value));
		}
	}
	yield getAllUsers();
	yield put(userActionCreators.setBusy(false));
}

function* getAllOrganizations() {
	const user = (yield select()).navApp.user as User;
	const isSuperAdmin = isAdministrator(user.roles);
	const getOrganizationsUrl = isSuperAdmin ? '/api/v2/identity/organizations' : `/api/v2/identity/organizations/${user.orgId}/sub-organizations`;
	const { result, error } = yield sendRequestV2(getOrganizationsUrl);
	if (!error && result && Array.isArray(result)) {
		if (!isSuperAdmin) {
			const org = yield sendRequestV2(`/api/v2/identity/organizations/${user.orgId}`);
			if (org.result && !org.error) {
				result.push(org.result);
			}
			return result;
		} else {
			const tenants = [...result];
			for (const t of tenants) {
				if (!t.parentTenantId && t.id !== GlobalOrganization.Id) {
					t.parentTenantId = GlobalOrganization.Id;
				}
			}
			return tenants;
		}
	}
	return [];
}

function* getAllUsers() {
	yield put(userActionCreators.setBusy(true));
	const user = (yield select()).navApp.user as User;
	const targetUrl = (isAdministrator(user.roles) || !window.AdminPortal.LimitOrganizationMembersVisibility) ?
		'/api/v2/identity/users?Paging=false' :
		`/api/v2/identity/organizations/${user.orgId}/users?includeSubOrganizationUsers=true`;
	const { result } = yield sendRequestV2(targetUrl);
	if (result) {
		const users = result.models || result;
		users.forEach(user => {
			user.organizations = user.tenantRoles ? Object.keys(user.tenantRoles) : [];
		});
		yield put(userActionCreators.setAllUsers(users));
	}
	yield put(userActionCreators.setBusy(false));
}

const updateSystemStatistics = () => window.AdminPortal && window.AdminPortal.UpdateSystemStatistics && window.AdminPortal.UpdateSystemStatistics();

function* createUser(action: CreateUserAction) {
	yield put(userActionCreators.setBusy(true));
	const { navApp: { user } } = yield select();
	const addUserUrl = '/api/v2/identity/users';
	const { result, error } = yield sendRequestV2(addUserUrl, 'POST', action.payload.newUser);
	if (!error && result) {
		const allUsers = (yield select()).user.allUsers;
		const newAllUsers = updateUserInfo(result, allUsers);
		yield put(userActionCreators.setAllUsers(newAllUsers));
		yield put(userActionCreators.setIsAddingUser(false));
		if (isGlobalOrg(user.tenantId)) {
			updateSystemStatistics();
		}
		if (action.payload.newUser.homePageId) {
			yield setUserHomePage(result.id, action.payload.newUser.homePageId);
		}
	}
	yield put(userActionCreators.setBusy(false));
}

function* setUserHomePage(userId: string, homePageId: string) {
	const requestBody = {
		userId,
		userHomeDocumentId: homePageId,
	};
	yield safeFetchV2(`api/v2/common/users/${userId}/homepage`, {
		method: 'PUT',
		headers: { 'Content-Type': 'application/json' },
		body: JSON.stringify(requestBody),
	});
}

function* updateUser(action: UpdateUserAction) {
	yield put(userActionCreators.setBusy(true));
	const addUserUrl = `/api/v2/identity/users/${action.payload.newUser.id}?ignoreroles=true`;
	const { result, error } = yield sendRequestV2(addUserUrl, 'PUT', action.payload.newUser);
	if (!error && result) {
		const allUsers = (yield select()).user.allUsers;
		const newAllUsers = updateUserInfo(result, allUsers);
		yield put(userActionCreators.setAllUsers(newAllUsers));
		yield put(userActionCreators.setEditingUser(null));
	}
	yield put(userActionCreators.setBusy(false));
}

function* deleteUser(action: DeleteUserAction) {
	yield put(userActionCreators.setBusy(true));
	const { navApp: { user } } = yield select();
	const delUserUrl = `/api/v2/identity/users/${action.payload.userId}`;
	const { result } = yield sendRequestV2(delUserUrl, 'DELETE');
	if (result) {
		const allUsers = [...(yield select()).user.allUsers] as UserInfo[];
		const newAllUsers = allUsers.filter(u => u.id !== action.payload.userId);
		yield put(userActionCreators.setAllUsers(newAllUsers));
		if (isGlobalOrg(user.tenantId)) {
			updateSystemStatistics();
		}
	}
	yield put(userActionCreators.setBusy(false));
}

function* importUsers(action: ImportUsersAction) {
	yield put(userActionCreators.setBusy(true));
	const { navApp: { user } } = yield select();
	const importUserUrl = '/api/v2/identity/users/import';
	const requestInit: RequestInit = {
		credentials: 'same-origin',
		method: 'POST',
		body: action.payload.users
	};
	const { result, error } = yield safeFetchV2(importUserUrl, requestInit);
	if (!error && result) {
		yield put(userActionCreators.setIsImportingUser(false));
		yield put(userActionCreators.setImportResult(result));
		if (isGlobalOrg(user.tenantId)) {
			updateSystemStatistics();
		}
	}
	yield put(userActionCreators.setBusy(false));
}

function* toggleUserStatus(action: ToggleUserStatusAction) {
	yield put(userActionCreators.setBusy(true));
	const toggleUserStatusUrl = `/api/v2/identity/users/${action.payload.userId}/enable`;
	const { result } = yield sendRequestV2(toggleUserStatusUrl, 'PUT', action.payload.enable);
	if (result) {
		const allUsers = [...(yield select()).user.allUsers] as UserInfo[];
		allUsers.find(u => u.id === action.payload.userId).status = action.payload.enable ? 1 : 2;
		yield put(userActionCreators.setAllUsers(allUsers));
	}
	yield put(userActionCreators.setBusy(false));
}

function* updateOrganizationUsers(action: UpdateOrganizationUsersAction) {
	yield put(userActionCreators.setBusy(true));
	const targetUrl = `/api/v2/identity/organizations/${action.payload.selectedOrganizationId}/users`;
	const userIds = { userIds: action.payload.userIds };
	const { result } = yield sendRequestV2(targetUrl, 'PUT', userIds);
	if (result) {
		yield put(userActionCreators.getAllUsers());
		yield put(userActionCreators.setIsSelectingMembers(false));
	}
	yield put(userActionCreators.setBusy(false));
}

function* removeUserFromOrganization(action: RemoveUserFromOrgnizationAction) {
	yield put(userActionCreators.setBusy(true));
	const targetUrl = `/api/v2/identity/organizations/${action.payload.selectedOrganizationId}/users/${action.payload.userId}`;
	const { result } = yield sendRequestV2(targetUrl, 'DELETE');
	if (result) {
		yield getAllUsers();
	}
	yield put(userActionCreators.setBusy(false));
}

function* GetRolesOfCurOrganization(action: GetRolesOfCurOrganizationAction) {
	yield put(userActionCreators.setBusy(true));
	const { selectedOrganizationId } = action.payload;
	const targetUrl = (!selectedOrganizationId || selectedOrganizationId === GlobalOrganization.Id) ?
		'/api/v2/identity/roles/non-org-roles' :
		`/api/v2/identity/organizations/${selectedOrganizationId}/roles`;
	const { result } = yield sendRequestV2(targetUrl);
	if (result && Array.isArray(result)) {
		yield put(userActionCreators.setRoles(result));
	}
	yield put(userActionCreators.setBusy(false));
}

export function* watchUser() {
	yield takeEvery(SagaActionTypes.Init, init);
	yield takeEvery(SagaActionTypes.GetAllUsers, getAllUsers);
	yield takeEvery(SagaActionTypes.CreateUser, createUser);
	yield takeEvery(SagaActionTypes.UpdateUser, updateUser);
	yield takeEvery(SagaActionTypes.DeleteUser, deleteUser);
	yield takeEvery(SagaActionTypes.ImportUsers, importUsers);
	yield takeEvery(SagaActionTypes.ToggleUserStatus, toggleUserStatus);
	yield takeEvery(SagaActionTypes.UpdateOrganizationUsers, updateOrganizationUsers);
	yield takeEvery(SagaActionTypes.RemoveUserFromOrganization, removeUserFromOrganization);
	yield takeEvery(SagaActionTypes.GetRolesOfCurOrganization, GetRolesOfCurOrganization);
}
