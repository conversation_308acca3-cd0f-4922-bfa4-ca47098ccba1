﻿import * as React from 'react';
import { Tab } from 'gces-ui';
export type TabContainerProps = {
	onSelectedTabChanged?: (index: number) => void;
	disabled?: boolean;
	items: any;
	selectedIndex?: number;
};
type TabContainerState = {
	selectedTabIndex: number;
};
export class TabContainer extends React.Component<TabContainerProps, TabContainerState> {
	constructor(props, context) {
		super(props, context);

		this.state = { selectedTabIndex: 0 };
	}

	componentWillMount() {
		this.updateSelectedByProps(this.props);
	}

	componentWillReceiveProps(nextProps: TabContainerProps) {
		this.updateSelectedByProps(nextProps);
	}

	updateSelectedByProps = (props: TabContainerProps) => {
		const { items, selectedIndex } = props;
		const { selectedTabIndex } = this.state;
		if (typeof (selectedIndex) === 'number' && selectedIndex !== selectedTabIndex) {
			const index = selectedIndex < items.length ? selectedIndex : 0;
			this.setState({ selectedTabIndex: index });
		}
	}

	onSelectedTabChanged = (index) => {
		const { onSelectedTabChanged } = this.props;

		if (onSelectedTabChanged) onSelectedTabChanged(index);

		this.setState({ selectedTabIndex: index });
	}

	render() {
		const { items, disabled } = this.props;

		if (!items) return null;
		const index = this.state.selectedTabIndex < items.length ? this.state.selectedTabIndex : 0;

		return <div className='tab-container'>
			<div className='tab-container-header'>
				<Tab
					items={items.map(i => i.name)}
					selected={index}
					onSelectedChanged={this.onSelectedTabChanged}
					disabled={disabled}
				/>
			</div>
			<div className='tab-container-content'>
				{items[index].content}
			</div>
		</div>;
	}
}
