{
	// Settings
	"Enable": "启用",
	"Disable": "禁用",
	"synchronizeStateTip": "同步用户、组织、角色",
	"synchronizationDatasets!title": "选择数据集",
	"synchronizationDatasets!tip": "请选择用于同步用户/组织/角色等的数据集",
	"synchronizationDatasets!user": "用户数据集",
	"synchronizationDatasets!organization": "组织数据集",
	"synchronizationDatasets!role": "角色数据集",
	"synchronizationDatasets!userRoleRelation": "用户-角色关系数据集",
	"synchronizationDatasets!userOrganization": "用户-组织关系数据集",
	"searchPlaceHolder": "搜索",
	"schedulingSettings": "定时同步设置",
	"timeoutSettings": "超时设置",
	"schedulingSync": "自动定时同步",
	"failureNotification": "失败通知",
	"noFailureNotification": "未配置任何通知，点击跳转到配置页面",
	"applicationSecret!label": "应用密钥",
	"applicationSecret!title": "应用密钥",
	"applicationSecret!tip": "所有的同步用户都可以使用应用密钥生成令牌，但是不能使用它进行登录；修改应用密钥不会影响已经签发的令牌的有效性；为了确保安全性，请谨慎分享此密钥",
	"generate": "生成",
	"copy": "复制",
	"manualSync": "手动同步",
	"saveChanges": "保存更改",
	"cancel": "取消",
	"Email": "邮件",
	"WeChat4Work": "企业微信",
	"DingTalk": "钉钉",
	"MSTeams": "MSTeams",
	"Slack": "Slack",
	"API": "API",
	"ManualSyncSuccessful": "手动触发同步任务成功",
	"SaveSuccessful": "保存同步设置成功",
	"scheduleFailedMessage": "未成功启动用户同步运行计划",
	// Views
	"syncResults": "同步结果",
	"nextRunTime": "下次同步时间",
	"NoAutoSyncTask": "没有自动同步的任务",
	"syncedTime": "同步时间",
	"synchronizing": "正在同步...",
	"syncedMessage!success": "同步成功",
	"syncedMessage!notAllSuccess": "未能同步所有数据",
	"syncedMessage!canceled": "同步已取消",
	"syncedMessage!failed": "同步失败",
	"syncRecords": "同步记录",
	"entity": "实体",
	"successfulQuantity": "成功数量",
	"failedQuantity": "失败数量",
	"warningQuantity": "警告数量",
	"noRecordTip": "无同步记录",
	"informationNotSynced": "信息未同步",
	"download": "下载",
	"type": "类型",
	"name": "名称",
	"reason": "原因",
	"user": "用户",
	"organization": "组织",
	"role": "角色",
	"userRoleRelation": "用户-角色关系",
	"userOrgRelation": "用户-组织关系",
	"userOrganization": "用户-组织关系",
	"downloadDescription": "点击可下载完整错误信息",
	"Error": "错误",
	"Warning": "警告",
	// Recurrence Editor
	"recEditorHeaderRepeat": "重复周期",
	"recEditorLabelStart": "开始时间",
	"recEditorLabelEnd": "结束时间",
	"recEditorPlaceholderNoEndDate": "无结束时间",
	"recEditorDailyEditor": "日计划",
	"recEditorWeeklyEditor": "周计划",
	"recEditorIntervalWeeklyEditor": "周计划",
	"recEditorMonthlyEditor": "月计划",
	"timeout": "超时时间",
	"hour": "小时",
	"minute": "分钟",
	// Daily Editor
	"dailyEditorTextAt": "的 {{time}} 执行",
	"dailyEditorTextEvery": "每隔",
	"dailyEditorHoursTextUnits": "小时",
	"dailyEditorMinutesTextUnits": "分钟",
	"dailyEditorSecondsTextUnits": "秒",
	"dailyEditorAddExecutionTimeRange": "添加执行时间区间",
	"dailyEditorExecutionTimeRangeStartTime": "开始时间",
	"dailyEditorExecutionTimeRangeEndTime": "结束时间",
	"dailyExecutionTimeRangeDescription": "每日执行时间范围为 {{startTime}} 到 {{endTime}}",
	"dailyExecutionTimeRangeNeedToBeRemove": "移除执行时间区间",
	"dailyTaskLessThan24HoursRemoveExecutionTimeRange": "天计划任务开始到结束时间的间隔小于24小时，需要移除执行时间区间。",
	// Interval Weeks Weekly Editor
	"intervalWeeklyEditorTextEvery": "每隔",
	"intervalWeeklyEditorTextUnits": "周",
	"repeatWeekDes": "每{{weekNumber}}{{weekUnitStr}}",
	"repeatWeek": "周",
	"repeatWeeks": "周",
	"comma": "，",
	"and": "和",
	"onlyInYear": "仅在 {{year}}",
	"throughYears": "{{startYear}} 到 {{endYear}}",
	"intervalWeeklyRepeatDescription": "在 {{startTime}}，{{repeatWeekDes}}，{{daysOfWeekDes}}{{years}}",
	"dayOfWeek_1": "星期日",
	"dayOfWeek_2": "星期一",
	"dayOfWeek_3": "星期二",
	"dayOfWeek_4": "星期三",
	"dayOfWeek_5": "星期四",
	"dayOfWeek_6": "星期五",
	"dayOfWeek_7": "星期六",
	// Monthly Editor
	"monthlyEditorTextEvery": "每隔",
	"monthlyEditorTextUnits": "月",
	"monthlyEditorTextOnDay": "指定日期",
	"monthlyEditorTextOnDayUnits": "",
	"monthlyEditorTextOnTheLast": "最后一个",
	"monthlyEditorTextOnTheFirst": "第一个",
	"monthlyEditorTextLastDay": "自然日",
	"monthlyEditorTextLastWeekday": "工作日",
	// Timezone
	"taskExecutingTimezone": "执行基准时区",
	"timezone_Etc/GMT+12": "(UTC-12:00) 国际日期变更线西",
	"timezone_Etc/GMT+11": "(UTC-11:00) 协调世界时-11",
	"timezone_Pacific/Honolulu": "(UTC-10:00) 夏威夷",
	"timezone_America/Adak": "(UTC-10:00) 阿留申群岛",
	"timezone_Pacific/Marquesas": "(UTC-09:30) 马克萨斯群岛",
	"timezone_Etc/GMT+9": "(UTC-09:00) 协调世界时-09",
	"timezone_America/Anchorage": "(UTC-09:00) 阿拉斯加",
	"timezone_America/Tijuana": "(UTC-08:00) 下加利福尼亚州",
	"timezone_Etc/GMT+8": "(UTC-08:00) 协调世界时-08",
	"timezone_America/Los_Angeles": "(UTC-08:00) 太平洋时间(美国和加拿大)",
	"timezone_America/Phoenix": "(UTC-07:00) 亚利桑那",
	"timezone_America/Chihuahua": "(UTC-07:00) 奇瓦瓦，拉巴斯，马萨特兰",
	"timezone_America/Denver": "(UTC-07:00) 山地时间(美国和加拿大)",
	"timezone_America/Guatemala": "(UTC-06:00) 中美洲",
	"timezone_America/Chicago": "(UTC-06:00) 中部时间(美国和加拿大)",
	"timezone_Pacific/Easter": "(UTC-06:00) 复活节岛",
	"timezone_America/Mexico_City": "(UTC-06:00) 瓜达拉哈拉，墨西哥城，蒙特雷",
	"timezone_America/Regina": "(UTC-06:00) 萨斯喀彻温",
	"timezone_America/New_York": "(UTC-05:00) 东部时间(美国和加拿大)",
	"timezone_America/Cancun": "(UTC-05:00) 切图马尔",
	"timezone_America/Indiana/Indianapolis": "(UTC-05:00) 印地安那州(东部)",
	"timezone_America/Havana": "(UTC-05:00) 哈瓦那",
	"timezone_America/Bogota": "(UTC-05:00) 波哥大，利马，基多，里奥布朗库",
	"timezone_America/Port-au-Prince": "(UTC-05:00) 海地",
	"timezone_America/Grand_Turk": "(UTC-05:00) 特克斯和凯科斯群岛",
	"timezone_America/La_Paz": "(UTC-04:00) 乔治敦，拉巴斯，马瑙斯，圣胡安",
	"timezone_America/Asuncion": "(UTC-04:00) 亚松森",
	"timezone_America/Caracas": "(UTC-04:00) 加拉加斯",
	"timezone_America/Santiago": "(UTC-04:00) 圣地亚哥",
	"timezone_America/Halifax": "(UTC-04:00) 大西洋时间(加拿大)",
	"timezone_America/Cuiaba": "(UTC-04:00) 库亚巴",
	"timezone_America/St_Johns": "(UTC-03:30) 纽芬兰",
	"timezone_America/Cayenne": "(UTC-03:00) 卡宴，福塔雷萨",
	"timezone_America/Miquelon": "(UTC-03:00) 圣皮埃尔和密克隆群岛",
	"timezone_America/Sao_Paulo": "(UTC-03:00) 巴西利亚",
	"timezone_America/Argentina/Buenos_Aires": "(UTC-03:00) 布宜诺斯艾利斯",
	"timezone_America/Godthab": "(UTC-03:00) 格陵兰",
	"timezone_America/Bahia": "(UTC-03:00) 萨尔瓦多",
	"timezone_America/Montevideo": "(UTC-03:00) 蒙得维的亚",
	"timezone_America/Punta_Arenas": "(UTC-03:00) 蓬塔阿雷纳斯",
	"timezone_America/Araguaina": "(UTC-03:00) 阿拉瓜伊纳",
	"timezone_Etc/GMT+2": "(UTC-02:00) 中大西洋 - 旧用",
	"timezone_Atlantic/Azores": "(UTC-01:00) 亚速尔群岛",
	"timezone_Atlantic/Cape_Verde": "(UTC-01:00) 佛得角群岛",
	"timezone_Etc/UTC": "(UTC) 协调世界时",
	"timezone_Africa/Sao_Tome": "(UTC+00:00) 圣多美",
	"timezone_Atlantic/Reykjavik": "(UTC+00:00) 蒙罗维亚，雷克雅未克",
	"timezone_Europe/London": "(UTC+00:00) 都柏林，爱丁堡，里斯本，伦敦",
	"timezone_Africa/Casablanca": "(UTC+01:00) 卡萨布兰卡",
	"timezone_Africa/Lagos": "(UTC+01:00) 中非西部",
	"timezone_Europe/Paris": "(UTC+01:00) 布鲁塞尔，哥本哈根，马德里，巴黎",
	"timezone_Europe/Warsaw": "(UTC+01:00) 萨拉热窝，斯科普里，华沙，萨格勒布",
	"timezone_Europe/Budapest": "(UTC+01:00) 贝尔格莱德，布拉迪斯拉发，布达佩斯，卢布尔雅那，布拉格",
	"timezone_Europe/Berlin": "(UTC+01:00) 阿姆斯特丹，柏林，伯尔尼，罗马，斯德哥尔摩，维也纳",
	"timezone_Asia/Hebron": "(UTC+02:00) 加沙，希伯伦",
	"timezone_Europe/Kaliningrad": "(UTC+02:00) 加里宁格勒",
	"timezone_Africa/Johannesburg": "(UTC+02:00) 哈拉雷，比勒陀利亚",
	"timezone_Africa/Khartoum": "(UTC+02:00) 喀土穆",
	"timezone_Europe/Chisinau": "(UTC+02:00) 基希讷乌",
	"timezone_Asia/Damascus": "(UTC+02:00) 大马士革",
	"timezone_Asia/Amman": "(UTC+02:00) 安曼",
	"timezone_Africa/Cairo": "(UTC+02:00) 开罗",
	"timezone_Africa/Windhoek": "(UTC+02:00) 温得和克",
	"timezone_Africa/Tripoli": "(UTC+02:00) 的黎波里",
	"timezone_Asia/Jerusalem": "(UTC+02:00) 耶路撒冷",
	"timezone_Asia/Beirut": "(UTC+02:00) 贝鲁特",
	"timezone_Europe/Kiev": "(UTC+02:00) 赫尔辛基，基辅，里加，索非亚，塔林，维尔纽斯",
	"timezone_Europe/Bucharest": "(UTC+02:00) 雅典，布加勒斯特",
	"timezone_Europe/Istanbul": "(UTC+03:00) 伊斯坦布尔",
	"timezone_Africa/Nairobi": "(UTC+03:00) 内罗毕",
	"timezone_Asia/Baghdad": "(UTC+03:00) 巴格达",
	"timezone_Europe/Minsk": "(UTC+03:00) 明斯克",
	"timezone_Asia/Riyadh": "(UTC+03:00) 科威特，利雅得",
	"timezone_Europe/Moscow": "(UTC+03:00) 莫斯科，圣彼得堡",
	"timezone_Asia/Tehran": "(UTC+03:30) 德黑兰",
	"timezone_Europe/Samara": "(UTC+04:00) 伊热夫斯克，萨马拉",
	"timezone_Europe/Volgograd": "(UTC+04:00) 伏尔加格勒",
	"timezone_Asia/Yerevan": "(UTC+04:00) 埃里温",
	"timezone_Asia/Baku": "(UTC+04:00) 巴库",
	"timezone_Asia/Tbilisi": "(UTC+04:00) 第比利斯",
	"timezone_Europe/Saratov": "(UTC+04:00) 萨拉托夫",
	"timezone_Indian/Mauritius": "(UTC+04:00) 路易港",
	"timezone_Asia/Dubai": "(UTC+04:00) 阿布扎比，马斯喀特",
	"timezone_Europe/Astrakhan": "(UTC+04:00) 阿斯特拉罕，乌里扬诺夫斯克",
	"timezone_Asia/Kabul": "(UTC+04:30) 喀布尔",
	"timezone_Asia/Karachi": "(UTC+05:00) 伊斯兰堡，卡拉奇",
	"timezone_Asia/Qyzylorda": "(UTC+05:00) 克孜洛尔达",
	"timezone_Asia/Yekaterinburg": "(UTC+05:00) 叶卡捷琳堡",
	"timezone_Asia/Tashkent": "(UTC+05:00) 阿什哈巴德，塔什干",
	"timezone_Asia/Colombo": "(UTC+05:30) 斯里加亚渥登普拉",
	"timezone_Asia/Kolkata": "(UTC+05:30) 钦奈，加尔各答，孟买，新德里",
	"timezone_Asia/Kathmandu": "(UTC+05:45) 加德满都",
	"timezone_Asia/Dhaka": "(UTC+06:00) 达卡",
	"timezone_Asia/Omsk": "(UTC+06:00) 鄂木斯克",
	"timezone_Asia/Almaty": "(UTC+06:00) 阿斯塔纳",
	"timezone_Asia/Yangon": "(UTC+06:30) 仰光",
	"timezone_Asia/Krasnoyarsk": "(UTC+07:00) 克拉斯诺亚尔斯克",
	"timezone_Asia/Barnaul": "(UTC+07:00) 巴尔瑙尔，戈尔诺-阿尔泰斯克",
	"timezone_Asia/Tomsk": "(UTC+07:00) 托木斯克",
	"timezone_Asia/Novosibirsk": "(UTC+07:00) 新西伯利亚",
	"timezone_Asia/Bangkok": "(UTC+07:00) 曼谷，河内，雅加达",
	"timezone_Asia/Hovd": "(UTC+07:00) 科布多",
	"timezone_Asia/Ulaanbaatar": "(UTC+08:00) 乌兰巴托",
	"timezone_Asia/Irkutsk": "(UTC+08:00) 伊尔库茨克",
	"timezone_Asia/Shanghai": "(UTC+08:00) 北京，重庆，香港特别行政区，乌鲁木齐",
	"timezone_Asia/Taipei": "(UTC+08:00) 台北",
	"timezone_Asia/Singapore": "(UTC+08:00) 吉隆坡，新加坡",
	"timezone_Australia/Perth": "(UTC+08:00) 珀斯",
	"timezone_Australia/Eucla": "(UTC+08:45) 尤克拉",
	"timezone_Asia/Tokyo": "(UTC+09:00) 大阪，札幌，东京",
	"timezone_Asia/Pyongyang": "(UTC+09:00) 平壤",
	"timezone_Asia/Chita": "(UTC+09:00) 赤塔市",
	"timezone_Asia/Yakutsk": "(UTC+09:00) 雅库茨克",
	"timezone_Asia/Seoul": "(UTC+09:00) 首尔",
	"timezone_Australia/Darwin": "(UTC+09:30) 达尔文",
	"timezone_Australia/Adelaide": "(UTC+09:30) 阿德莱德",
	"timezone_Pacific/Port_Moresby": "(UTC+10:00) 关岛，莫尔兹比港",
	"timezone_Australia/Sydney": "(UTC+10:00) 堪培拉，墨尔本，悉尼",
	"timezone_Australia/Brisbane": "(UTC+10:00) 布里斯班",
	"timezone_Asia/Vladivostok": "(UTC+10:00) 符拉迪沃斯托克",
	"timezone_Australia/Hobart": "(UTC+10:00) 霍巴特",
	"timezone_Australia/Lord_Howe": "(UTC+10:30) 豪勋爵岛",
	"timezone_Asia/Srednekolymsk": "(UTC+11:00) 乔库尔达赫",
	"timezone_Pacific/Bougainville": "(UTC+11:00) 布干维尔岛",
	"timezone_Pacific/Guadalcanal": "(UTC+11:00) 所罗门群岛，新喀里多尼亚",
	"timezone_Asia/Sakhalin": "(UTC+11:00) 萨哈林",
	"timezone_Pacific/Norfolk": "(UTC+11:00) 诺福克岛",
	"timezone_Asia/Magadan": "(UTC+11:00) 马加丹",
	"timezone_Etc/GMT-12": "(UTC+12:00) 协调世界时+12",
	"timezone_Pacific/Auckland": "(UTC+12:00) 奥克兰，惠灵顿",
	"timezone_Asia/Kamchatka": "(UTC+12:00) 彼得罗巴甫洛夫斯克-堪察加 - 旧用",
	"timezone_Pacific/Fiji": "(UTC+12:00) 斐济",
	"timezone_Pacific/Chatham": "(UTC+12:45) 查塔姆群岛",
	"timezone_Pacific/Tongatapu": "(UTC+13:00) 努库阿洛法",
	"timezone_Etc/GMT-13": "(UTC+13:00) 协调世界时+13",
	"timezone_Pacific/Apia": "(UTC+13:00) 萨摩亚群岛",
	"timezone_Pacific/Kiritimati": "(UTC+14:00) 圣诞岛",
	"timezone_America/Mazatlan": "(UTC-06:00) 马萨特兰",
	"timezone_America/Whitehorse": "(UTC-07:00) 白马市",
	"timezone_America/Nuuk": "(UTC-02:00) 努克",
	"timezone_Africa/Abidjan": "(UTC+00:00) 阿比让",
	"timezone_Africa/Juba": "(UTC+03:00) 朱巴",
	"timezone_Europe/Kyiv": "(UTC+03:00) 基辅",
	//ErrorCode
	"error_V2_000_019_0001": "无效的用户同步设置，请检查'{{configItem}}设置。'",
	"error_V2_000_019_0002": "请至少选择一个数据集。",
	"error_V2_000_019_0003": "用户同步任务失败。",
	"error_V2_000_019_0004": "未通过数据集字段验证，缺失必需字段如下：",
	"error_0001": "Id 不能为空。",
	"error_0002": "名称不能为空。实体id：{{id}}。",
	"error_0003": "检测到重复的id。实体id：{{id}}，重复的实体：{{names}}",
	"error_0004": "检测到重复的名称。",
	"error_0005": "检测到无效的名称。",
	"error_0006": "id只能包含数字，字母，_，-，.，和@，长度不能超过128。实体ID：{{id}}。",
	"error_0007": "检测到重复的电子邮件。电子邮件：{{email}}，重复的用户：{{names}}。",
	"error_0011": "实体不存在。实体ID：{{id}}。",
	"error_0011!extension": "$t(synchronization:error_0011) 实体类型：{{type}}。",
	"error_0021": "属性({{name}})不存在。",
	"error_0022": "无效的属性({{propName}})值({{propValues}})。",
	"error_0023": "无效的电子邮件值：{{email}}。",
	"error_0024": "无效的头像值，头像值只支持URL。",
	"error_0031": "组织树中存在循环。",
	"error_0032": "组织未链接到组织树。",
	"error_0041_org": "权限冲突。冲突的实体：(组织){{orgId}} 和 (父组织){{parentId}}。",
	"error_0041_org_role": "权限冲突。冲突的实体：(组织){{orgId}} 和 (组织内角色){{roleId}}。",
	"error_0098": "同步超时，或者被异常取消。",
	"error_0099": "发生意外异常。",
	"error_1001": "无效的用户同步设置，请检查'{{configItem}}设置。'",
	"error_1002": "至少分配一个数据集。",
	"error_1003": "用户同步任务失败，请检查运行计划设置。",
	"error_1004": "未通过数据集字段验证。缺失必需字段：{{errorMessage}}。",
	"error_1005": "用户同步超时。",
	"error_1006": "获取数据集结果集失败，失败的数据集类型：{{types}}。",
	"errorGetAppContacts": "获取联系人失败。",
	"fallback_unknown_error": "系统内部错误。",
}