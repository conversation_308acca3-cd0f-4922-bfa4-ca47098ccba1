.form-item {
	.efc-sensitive-wrapper {
		display: flex;
		align-items: center;

		.document-selector {
			position: relative;
			width: 100%;

			@at-root {
				.home-page-selector-menu {
					.doc-icon {
						color: $ef-text-inv;
						font-size: $ef-font-size-lg;
						margin-right: 5px;
					}
				}
			}

			.document-dropdown {
				span {
					padding-left: 30px;
				}
			}

			.doc-title-icon {
				z-index: 2;
				font-size: $ef-font-size-lg;
				color: $ef-text;
				position: absolute;
				top: 3px;
				text-align: center;
				padding: 0 5px 0 8px;
			}
		}

		.efc-input-visible-toggle {
			margin-left: 5px;

			> i {
				font-size: $ef-font-size-lg;
			}

			&.efc-input-visible-hidden {
				visibility: hidden;
			}

			&.efc-input-visible-none {
				display: none;
			}
		}
	}
}