export const tenantTW: LanguageKeyValueMap = {
	// section
	'account-management-organization!title': '組織管理',
	'account-management-organization!description': '組織管理',

	Yes: '確定',
	Close: '關閉',
	tntTenantSchema: '組織模型',
	tntAddTenant: '添加組織',
	tntEditTenant: '編輯組織',
	tntNoItemsTip: '沒有記錄',
	tntAddProp: '添加屬性',
	tntAddTenantProp: '添加組織屬性',
	tntEditTenantProp: '編輯組織屬性',
	tntPropName: '屬性名稱',
	tntPropVlue: '屬性值',
	tntPropValueType: '屬性值類型',
	tntPropValueType_String: '字符串',
	tntPropValueType_Boolean: '布爾',
	tntPropValueType_Integer: '整型',
	tntPropValueType_Float: '浮點型',
	tntPropValueType_Date: '日期',
	tntPropValueType_DateTime: '日期時間',
	tntRequired: '必填項',
	tntMultivalued: '允許多值',
	tntSensitive: '允許隱藏值',
	tntShowValue: '顯示值',
	tntHideValue: '隱藏值',
	tntClose: '關閉',
	tntDelete: '永久删除',
	tntName: '名稱',
	tntEdit: '編輯',
	tntFromEmail: '郵件地址',
	tntAdd: '添加',
	tntSave: '保存',
	tntCancel: '取消',
	tntTenantMembers: '組織成員',
	tntSelectMembers: '選擇成員',
	tntUsername: '用戶名',
	tntEmail: '郵件',
	tntProvider: '提供者',
	tntMembers: '{{count}}個成員',
	tntDeleteTenantProp: '刪除組織屬性',
	tntDeleteTenantPropConfirmMessage: '您要永久刪除組織屬性"{{tenantPropName}}"嗎?',
	tntDeleteTenant: '刪除組織',
	tntDeleteTenantConfirmMessage: '您要永久刪除組織"{{tenantName}}"嗎?',
	tntNoMemberTip: '當前組織下沒有用戶，如需添加請點擊',
	tntMultiLineTip: '每行一個值',
	tntAddMember: '添加',

	tntTenantBasicInformation: '基本資訊',
	tntTenantRoles: '組織角色',
	tntSelectRoles: '選擇角色',
	tntTenantMoveUp: '上移',
	tntTenantMoveDown: '下移',
	tntExpandAll: '展開所有',
	tntCollapseAll: '收起所有',
	tntActions: '活動',
	tntGlobal: '全域',
	tntRoleName: '角色名',
	tntUsersNumber: '用戶數',
	tntNewOrganization: '新組織',
	tntTenantPermissions: '權限',
	tntTenantPermissionScope: '修改權限範圍',
	tntModifyPermissionScope: '權限範圍',
	tntExpand: '展開',
	tntCollapse: '折疊',
	tntOrganizationName: '組織名',
	tntModifyPermissionScopeMessage: '是否確認修改"{{tenantName}}"組織的權限範圍？',
	tntModifyPermissionScopeMessageWithTenant: '本次修改將會同時刪除以下子組織的下列權限範圍：',
	tntModifyPermissionScopeMessageWithRole: '本次修改將會同時刪除以下角色的下列權限：',
	tntNoRolesTip: '當前組織下沒有角色',
	tntNoPermissionsTip: '當前組織下沒有權限',
	tntPermissionsName: '權限名',
	tntPermissionsDescription: '描述',
	tntInvisible: '不可見',
	tntDisableSubView: '禁止子組織查看值',
	tntDisableSubEdit: '禁止子組織編輯值',

	tntErrorOrganizationNameNull: '組織名不能為空',
	tntErrorOrganizationNameDuplicated: '組織名不能重複',
	tntErrorInvalidCharInName: '組織名不能包含: < > / \\ $',
	tntErrorFromPropNull: '屬性值不能為空',

	tntErrorOrganizationPropNameNull: '組織屬性名不能為空',
	tntErrorOrganizationPropNameDuplicated: '組織屬性名不能重複',

	error_5001: '組織名字不能為空',
	error_5002: '組織"{{TenantName}}"已經存在',
	error_5003: '組織屬性名字不能為空',
	error_5004: '組織名字"{{TenantPropName}}"已經存在',
	error_5005: '組織屬性"{{TenantPropName}}"為系統保留屬性',
	error_5006: '郵件地址"{{FromEmail}}"已經存在',

	changeOrganization: '組織變更',
	organizationDragAbove: '到當前組織前',
	organizationDragBelow: '到當前組織後',
	organizationDragSub: '到當前組織的子組織',
	moveOrganization: '移動組織 "{{tenantName}}"',
	OK: '確定',
	Cancel: '取消',

	rt_organization: '組織',
	'rt_organization property': '組織屬性',

	error_V2_007_005_0005: '檢測到無效的組織屬性值。',
	error_V2_007_005_0012: '子組織的權限不能大於父組織的權限，因此請在進行操作前修改組織權限範圍。',

	strictPermissionOrgTip: '組織權限範圍限制了其角色和子組織的最大權限。',
	loosePermissionOrgTip: '組織權限代表了組織中“每個人”角色的權限。',
	globalPermissionTip: '全局組織權限不能修改。',
};