// tslint:disable-next-line
declare interface grapecity {
	wyn: {
		integrationTokenName: string
	};
}

declare interface Window {
	grapecity: grapecity;
}

declare var AppContext: AppContext;
declare interface Action {
	type: string;
	payload?: any;
}
type Dispatch<S> = <A extends Action>(action: A) => A;
declare interface MiddlewareAPI<S> {
	dispatch: Dispatch<S>;
	getState(): S;
}
type Middleware = <S>(api: MiddlewareAPI<S>) => (next: Dispatch<S>) => Dispatch<S>;
type InternalMiddleware = <S, A extends Action>(api: MiddlewareAPI<S>, next: Dispatch<S>, action: A) => A;
declare interface AppContext {
	internalMiddleware: InternalMiddleware;
	reduxMiddleware: Middleware;
	registerMiddleware: (middleware: InternalMiddleware) => void;
	unregisterMiddleware: (middleware: InternalMiddleware) => void;
}