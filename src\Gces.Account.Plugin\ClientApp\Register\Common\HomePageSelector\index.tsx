import * as React from 'react';
import * as classnames from 'classnames';
import { Button, Dropdown, DropdownItemProps, DropdownProps, VirtualListItem } from 'gces-ui';
import { safeFetchV2 } from '../../../utils/safeFetchV2';

interface Document {
	id: string;
	type: string;
	displayName: string;
}

interface HomePageSelectorProps {
	onHomePageSelectorChange: (value: string) => void;
	homePageId: string;
	noVisibilityToggle?: boolean;
	t: any;
}

interface HomePageSelectorState {
	documents: Document[];
	selectDocumentId: string;
	documentsPageSize: number;
	documentsPageNumber: number;
	documentsTotal: number;
	loadingDocuments: boolean;
	currentDocument: Document;
	orderBy: string;
	search: string;
}

export class HomePageSelector extends React.PureComponent<HomePageSelectorProps, HomePageSelectorState> {

	state = {
		documents: [],
		selectDocumentId: '',
		documentsPageSize: 20,
		documentsPageNumber: 1,
		documentsTotal: 0,
		loadingDocuments: false,
		currentDocument: undefined,
		orderBy: '+displayName',
		search: '',
	};

	UNSAFE_componentWillMount(): void {
		const { homePageId } = this.props;
		if (homePageId) {
			safeFetchV2(`api/v2/common/documents/${homePageId}/info`, {
				credentials: 'same-origin',
				method: 'GET',
				headers: { 'Content-type': 'application/json', 'Accept': 'application/json' }
			}).then(fetchResult => {
				const { result } = fetchResult;
				if (result) {
					const homePageDocument: Document = {
						id: result.id,
						displayName: result.displayName,
						type: result.type,
					};
					this.setState({ currentDocument: homePageDocument });
				}
			});
		}
		this.onLoadDocuments(20, 1, true);
	}

	UNSAFE_componentWillReceiveProps(nextProps: Readonly<HomePageSelectorProps>, _: any): void {
		const { homePageId } = this.props;
		if (nextProps.homePageId !== homePageId) {
			if (nextProps.homePageId) {
				safeFetchV2(`api/v2/common/documents/${nextProps.homePageId}/info`, {
					credentials: 'same-origin',
					method: 'GET',
					headers: { 'Content-type': 'application/json', 'Accept': 'application/json' }
				}).then(fetchResult => {
					const { result } = fetchResult;
					const homePageDocument: Document = {
						id: result.id,
						displayName: result.displayName,
						type: result.type,
					};
					this.setState({ currentDocument: homePageDocument });
				});
			}
			this.onLoadDocuments(20, 1, true);
		}
	}

	onDocumentChange = (document: Document) => {
		const { onHomePageSelectorChange } = this.props;
		const { selectDocumentId } = this.state;
		if (selectDocumentId === document.id) {
			return;
		}
		this.setState({ selectDocumentId: document.id, currentDocument: document });
		onHomePageSelectorChange(document.id);
	}

	onLoadDocuments = (pageSize: number, pageNumber: number, overwrite: boolean, search?: string, orderBy?: string) => {
		this.setState({ loadingDocuments: true });
		safeFetchV2('/api/v2/common/documents', {
			credentials: 'same-origin',
			method: 'POST',
			headers: { 'Content-type': 'application/json', 'Accept': 'application/json' },
			body: JSON.stringify({
				pageSize,
				pageNumber,
				search: search ?? this.state.search,
				orderBy: orderBy ?? this.state.orderBy ?? '+displayName',
				types: 'dbd,rdl,rdlx,wbp,ipf'
			})
		}).then(fetchResult => {
			this.setState({ loadingDocuments: false });
			const { result } = fetchResult;
			if (result?.data && result?.pagination) {
				const documentsResult = result?.data;
				if (Array.isArray(documentsResult)) {
					if (overwrite) {
						this.setState({ documents: documentsResult });
					} else {
						const { documents } = this.state;
						const newDocuments = documents ? [...documents] : [];
						for (const document of documentsResult) {
							const index = newDocuments.findIndex(d => d.id === document.id);
							if (index === -1) {
								newDocuments.push(document);
							} else {
								newDocuments[index] = document;
							}
						}
						this.setState({ documents: newDocuments });
					}
				}
				const pagination = result.pagination;
				this.setState({ documentsPageSize: pagination.pageSize, documentsPageNumber: pagination.pageNumber, documentsTotal: pagination.total });
			}
		});
	}

	onReferencedDocumentLoadMore = () => {
		const { documentsPageSize, documentsPageNumber, documentsTotal } = this.state;
		const maxPageNumebr = Math.ceil(documentsTotal / documentsPageSize);
		if (documentsPageNumber < maxPageNumebr) {
			this.onLoadDocuments(documentsPageSize, documentsPageNumber + 1, false);
		}
	}

	onReferencedDocumentSortChanged = (asc: boolean) => {
		const { documentsPageSize, orderBy } = this.state;
		const newOrderBy = asc ? '+displayName' : '-displayName';
		if (newOrderBy !== orderBy) {
			this.setState({ orderBy: newOrderBy });
			this.onLoadDocuments(documentsPageSize, 1, true, null, newOrderBy);
		}
	}

	onReferencedDocumentSearchChanged = (value: string) => {
		const { documentsPageSize, search } = this.state;
		if (value !== search) {
			this.setState({ search: value });
			this.onLoadDocuments(documentsPageSize, 1, true, value);
		}
	}

	renderReferencedDocumentItem = (item: VirtualListItem<any>, index: number) => {
		return (
			<React.Fragment>
				{this.getDocumentIcon(item.value.type)}
				<span>{item.label}</span>
			</React.Fragment>
		);
	}

	getDocumentIcon = (type: string) => {
		if (!type) {
			return;
		}
		switch (type.toLowerCase()) {
			case 'dbd':
				return <span className='mdi mdi-chart-pie doc-icon' />; // Dashborad
			case 'rdl':
			case 'rdlx':
				return <span className='mdi mdi-file-document doc-icon' />; // Report
			case 'wbp':
				return <span className='mdi mdi-web doc-icon' />; // Web Page
			case 'ipf':
				return <span className='mdi mdi-file-table doc-icon' />; // Inputting page
			default:
				break;
		}
	}

	render() {
		const { noVisibilityToggle, t } = this.props;
		const { documents, loadingDocuments, selectDocumentId, currentDocument } = this.state;
		const documentDropdownItems: DropdownItemProps[] = (documents || []).map(doc => ({
			value: doc,
			text: doc.displayName,
			selected: selectDocumentId === doc.id,
			key: doc.id,
		}));

		const dropdownProps: DropdownProps = {
			className: 'efc-dropdown combo-box-with-icon document-dropdown',
			offset: true,
			width: '100%',
			menuWidth: '100%',
			menuClassName: 'home-page-selector-menu',
			style: 'default',
			size: 'small',
			textAlign: 'left',
			canSearch: true,
			canSort: true,
			readOnly: true,
			noResultTip: loadingDocuments ? t('loadingDocuments') : t('noSearchResultTip'),
			searchPlaceHolder: t('searchPlaceHolder'),
			text: currentDocument ? currentDocument.displayName : '',
			useVirtualList: true,
			scrollbarProps: { autoHide: false },
			items: documentDropdownItems,
			onSelect: this.onDocumentChange,
			onToggle: (open) => {
				if (open) {
					this.setState({ search: '', orderBy: '+displayName' });
					this.onLoadDocuments(20, 1, true, '', '+displayName');
				}
			},
			loadingMore: loadingDocuments,
			onLoadMore: this.onReferencedDocumentLoadMore,
			onSortChanged: this.onReferencedDocumentSortChanged,
			onSearchChanged: this.onReferencedDocumentSearchChanged,
			onRenderCustomVirtualListItem: this.renderReferencedDocumentItem,
		};
		return (
			<div className={classnames('efc-sensitive-wrapper')}>
				<div className='document-selector'>
					<div className='doc-title-icon'>
						{this.getDocumentIcon(currentDocument ? currentDocument.type : '')}
					</div>
					<Dropdown {...dropdownProps} />
				</div >
				<Button
					className={classnames('efc-input-visible-toggle', 'efc-input-visible-hidden', { 'efc-input-visible-none': noVisibilityToggle })}
					icon='mdi mdi-eye-off'
					size='small'
					style='transparent'
					rounded
				/>
			</div>
		);
	}
}