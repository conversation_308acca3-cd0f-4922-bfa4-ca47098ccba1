import * as React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';
import { Button, ConfirmDialog, ConfirmDialogProps } from 'gces-ui';
import * as util from '../../../util';
import { OrganizationItem, tenantActionCreators, OrganizationState } from '../store';
import { OrganizationSchemaEditor } from './OrganizationSchemaEditor';
import { OrganizationTreeCommon, OrganizationTreeProps } from '../../Common/OrganizationTreeCommon';
import { OrganizationDragDialog, OrganizationDragDialogProps } from './OrganizationDragDialog';

interface ConnectedProps {
	items: OrganizationItem[];
	selected: string;
	showSchemaEditor: boolean;
	showTenantEditor: boolean;
	showMembersEditor: boolean;
	userTenantId?: string;
	isAdding?: boolean;
	isEditing?: boolean;
	dispatch: any;
	t: (a: any, b?: any) => string;
}

interface PositionChangeDlg {
	open: boolean;
	from: any;
	to: any;
	order: number;
	inside: boolean;
}

interface LocalState {
	deletingTenant: OrganizationItem;
	ChangingTenant: OrganizationItem;
	positionChangeDlg: PositionChangeDlg;
}

@translate('organization', { wait: true })
class OrganizationsPanelInner extends React.PureComponent<ConnectedProps, LocalState> {
	state: LocalState = {
		deletingTenant: null,
		ChangingTenant: null,
		positionChangeDlg: {
			open: false,
			from: null,
			to: null,
			order: 0,
			inside: false,
		}
	};

	onTenantSchemaClick = () => {
		this.props.dispatch(tenantActionCreators.setShowSchemaEditor(true));
	}

	buildDeletingTenantConfirmDialog = () => {
		const { t, dispatch } = this.props;
		const dlgProps: ConfirmDialogProps = {
			parentSelector: (): HTMLElement => document.querySelector(util.portalAppId),
			title: t('tntDeleteTenant'),
			yesText: t('Yes'),
			noText: t('Close'),
			portalClassName: '',
			yesCallback: () => {
				this.setState({ deletingTenant: null });
				dispatch(tenantActionCreators.deleteTenant(this.state.deletingTenant.id));
			},
			noCallback: () => this.setState({ deletingTenant: null }),
		};
		return (
			<ConfirmDialog {...dlgProps}>
				{t('tntDeleteTenantConfirmMessage', { tenantName: this.state.deletingTenant.name })}
			</ConfirmDialog>
		);
	}

	isSystemAdmin = () => {
		return this.props.userTenantId === util.GlobalOrganization.Id;
	}

	moveUpHandler = (selectedId: string) => {
		this.props.dispatch(tenantActionCreators.move(selectedId, -1));
	}

	moveDownHandler = (selectedId: string) => {
		this.props.dispatch(tenantActionCreators.move(selectedId, 1));
	}
	onPositionChange = (from: any, to: any, inside: boolean, order: number) => {
		if (from.ref.id !== 'fakeNewItem') {
			this.setState({
				positionChangeDlg: {
					open: true,
					from,
					to,
					order,
					inside,
				}
			});
		}
	}

	changeOrganization = (from: any, to: any, inside: boolean, order: number) => {
		if (!inside) {
			this.props.dispatch(tenantActionCreators.migrate(from.ref.id, to.ref.parentTenantId, order));
		} else {
			this.props.dispatch(tenantActionCreators.migrate(from.ref.id, to.ref.id, order));
		}
	}

	closePositionChangeDlg = () => this.setState({ positionChangeDlg: { open: false, from: null, to: null, order: 0, inside: false } });

	buildChangeTenantPositionConfirmDlg = (from: any, to: any, inside: boolean, order: number) => {
		const { t } = this.props;
		const dlgProps: OrganizationDragDialogProps = {
			from,
			to,
			order,
			parentSelector: (): HTMLElement => document.querySelector(util.portalAppId),
			title: t('changeOrganization'),
			yesText: t('OK'),
			noText: t('Cancel'),
			portalClassName: '',
			yesCallback: () => {
				this.changeOrganization(from, to, inside, order);
				this.closePositionChangeDlg();
			},
			noCallback: this.closePositionChangeDlg,
			onPositionChange: this.onPositionChange,
		};
		return (
			<OrganizationDragDialog {...dlgProps} />
		);
	}

	onDragStart = (e: any, path: string, item: any) => {
		if (item.id === util.GlobalOrganization.Id) {
			e.stopPropagation();
			e.preventDefault();
			return true;
		}
	}

	onDragOver = (e: any, path: string, item: any, inside: boolean, draggingItemPath: string, draggingItem: any) => {
		const moveTagAsRootSibling = item.id === util.GlobalOrganization.Id && !inside;
		const moveToHimself = item.id === draggingItem.id;
		const shouldForbidToDrop = moveTagAsRootSibling || moveToHimself;

		if (shouldForbidToDrop) {
			e.stopPropagation();
			return true;
		}
	}

	render() {
		const { t, items, showSchemaEditor, showMembersEditor, userTenantId, selected, isAdding, isEditing } = this.props;
		const { positionChangeDlg } = this.state;
		const isSystemAdmin = this.isSystemAdmin();
		const organizationTreeProps: OrganizationTreeProps = {
			items,
			enableEdit: true,
			userTenantId,
			selectedId: selected,
			onItemSelected: (id: string): void => { this.props.dispatch(tenantActionCreators.setTenantItemsSelected(id)); },
			isAdding,
			isEditing,
			onAdd: (selectedId: string) => { this.props.dispatch(tenantActionCreators.setIsAdding(true)); },
			onMoveDelete: (selectedId: string) => {
				const item = items.find(s => s.id === selectedId);
				this.setState({ deletingTenant: item });
			},
			onMoveDown: this.moveDownHandler,
			onMoveUp: this.moveUpHandler,
			draggable: true,
			onPositionChange: this.onPositionChange,
			onDragStart: this.onDragStart,
			onDragOver: this.onDragOver,
		};

		const listContent = (
			<React.Fragment>
				{isSystemAdmin &&
					<div className='btn-group tenant-schema'>
						<Button
							className='tenant-schema-btn'
							style='accent'
							size='small'
							icon='mdi mdi-cog-outline'
							disabled={showMembersEditor || isAdding || isEditing}
							text={t('tntTenantSchema')}
							title={t('tntTenantSchema')}
							onClick={this.onTenantSchemaClick}
						/>
					</div>
				}
				<OrganizationTreeCommon {...organizationTreeProps} />
			</React.Fragment>
		);

		return (
			<div className='tenant-list'>
				{showSchemaEditor
					? <OrganizationSchemaEditor />
					: listContent
				}
				{this.state.deletingTenant && this.buildDeletingTenantConfirmDialog()}
				{positionChangeDlg.open && this.buildChangeTenantPositionConfirmDlg(positionChangeDlg.from, positionChangeDlg.to, positionChangeDlg.inside, positionChangeDlg.order)}
			</div>
		);
	}
}

export const OrganizationsPanel = connect(
	(state: { tenant: OrganizationState, navApp: any }) => ({
		items: state.tenant.items,
		selected: state.tenant.selected,
		showMembersEditor: state.tenant.showMembersEditor,
		showSchemaEditor: state.tenant.showSchemaEditor,
		userTenantId: state.navApp.user.orgId,
		isAdding: state.tenant.isAdding,
		isEditing: state.tenant.isEditing,
	})
)(OrganizationsPanelInner) as React.ComponentClass<{}>;