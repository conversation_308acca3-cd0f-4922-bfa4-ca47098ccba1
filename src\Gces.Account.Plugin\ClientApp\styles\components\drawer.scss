.gc-drawer {
	position: fixed;
	display: flex;
	flex-direction: column;
	top: 0;
	bottom: 0;
	background-color: $ef-bg-lt;
	pointer-events: auto;
	transition: all 0.2s ease-in-out;
	z-index: 10000;
	width: 340px;
	opacity: 1;

	&.left {
		left: 0;
		box-shadow: 0 0 5px 2px rgba(0, 0, 0, .1);
	}

	&.right {
		right: 0;
		box-shadow: 0 0 5px 2px rgba(0, 0, 0, .1);
	}

	&.middle.open {
		width: 628px;
	}

	.gc-drawer-header {
		width: 100%;
		height: 80px;
		color: $ef-text;
		padding: 0 10px 0 20px;

		.title {
			display: inline-block;
			font-size: 20px;
			width: calc(100% - 50px);
			height: 80px;
			line-height: 80px;
			vertical-align: middle;
			text-align: center;

			@include gces-truncate;
		}

		.close-btn {
			display: inline-block;
			float: right;
			font-size: 20px;
			width: 40px;
			height: 40px;
			line-height: 40px;
			text-align: center;
			margin: 8px 8px 0 0;
			border-radius: 40px;
			cursor: pointer;
			color: $ef-accent;

			&:hover {
				background-color: rgba(0, 0, 0, .2);
			}
		}
	}

	.fake-input {
		position: fixed;
		bottom: -10px;
		right: -10px;
		width: 0;
		height: 0;
		border: none;
		margin: 0;
		padding: 0;
		outline: none;
	}

	.gc-drawer-body {
		padding: 0 10px 0 20px;
		overflow: auto;
		flex: 1;

		.form-item {
			font-size: 12px;
			padding-top: 1px;
			margin: 0 10px 10px 0;

			.ef-control {
				margin-bottom: 1px;

				div.efc-label {
					width: 100%;

					span {
						white-space: nowrap;
						text-overflow: ellipsis;
						overflow: hidden;
						width: 100%;
					}
				}
			}

			.efc-boolean .efc-radio span {
				color: $ef-text;

				@include gces-truncate;
			}

			.required {
				color: $ef-text;
				font-weight: 400;
				max-width: 100%;

				@include gces-truncate;

				&::after {
					margin-left: 4px;
					content: "*";
					font-size: $ef-font-size-sm;
					color: $danger;
				}
			}

			.error-text {
				font-size: 13px;
				color: red;
			}
		}

		.custom-property-alert {
			position: relative;
			padding: 5px 10px 5px 30px;
			font-size: 12px;
			line-height: 15px;
			color: $ef-text;

			i {
				position: absolute;
				top: 0;
				left: 0;
				display: block;
				width: 30px;
				height: 100%;
				text-align: center;
				color: $warning;
				font-size: 18px;
				line-height: 25px;
			}
		}
	}

	.gc-drawer-footer {
		width: 100%;
		display: flex;
		flex-direction: row;
		justify-content: flex-end;
		padding: 20px 25px;

		button {
			>span {
				display: block;
				max-width: 100px;

				@include gces-truncate;
			}
		}
	}
}

.gc-drawer-mask-layer {
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	height: 100%;
	background-color: rgba(255, 255, 255, .2);
	z-index: 9999;
}