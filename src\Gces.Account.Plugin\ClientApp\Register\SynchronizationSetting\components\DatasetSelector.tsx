import { Dropdown, DropdownItemProps, DropdownProps } from 'gces-ui';
import React, { PureComponent } from 'react';
import { translate } from 'react-i18next';
import { Dataset } from '../store/interface';

export interface DatasetSelectorProps {
	disabled: boolean;
	datasets: Dataset[];
	currentDataset: Dataset;
	datasetsLoaded: boolean;
	onSelectDataset: (dataset: Dataset) => void;
	loadDirectDatasets: () => void;
	t?: any;
}

interface DatasetSelectorState {
	search: string;
}

@translate('synchronization', { wait: true })
export default class DatasetSelector extends PureComponent<DatasetSelectorProps, DatasetSelectorState> {

	state: DatasetSelectorState = {
		search: '',
	};

	onSearchChanged = (value: string) => {
		this.setState({ search: value });
	}

	render() {
		const { disabled, datasets, currentDataset, datasetsLoaded, onSelectDataset, loadDirectDatasets, t } = this.props;
		const { search } = this.state;

		const dpItems: DropdownItemProps[] = (datasets || [])
			.filter(d => d.name.toLowerCase().includes(search.toLowerCase()))
			.map(d => ({
				value: d,
				text: d.name,
				selected: currentDataset?.id === d.id,
				divider: true,
				key: d.id,
			}));
		const dropdownProps: DropdownProps = {
			className: 'efc-dropdown combo-box-with-icon',
			menuClassName: 'dataset-editor-dropdown-menu dataset-list',
			offset: true,
			width: '100%',
			menuWidth: '100%',
			style: 'default',
			size: 'small',
			textAlign: 'left',
			canSearch: true,
			canSort: true,
			readOnly: true,
			searchPlaceHolder: t('searchPlaceHolder'),
			text: currentDataset?.name,
			useVirtualList: true,
			scrollbarProps: { autoHide: false },
			items: dpItems,
			disabled,
			onSelect: onSelectDataset,
			onToggle: (open) => {
				if (open) {
					this.setState({ search: '' });
					if (!datasetsLoaded) {
						loadDirectDatasets();
					}
				}
			},
			onSearchChanged: this.onSearchChanged,
		};
		return (
			<Dropdown {...dropdownProps} />
		);
	}
}
