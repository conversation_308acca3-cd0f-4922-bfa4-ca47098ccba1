import { takeEvery, put } from 'redux-saga/effects';
import * as util from '../util/index';
import ActionTypes from '../actions/action-types';
import Actions from '../actions/actions';
import * as util2 from '../util/generateTokenUtil';
import { SaveTokenRequestModelV2 } from '../interfaces/generate-token-interfaces';
import { safeFetchV2 } from '../utils/safeFetchV2';

function* getClaimMappings() {
	const { result } = yield util.sendRequestV2(util.claimMappingsUrl, 'GET');
	if (result) {
		const claimMappings = result.map(c => {
			return {
				id: c.id,
				name: c.name,
				propName: c.isBuiltIn ? window.AdminPortal.i18n.t(c.propName, { ns: 'account' }) : c.propName,
				isBuiltIn: c.isBuiltIn
			};
		});
		yield put(Actions.SetClaimMappings(claimMappings));
	}
}
function* getProperties() {
	const { result } = yield util.sendRequestV2(util.customizePropertiesUrl, 'GET');
	if (result) {
		yield put(Actions.SetProperties(result));
	}
}
function* addClaimMapping(action) {
	const { result } = yield util.sendRequestV2(util.claimMappingsUrl, 'POST', action.data);
	if (result) {
		yield put(Actions.GetClaimMappings());
		yield put(Actions.SetClaimMappingsViewMode('claimMappingsList'));
	}
}
function* deleteClaimMapping(action) {
	const { result } = yield util.sendRequestV2(`${util.claimMappingsUrl}/${action.data.id}`, 'DELETE');
	if (result) {
		yield put(Actions.GetClaimMappings());
		yield put(Actions.SetDeletingClaimMapping(null));
	}
}
function* getSecuritySettings() {
	yield put(Actions.SetSecuritySettingsBusy(true));
	const { result } = yield util.sendRequestV2(util.securitySettingsUrl, 'GET');
	if (result) {
		const rst = yield util.sendRequestV2('/api/v2/identity/clients/integration', 'GET');
		if (rst && rst.result && rst.result.clientSecret) {
			result.integrationClientSecret = rst.result.clientSecret;
		}
		yield put(Actions.SetSecuritySettings(result));
	}
	yield put(Actions.SetSecuritySettingsBusy(false));
}
function* setSecuritySettings(action) {
	yield put(Actions.StartLoading());
	const { result } = yield util.sendRequestV2(util.securitySettingsUrl, 'PUT', action.data.securitySettings);
	if (result) {
		if (action.data.securitySettings.integrationClientSecret) {
			yield util.sendRequestV2('/api/v2/identity/clients/integration/secret', 'PUT', { ClientSecret: action.data.securitySettings.integrationClientSecret });
		}
		yield put(Actions.SetSecuritySettings({ ...action.data.securitySettings }));
	}
	yield put(Actions.EndLoading());
}

function* getTokens(action) {
	yield put(Actions.SetBusy(true));
	const response = yield safeFetchV2(`${util.accountIntegrationTokenV2Url}/${action.data.id}`, {
		credentials: 'same-origin',
		method: 'GET',
		headers: { 'Content-type': 'application/json', 'Accept': 'application/json' }
	});
	if (action.data.id === 'all') {
		if (response && Array.isArray(response.result)) {
			yield put(Actions.SetTokens(response.result));
		}
	}
	else {
		if (response && Array.isArray(response.result) && response.result[0]) {
			util2.copyTextToClipboard(response.result[0].token);
		}
	}
	yield put(Actions.SetBusy(false));
}

function* revokeToken(action) {
	yield put(Actions.SetBusy(true));
	const response = yield safeFetchV2(`${util.accountIntegrationTokenV2Url}/${action.data.id}`, {
		credentials: 'same-origin',
		method: 'DELETE',
		headers: { 'Content-type': 'application/json', 'Accept': 'application/json' }
	});
	if (response.result) {
		const msg = window.AdminPortal.i18n.t('account:gtRevokeTokenSuccess');
		window.AdminPortal.Notifications.Send(2, msg, msg, 5000);
		yield getTokens({ data: { id: 'all' } });
	}
	yield put(Actions.SetBusy(false));
}

function* generateUrl(action) {
	yield put(Actions.SetBusy(true));
	const response = yield safeFetchV2(`${util.accountIntegrationTokenV2Url}/generate-url`, {
		credentials: 'same-origin',
		method: 'POST',
		headers: { 'Content-type': 'application/json', 'Accept': 'application/json' },
		body: JSON.stringify({ ...action.data })

	});
	if (response.result) {
		yield put(Actions.SetUrls(response.result.doc, response.result.admin, response.result.resource));
	} else if (response.error) {
		const { code, context } = response.error;
		window.AdminPortal.Notifications.Send(0, window.AdminPortal.i18n.t('account:Error'), window.AdminPortal.i18n.t(`account:error_${code}`, context));
	}
	yield put(Actions.SetBusy(false));
}

function* saveToken(action) {
	yield put(Actions.SetBusy(true));
	const requestBody: SaveTokenRequestModelV2 = {
		title: action.data.title,
		accessToken: action.data.accessToken,
		description: action.data.description,
		expiresIn: action.data.expiresIn,
		tokenType: action.data.tokenType,
		user: action.data.user,
		organizationPath: action.data.organizationPath,
	};
	const response = yield safeFetchV2(util.accountIntegrationTokenV2Url, {
		credentials: 'same-origin',
		method: 'POST',
		headers: { 'Content-type': 'application/json', 'Accept': 'application/json' },
		body: JSON.stringify(requestBody)
	});
	if (response.result) {
		yield getTokens({ data: { id: 'all' } });
	}
	yield put(Actions.SetBusy(false));
}

function* getConcurrenceStatus() {
	yield put(Actions.StartLoading());
	const { result } = yield util.sendRequestV2(`/heartbeatStatus?p=${(new Date()).valueOf()}`, 'GET');
	if (result) {
		yield put(Actions.SetConcurrenceStatus(result));
	}
	yield put(Actions.EndLoading());
}

function* banUserInConcurrence(action) {
	const { result } = yield util.sendRequestV2('/heartbeatBan', 'POST', { 'UserId': action.payload.userId, 'Ip': action.payload.ipRaw });
	if (result) {
		yield put(Actions.GetConcurrenceStatus());
	}
}

function* syncingData(action) {
	yield put(Actions.StartLoading());
	yield util.sendRequestV2(`${util.externalLoginProvidersUrl}/${action.provider.providerName}/sync`, 'POST');
	yield put(Actions.EndLoading());
}

function* getTFASettings(action) {
	yield put(Actions.SetTFASettingsBusy(true));
	const { result } = yield util.sendRequestV2(util.tfaSettingsUrl, 'GET');
	if (result && result.settings) {
		if (result.type === 0) {
			result.type = 'SMS';
		} else if (result.type === 1) {
			result.type = 'EMAIL';
			yield put(Actions.GetEmailSettings());
		}
		result.settings = JSON.parse(result.settings);
		yield put(Actions.SetTFASettingsSaved(true));
		yield put(Actions.SetTFASettings(result));
		yield put(Actions.SetTFASettingsSaved(false));
	}
	yield put(Actions.SetTFASettingsBusy(false));
}
function* setTFASettings(action) {
	yield put(Actions.SetTFASettingsBusy(true));
	const { result } = yield util.sendRequestV2(util.tfaSettingsUrl, 'PUT', {
		enabled: action.data.tfaSettings.enabled,
		type: action.data.tfaSettings.type,
		length: 6,
		lifetime: action.data.tfaSettings.lifetime,
		settings: JSON.stringify(action.data.tfaSettings.settings || '')
	});
	if (result) {
		yield put(Actions.SetTFASettingsSaved(true));
		yield put(Actions.SetTFASettings(action.data.tfaSettings));
		yield put(Actions.SetTFASettingsSaved(false));
	}
	yield put(Actions.SetTFASettingsBusy(false));
}
function* testTFASettings(action) {
	yield put(Actions.SetTFASettingsBusy(true));
	const { result } = yield util.sendRequestV2('/api/v2/identity/users/me/tfa/testing', 'POST', {
		enabled: action.data.tfaSettings.enabled,
		type: action.data.tfaSettings.type,
		length: 6,
		lifetime: action.data.tfaSettings.lifetime,
		settings: JSON.stringify(action.data.tfaSettings.settings || '')
	});
	if (result && result.success) {
		window.AdminPortal.Notifications.Send(2, window.AdminPortal.i18n.t('account:SendVerificationCodeSuccess'), window.AdminPortal.i18n.t('account:SendVerificationCodeSuccess'));
		yield put(Actions.SetTFASettingsValidated(true));
	} else {
		const errMsg = result.errorCode ? window.AdminPortal.i18n.t(`account:${result.errorCode}`) : result.errorMessage;
		window.AdminPortal.Notifications.Send(0, window.AdminPortal.i18n.t('account:Error'), window.AdminPortal.i18n.t('account:SendVerificationCodeFailed', { error: errMsg }));
		yield put(Actions.SetTFASettingsValidated(false));
	}
	yield put(Actions.SetTFASettingsBusy(false));
}
function* getEmailSettings(action) {
	const { result } = yield util.sendRequestV2(util.emailSettingsUrl, 'GET', null, null, true);
	if (result) {
		yield put(Actions.SetEmailSettings(true));
	} else {
		yield put(Actions.SetEmailSettings(false));
	}
}

function* getInactiveSessionSettings() {
	yield put(Actions.SetInactiveSessionSettingsBusy(true));
	const { result } = yield util.sendRequestV2(util.inactiveSessionSettingsUrl);
	if (result) {
		yield put(Actions.SetInactiveSessionSettings(result));
	}
	yield put(Actions.SetInactiveSessionSettingsBusy(false));
}
function* setInactiveSessionSettings(action) {
	yield put(Actions.SetInactiveSessionSettingsBusy(true));
	const { result } = yield util.sendRequestV2(util.inactiveSessionSettingsUrl, 'PUT', action.inactiveSessionSettings);
	if (result) {
		yield put(Actions.SetSaveInactiveSessionSettingsState(true));
	} else {
		yield put(Actions.SetSaveInactiveSessionSettingsState(false));
	}
	yield put(Actions.SetInactiveSessionSettingsBusy(false));
}
function* getAllUsers() {
	const { result } = yield util.sendRequestV2(util.usersUrl, 'GET');
	if (result && result.models) {
		const users = [];
		result.models.map(m => users.push({ name: m.username, provider: m.provider }));
		yield put(Actions.SetUsers(users));
	}
}

export default function* watchAll() {
	yield takeEvery(ActionTypes.GetClaimMappings, getClaimMappings);
	yield takeEvery(ActionTypes.GetProperties, getProperties);
	yield takeEvery(ActionTypes.AddClaimMapping, addClaimMapping);
	yield takeEvery(ActionTypes.DeleteClaimMapping, deleteClaimMapping);
	yield takeEvery(ActionTypes.GetSecuritySettings, getSecuritySettings);
	yield takeEvery(ActionTypes.SetSecuritySettingsSaga, setSecuritySettings);
	yield takeEvery(ActionTypes.GetTokens, getTokens);
	yield takeEvery(ActionTypes.RevokeToken, revokeToken);
	yield takeEvery(ActionTypes.GenerateUrl, generateUrl);
	yield takeEvery(ActionTypes.SaveToken, saveToken);

	// Concurrence
	yield takeEvery(ActionTypes.GetConcurrenceStatus, getConcurrenceStatus);
	yield takeEvery(ActionTypes.BanUserInConcurrence, banUserInConcurrence);

	// External Login Providers
	yield takeEvery(ActionTypes.SyncData, syncingData);

	// Two Factor Authentication
	yield takeEvery(ActionTypes.GetTFASettings, getTFASettings);
	yield takeEvery(ActionTypes.SetTFASettingsSaga, setTFASettings);
	yield takeEvery(ActionTypes.TestTFASettings, testTFASettings);
	yield takeEvery(ActionTypes.GetEmailSettings, getEmailSettings);

	// Inactive Session Settings
	yield takeEvery(ActionTypes.GetInactiveSessionSettings, getInactiveSessionSettings);
	yield takeEvery(ActionTypes.SetInactiveSessionSettingsSaga, setInactiveSessionSettings);
	yield takeEvery(ActionTypes.GetAllUsers, getAllUsers);
}