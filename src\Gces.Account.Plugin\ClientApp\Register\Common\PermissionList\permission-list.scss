.permission-list {
	width: 100%;
	height: 100%;

	.permission-category-section {
		.section-header {
			display: flex;
			align-items: center;
			padding: 15px 0;

			.section-check-box {
				padding: 0 5px 5px 0;
				margin-left: 5px;
			}

			.section-divider {
				flex: 1;
				height: 1px;
				background-color: $ef-accent;
				margin: 0;
			}
		}

		.section-body {
			display: flex;
			flex-wrap: wrap;
			padding-left: 60px;

			.permission-item {
				display: flex;
				align-items: center;
				flex: 0 0 210px;
				max-width: 210px;
				height: 40px;
				padding: 5px;

				.permission-item-check-box {
					margin-right: 5px;
					max-width: 90%;

					span {
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}
				}

				.permission-item-tips {
					padding-top: 5px;

					&.disable {
						opacity: 0.7;
					}
				}
			}
		}
	}

	.note-item {
		height: 30px;
		background-color: var(--gces-accent1-transparentize90);
		display: flex;
		align-items: center;
		width: 100%;
		border-radius: 5px;
		padding: 0 7px;

		.note-icon {
			margin: 0 10px 0 0;
		}

		.note-text {
			font-size: var(--gces-ef-font-size-sm);

			@include gces-truncate;
		}
	}

	.permission-global {
		width: 100%;
		height: calc(100% - 30px);
	}
}
