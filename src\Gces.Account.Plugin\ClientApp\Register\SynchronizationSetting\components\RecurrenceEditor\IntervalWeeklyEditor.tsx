import * as update from 'immutability-helper';
import * as React from 'react';
import { translate } from 'react-i18next';

import { Dropdown, DropdownItemProps } from 'gces-ui/lib/components/Dropdown';
import { Label } from 'gces-ui/lib/components/Label';
import { IntervalWeeklyRepeatDetail, ScheduledInfo } from '../../store/interface';
import { generateNumbers } from '../../utils';
import { DayPicker } from './DayPicker';
import { RecSettingEditor } from './RecSettingEditor';

interface ConnectedProps {
	scheduleInfo: ScheduledInfo;
	updateScheduleInfo: (scheduleInfo: ScheduledInfo) => void;
	t?: any;
}

const weeks = generateNumbers(1, 52);

@translate('synchronization', { wait: true })
export class IntervalWeeklyEditor extends React.PureComponent<ConnectedProps> {

	onWeekSelect = (week: number) => {
		const { scheduleInfo, updateScheduleInfo } = this.props;
		const newSchedule = update(scheduleInfo, { detail: { weeksRepeatInterval: { $set: week } } });
		updateScheduleInfo(newSchedule);
	}

	render() {
		const { scheduleInfo, scheduleInfo: { repeatType, detail }, updateScheduleInfo, t, } = this.props;
		if (repeatType !== 'IntervalWeekly') return null;

		const { weeksRepeatInterval } = detail as IntervalWeeklyRepeatDetail;
		const weeksRepeatItems: DropdownItemProps[] = weeks.map(hour => ({ text: `${hour}`, value: hour, selected: hour === weeksRepeatInterval }));
		return (
			<div className='sc-rec-daily'>
				<Label inverted={window.inverted} labelOnTop>
					<DayPicker inverted={window.inverted} scheduleInfo={scheduleInfo} updateScheduleInfo={updateScheduleInfo} />
				</Label>

				<RecSettingEditor
					text={t('intervalWeeklyEditorTextEvery')}
					value='Weeks'
					units={t('intervalWeeklyEditorTextUnits')}
					checked={true}
					inverted={window.inverted}
					noRadio={true}
				>
					<Dropdown
						text={`${weeksRepeatInterval}`}
						items={weeksRepeatItems}
						menuWidth='100%'
						offset
						size='small'
						textAlign='left'
						position='center'
						onSelect={this.onWeekSelect}
						disabled={false}
						inverted={window.inverted}
					/>
				</RecSettingEditor>

			</div>
		);
	}
}