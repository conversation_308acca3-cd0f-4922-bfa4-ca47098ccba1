{
	// Settings
	"Enable": "Enable",
	"Disable": "Disable",
	"synchronizeStateTip": "Synchronize user, organization, roles",
	"synchronizationDatasets!title": "Select Datasets",
	"synchronizationDatasets!tip": "Please choose dataset(s) used to synchronize users/organizations/roles, etc.",
	"synchronizationDatasets!user": "User Dataset",
	"synchronizationDatasets!organization": "Organization Dataset",
	"synchronizationDatasets!role": "Role Dataset",
	"synchronizationDatasets!userRoleRelation": "User-Role Relation Dataset",
	"synchronizationDatasets!userOrganization": "User-Organization Dataset",
	"searchPlaceHolder": "Search",
	"schedulingSettings": "Scheduled Synchronization Settings",
	"timeoutSettings": "Timeout Setting",
	"schedulingSync": "Automatic Synchronize",
	"failureNotification": "Failure Notification",
	"noFailureNotification": "No notification has been configured, click to navigate to the configuration page.",
	"applicationSecret!label": "Application Secret",
	"applicationSecret!title": "Application Secret",
	"applicationSecret!tip": "All synchronized users can use the application key to generate tokens, but they cannot log in with it; modifying the application key will not affect the validity of issued tokens; for security reasons, please be careful about sharing the key",
	"generate": "Generate",
	"copy": "Copy",
	"manualSync": "Manual Sync",
	"saveChanges": "Save Changes",
	"cancel": "Cancel",
	"Email": "Email",
	"WeChat4Work": "WeCom",
	"DingTalk": "DingTalk",
	"MSTeams": "MS Teams",
	"Slack": "Slack",
	"API": "API",
	"ManualSyncSuccessful": "Manually triggering synchronization task successfully",
	"SaveSuccessful": "Sync settings saved successfully",
	"scheduleFailedMessage": "Task scheduling did not start successfully",
	// Views
	"syncResults": "Synchronization Results",
	"nextRunTime": "Next Sync Time",
	"NoAutoSyncTask": "No automatically synchronized tasks",
	"syncedTime": "Synchronized time",
	"synchronizing": "Synchronizing...",
	"syncedMessage!success": "Synchronization successful",
	"syncedMessage!notAllSuccess": "Not all data is synchronized",
	"syncedMessage!canceled": "Synchronization canceled",
	"syncedMessage!failed": "Synchronization failed",
	"syncRecords": "Synchronized Records",
	"entity": "Entity",
	"successfulQuantity": "Successful Quantity",
	"failedQuantity": "Fail Quantity",
	"warningQuantity": "Warning Quantity",
	"noRecordTip": "No sync records",
	"informationNotSynced": "Information Not Synced",
	"download": "Download",
	"type": "Type",
	"name": "Name",
	"reason": "Reason",
	"user": "User",
	"organization": "Organization",
	"role": "Role",
	"userRoleRelation": "User-Role Relation",
	"userOrgRelation": "User-Organization",
	"userOrganization": "User-Organization",
	"downloadDescription": "Click to download the full error message",
	"Error": "Error",
	"Warning": "Warning",
	// Recurrence Editor
	"recEditorHeaderRepeat": "Repeat",
	"recEditorLabelStart": "Start",
	"recEditorLabelEnd": "End",
	"recEditorPlaceholderNoEndDate": "No End Date",
	"recEditorDailyEditor": "Daily",
	"recEditorWeeklyEditor": "Weekly",
	"recEditorIntervalWeeklyEditor": "Weekly",
	"recEditorMonthlyEditor": "Monthly",
	"timeout": "Timeout",
	"hour": "Hour(s)",
	"minute": "Minute(s)",
	// Daily Editor
	"dailyEditorTextAt": "At {{time}}",
	"dailyEditorTextEvery": "Every",
	"dailyEditorHoursTextUnits": "hour(s)",
	"dailyEditorMinutesTextUnits": "minute(s)",
	"dailyEditorSecondsTextUnits": "second(s)",
	"dailyEditorAddExecutionTimeRange": "Add Execution Time Range",
	"dailyEditorExecutionTimeRangeStartTime": "Start Time",
	"dailyEditorExecutionTimeRangeEndTime": "End Time",
	"dailyExecutionTimeRangeDescription": "The daily execution time range is from {{startTime}} to {{endTime}}",
	"dailyExecutionTimeRangeNeedToBeRemove": "Remove the daily execution time range",
	"dailyTaskLessThan24HoursRemoveExecutionTimeRange": "The interval between the start and end date of daily scheduled task is less than 24 hours, the daily execution time range needs to be removed.",
	// Interval Weeks Weekly Editor
	"intervalWeeklyEditorTextEvery": "Every",
	"intervalWeeklyEditorTextUnits": "week(s)",
	"repeatWeekDes": "every {{weekNumber}} {{weekUnitStr}}",
	"repeatWeek": "week",
	"repeatWeeks": "weeks",
	"comma": ", ",
	"and": "and",
	"onlyInYear": "only in {{year}}",
	"throughYears": "{{startYear}} through {{endYear}}",
	"intervalWeeklyRepeatDescription": "At {{startTime}}, {{repeatWeekDes}}, on {{daysOfWeekDes}}{{years}}",
	"dayOfWeek_1": "Sunday",
	"dayOfWeek_2": "Monday",
	"dayOfWeek_3": "Tuesday",
	"dayOfWeek_4": "Wednesday",
	"dayOfWeek_5": "Thursday",
	"dayOfWeek_6": "Friday",
	"dayOfWeek_7": "Saturday",
	// Monthly Editor
	"monthlyEditorTextEvery": "Every",
	"monthlyEditorTextUnits": "month(s)",
	"monthlyEditorTextOnDay": "On day",
	"monthlyEditorTextOnDayUnits": "",
	"monthlyEditorTextOnTheLast": "On the last",
	"monthlyEditorTextOnTheFirst": "On the first",
	"monthlyEditorTextLastDay": "Day",
	"monthlyEditorTextLastWeekday": "Weekday",
	// Timezone
	"taskExecutingTimezone": "Executing Timezone",
	"timezone_Etc/GMT+12": "(UTC-12:00) International Date Line west",
	"timezone_Etc/GMT+11": "(UTC-11:00) Coordinated Universal Time-11",
	"timezone_America/Adak": "(UTC-10:00) Aleutian Islands",
	"timezone_Pacific/Honolulu": "(UTC-10:00) Hawaii",
	"timezone_Pacific/Marquesas": "(UTC-09:30) Marquesas Islands",
	"timezone_America/Anchorage": "(UTC-09:00) Alaska",
	"timezone_Etc/GMT+9": "(UTC-09:00) Coordinated Universal Time-09",
	"timezone_America/Tijuana": "(UTC-08:00) Baja California",
	"timezone_Etc/GMT+8": "(UTC-08:00) Coordinated Universal Time-08",
	"timezone_America/Los_Angeles": "(UTC-08:00) Pacific Time (US & Canada)",
	"timezone_America/Phoenix": "(UTC-07:00) Arizona",
	"timezone_America/Chihuahua": "(UTC-07:00) Chihuahua, La Paz, Mazatlan",
	"timezone_America/Denver": "(UTC-07:00) Mountain Time (US & Canada)",
	"timezone_America/Guatemala": "(UTC-06:00) Central America",
	"timezone_America/Chicago": "(UTC-06:00) Central Time (US & Canada)",
	"timezone_Pacific/Easter": "(UTC-06:00) Easter Island",
	"timezone_America/Mexico_City": "(UTC-06:00) Guadalajara, Mexico City, Monterrey",
	"timezone_America/Regina": "(UTC-06:00) Saskatchewan",
	"timezone_America/Bogota": "(UTC-05:00) Bogota, Lima, Quito, Rio Branco",
	"timezone_America/Cancun": "(UTC-05:00) Chetumal",
	"timezone_America/New_York": "(UTC-05:00) Eastern Time (US & Canada)",
	"timezone_America/Port-au-Prince": "(UTC-05:00) Haiti",
	"timezone_America/Havana": "(UTC-05:00) Havana",
	"timezone_America/Indiana/Indianapolis": "(UTC-05:00) Indiana (East)",
	"timezone_America/Grand_Turk": "(UTC-05:00) Turks and Caicos",
	"timezone_America/Asuncion": "(UTC-04:00) Asuncion",
	"timezone_America/Halifax": "(UTC-04:00) Atlantic Time (Canada)",
	"timezone_America/Caracas": "(UTC-04:00) Caracas",
	"timezone_America/Cuiaba": "(UTC-04:00) Cuiaba",
	"timezone_America/La_Paz": "(UTC-04:00) Georgetown, La Paz, Manaus, San Juan",
	"timezone_America/Santiago": "(UTC-04:00) Santiago",
	"timezone_America/St_Johns": "(UTC-03:30) Newfoundland",
	"timezone_America/Araguaina": "(UTC-03:00) Araguaina",
	"timezone_America/Sao_Paulo": "(UTC-03:00) Brasilia",
	"timezone_America/Cayenne": "(UTC-03:00) Cayenne, Fortaleza",
	"timezone_America/Argentina/Buenos_Aires": "(UTC-03:00) City of Buenos Aires",
	"timezone_America/Godthab": "(UTC-03:00) Greenland",
	"timezone_America/Montevideo": "(UTC-03:00) Montevideo",
	"timezone_America/Punta_Arenas": "(UTC-03:00) Punta Arenas",
	"timezone_America/Miquelon": "(UTC-03:00) Saint Pierre and Miquelon",
	"timezone_America/Bahia": "(UTC-03:00) Salvador",
	"timezone_Etc/GMT+2": "(UTC-02:00) Coordinated Universal Time-02",
	"timezone_Atlantic/Azores": "(UTC-01:00) Azores",
	"timezone_Atlantic/Cape_Verde": "(UTC-01:00) Cabo Verde Is.",
	"timezone_Etc/UTC": "(UTC) Coordinated Universal Time",
	"timezone_Europe/London": "(UTC+00:00) Dublin, Edinburgh, Lisbon, London",
	"timezone_Atlantic/Reykjavik": "(UTC+00:00) Monrovia, Reykjavik",
	"timezone_Africa/Sao_Tome": "(UTC+00:00) Sao Tome",
	"timezone_Africa/Casablanca": "(UTC+01:00) Casablanca",
	"timezone_Europe/Berlin": "(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna",
	"timezone_Europe/Budapest": "(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague",
	"timezone_Europe/Paris": "(UTC+01:00) Brussels, Copenhagen, Madrid, Paris",
	"timezone_Europe/Warsaw": "(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb",
	"timezone_Africa/Lagos": "(UTC+01:00) West Central Africa",
	"timezone_Asia/Amman": "(UTC+02:00) Amman",
	"timezone_Europe/Bucharest": "(UTC+02:00) Athens, Bucharest",
	"timezone_Asia/Beirut": "(UTC+02:00) Beirut",
	"timezone_Africa/Cairo": "(UTC+02:00) Cairo",
	"timezone_Europe/Chisinau": "(UTC+02:00) Chisinau",
	"timezone_Asia/Damascus": "(UTC+02:00) Damascus",
	"timezone_Asia/Hebron": "(UTC+02:00) Gaza, Hebron",
	"timezone_Africa/Johannesburg": "(UTC+02:00) Harare, Pretoria",
	"timezone_Europe/Kiev": "(UTC+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius",
	"timezone_Asia/Jerusalem": "(UTC+02:00) Jerusalem",
	"timezone_Europe/Kaliningrad": "(UTC+02:00) Kaliningrad",
	"timezone_Africa/Khartoum": "(UTC+02:00) Khartoum",
	"timezone_Africa/Tripoli": "(UTC+02:00) Tripoli",
	"timezone_Africa/Windhoek": "(UTC+02:00) Windhoek",
	"timezone_Asia/Baghdad": "(UTC+03:00) Baghdad",
	"timezone_Europe/Istanbul": "(UTC+03:00) Istanbul",
	"timezone_Asia/Riyadh": "(UTC+03:00) Kuwait, Riyadh",
	"timezone_Europe/Minsk": "(UTC+03:00) Minsk",
	"timezone_Europe/Moscow": "(UTC+03:00) Moscow, St. Petersburg",
	"timezone_Africa/Nairobi": "(UTC+03:00) Nairobi",
	"timezone_Asia/Tehran": "(UTC+03:30) Tehran",
	"timezone_Asia/Dubai": "(UTC+04:00) Abu Dhabi, Muscat",
	"timezone_Europe/Astrakhan": "(UTC+04:00) Astrakhan, Ulyanovsk",
	"timezone_Asia/Baku": "(UTC+04:00) Baku",
	"timezone_Europe/Samara": "(UTC+04:00) Izhevsk, Samara",
	"timezone_Indian/Mauritius": "(UTC+04:00) Port Louis",
	"timezone_Europe/Saratov": "(UTC+04:00) Saratov",
	"timezone_Asia/Tbilisi": "(UTC+04:00) Tbilisi",
	"timezone_Europe/Volgograd": "(UTC+04:00) Volgograd",
	"timezone_Asia/Yerevan": "(UTC+04:00) Yerevan",
	"timezone_Asia/Kabul": "(UTC+04:30) Kabul",
	"timezone_Asia/Tashkent": "(UTC+05:00) Ashgabat, Tashkent",
	"timezone_Asia/Yekaterinburg": "(UTC+05:00) Ekaterinburg",
	"timezone_Asia/Karachi": "(UTC+05:00) Islamabad, Karachi",
	"timezone_Asia/Qyzylorda": "(UTC+05:00) Qyzylorda",
	"timezone_Asia/Kolkata": "(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi",
	"timezone_Asia/Colombo": "(UTC+05:30) Sri Jayawardenepura",
	"timezone_Asia/Kathmandu": "(UTC+05:45) Kathmandu",
	"timezone_Asia/Almaty": "(UTC+06:00) Astana",
	"timezone_Asia/Dhaka": "(UTC+06:00) Dhaka",
	"timezone_Asia/Omsk": "(UTC+06:00) Omsk",
	"timezone_Asia/Yangon": "(UTC+06:30) Yangon (Rangoon)",
	"timezone_Asia/Bangkok": "(UTC+07:00) Bangkok, Hanoi, Jakarta",
	"timezone_Asia/Barnaul": "(UTC+07:00) Barnaul, Gorno-Altaysk",
	"timezone_Asia/Hovd": "(UTC+07:00) Hovd",
	"timezone_Asia/Krasnoyarsk": "(UTC+07:00) Krasnoyarsk",
	"timezone_Asia/Novosibirsk": "(UTC+07:00) Novosibirsk",
	"timezone_Asia/Tomsk": "(UTC+07:00) Tomsk",
	"timezone_Asia/Shanghai": "(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi",
	"timezone_Asia/Irkutsk": "(UTC+08:00) Irkutsk",
	"timezone_Asia/Singapore": "(UTC+08:00) Kuala Lumpur, Singapore",
	"timezone_Australia/Perth": "(UTC+08:00) Perth",
	"timezone_Asia/Taipei": "(UTC+08:00) Taipei",
	"timezone_Asia/Ulaanbaatar": "(UTC+08:00) Ulaanbaatar",
	"timezone_Australia/Eucla": "(UTC+08:45) Eucla",
	"timezone_Asia/Chita": "(UTC+09:00) Chita",
	"timezone_Asia/Tokyo": "(UTC+09:00) Osaka, Sapporo, Tokyo",
	"timezone_Asia/Pyongyang": "(UTC+09:00) Pyongyang",
	"timezone_Asia/Seoul": "(UTC+09:00) Seoul",
	"timezone_Asia/Yakutsk": "(UTC+09:00) Yakutsk",
	"timezone_Australia/Adelaide": "(UTC+09:30) Adelaide",
	"timezone_Australia/Darwin": "(UTC+09:30) Darwin",
	"timezone_Australia/Brisbane": "(UTC+10:00) Brisbane",
	"timezone_Australia/Sydney": "(UTC+10:00) Canberra, Melbourne, Sydney",
	"timezone_Pacific/Port_Moresby": "(UTC+10:00) Guam, Port Moresby",
	"timezone_Australia/Hobart": "(UTC+10:00) Hobart",
	"timezone_Asia/Vladivostok": "(UTC+10:00) Vladivostok",
	"timezone_Australia/Lord_Howe": "(UTC+10:30) Lord Howe Island",
	"timezone_Pacific/Bougainville": "(UTC+11:00) Bougainville Island",
	"timezone_Asia/Srednekolymsk": "(UTC+11:00) Chokurdakh",
	"timezone_Asia/Magadan": "(UTC+11:00) Magadan",
	"timezone_Pacific/Norfolk": "(UTC+11:00) Norfolk Island",
	"timezone_Asia/Sakhalin": "(UTC+11:00) Sakhalin",
	"timezone_Pacific/Guadalcanal": "(UTC+11:00) Solomon Is., New Caledonia",
	"timezone_Asia/Kamchatka": "(UTC+12:00) Anadyr, Petropavlovsk-Kamchatsky",
	"timezone_Pacific/Auckland": "(UTC+12:00) Auckland, Wellington",
	"timezone_Etc/GMT-12": "(UTC+12:00) Coordinated Universal Time+12",
	"timezone_Pacific/Fiji": "(UTC+12:00) Fiji",
	"timezone_Pacific/Chatham": "(UTC+12:45) Chatham Islands",
	"timezone_Etc/GMT-13": "(UTC+13:00) Coordinated Universal Time+13",
	"timezone_Pacific/Tongatapu": "(UTC+13:00) Nuku'alofa",
	"timezone_Pacific/Apia": "(UTC+13:00) Samoa",
	"timezone_Pacific/Kiritimati": "(UTC+14:00) Kiritimati Island",
	"timezone_America/Mazatlan": "(UTC-06:00) Mazatlan",
	"timezone_America/Whitehorse": "(UTC-07:00) Whitehorse",
	"timezone_America/Nuuk": "(UTC-02:00) Nuuk",
	"timezone_Africa/Abidjan": "(UTC+00:00) Abidjan",
	"timezone_Africa/Juba": "(UTC+03:00) Juba",
	"timezone_Europe/Kyiv": "(UTC+03:00) Kyiv",
	//ErrorCode
	"error_V2_000_019_0001": "Invalid user synchronization setting, please check the '{{configItem}}' setting.",
	"error_V2_000_019_0002": "Please select at least one dataset.",
	"error_V2_000_019_0003": "Failed to schedule the user synchronization task.",
	"error_V2_000_019_0004": "Failed to validate dataset field, missing fields: ",
	"error_0001": "Id cannot be empty.",
	"error_0002": "Name cannot be empty. Entity id: {{id}}.",
	"error_0003": "Duplicated id detected. Entity id: {{id}}. Duplicated entities: {{names}}",
	"error_0004": "Duplicated name detected.",
	"error_0005": "Invalid name detected.",
	"error_0006": "The id can only contain digits, letters, _, -, ., and @, and must not exceed a length of 128. Entity id: {{id}}.",
	"error_0007": "Duplicated email detected. Email: {{email}}. Duplicated users: {{names}}",
	"error_0011": "The entity does not exist. Entity id: {{id}}.",
	"error_0011!extension": "$t(synchronization:error_0011) Entity type: {{type}}.",
	"error_0021": "The property({{name}}) does not exist.",
	"error_0022": "Invalid property({{propName}}) value: {{propValues}}.",
	"error_0023": "Invalid email value: {{email}}.",
	"error_0024": "Invalid avatar value. The avatar value only support URL.",
	"error_0031": "Circle exists in the organization tree.",
	"error_0032": "The organization is not linked to the organization tree.",
	"error_0041_org": "Permission conflict. The conflict entities: (organization){{orgId}} and (parent organization){{parentId}}.",
	"error_0041_org_role": "Permission conflict. The conflict entities: (organization){{orgId}} and (organization role){{roleId}}.",
	"error_0098": "Synchronization timed out, or was abnormally cancelled.",
	"error_0099": "Unexpected exception occurs.",
	"error_1001": "Invalid user synchronization setting, please check the '{{configItem}}' setting.",
	"error_1002": "At least 1 dataset need to be assigned.",
	"error_1003": "Failed to schedule the user synchronization task. Please check the scheduling setting.",
	"error_1004": "Failed to validate dataset field. Missing fields: {{errorMessage}}.",
	"error_1005": "User synchronization timeout.",
	"error_1006": "Failed to get dataset result set. Failed dataset types: {{types}}.",
	"errorGetAppContacts": "Failed to get available contacts.",
	"fallback_unknown_error": "internal error",
}