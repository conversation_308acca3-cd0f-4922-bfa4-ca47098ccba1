.settings-panel {
	flex: 0 0 425px;
	padding: 0 10px;

	.synchronize-content {
		height: calc(100% - 60px);

		.synchronize-switch {
			display: flex;
			flex-direction: row;
			align-items: center;
			margin-top: 5px;

			.efc-label {
				width: 60%;
			}

			.efc-content {
				display: flex;
				justify-content: flex-end;
			}
		}

		.synchronize-settings-row {
			display: flex;
			align-items: center;
			margin: 10px 0;

			.efc-checkbox {
				flex: 0 0 50%;
			}

			.ef-dd {
				flex: 0 0 50%;
			}

			.efc-textbox {
				flex: 0 0 50%;
			}
		}

		.scheduling-settings {
			margin-top: 20px;

			.sc-recurrence {
				.sc-rec-daily {
					.sch-day {
						display: flex;
						justify-content: space-between;
					}

					.add-execution-time-range {
						.efc-checkbox {
							margin-bottom: 20px;
						}

						.execution-time-editors {
							margin-left: 30px;

							.execution-time-editor {
								display: flex;
								justify-content: space-between;
								margin-bottom: 10px;

								.efc-datetime {
									width: 200px;
								}
							}
						}
					}
				}

				.sc-rec-setting {
					height: 35px;
					margin-bottom: 10px;
					padding-top: 5px;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.sc-rec-setting-title {
						flex: 0 0 50%;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					.sc-rec-setting-content {
						flex: 0 0 50%;
						display: flex;
						justify-content: space-between;

						.sc-rec-setting-units {
							flex: 0 0 30%;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							margin-left: 10px;
							word-break: break-all;
							overflow-wrap: break-word;
							line-height: 30px;
						}

						.sc-rec-setting-editor {
							flex: auto;
							display: flex;
							flex-direction: row-reverse;

							.ef-dd {
								width: 100%;
							}
						}
					}
				}

				.sc-rec-descr {
					border-bottom: 1px solid var(--gces-nav-text-transparentize60);
					border-top: 1px solid var(--gces-nav-text-transparentize60);
					font-size: var(--gces-ef-font-size-sm);
					line-height: 18px;
					margin: 15px 0 0;
					padding: 10px 0;
				}
			}
		}

		.timeout-setting {
			margin-top: 20px;

			.timeout-control {
				margin: 10px 0;

				.efc-content {
					display: flex;

					.efc-number {
						flex: 0 0 35%;
						margin-right: 10px;
					}

					.ef-dd {
						flex: auto;
					}
				}
			}
		}

		.failure-notification {
			margin-top: 20px;
		}

		.synchronize-settings-divider {
			margin: 20px 0;
		}

		.application-secret {
			.password-copy {
				margin: 0 10px;
			}

			.synchronize-application-secret-tip {
				color: var(--gces-ef-accent);
				margin-left: 5px;
			}

			.application-secret-content {
				.flexAlignMiddle {
					display: flex;
					align-items: center;
					flex: auto;

					.efc-textbox {
						flex: 0 0 calc(100% - 30px);
					}

					.btn {
						flex: auto;
					}
				}

				.application-secret-row {
					display: flex;
					justify-content: space-between;
				}
			}
		}
	}

	.synchronize-settings-footer {
		display: flex;
		justify-content: space-between;
		padding: 10px 0;
		height: 60px;

		.right-button-list {
			display: flex;

			.btn-cancel {
				margin-left: 10px;
			}
		}
	}
}
