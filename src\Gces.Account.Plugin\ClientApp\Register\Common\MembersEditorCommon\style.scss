.members-editor {
	display: flex;
	flex-direction: column;
	background-color: var(--gces-content-bg-lt-5);

	.me-header {
		display: flex;
		flex: 0 0 50px;
		justify-content: space-between;
		align-items: center;

		.select-members-title {
			flex: 0 0 200px;
			font-weight: bold;
			font-size: $ef-font-size;
			margin: 0;
			padding: 0;
		}
	}

	.me-body {
		flex: 1;
		display: flex;

		.left-users {
			flex: 1;
			display: flex;
			flex-direction: column;
			border: 1px solid $ef-bg-dk;

			.search-box-wp {
				margin-right: 10px;
				flex: 0 0 50px;

				.search-box {
					width: 180px;
					background: $ef-bg-dk;
					margin-left: auto;
					margin-top: 10px;
					border-radius: 30px;
					display: flex;
					align-items: center;

					.sc-icon {
						flex: 0 0 30px;
						color: $ef-accent;
						font-size: $ef-icon-18;
						display: flex;
						align-items: center;
						justify-content: center;
					}

					.sc-input {
						flex: 1;
						background: none !important;
						border: none !important;
					}
				}
			}

			.user-list {
				flex: 1;

				.member-selector-action {
					align-self: center;
				}

				.empty-result-tip {
					margin-top: 30px;
					text-align: center;
					font-size: $ef-font-size-sm;
					font-style: italic;
				}
			}
		}

		.add-btn-wp {
			flex: 0 0 50px;
			display: flex;
			align-items: center;
			justify-content: center;

			.add-btn {
				display: flex;
				flex-direction: column;
				cursor: initial;

				i {
					font-size: 24px;
					color: $ef-accent;
				}

				span {
					font-size: $ef-font-size-sm;
					color: $ef-accent;
				}

				&.disabled {
					i,
					span {
						color: $ef-text-disabled;
						cursor: not-allowed;
					}
				}
			}
		}

		.right-users {
			flex: 1;
			display: flex;
			flex-direction: column;
			border: 1px solid $ef-bg-dk;

			.summary {
				flex: 0 0 50px;
				display: flex;
				flex-direction: row-reverse;
				align-items: center;
				font-size: $ef-font-size-sm;
				font-weight: bold;
				padding-right: 10px;

				>span {
					display: block;
					position: relative;
					padding-right: 10px;
					width: 200px;

					@include gces-truncate;
				}
			}

			.user-list {
				flex: 1;

				.member-selector-action {
					align-self: center;
				}
			}
		}
	}

	.me-footer {
		flex: 0 0 30px;
		display: flex;
		flex-direction: row-reverse;
		margin-top: 10px;

		.btn-group {
			display: flex;

			button {
				margin-left: 10px;

				>span {
					display: inline-block;
					max-width: 100px;

					@include gces-truncate;
				}
			}
		}
	}
}