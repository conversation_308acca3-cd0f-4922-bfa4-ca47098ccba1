.import-users-drawer {
	background-color: $ef-bg-inv;
	color: $ef-text-inv;
	width: 320px;
	left: 225px;

	.gc-drawer-header {
		background-color: $ef-accent;
		color: $text-contrast;
		font-size: 20px;

		.title {
			text-align: left;
			padding-left: 15px;
		}

		.close-btn {
			color: $text-contrast;
		}
	}

	.gc-drawer-body {
		height: calc(100% - 60px);
		min-height: 240px;
		padding: 10px !important;

		.users-upload {
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: space-around;
			background-color: $ef-bg-inv-dk;
			border: 2px dashed $ef-text-inv;

			&.users-upload-active {
				border: 2px dashed $ef-accent;
			}

			.main-content {
				text-align: center;
				cursor: pointer;

				i {
					display: block;
					font-size: 36px;
				}

				span {
					display: block;

					.big-font {
						font-size: 16px;
					}

					.small-font {
						font-size: 12px;
					}
				}

				button {
					margin: 2px auto;
					font-size: 15px;
					border-radius: 0;
					color: $ef-text-inv;
					background: $ef-bg-inv;

					i {
						font-size: 18px;
					}
				}
			}
		}
	}
}

.import-user-result-dlg {
	.dialog-body {
		display: block !important;
	}
}
