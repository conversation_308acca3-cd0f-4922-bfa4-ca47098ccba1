.tenant-schema-editor {
	padding: 10px 10px 10px 0;
	position: absolute;
	left: 0;
	top: 0;
	bottom: 0;
	right: 0;
	background: $ef-bg-lt;
	display: flex;
	flex-direction: column;

	.tse-header {
		flex: 0 0 50px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: $ef-font-size-sm;
		font-weight: bold;

		> span {
			display: inline-block;
			max-width: 125px;

			@include gces-truncate;
		}
	}

	.tse-body {
		flex: 1;

		.tnt-prop {
			display: flex;
			align-items: center;
			height: 40px;
			padding-left: 10px;
			font-size: $ef-font-size-sm;

			.prop-name {
				flex: 1;
				font-size: $ef-font-size-sm;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			button {
				margin-right: 5px;
				color: $ef-accent !important;
			}

			&.odd {
				background: $ef-bg;
			}
		}
	}

	.tse-footer {
		flex: 0 0 40px;
		display: flex;
		flex-direction: row-reverse;
		align-items: center;

		button {
			max-width: 100px;

			@include gces-truncate;
		}
	}

	@import "./tenant-prop-editor.scss";
}
