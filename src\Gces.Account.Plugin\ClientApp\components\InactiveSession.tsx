import { Scrollbars } from 'gces-react-custom-scrollbars';
import { <PERSON>Loader, BooleanEditor, Button, NumberEditor, AriaIcon, InputEditor, InputEditorProps, ComboBox, Label, SearchBox, Checkbox } from 'gces-ui';
import * as update from 'immutability-helper';
import * as React from 'react';
import { translate } from 'react-i18next';
import { connect } from 'react-redux';
import Actions from '../actions/actions';
import CGrid, { CGridProps } from 'gces-react-grid';

interface UserItem {
	id: number;
	name: string;
	provider: string;
}
interface ISSettings {
	enabled: boolean;
	timeout: number;
	redirectUrl: string;
	unrestrictedUsers: UserItem[];
}

interface ISSettingsProps {
	settings: ISSettings;
	users: UserItem[];
	busy: boolean;
	saved?: boolean;
}

interface ConnectProps {
	dispatch: any;
	t: any;
}

interface LocalState {
	settings: ISSettings;
	dirty: boolean;
	selectingUsers: boolean;
	searchText: string;
	selectedUsers: UserItem[];
	availableUsers: UserItem[];
}

const uuid = (() => {
	let st = 1000000;
	return () => {
		return st++;
	};
})();

class InactiveSessionSettings extends React.PureComponent<ISSettingsProps & ConnectProps, LocalState> {
	constructor(props, context) {
		super(props, context);
		this.state = {
			settings: null,
			dirty: false,
			selectingUsers: false,
			searchText: '',
			selectedUsers: [],
			availableUsers: [],
		};
	}

	componentDidMount() {
		this.props.dispatch(Actions.GetInactiveSessionSettings());
	}
	componentWillReceiveProps(nextProps: ISSettingsProps & ConnectProps) {
		if (nextProps.settings && !this.state.settings) {
			const settings = { ...nextProps.settings };
			if (settings.unrestrictedUsers) {
				settings.unrestrictedUsers.map(u => u.id = u.id || uuid());
			}
			this.setState({ settings });
		}
		if (nextProps.users) {
			const users = [...nextProps.users];
			this.setState({ availableUsers: users });
		}
		if (nextProps.saved !== null) {
			this.setState({ dirty: !nextProps.saved });
		}
	}

	saveSettings = () => {
		this.props.dispatch(Actions.SetInactiveSessionSettingsSaga(this.state.settings));
	}
	resetSettings = () => {
		const settings = { ...this.props.settings };
		this.setState({ settings, dirty: false, selectedUsers: [] });
	}

	onSelectUser = () => {
		if (!this.props.users) {
			this.props.dispatch(Actions.GetAllUsers());
		}
		this.setState({ selectingUsers: true });
	}
	onAddUser = () => {
		const { unrestrictedUsers } = this.state.settings;
		const newUsers = [...(unrestrictedUsers || [])];
		newUsers.push({ id: uuid(), name: '', provider: '' });
		const settings = update(this.state.settings, { unrestrictedUsers: { $set: newUsers } });
		this.setState({ settings, dirty: true });
	}
	onUpdateUser = (key: string, id: number, newValue: string) => {
		const { unrestrictedUsers } = this.state.settings;
		const newUsers = [...(unrestrictedUsers || [])];
		const updatedUser = newUsers.find(u => u.id === id);
		updatedUser[key] = newValue;
		const settings = update(this.state.settings, { unrestrictedUsers: { $set: newUsers } });
		this.setState({ settings, dirty: true });
	}
	onDeleteUser = (id: number) => {
		const { unrestrictedUsers } = this.state.settings;
		const newUsers = [...(unrestrictedUsers || [])];
		const idx = newUsers.findIndex(u => u.id === id);
		if (idx > -1) {
			newUsers.splice(idx, 1);
			const settings = update(this.state.settings, { unrestrictedUsers: { $set: newUsers } });
			this.setState({ settings, dirty: true });
		}
	}

	onRenderCell = (key: string, user: UserItem) => {
		const { t } = this.props;

		if (key === 'deleteAction') {
			return (
				<Button
					rounded
					size='small'
					style='transparent'
					icon='mdi mdi-delete-forever'
					title={t('deleteUser')}
					onClick={() => this.onDeleteUser(user.id)}
				/>
			);
		}

		const placeText = key === 'provider' ? t('providerPlaceText') : t('namePlaceText');
		const text = user[key];
		const inputProps: InputEditorProps = {
			className: 'cell-input',
			type: 'text',
			value: text,
			placeholder: placeText,
			onEveryChange: value => this.onUpdateUser(key, user.id, value as string),
		};
		return <InputEditor {...inputProps} />;
	}

	buildUserGrid = () => {
		const { t } = this.props;

		const gridColumns = [
			{ key: 'name', label: t('UserName'), width: 180 },
			{ key: 'provider', label: t('Provider'), width: 180 },
			{ key: 'deleteAction', label: '', width: 15 },
		];

		const rows = [];
		const users = (this.state.settings && this.state.settings.unrestrictedUsers) || [];
		users.map(u => rows.push(u));
		const gridProps: CGridProps = {
			rows,
			columns: gridColumns,
			rowHeight: 30,
			colClassName: 'dl-header',
			bodyClassName: 'dl-body',
			hideGridLine: true,
			onRenderCell: this.onRenderCell
		};

		return (
			<main className='unrestricted-users-list'>
				<CGrid {...gridProps} />
			</main>
		);
	}

	getRedirectUrl = (): string => {
		const { t } = this.props;
		const redirectUrl = (this.state.settings && this.state.settings.redirectUrl) || '/account/login';
		if (redirectUrl.toLowerCase() === '/account/login') {
			return t('LoginPage');
		} else if (redirectUrl.toLowerCase() === '/sessiondisconnected') {
			return t('SessionDisconnectedPage');
		} else {
			return redirectUrl;
		}
	}

	renderSettings = () => {
		const { t } = this.props;
		const dropdownItems = [
			{ value: '/account/login', text: t('LoginPage') },
			{ value: '/SessionDisconnected', text: t('SessionDisconnectedPage') },
			{ value: 'https://', text: t('CustomAddress') }
		];

		return (<div className='inactive-session-settings'>
			<div className='setting-items'>
				<div className='setting-item'>
					<div className='setting-label'>
						<span className='label-text-wrapper'>
							<span className='label-text' title={t('Enable')}>
								{t('Enable')}
							</span>
							<AriaIcon
								className='ml-1 mdi mdi-help-circle-outline'
								title={t('EnableInactiveSessionDescription')}
							/>
						</span>
					</div>
					<div className='setting-value'>
						<BooleanEditor
							value={(this.state.settings && this.state.settings.enabled) ? 'true' : 'false'}
							trueLabel={t('Yes')}
							falseLabel={t('No')}
							onChange={(value) => {
								const settings = update(this.state.settings, { enabled: { $set: value } });
								this.setState({ settings, dirty: true });
							}}
						/>
					</div>
				</div>
				<div className='setting-item'>
					<div className='setting-label' title={t('Timeout')}>{t('Timeout')}</div>
					<div className='setting-value'>
						<NumberEditor
							minValue={1}
							maxValue={120}
							value={(this.state.settings && this.state.settings.timeout) || 15}
							onChange={(value) => {
								const settings = update(this.state.settings, { timeout: { $set: value } });
								this.setState({ settings, dirty: true });
							}}
						/>
					</div>
				</div>
				<div className='setting-item'>
					<div className='setting-label' title={t('RedirectURL')}>{t('RedirectURL')}</div>
					<div className='setting-value'>
						<ComboBox
							items={dropdownItems}
							text={this.getRedirectUrl()}
							onChange={(value) => {
								const dirty = (this.props.settings.redirectUrl !== value) || this.state.dirty;
								const settings = update(this.state.settings, { redirectUrl: { $set: value } });
								this.setState({ settings, dirty });
							}}
						/>
					</div>
				</div>
				<div className='setting-item'>
					<div className='setting-label' title={t('UnrestrictedUsers')}>{t('UnrestrictedUsers')}</div>
					<div className='setting-value'>
						<div className='btn-group'>
							<Button
								className='btn-selectUsers'
								style='accent'
								size='small'
								icon='mdi mdi-plus'
								text={t('selectUsers')}
								title={t('selectUsers')}
								onClick={this.onSelectUser}
							/>
							<Button
								className='btn-addUser'
								style='accent'
								size='small'
								icon='mdi mdi-plus'
								text={t('addUser')}
								title={t('addUser')}
								onClick={this.onAddUser}
							/>
						</div>
					</div>
				</div>
				{this.buildUserGrid()}
			</div>
			<div className='settings-footer'>
				<Button
					style='accent'
					inline={true}
					text={t('Save')}
					title={t('Save')}
					disabled={!this.state.dirty}
					icon='mdi mdi-content-save'
					onClick={this.saveSettings}
				/>
				<Button
					inline={true}
					text={t('Cancel')}
					title={t('Cancel')}
					disabled={!this.state.dirty}
					icon='mdi mdi-cancel'
					onClick={this.resetSettings}
				/>
			</div>
		</div>);
	}

	onUserCheckChange = (value) => {
		const users = [...this.state.selectedUsers];
		const idx = users.findIndex(u => u.name === value.name && u.provider === value.provider);
		if (idx === -1) {
			users.push(value);
		} else {
			users.splice(idx, 1);
		}
		this.setState({ selectedUsers: users });
	}
	onRenderUserCell = (key: string, user: UserItem & { isChecked: boolean }) => {
		if (key === 'checkAction') {
			return (
				<Checkbox
					value={user}
					checked={user.isChecked}
					onChange={this.onUserCheckChange}
				/>
			);
		} else {
			const text = user[key];
			return (<span title={text}>{text}</span>);
		}
	}
	onSearchUser = (value) => {
		const users = [];
		this.props.users.map(u => {
			if (!value || u.name.indexOf(value) !== -1) {
				users.push(u);
			}
		});
		this.setState({ availableUsers: users });
	}
	onSelectUsersSave = () => {
		const availableUsers = [...this.props.users];
		const unrestrictedUsers = [...(this.state.settings.unrestrictedUsers || [])];
		this.state.selectedUsers.map(u => unrestrictedUsers.push(u));
		const settings = update(this.state.settings, { unrestrictedUsers: { $set: unrestrictedUsers } });
		if (this.state.selectedUsers.length > 0) {
			this.setState({ dirty: true });
		}
		this.setState({ settings, selectingUsers: false, availableUsers, selectedUsers: [] });
	}
	onSelectUsersCancel = () => {
		const availableUsers = [...this.props.users];
		this.setState({ selectingUsers: false, availableUsers, selectedUsers: [] });
	}

	renderUserList = () => {
		const { t } = this.props;
		const { availableUsers } = this.state;
		if (!availableUsers) return;

		const gridColumns = [
			{ key: 'checkAction', label: '', width: 40 },
			{ key: 'name', label: t('UserName'), width: 160 },
			{ key: 'provider', label: t('Provider'), width: 160 },
		];

		const rows = [];
		availableUsers.map(u => {
			const { settings: { unrestrictedUsers }, selectedUsers } = this.state;

			if (!unrestrictedUsers || unrestrictedUsers.findIndex(i => i.name === u.name && i.provider === u.provider) === -1) {
				const isChecked = selectedUsers.findIndex(su => su.name === u.name && su.provider === u.provider) > -1;
				rows.push({ ...u, isChecked });
			}
		});
		const gridProps: CGridProps = {
			rows,
			columns: gridColumns,
			rowHeight: 30,
			colClassName: 'dl-header',
			bodyClassName: 'dl-body',
			hideGridLine: true,
			onRenderCell: this.onRenderUserCell
		};

		return (
			<main className='users-container'>
				<div className='main-content'>
					<div className='header'>
						<Label text={t('AvailableUsers')} title={t('AvailableUsers')} />
						<SearchBox value={this.state.searchText || ''} onChange={this.onSearchUser} />
					</div>
					<main className='body'>
						<CGrid {...gridProps} />
					</main>
				</div>
				<div className='footer'>
					<Button
						style='accent'
						inline={true}
						text={t('Save')}
						title={t('Save')}
						icon='mdi mdi-content-save'
						onClick={this.onSelectUsersSave}
					/>
					<Button
						inline={true}
						text={t('Cancel')}
						title={t('Cancel')}
						icon='mdi mdi-cancel'
						onClick={this.onSelectUsersCancel}
					/>
				</div>
			</main>
		);
	}

	render() {
		const { busy } = this.props;
		const scrollbarProps = {
			style: { width: '100%', height: '100%' },
			renderThumbVertical: props => <div {...props} style={{ width: '4px' }} className='thumb-vertical' />,
		};

		return <Scrollbars {...scrollbarProps}>
			{this.state.selectingUsers ?
				this.renderUserList() :
				this.renderSettings()
			}
			{busy && <BlockLoader />}
		</Scrollbars>;
	}
}

export default translate('account', { withRef: true })(connect(state => state['account-management'].iss)(InactiveSessionSettings));
