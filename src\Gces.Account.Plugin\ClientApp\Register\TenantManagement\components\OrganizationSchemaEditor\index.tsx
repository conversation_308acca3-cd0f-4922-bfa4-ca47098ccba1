import * as React from 'react';
import { translate } from 'react-i18next';
import { connect } from 'react-redux';
import * as classnames from 'classnames';
import { Scrollbars } from 'gces-react-custom-scrollbars';
import { TenantProp, tenantActionCreators, OrganizationState } from '../../store';
import { OrganizationPropEditor } from './TenantPropEditor';
import { Button } from 'gces-ui';
import ConfirmDialog from '../../../../components/ConfirmDialog';
import * as util from '../../../../util';

interface ConnectedProps {
	show: boolean;
	tenantProps: TenantProp[];
	showTenantPropEditor: boolean;
	dispatch: any;
	t: any;
}

interface LocalState {
	editingPropId: string;
	deletingProp: TenantProp;
}

@translate('organization', { wait: true })
class OrganizationSchemaEditorInner extends React.PureComponent<ConnectedProps, LocalState> {
	state: LocalState = {
		editingPropId: '',
		deletingProp: null
	};

	onAddPropClick = () => {
		this.setState({ editingPropId: '' });
		this.props.dispatch(tenantActionCreators.setShowTenantPropEditor(true));
	}
	onCloseClick = () => {
		this.props.dispatch(tenantActionCreators.setShowSchemaEditor(false));
	}

	buildDeletePropConfirmDialog = () => {
		const { t, dispatch } = this.props;
		const dlgProps = {
			isOpen: true,
			parentSelector: () => document.querySelector(util.portalAppId),
			headerText: t('tntDeleteTenantProp'),
			contentText: t('tntDeleteTenantPropConfirmMessage', { tenantPropName: this.state.deletingProp.name }),
			yesText: t('Yes'),
			closeText: t('Close'),
			cancelText: t('tntCancel'),
			onYes: () => {
				this.setState({ deletingProp: null });
				dispatch(tenantActionCreators.deleteTenantProp(this.state.deletingProp.id));
			},
			onCancel: () => this.setState({ deletingProp: null }),
			onClose: () => this.setState({ deletingProp: null })
		};
		return <ConfirmDialog {...dlgProps} />;
	}

	render() {
		const { show, tenantProps, showTenantPropEditor, t } = this.props;
		if (!show) return null;

		const scrollbarsProps = {
			style: { width: '100%', height: '100%' },
			renderThumbVertical: props => <div {...props} style={{ width: '4px' }} className='ef-thumb-vertical' />,
			renderTrackHorizontal: props => <div {...props} style={{ display: 'none' }} />,
			renderThumbHorizontal: props => < div {...props} style={{ display: 'none' }} />,
			autoHide: false,
		};
		const toProp = (tenantProp: TenantProp, index: number) => {
			const onEditClick = () => {
				this.setState({ editingPropId: tenantProp.id });
				this.props.dispatch(tenantActionCreators.setShowTenantPropEditor(true));
			};
			const onDeleteClick = () => {
				this.setState({ deletingProp: tenantProp });
			};
			return (
				<div key={tenantProp.id} className={classnames('tnt-prop', { odd: index % 2 !== 0 })}>
					<span className='prop-name'>{tenantProp.name}</span>
					<Button rounded size='small' icon='mdi mdi-pencil' style='transparent' onClick={onEditClick} title={t('tntEdit')} />
					<Button rounded size='small' icon='mdi mdi-delete-forever' style='transparent' onClick={onDeleteClick} title={t('tntDelete')} />
				</div>
			);
		};

		const schemaEditorContent = (
			<React.Fragment>
				<div className='tse-header'>
					<span className='header-title' title={t('tntTenantSchema')}>{t('tntTenantSchema')}</span>
					<Button icon='mdi mdi-plus' maxWidth='130px' text={t('tntAddProp')} title={t('tntAddProp')} style='accent' size='small' onClick={this.onAddPropClick} />
				</div>
				<div className='tse-body'>
					<Scrollbars {...scrollbarsProps}>
						<div className='tnt-prop odd' title={t('tntName')}>{t('tntName')}</div>
						{tenantProps.map((t, i) => toProp(t, i))}
					</Scrollbars>
				</div>
				<div className='tse-footer'>
					<Button text={t('tntClose')} title={t('tntClose')} onClick={this.onCloseClick} style='accent' size='small' />
				</div>
			</React.Fragment>
		);

		return (
			<div className='tenant-schema-editor'>
				{showTenantPropEditor
					? <OrganizationPropEditor editingId={this.state.editingPropId} />
					: schemaEditorContent}
				{this.state.deletingProp && this.buildDeletePropConfirmDialog()}
			</div>
		);
	}
}

export const OrganizationSchemaEditor = connect(
	(state: { tenant: OrganizationState }) => ({
		show: state.tenant.showSchemaEditor,
		tenantProps: state.tenant.tenantProps,
		showTenantPropEditor: state.tenant.showTenantPropEditor,
	})
)(OrganizationSchemaEditorInner) as React.ComponentClass<{}>;