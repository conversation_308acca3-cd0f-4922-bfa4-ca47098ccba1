export const userEN: LanguageKeyValueMap = {
	// common
	cmNoMembersInOrg: 'No members in Organization',

	UserManagement: 'User Management',
	Yes: 'Yes',
	Edit: 'Edit',
	Delete: 'Delete',
	Save: 'Save',
	Cancel: 'Cancel',
	Close: 'Close',
	AddUser: 'Create User',
	SelectMembers: 'Select Members',
	EditUser: 'Edit User',
	SearchText: 'search text',
	DeleteUser: 'Delete User',
	RemoveUserFromOrg: 'Remove User from Organization',
	Username: 'Username',
	Email: 'Email',
	Mobile: 'Mobile Number',
	FirstName: 'First Name',
	LastName: 'Last Name',
	FullName: 'Full Name',
	Password: 'Password',
	ConfirmPassword: 'Confirm Password',
	PasswordNeverExpire: 'Password Never Expire',
	Roles: 'Roles',
	HomePage: 'Home Page',
	ManagedBy: 'Managed By',
	Provider: 'Provider',
	Organizations: 'Organizations',
	Status: 'Status',
	Enabled: 'Enabled',
	Disabled: 'Disabled',
	Enable: 'Enable',
	Disable: 'Disable',
	Locked: 'Locked',
	Actions: 'Actions',
	GetUsersError: 'Get users error',
	UsernameIsRequired: 'Username is required',
	UsernameAlreadyExists: 'Username already exists',
	EmailIsRequired: 'Email is required',
	EmailIsInvalid: 'Email is invalid',
	EmailAlreadyExists: 'Email already exists',
	MobileAlreadyExists: 'Mobile already exists',
	WeakPasswordRequirement: 'Password can not be null, and must be between 1 and 150 characters in length.',
	StrongPasswordRequirement: 'Password must be between 8 and 150 characters in length, contain at least 1 lowercase letter, 1 uppercase letter and 1 number.',
	PasswordNotMatch: 'Password and confirm password do not match.',
	PasswordIsBlank: 'Password cannot only contain whitespace characters.',
	DeleteUserConfirmMessage: 'Do you want to delete user "{{user}}" permanently?',
	RemoveUserConfirmMessage: 'Do you want to remove user "{{user}}" from organization "{{organization}}"?',
	OneRolePerLine: ' (One role per line)',
	OneValuePerLine: ' (One value per line)',
	Import: 'Import',
	Export: 'Export',
	Users: 'Users',
	Template: 'Template',
	NewUserName: 'new user name',
	NewUserEmail: 'new user email',
	SelectValue: 'select value',
	NullValue: '<empty>',
	ImportUsers: 'Import Users',
	DragDropFile: 'Drag & Drop File Here',
	Or: 'or',
	ClickHere: 'click here',
	SelectFile: 'to select file',
	ExportTemplate: 'Export Template',
	UnlockUser: 'Unlock User',
	UnlockUserConfirm: 'Do you want to unlock user "{{user}}" ?',
	UserTemplate: 'user_template',
	ErrorRemoveAdminFromAdministratorRole: "Can not remove user '{{userName}}' from role '{{roleName}}'.",
	ImportUsersSuccessMsg: 'Successfully imported {{count}} user(s).',
	ImportUsersFailMsg: '{{count}} user(s) failed to import, <a href="{{url}}" target="_blank">click here for details</a>.',
	ImportUsersFailed: 'Import users failed.',
	UsernameLengthExceedLimit: 'Username cannot be empty, and must be between 1 and 150 characters in length.',
	ShowPropertyValue: 'Show value',
	HidePropertyValue: 'Hide value',

	// homePageSelector
	loadingDocuments: 'Loading Documents...',
	noSearchResultTip: 'no result',
	searchPlaceHolder: 'Enter search text here...',

	// select members
	smSelectMembers: 'Select Members',
	smEmptyResult: 'empty result',
	smMembers: '({{count}} members)',
	smAddUser: 'Add',

	UserDetail: 'User Details',
	ShowSubOrg: 'Show the members of Sub-organization',
	NotShowSubOrg: 'Not show the members of Sub-organization',
	RemoveUser: 'Remove user from the current organization',

	udBasicInfo: 'Basic Information',
	udEmpty: '[empty]',
	NoOrganizations: '[No Organization]',
	NoRoles: '[No Role]',
	onlyNoMemberOfSubOrg: 'remove user from organization can only be used when not show the members of sub-organization',
	ecNoMemberTip: 'No Users in the organization. Please click',

	Error: 'Error',
	umError_1024: 'Mobile Number already exists',
	umError_1028: 'User "admin" can not be deleted.',
	umError_1039: 'The manager of system Administrators should always be Global.',
	umError_1040: 'no enough permission',
	umError_5010: 'You cannot remove yourself from the current organization.',

	rt_user: 'user',
	rt_role: 'role',
	'rt_organization user': 'organization user',

	error_V2_007_001_0002: 'The user name is required.',
	error_V2_007_001_0003: 'The user email is required.',
	error_V2_007_001_0004: 'The password is required.',
	error_V2_007_001_0005: 'The user name "{{UserName}}" already exists.',
	error_V2_007_001_0006: 'The email "{{Email}}" already exists.',
	error_V2_007_001_0015: 'Invalid email address "{{Email}}".',
	error_V2_007_001_0028: 'The manager of system Administrators should always be Global.',
	error_V2_007_001_0030: 'The mobile "{{Mobile}}" already exists.',
	error_V2_007_004_0003: 'The value "{{Value}}" of Custom Property "{{CustomProperty}}" is not legal.',
};