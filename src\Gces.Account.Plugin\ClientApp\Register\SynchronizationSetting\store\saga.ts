import moment from 'moment';
import { put, select, takeEvery } from 'redux-saga/effects';
import { convertEntityTypeToDatasetType, findEntityName, generateCronExpression, generateScheduleInfo, sendRequestV2 } from '../utils';
import { heartbeat } from '../utils/heartbeat';
import { SagaActionTypes, SaveAction, UpdateTaskStatusAction, syncSettingActionCreator } from './action';
import { DatasetSynchronizationType, DatasetType, HistoryTaskState, NotificationInfo, NotificationProvider, SyncDataset, SyncError, SyncHistory, SyncInfo, SyncRecord, SynchronizationSettingsState, SynchronizationView, TaskStatus, TimeoutUnit, UserSyncProcess } from './interface';
import { defaultSynchronizationScheduling } from './reducer';
import { FailedInformProvider, HistoryViewModel, SecretViewModel, SyncDatasetViewModel, TaskStatusViewModel, UserSyncProcessViewModel, UserSyncTaskRequestViewModel, UserSyncTaskResponseViewModel, UserSyncUpdateResponseModel } from './viewModel';

const synchronizationUrlPre = '/api/v2/admin/account/sync';
const t = (key: string, params?: any): string => window.AdminPortal.i18n.t([`synchronization:${key}`, `account:${key}`, `portal:${key}`], params);

function* init() {
	// 1. get channels
	const { result } = yield sendRequestV2('/api/notification/channels');
	const channels: string[] = result?.data?.channels || [];
	const providers: Record<string, NotificationProvider> = {};
	channels.forEach(c => {
		if (c !== 'API') {
			providers[c] = { selected: false, disabled: false, notifications: [] };
		}
	});

	// 2. get settings
	const { result: settingsResult } = yield sendRequestV2(`${synchronizationUrlPre}/settings`);
	if (settingsResult) {
		const { dataset, enableAutoSync, triggerInfo, timeout, failInformProviders, enabled: syncEnabled } = settingsResult as UserSyncTaskResponseViewModel;
		let syncDatasets: Record<DatasetSynchronizationType, SyncDataset> = {
			'user': { selected: false },
			'organization': { selected: false },
			'role': { selected: false },
			'userRoleRelation': { selected: false },
			'userOrganization': { selected: false },
		};
		if (dataset) {
			syncDatasets = {
				'user': getDataset(dataset.user),
				'organization': getDataset(dataset.organization),
				'role': getDataset(dataset.role),
				'userRoleRelation': getDataset(dataset.userRole),
				'userOrganization': getDataset(dataset.userOrganization),
			};
		}

		let synchronizationScheduling = defaultSynchronizationScheduling;
		if (triggerInfo) {
			const { endDate, timeZoneId, cronExpression } = triggerInfo;
			const startDate = triggerInfo.startDate ?? new Date();
			const timeoutUnit = timeout >= 60 && timeout % 60 === 0 ? TimeoutUnit.Hours : TimeoutUnit.Minutes;
			const patchedTimeout = timeoutUnit === TimeoutUnit.Hours ? timeout / 60 : timeout;
			synchronizationScheduling = {
				enabled: enableAutoSync,
				scheduleInfo: generateScheduleInfo(cronExpression, startDate, endDate, timeZoneId),
				timeout: { value: patchedTimeout, unit: timeoutUnit },
			};
		}

		if (failInformProviders) {
			failInformProviders.forEach((p => {
				const notifications = p.channel === 'Email' ? p.contacts.map(c => c.address).join(';') : p.contacts.map(c => ({ id: c.address, label: c.displayName }));
				if (providers[p.channel]) {
					providers[p.channel].selected = true;
					providers[p.channel].notifications = notifications;
				} else {
					providers[p.channel] = { selected: true, disabled: true, notifications };
				}
			}));
		}

		const syncInfo: SyncInfo = {
			synchronizationEnable: syncEnabled,
			syncDatasets,
			synchronizationScheduling,
			failureNotifications: providers,
		};
		yield put(syncSettingActionCreator.setSyncInfo(syncInfo));

		// 3. get secret
		const { result: secretResult } = yield sendRequestV2('/api/v2/admin/app-secret');
		const { enabled: secretEnable, value } = secretResult as SecretViewModel;
		yield put(syncSettingActionCreator.setSecret(secretEnable, value));

		// 4. get history
		yield* getHistory();

		// 5.Enable polling and call /api/v2/admin/account/sync/status to obtain the latest synchronization status
		if (syncEnabled && enableAutoSync) {
			heartbeat.setIsScheduled(true);
		}
	} else {
		yield put(syncSettingActionCreator.setFailureNotifications(providers));
	}
}

const getDataset = (dataset: SyncDatasetViewModel): SyncDataset => {
	const { id, name } = dataset;
	return { selected: !!id, dataset: { id, name } };
};

function* getHistory() {
	yield put(syncSettingActionCreator.setGettingHistory(true));
	const { result: historyResult } = yield sendRequestV2(`${synchronizationUrlPre}/history`);
	if (historyResult) {
		const { cancelled, runTime, nextRunTime, syncResult, errors, omit } = historyResult as HistoryViewModel;
		if (syncResult) {
			const { success, detail: { user: userCount, org: orgCount, role: roleCount, userRoleRelation: userRoleRelationCount, userOrgRelation: userOrgRelationCount, failureItems }, error } = syncResult;
			const errorMessages: string[] = errors.length > 0 ? errors.map(e => {
				if (!e.errorKey) return e.message;
				if (e.errorKey === '0041') {
					const errorType = convertEntityTypeToDatasetType(e.context.entityType as any);
					if (errorType === 'organization') {
						return t('error_0041_org', { ...e.context, type: t(errorType) });
					} else {
						return t('error_0041_org_role', { ...e.context, type: t(errorType) });
					}
				}
				else if (e.errorKey === '1004') {
					const errMessages: string[] = [];
					const generateMessage = (context, type: string, tKey: string) => {
						if (context[type]) {
							const datasetName = t(`${`synchronizationDatasets!${tKey}`}`);
							return `${context[type]}(${datasetName})`;
						}
					};
					errMessages.push(generateMessage(e.context, 'User', 'user'));
					errMessages.push(generateMessage(e.context, 'Organization', 'organization'));
					errMessages.push(generateMessage(e.context, 'Role', 'role'));
					errMessages.push(generateMessage(e.context, 'UserRole', 'userRoleRelation'));
					errMessages.push(generateMessage(e.context, 'UserOrg', 'userOrganization'));
					const msg = errMessages.filter(m => m).join(', ') || t('fallback_unknown_error');
					return t(`error_${e.errorKey}`, { errorMessage: msg });
				}
				else if (e.errorKey === '1006') {
					const errMessages: string[] = [];
					const generateMessage = (context, type: string, tKey: string) => {
						if (context[type]) {
							const datasetName = t(`${`synchronizationDatasets!${tKey}`}`);
							return datasetName;
						}
					};
					errMessages.push(generateMessage(e.context, 'User', 'user'));
					errMessages.push(generateMessage(e.context, 'Organization', 'organization'));
					errMessages.push(generateMessage(e.context, 'Role', 'role'));
					errMessages.push(generateMessage(e.context, 'UserRole', 'userRoleRelation'));
					errMessages.push(generateMessage(e.context, 'UserOrg', 'userOrganization'));
					const msg = errMessages.filter(m => m).join(', ') || t('fallback_unknown_error');
					return t(`error_${e.errorKey}`, { types: msg });
				}
				else {
					return t(`error_${e.errorKey}`, e.context);
				}
			}) : [];
			error && errorMessages.push(t(`error_${error.errorCode}`, error.context));
			const errorDetails: SyncError[] = failureItems.map(item => {
				const syncError = {
					type: convertEntityTypeToDatasetType(item.entity.type),
					name: findEntityName(item.entity),
					reason: '',
					level: item.error?.level,
				};
				const type = convertEntityTypeToDatasetType(item.entity.type);
				if (item.error?.errorCode === '0011' && (type === 'userOrgRelation' || type === 'userRoleRelation' || type === 'role')) {
					const errorType = t(convertEntityTypeToDatasetType(item.error.entityType));
					syncError.reason = t('error_0011!extension', { ...item.error?.context, type: errorType });
				} else {
					syncError.reason = t(`error_${item.error?.errorCode}`, item.error?.context);
				}
				return syncError;
			});
			const records: Record<DatasetType, SyncRecord> = {
				user: { ...userCount },
				organization: { ...orgCount },
				role: { ...roleCount },
				userRoleRelation: { ...userRoleRelationCount },
				userOrganization: { ...userOrgRelationCount },
			};

			let state: HistoryTaskState;
			if (cancelled) {
				state = HistoryTaskState.Canceled;
			} else if (!success) {
				state = HistoryTaskState.Failed;
			} else if (failureItems.length > 0) {
				state = HistoryTaskState.NotAllSuccess;
			} else {
				state = HistoryTaskState.Success;
			}

			const history: SyncHistory = {
				info: {
					state,
					runTime: moment.utc(runTime).local(),
				},
				records,
				errorDetails,
				errors: errorMessages,
				omit,
			};

			const view: SynchronizationView = {
				taskStatus: {
					process: UserSyncProcess.None,
					isScheduled: !!nextRunTime,
					nextRunTime: nextRunTime ? moment.utc(nextRunTime).local() : null,
				},
				history,
				gettingHistory: false
			};
			yield put(syncSettingActionCreator.setView(view));
		}
	}
	yield put(syncSettingActionCreator.setGettingHistory(false));
}

function* sync() {
	const { error } = yield sendRequestV2(`${synchronizationUrlPre}/trigger`, 'POST');
	if (!error) {
		heartbeat.setIsRunning(true);
		window.AdminPortal.Notifications.Send(2, t('ManualSyncSuccessful'), t('ManualSyncSuccessful'));
	}
}

const generateFailedInformProvider = (failureNotifications: Record<string, NotificationProvider>): FailedInformProvider[] => {
	return Object.keys(failureNotifications).filter(type => failureNotifications[type].selected && failureNotifications[type].notifications.length > 0).map(type => {
		if (type.toLowerCase() === 'email') {
			const notifications = failureNotifications[type].notifications as string;
			return {
				channel: type,
				contacts: notifications.split(';').map((n: string) => ({ address: n, displayName: n, addressType: 'User' })),
			};
		} else {
			const notifications = failureNotifications[type].notifications as NotificationInfo[];
			return {
				channel: type,
				contacts: notifications.map(n => ({ address: n.id, displayName: n.label, addressType: 'User' })),
			};
		}
	});
};

function* save(action: SaveAction) {
	const { payload: { syncInfo, secretInfo } } = action;
	const {
		syncDatasets: { user, organization, role, userRoleRelation, userOrganization },
		synchronizationScheduling: { enabled: enableAutoSync, scheduleInfo, timeout },
		synchronizationEnable,
		failureNotifications,
	} = syncInfo;
	const syncViewModel: UserSyncTaskRequestViewModel = {
		dataset: {
			user: user.selected ? user.dataset.id : '',
			organization: organization.selected ? organization.dataset.id : '',
			role: role.selected ? role.dataset.id : '',
			userRole: userRoleRelation.selected ? userRoleRelation.dataset.id : '',
			userOrganization: userOrganization.selected ? userOrganization.dataset.id : '',
		},
		triggerInfo: {
			startDate: scheduleInfo?.startDate?.toDate(),
			endDate: scheduleInfo?.endDate?.toDate(),
			timeZoneId: scheduleInfo?.timeZoneId,
			cronExpression: generateCronExpression(scheduleInfo),
		},
		enableAutoSync,
		failInformProviders: generateFailedInformProvider(failureNotifications),
		timeout: timeout.value * (timeout.unit === TimeoutUnit.Hours ? 60 : 1),
		enabled: synchronizationEnable
	};
	const { result } = yield sendRequestV2(`${synchronizationUrlPre}/settings`, 'PUT', syncViewModel);
	const saveResult = result as UserSyncUpdateResponseModel;
	if (saveResult) {
		yield put(syncSettingActionCreator.setSyncInfo(syncInfo));
		const { scheduleFailed, errorCode, errorMessage } = saveResult;
		if (!!errorCode || !!errorMessage) {
			window.AdminPortal.Notifications.Send(1, t((synchronizationEnable && enableAutoSync && scheduleFailed) ? 'scheduleFailedMessage' : 'Warning'), t(`error_${errorCode}`) || errorMessage);
		}
		if (scheduleFailed) {
			heartbeat.setIsScheduled(false);
			yield put(syncSettingActionCreator.setNextRuntime(null));
		} else {
			heartbeat.setIsScheduled(true);
		}
	}
	let isSecretSuccessful = true;
	if (secretInfo) {
		const secretViewModel: SecretViewModel = {
			enabled: secretInfo.applicationSecretEnable,
			value: secretInfo.applicationSecret
		};
		const { error: secretError } = yield sendRequestV2('/api/v2/admin/app-secret', 'PUT', secretViewModel);
		if (!secretError) {
			yield put(syncSettingActionCreator.setSecret(secretViewModel.enabled, secretViewModel.value));
		} else {
			isSecretSuccessful = false;
		}
	}
	if (result && isSecretSuccessful) {
		window.AdminPortal.Notifications.Send(2, t('SaveSuccessful'), t('SaveSuccessful'));
	}
}

const convertStateViewModelToModel = (state: TaskStatusViewModel): TaskStatus => {
	let process: UserSyncProcess;
	switch (state.process) {
		case UserSyncProcessViewModel.None:
			process = UserSyncProcess.None;
			break;
		case UserSyncProcessViewModel.Validating:
			process = UserSyncProcess.Validating;
			break;
		case UserSyncProcessViewModel.FetchingData:
			process = UserSyncProcess.FetchingData;
			break;
		case UserSyncProcessViewModel.Synchronizing:
			process = UserSyncProcess.Synchronizing;
			break;
		case UserSyncProcessViewModel.Completed:
			process = UserSyncProcess.Completed;
			break;
	}
	return {
		process,
		isScheduled: state.isScheduled,
		nextRunTime: state.nextRunTime ? moment.utc(state.nextRunTime).local() : null,
	};
};

const updateSystemStatistics = () => window.AdminPortal && window.AdminPortal.UpdateSystemStatistics && window.AdminPortal.UpdateSystemStatistics();

function* updateTaskState(action: UpdateTaskStatusAction) {
	const state: SynchronizationSettingsState = (yield select()).synchronization;
	const { manualExecutionId } = state;
	const { payload: { status } } = action;
	if (!status) {
		console.error('status is null');
		return;
	}

	// Must update process, isScheduled, nextRunTime
	// If manual execution completes, clear the executionId and refetch history
	// If nextRunTime doesn't match, refetch history
	// If neither scheduling nor manual execution is on, stop the heartbeat

	const { process, isScheduled, nextRunTime } = convertStateViewModelToModel(status);
	yield put(syncSettingActionCreator.setTaskStatus({ process, isScheduled, nextRunTime }));
	heartbeat.setIsScheduled(isScheduled);

	if (manualExecutionId !== status.executionId && process === UserSyncProcess.Completed) {
		yield heartbeat.setIsRunning(false);
		yield put(syncSettingActionCreator.setExecutionId(status.executionId));
		yield* getHistory();
		updateSystemStatistics();
	}
}

export function* watchSynchronization() {
	yield takeEvery(SagaActionTypes.init, init);
	yield takeEvery(SagaActionTypes.sync, sync);
	yield takeEvery(SagaActionTypes.save, save);
	yield takeEvery(SagaActionTypes.updateTaskState, updateTaskState);
}