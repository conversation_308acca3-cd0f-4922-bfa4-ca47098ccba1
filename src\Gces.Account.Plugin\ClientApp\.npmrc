registry=https://pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/         
always-auth=true
; begin auth token
//pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/:username=dt4dev
//pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/:_password="${BASE64_ENCODED_PERSONAL_ACCESS_TOKEN}"
//pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/registry/:email=<EMAIL>
//pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/:username=dt4dev
//pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/:_password="${BASE64_ENCODED_PERSONAL_ACCESS_TOKEN}"
//pkgs.dev.azure.com/dt4dev/Wyn/_packaging/wyn-dev/npm/:email=<EMAIL>
; end auth token