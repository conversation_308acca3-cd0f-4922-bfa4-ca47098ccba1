import * as React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';
import { UserState, userActionCreators, UserInfo, Organization } from '../store';
import { MemberEditorUserInfo, MembersEditorCommon } from '../../Common/MembersEditorCommon';
import { GlobalOrganization } from '../../../util';

interface ConnectedProps {
	user: User;
	allUsers: UserInfo[];
	selectedOrganizationId: string;
	isSelectingMembers: boolean;
	organizations?: Organization[];
	dispatch: any;
	t: any;
}

interface LocalState {
	users: MemberEditorUserInfo[];
}

@translate('user', { wait: true })
class MembersEditorInner extends React.PureComponent<ConnectedProps, LocalState> {
	state: LocalState = {
		users: [],
	};

	componentWillMount() {
		this.initUserIds(this.props);
	}

	componentWillReceiveProps(nextProps: ConnectedProps) {
		if (nextProps.allUsers !== this.state.users
			|| nextProps.selectedOrganizationId !== this.props.selectedOrganizationId
			|| nextProps.isSelectingMembers !== this.props.isSelectingMembers) {
			this.initUserIds(nextProps);
		}
	}

	initUserIds = (props: ConnectedProps) => {
		const { allUsers, selectedOrganizationId, organizations, isSelectingMembers } = props;
		if (!isSelectingMembers) return;
		if (!allUsers.length || !selectedOrganizationId || selectedOrganizationId === GlobalOrganization.Id) {
			this.setState({ users: allUsers });
			return;
		}
		const selectedOrgPath = organizations.find(o => o.id === selectedOrganizationId).path;
		const users = allUsers.map(u => ({ ...u, ...{ isInRight: u.organizations.findIndex(o => o === selectedOrgPath) > -1 } }));
		this.setState({ users });
	}

	closeMemberEditor = () => {
		this.props.dispatch(userActionCreators.setIsSelectingMembers(false));
	}

	saveMemberEditor = (rightUserIds: string[]) => {
		const { selectedOrganizationId, dispatch } = this.props;
		dispatch(userActionCreators.updateOrganizationUsers(selectedOrganizationId, rightUserIds));
	}

	render() {
		const { isSelectingMembers } = this.props;
		const { users } = this.state;
		if (!isSelectingMembers) return null;
		const editorCommonProps = {
			allUsers: users,
			onSaveClick: this.saveMemberEditor,
			onCancelClick: this.closeMemberEditor,
		};

		return (<MembersEditorCommon {...editorCommonProps} />);
	}
}

export const MembersEditor = connect(
	(state: { user: UserState, navApp: { user: User } }) => ({
		user: state.navApp.user,
		selectedOrganizationId: state.user.selectedOrganizationId,
		allUsers: state.user.allUsers,
		isSelectingMembers: state.user.isSelectingMembers,
		organizations: state.user.organizations,
	})
)(MembersEditorInner) as React.ComponentClass<{}>;