﻿import ActionTypes from './../actions/action-types'

const SecurityProviderReducer = (state = {}, action) => {
    switch (action.type) {
        case ActionTypes.SecurityProviders:
            return Object.assign({}, state, { securityProviders: action.securityProviders ? action.securityProviders.map(p => ({ ...p })) : null })

        default:
            return state
    }
}

export default SecurityProviderReducer
