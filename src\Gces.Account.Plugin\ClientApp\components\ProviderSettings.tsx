﻿import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, InputEditor, NumberEditor } from 'gces-ui';
import * as React from 'react';
import { translate } from 'react-i18next';
import { Provider, ProviderSetting } from '../interfaces';
import * as util from '../util';
import EmptyPage from './EmptyPage';

interface ConnectProps {
	provider: Provider;
	onSave?: (Provider: Provider) => void;
	hideHeader?: boolean;
	dispatch?: any;
	t?: any;
}

interface LocalState {
	newProvider: Provider;
	hasValueChanged: boolean;
	showPassword: { [key: string]: boolean };
	syncingData: boolean;
	testSP: {
		testing: boolean;
		username: string;
		password: string;
		customParam: string;
		result: SecurityProviderTestingResult;
		details: boolean;
	};
}

interface Exception {
	message: string;
	source: string;
	stackTrace: string;
}
type UserContext = { [key: string]: string[] };
interface SecurityProviderTestingResult {
	success: boolean;
	errorCode: string;
	errorMessage: string;
	exception: Exception;
	providerName: string;
	userId: string;
	userName: string;
	userContext: UserContext;
	roles: string[];
	organizations: string[];
}

class ProviderSettings extends React.Component<ConnectProps, LocalState> {
	constructor(props, context) {
		super(props, context);
		this._testPasswordKey = 'elknsfuisheg89pHSEPuf';
		this.state = {
			newProvider: null,
			hasValueChanged: false,
			showPassword: { [this._testPasswordKey]: false },
			syncingData: false,
			testSP: {
				testing: false,
				username: '',
				password: '',
				customParam: '',
				result: null,
				details: false
			}
		};
	}

	_testPasswordKey: string;

	componentDidMount = () => {
		this.updateStateIfNeed(this.props);
	}
	componentWillReceiveProps = (nextProps: ConnectProps) => {
		this.updateStateIfNeed(nextProps);
	}
	updateStateIfNeed = (props: ConnectProps) => {
		const needUpdateState = !props.provider || !this.state.newProvider || this.state.newProvider.providerName !== props.provider.providerName;

		if (needUpdateState) this.setState({
			newProvider: !props.provider ? null : this.cloneProvider(props.provider),
			hasValueChanged: false,
			showPassword: { [this._testPasswordKey]: false },
			syncingData: false,
			testSP: {
				testing: false,
				username: '',
				password: '',
				customParam: '',
				result: null,
				details: false
			}
		});
	}

	handleSave = () => {
		const { onSave } = this.props;
		if (onSave) {
			onSave(this.state.newProvider);
			this.setState({ hasValueChanged: false });
		}
	}
	handleCancel = () => {
		this.setState({ newProvider: this.cloneProvider(this.props.provider), hasValueChanged: false });
	}

	showPasswordStateChanged = (key: string) => {
		const showPassword = { ...this.state.showPassword };
		const show = this.state.showPassword[key];
		showPassword[key] = !show;
		this.setState({ showPassword });
	}

	syncData = () => {
		const { t } = this.props;
		this.setState({ syncingData: true });
		const url = `/api/v2/identity/external-providers/${this.state.newProvider.providerName}/sync`;
		const callback = () => {
			window.AdminPortal.Notifications.Send(2, t('SyncDataSuccessTitle'), t('SyncDataSuccess'), 5000);
			this.setState({ syncingData: false });
			window.AdminPortal && window.AdminPortal.UpdateSystemStatistics && window.AdminPortal.UpdateSystemStatistics();
		};
		util.ajax(url, callback, 'POST', this.state.newProvider, () => this.setState({ syncingData: false }));
	}

	buildExplainText = () => {
		const { t } = this.props;
		return (<div className='explain-text'>
			<span title={t('EnableExternalLoginProviderExplainText')}>{t('EnableExternalLoginProviderExplainText')}</span>
			<span title={t('DataSyncingExplainText')}>{t('DataSyncingExplainText')}</span>
			<span title={t('DataSyncingFailsExplainText')}>{t('DataSyncingFailsExplainText')}</span>
		</div>);
	}

	buildSettingItemLabel = (providerName: string, item: ProviderSetting) => {
		const { t } = this.props;
		return <div className='provider-setting-item-label' title={item.description}>
			<label title={t(`setting_item_desc!${providerName.toLowerCase()}!${item.name.toLowerCase()}`, item.description || item.displayName || item.name)}>
				{t(`setting_item_name!${providerName.toLowerCase()}!${item.name.toLowerCase()}`, item.displayName || item.name)}
				{item.restriction === util.SettingItemRestriction.Mandatory && <i className='provider-setting-item-required mdi mdi-star' />}
			</label>
		</div>;
	}
	buildSettingItemValue = (item: ProviderSetting) => {
		return <div className='provider-setting-item-value'> {this.buildSettingItemValueInput(item)} </div>;
	}
	buildSettingItemValueInput = (item: ProviderSetting) => {
		switch (item.valueType) {
			case util.SettingItemValueType.Boolean: return this.buildBooleanValueItem(item);
			case util.SettingItemValueType.Number: return this.buildNumberValueItem(item);
			case util.SettingItemValueType.Password: return this.buildPasswordValueItem(item);
			default: return this.buildTextValueItem(item);
		}
	}
	buildTextValueItem = (item: ProviderSetting) => {
		return <InputEditor value={item.value} onEveryChange={(value) => this.onValueChange(item, value)} />;
	}
	buildPasswordValueItem = (item: ProviderSetting) => {
		const { t } = this.props;
		return (
			<div className='inputPassword-group'>
				<InputEditor type={this.state.showPassword[item.name] ? 'text' : 'password'} title=' ' value={item.value} onEveryChange={(value) => this.onValueChange(item, value)} />
				<Button
					size='small'
					title={this.state.showPassword[item.name] ? t('HidePassword') : t('ShowPassword')}
					onClick={() => this.showPasswordStateChanged(item.name)}
					icon={this.state.showPassword[item.name] ? 'mdi mdi-eye-off' : 'mdi mdi-eye'}
				/>
			</div>
		);
	}
	buildNumberValueItem = (item: ProviderSetting) => {
		return <NumberEditor value={item.value} onEveryChange={(value) => this.onValueChange(item, value)} />;
	}
	buildBooleanValueItem = (item: ProviderSetting) => {
		return <BooleanEditor value={(null !== item.value && undefined !== item.value) ? item.value.toString() : 'true'} onChange={(value) => this.onValueChange(item, value)} />;
	}
	buildSettingItem = (providerName: string, item: ProviderSetting, index: number) => {
		return <div className='provider-setting-item' key={index}>
			{this.buildSettingItemLabel(providerName, item)}
			{this.buildSettingItemValue(item)}
		</div>;
	}
	buildSettings = () => {
		const displaySetting = this.state.newProvider.settings.filter(item => item.name !== 'EnableSendingMessage');
		return (
			<div className='provider-settings'>
				<div className='title-lg'>{this.props.t('Settings')}</div>
				{displaySetting.map((s, i) => this.buildSettingItem(this.state.newProvider.providerName, s, i))}
			</div>
		);
	}

	buildFooterContent = () => {
		const { t } = this.props;
		return <div className='btn-group'>
			<Button inline style='accent' title={t('Save')} text={t('Save')} onClick={this.handleSave} disabled={!this.state.hasValueChanged} />
			<Button inline title={t('Cancel')} text={t('Cancel')} onClick={this.handleCancel} disabled={!this.state.hasValueChanged} />
		</div>;
	}
	cloneProvider = (provider: Provider) => {
		return { ...provider, settings: provider.settings.map(s => ({ ...s })) };
	}
	onValueChange = (item: ProviderSetting, value) => {
		item.value = value;
		this.setState({ hasValueChanged: true });
	}

	onTestSPUsernameChanged = (value) => {
		const testSP = { ...this.state.testSP };
		testSP.username = value;
		this.setState({ testSP });
	}
	onTestSPPasswordChanged = (value) => {
		const testSP = { ...this.state.testSP };
		testSP.password = value;
		this.setState({ testSP });
	}
	onTestSPCustomParamChanged = (value) => {
		const testSP = { ...this.state.testSP };
		testSP.customParam = value;
		this.setState({ testSP });
	}
	startTestingSP = () => {
		const testSP = { ...this.state.testSP };
		testSP.testing = true;
		testSP.result = null;
		testSP.details = false;
		this.setState({ testSP });
	}
	stopTestingSP = (result: SecurityProviderTestingResult) => {
		const testSP = { ...this.state.testSP };
		testSP.testing = false;
		testSP.result = result;
		this.setState({ testSP });
	}

	onSecurityProviderTesting = () => {
		this.startTestingSP();
		const customParam = this.state.testSP.customParam ?
			this.state.testSP.customParam.split('\n') :
			null;
		const url = '/api/v2/identity/security-providers/testing';
		const data = {
			providerName: this.state.newProvider.providerName,
			username: this.state.testSP.username,
			password: this.state.testSP.password,
			settings: [...this.state.newProvider.settings],
			customParameters: customParam
		};
		util.ajax(url, (rst) => this.stopTestingSP(rst), 'POST', data, () => this.stopTestingSP(null));
	}

	toggleDetailResult = () => {
		const testSP = { ...this.state.testSP };
		testSP.details = !testSP.details;
		this.setState({ testSP });
	}

	buildSecurityProviderTestResultArea = () => {
		const { t } = this.props;
		const testSP = { ...this.state.testSP };
		const toggleDetailResult = this.toggleDetailResult;

		function buildUserId(userId: string) {
			return (
				<div key='user-id' className='test-result-item'>
					<label className='test-result-item-key' title={t('UserId')}>{t('UserId')}</label>
					<InputEditor className='test-result-item-value' readOnly={true} value={userId} />
				</div>
			);
		}
		function buildUserName(userName: string) {
			return (
				<div key='user-name' className='test-result-item'>
					<label className='test-result-item-key' title={t('UserName')}>{t('UserName')}</label>
					<InputEditor className='test-result-item-value' readOnly={true} value={userName} />
				</div>
			);
		}
		function buildUserContext(context: UserContext) {
			const pairs = [];
			let contextStr = '';
			if (context) {
				for (const key in context) {
					pairs.push(`${key} = ${context[key] ? context[key].join(';') : ''}`);
				}
				contextStr = pairs.join('\n');
			}
			return (
				<div key='user-context' className='test-result-item'>
					<label className='test-result-item-key' title={t('UserContext')}>{t('UserContext')}</label>
					<InputEditor className='test-result-item-value' readOnly={true} multiline={pairs.length > 1} value={contextStr} />
				</div>
			);
		}
		function buildUserRoles(roles: string[]) {
			const rolesStr = roles ? roles.join('\n') : '';
			return (
				<div key='roles' className='test-result-item'>
					<label className='test-result-item-key' title={t('Roles')}>{t('Roles')}</label>
					<InputEditor className='test-result-item-value' readOnly={true} multiline={roles && roles.length > 1} value={rolesStr} />
				</div>
			);
		}
		function buildUserOrganizations(orgs: string[]) {
			const orgsStr = orgs ? orgs.join('\n') : '';
			return (
				<div key='organizations' className='test-result-item'>
					<label className='test-result-item-key' title={t('Organizations')}>{t('Organizations')}</label>
					<InputEditor className='test-result-item-value' readOnly={true} multiline={orgs && orgs.length > 1} value={orgsStr} />
				</div>
			);
		}
		function buildException(exception: Exception) {
			const exceptionStr = `${exception.message}\n${exception.stackTrace}`;
			return (
				<div key='exception' className='test-result-item'>
					<label className='test-result-item-key' title={t('Exception')}>{t('Exception')}</label>
					<InputEditor className='test-result-item-value' readOnly={true} multiline={true} value={exceptionStr} />
				</div>
			);
		}
		function buildErrorMessage(errCode: string, errMsg: string) {
			return (
				<div key='error-message' className='test-result-item'>
					<label className='test-result-item-key' title={t('ErrorMessage')}>{t('ErrorMessage')}</label>
					<InputEditor className='test-result-item-value' readOnly={true} value={t(`error_${errCode}`, errMsg)} />
				</div>
			);
		}
		function buildSummaryResult(result: SecurityProviderTestingResult) {
			const className = !result ? '' : (result.success ? 'mdi mdi-check-circle success-result' : 'mdi mdi-alert-circle fail-result');
			const summaryText = !result ? t('SPTestDefaultResult') : (result.success ? t('SPTestSuccessResult') : t('SPTestFailResult'));
			return (
				<div key='result-summary' className='test-result-summary'>
					<span className={className}>{summaryText}</span>
					{result && <Button style='link' size='small' rounded={true} onClick={toggleDetailResult} text={testSP.details ? t('SPHideDetail') : t('SPShowDetail')} />}
				</div>
			);
		}
		function buildSuccessResult(result: SecurityProviderTestingResult) {
			const successResult = [buildSummaryResult(result)];
			if (testSP.details) {
				successResult.push(buildUserId(result.userId));
				successResult.push(buildUserName(result.userName));
				successResult.push(buildUserContext(result.userContext));
				successResult.push(buildUserRoles(result.roles));
				successResult.push(buildUserOrganizations(result.organizations));
			}
			return successResult;
		}
		function buildFailResult(result: SecurityProviderTestingResult) {
			const failResult = [buildSummaryResult(result)];
			if (testSP.details) {
				failResult.push(buildErrorMessage(result.errorCode, result.errorMessage));
				if (result.exception) {
					failResult.push(buildException(result.exception));
				}
			}
			return failResult;
		}

		return testSP.result ?
			(testSP.result.success ? buildSuccessResult(testSP.result) : buildFailResult(testSP.result)) :
			(buildSummaryResult(testSP.result));
	}

	buildSecurityProviderTestArea = () => {
		const { t } = this.props;
		return (<div className='security-provider-test-area'>
			<div className='title-lg' title={t('LoginTest')}>{t('LoginTest')}</div>
			<div className='test-config-area'>
				<div key='user-name' className='config-item'>
					<label title={t('Username')}>{t('Username')}</label>
					<InputEditor type='text' value={this.state.testSP.username || ''} onEveryChange={this.onTestSPUsernameChanged} />
				</div>
				<div key='password' className='config-item'>
					<label title={t('Password')}>{t('Password')}</label>
					<div className='test-password-group'>
						<InputEditor type={this.state.showPassword[this._testPasswordKey] ? 'text' : 'password'} value={this.state.testSP.password || ''} onEveryChange={this.onTestSPPasswordChanged} />
						<Button
							size='small'
							title={this.state.showPassword[this._testPasswordKey] ? t('HidePassword') : t('ShowPassword')}
							onClick={() => this.showPasswordStateChanged(this._testPasswordKey)}
							icon={this.state.showPassword[this._testPasswordKey] ? 'mdi mdi-eye-off' : 'mdi mdi-eye'}
						/>
					</div>
					<Button style='accent' className='btn-sp-test' text={this.state.testSP.testing ? t('Testing') : t('Test')} disabled={!this.state.testSP.password || !this.state.testSP.username || this.state.testSP.testing} onClick={this.onSecurityProviderTesting} />
				</div>
				<div key='custom-param' className='config-item multi-line'>
					<label title={t('CustomParam')}>{t('CustomParam')}</label>
					<InputEditor className='custom-param-text-area' type='text' placeholder={t('CustomParamDescribe')} multiline={true} value={this.state.testSP.customParam || ''} onChange={this.onTestSPCustomParamChanged} />
				</div>
			</div>
			<div className='test-result-area'>
				{this.buildSecurityProviderTestResultArea()}
			</div>
		</div>);
	}

	render() {
		const { t } = this.props;

		const emptyPageProps = {
			imageName: 'security-provider',
			tip: t('NoSecurityProviderTip'),
		};
		if (!this.state.newProvider || this.state.newProvider.providerName.toLowerCase() === 'local') return <EmptyPage {...emptyPageProps} />;

		const needRenderSettings = this.state.newProvider && this.state.newProvider.settings && this.state.newProvider.settings.length > 0;

		return <div className='provider-settings'>
			{!this.props.hideHeader && <div className='provider-settings-header' />}
			<div className='provider-settings-body'>
				{this.state.newProvider.type === 'ExternalLoginProvider' && this.buildExplainText()}
				{needRenderSettings && this.buildSettings()}
				{this.state.newProvider.type === 'SecurityProvider' && this.buildSecurityProviderTestArea()}
				{this.state.newProvider.type === 'ExternalLoginProvider' && < Button style='accent' className='btn-sync-data' text={this.state.syncingData ? t('SyncingData') : t('SyncData')} title={t('SyncDataDesc')} disabled={this.state.hasValueChanged || this.state.syncingData} onClick={this.syncData} />}
			</div>
			<div className='provider-settings-footer'>{this.buildFooterContent()}</div>
		</div>;
	}
}

export default translate('account', { withRef: true })(ProviderSettings);
