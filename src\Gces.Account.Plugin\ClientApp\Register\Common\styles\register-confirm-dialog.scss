@at-root [class*='theme-'].light-mode {
	.confirm-dialog {
		.dialog-footer {
			--gces-transformed-color: var(--gces-panels-bg-dk-5);
			--gces-btn-bg: var(--gces-panels-bg-dk-10);
			--gces-btn-hover-bg: var(--gces-panels-bg-dk-10);
			--gces-btn-active-bg: var(--gces-panels-bg-dk-15);
		}
	}
}

@at-root [class*='theme-'].dark-mode {
	.confirm-dialog {
		.dialog-footer {
			--gces-transformed-color: var(--gces-panels-bg-lt-5);
			--gces-btn-bg: var(--gces-panels-bg-lt-10);
			--gces-btn-hover-bg: var(--gces-panels-bg-lt-10);
			--gces-btn-active-bg: var(--gces-panels-bg-lt-15);
		}
	}
}

.confirm-dialog {
	.dialog-footer {
		display: flex;
		justify-content: flex-end;
		background: var(--gces-transformed-color) !important;
		height: 50px;
		padding: 10px 15px !important;

		span {
			font-size: 12px !important;
		}

		.ef-btn {
			margin-left: 15px !important;
			height: 30px;
			border-radius: 2px;
			min-width: 100px;
			background-color: var(--gces-btn-bg);

			&:not([disabled]):not(.disabled):hover {
				background-color: var(--gces-btn-hover-bg);
			}

			&:not([disabled]):not(.disabled):active {
				background-color: var(--gces-btn-active-bg);
			}
		}

		.ef-btn.ef-btn-accent {
			color: $text-contrast;
			background-color: $ef-accent;

			&:not([disabled]):not(.disabled):hover {
				background-color: var(--gces-accent1-dk-5);
			}

			&:not([disabled]):not(.disabled):active {
				background-color: var(--gces-accent1-dk-10);
			}
		}
	}
}