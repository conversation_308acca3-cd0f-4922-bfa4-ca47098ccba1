import * as React from 'react';
import { translate } from 'react-i18next';
import { connect } from 'react-redux';
import { OrganizationItem, tenantActionCreators, UsersDic, OrganizationState } from '../store';
import EmptyPage from '../../../components/EmptyPage';
import CGrid from 'gces-react-grid';

interface ConnectedProps {
	items: OrganizationItem[];
	selected: string;
	showMembersEditor: boolean;
	showTenantEditor: boolean;
	usersDic: UsersDic;
	dispatch: any;
	t: any;
}

@translate('organization', { wait: true })
class OrganizationMembersTabInner extends React.PureComponent<ConnectedProps> {
	componentWillMount() {
		this.props.dispatch(tenantActionCreators.getTenantUsers(this.props.selected));
	}
	componentWillReceiveProps(nextProps: ConnectedProps) {
		if (nextProps.selected !== this.props.selected) {
			this.props.dispatch(tenantActionCreators.getTenantUsers(nextProps.selected));
		}
	}

	render() {
		const { t, usersDic, selected } = this.props;
		const users = usersDic[selected] || [];

		if (users && users.length) {
			const gridProps = {
				rows: users,
				hideGridLine: true,
				columnResizing: false,
				columns: [
					{ key: 'username', label: t('tntUsername') },
					{ key: 'email', label: t('tntEmail') },
					{ key: 'provider', label: t('tntProvider') },
				]
			};
			return (<CGrid {...gridProps} />);
		}
		else {
			const emptyPageProps = {
				imageName: 'locked-user-management',
				tip: t('tntNoMemberTip'),
			};
			return (<EmptyPage {...emptyPageProps} />);
		}
	}
}

export const OrganizationMembersTab = connect(
	(state: { tenant: OrganizationState }) => ({
		items: state.tenant.items,
		selected: state.tenant.selected,
		showMembersEditor: state.tenant.showMembersEditor,
		showTenantEditor: state.tenant.showTenantEditor,
		usersDic: state.tenant.usersDic,
	})
)(OrganizationMembersTabInner) as React.ComponentClass<{}>;