import * as React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';
import { OrganizationTree } from './OrganizationTree';
import { RolesContainer } from './RolesContainer';
import { EditorsContainer } from './EditorsContainer';
import { RoleState, roleActionCreators } from '../store';
import { BlockLoader, MessageBox } from 'gces-ui';
import { Scrollbars } from 'gces-react-custom-scrollbars';

interface ConnectedProps {
	busy: boolean;
	messages: string[];
	dispatch: any;
	t: any;
}

@translate('role', { wait: true })
export class ManagementInner extends React.PureComponent<ConnectedProps> {
	componentWillMount() {
		this.props.dispatch(roleActionCreators.init());
	}
	componentWillUnmount() {
		this.props.dispatch(roleActionCreators.resetState());
	}

	render() {
		const { messages, busy, dispatch, t } = this.props;
		const scrollbarsProps = {
			style: { width: '100%', height: '100%' },
			renderThumbHorizontal: props => <div {...props} style={{ height: '4px' }} className='thumb-horizontal' />,
			renderThumbVertical: props => <div {...props} className='thumb-vertical' style={{ display: 'none' }} />,
			renderTrackVertical: props => <div {...props} className='track-vertical' style={{ display: 'none' }} />,
			autoHide: true
		};
		return (
			<Scrollbars {...scrollbarsProps}>
				<div className='role-management'>
					<OrganizationTree />
					<RolesContainer />
					<EditorsContainer />
					{busy && <BlockLoader />}
					{messages && messages.length &&
						<MessageBox
							title={t('cmMessageBox')}
							okText={t('cmOK')}
							messages={messages}
							portalClassName=''
							onClose={() => { dispatch(roleActionCreators.alertMessage(null)); }}
							parentSelector={(): HTMLElement => document.querySelector('#portal-modals')}
						/>
					}
				</div>
			</Scrollbars>
		);
	}
}

export const Management = connect(
	(state: { role: RoleState }) => ({
		busy: state.role.busy,
		messages: state.role.messages
	})
)(ManagementInner) as React.ComponentClass<{}>;