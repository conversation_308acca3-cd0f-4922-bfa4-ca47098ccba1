import { Permission } from '../../Common/interfaces';

export interface TenantProp {
	id: string;
	name: string;
	required: boolean;
	multivalued: boolean;
	valueType: PropValueType;
	sensitive: boolean;
}

export interface TenantItemProp {
	id: string;
	name: string;
	values: string[];
	allowSubTenantEditing: boolean;
	allowSubTenantViewing: boolean;
	editable: boolean;
	viewable: boolean;
	valueType: PropValueType;
	sensitive: boolean;
}

export enum PropValueType {
	String,
	Boolean,
	Integer,
	Float,
	Date,
	DateTime,
}

export interface User {
	id: string;
	email: string;
	username: string;
	provider: string;
}

export interface Role {
	id: string;
	name: string;
	members?: [];
	tenantId: string;
}

export type UsersDic = { [tenantId: string]: User[] };

export type RolesDic = { [tenantId: string]: Role[] };

export type PermissionsDic = { [tenantId: string]: Permission[] };

export interface OrganizationState {
	busy: boolean;
	tenantProps: TenantProp[];
	items: OrganizationItem[];
	selected: string;
	showSchemaEditor: boolean;
	showTenantEditor: boolean;
	showTenantPropEditor: boolean;
	showMembersEditor: boolean;
	usersDic: UsersDic;
	rolesDic: RolesDic;
	permissionsDic: PermissionsDic;
	noTenantUsers: User[];
	isAdding?: boolean;
	isEditing?: boolean;
	availablePermissions: Permission[];
	enableStrictPermissionManagement: boolean;
}

export interface OrganizationItem {
	id: string;
	name: string;
	order?: number;
	parentTenantId?: string;
	props?: TenantItemProp[];
	path?: string;
}