{"cmAdd": "Add", "cmSave": "Save", "cmCancel": "Cancel", "cmDelete": "Delete", "cmEmptyResult": "empty result", "cmMessageBox": "Message", "cmOK": "OK", "otExpandAll": "Expand All", "otCollapseAll": "Collapse All", "otOrganizationRoot": "Global", "cmAddRole": "Add Role", "rcDeleteRole": "Delete Role", "rcDeleteRoleConfirmMessage": "Are you sure you want to delete the role \"{{RoleName}}\"?", "rcEditRoleName": "Edit Role Name", "rcRoleName": "Role Name", "ecRoleMembers": "Role Members", "ecPermissions": "Permissions", "ecRoleColumn": "Document List Columns", "ecSelectMembers": "Select Members", "ecUsername": "Username", "ecEmail": "Email", "ecProvider": "Provider", "ecMembers": "({{count}} members)", "ecDeleteUser": "Delete User", "ecDeleteUserConfirmMessage": "Are you sure you want to remove the user \"{{UserName}}\" from the role?", "ecNoMemberTip": "No users under the role, to add, please click", "dlType": "Type", "dlUpdateBy": "Updated By", "dlUpdated": "Updated", "dlCreatedBy": "Created By", "dlCreated": "Created", "dlDescription": "Description", "dlReferences": "Referenced Data Document", "dlPermissions": "Permissions", "dlCategories": "Categories", "msgPerformance": "Please don't show the field in the document list in the production environment. It might decrease the load performance.", "ColumnName": "Column Name", "ShowInList": "Show In List", "updateRoleDocumentColumnsError": "Update failed", "rmError_003": "You don't have sufficient privileges to do the operation.", "rmError_2001": "Role \"{{name}}\" already exists.", "rmError_2004": "Can not update the members of role \"everyone\".", "rmError_2005": "Can not remove the member \"admin\" from role \"administrator\". ", "rmError_2006": "Can not update the permissions of role \"administrator\".", "rmError_2009": "Invalid role name. The characters '<', '>', '/', '\\' and '$' are not allowed in the role name and the maximum length of the role name should be less than 64.", "rmError_2010": "Can not create a built-in role named \"{{name}}\"", "rmError_5010": "You cannot remove yourself from the current organization.", "rmError_5011": "You cannot remove yourself from the current organization role.", "rmError_1039": "The users whose management organization is not the global organization cannot be set as the system administrator.", "rt_role": "role", "rt_organization": "organization", "rt_organization role": "organization role", "rt_organization role user": "organization role user", "error_V2_007_002_0001": "Role name can not conflict with permission name.", "error_V2_007_002_0008": "Invalid role name. The characters '<', '>', '/', '\\' and '$' are not allowed in the role name and the maximum length of the role name should be less than 64.", "error_V2_007_002_0009": "Cannot create the built-in role \"{{RoleName}}\".", "error_V2_007_002_0012": "Role name cannot be empty.", "error_V2_007_001_0028": "The users whose management organization is not the global organization cannot be set as the system administrator.", "error_V2_007_005_0007": "You cannot remove yourself from the current organization role.", "error_V2_007_002_0018": "Cannot update to the built-in role \"{{RoleName}}\"."}