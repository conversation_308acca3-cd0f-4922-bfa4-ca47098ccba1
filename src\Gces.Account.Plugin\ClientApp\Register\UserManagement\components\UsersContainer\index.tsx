import * as React from 'react';
import { connect } from 'react-redux';
import { ImportUsers } from './ImportUsers';
import { UserEditor } from './UserEditor';
import { ToolbarContainer } from './ToolbarContainer';
import { UsersGrid } from './UsersGrid';
import { UserInfoDetail } from './UserInfoDetail';
import { UserState, UserInfo } from '../../store';
interface ConnectedProps {
	isSelectingMembers: boolean;
	isAddingUser: boolean;
	editingUser: UserInfo;
	dispatch: any;
	t: any;
}

class UsersContainerInner extends React.Component<ConnectedProps> {

	render() {
		const { isAddingUser, editingUser, isSelectingMembers } = this.props;
		if (isSelectingMembers) return null;
		return (
			<div className='users-container'>
				<ToolbarContainer />
				<UsersGrid />
				{(isAddingUser || editingUser) && <UserEditor />}
				<ImportUsers />
				<UserInfoDetail />
			</div>
		);
	}
}
export const UsersContainer = connect(
	(state: { user: UserState }) => ({
		isSelectingMembers: state.user.isSelectingMembers,
		isAddingUser: state.user.isAddingUser,
		editingUser: state.user.editingUser,
	})
)(UsersContainerInner) as React.ComponentClass<{}>;
