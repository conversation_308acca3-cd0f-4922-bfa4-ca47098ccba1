.org-tree {
	border-right: 1px solid $ef-bg-dk;
	padding: 10px 10px 10px 0;
	position: relative;
	display: flex;
	flex-direction: column;
	max-width: 20%;

	.btn-group {
		flex: 0 0 50px;
		display: flex;
		align-items: center;
		flex-direction: row-reverse;

		button {
			margin-left: 5px;
			max-width: 100px;

			@include gces-truncate;
		}
	}

	.search-box {
		width: 100%;
		margin: 10px 0;
		flex: 0 0 30px;
		background: $ef-bg-dk;
		border-radius: 2px;
		display: flex;
		align-items: center;

		.sc-icon {
			flex: 0 0 30px;
			color: $ef-accent;
			font-size: $ef-icon-18;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.sc-input {
			flex: 1;
			background: none !important;
			border: none !important;
		}
	}

	.no-search-result-in-organization-tree {
		margin-top: 30px;
		text-align: center;
		font-size: $ef-font-size-sm;
		font-style: italic;
	}

	.organizations {
		flex: 1;
	}

	.c-tree-item {
		.tree-item-name {
			display: inline-block;
			padding-right: 10px;
			max-width: 90%;

			@include gces-truncate;
		}
	}
}