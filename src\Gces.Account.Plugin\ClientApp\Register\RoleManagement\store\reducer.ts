import { Reducer } from 'redux';
import * as update from 'immutability-helper';
import { RoleState } from './interfaces';
import { RoleReducerActions } from './actions';
import { nameSorter } from '../../../utils';

const defaultState: RoleState = {
	busy: false,
	allUsers: [],
	organizations: [],
	selectedOrganizationId: '',

	roles: [],
	isAddingRole: false,
	selectedRoleId: '',

	users: [],
	permissions: [],
	availablePermissions: [],

	isAddingMembers: false,

	messages: null,
	documentColumns: [],
	originalDocumentColumns: [],

	enableStrictPermissionManagement: false,
};

export const roleReducer: Reducer<RoleState> = (state: RoleState = defaultState, action: RoleReducerActions) => {
	switch (action.type) {
		case 'Portal/Role/ResetState':
			return JSON.parse(JSON.stringify(defaultState));
		case 'Portal/Role/SetBusy':
			return update(state, { busy: { $set: action.payload.busy } });
		case 'Portal/Role/SetAllUsers':
			return update(state, { allUsers: { $set: action.payload.allUsers } });
		case 'Portal/Role/SetOrganizations':
			const sortedOrganizations = [...action.payload.organizations];
			sortedOrganizations.sort(nameSorter);
			return update(state, { organizations: { $set: sortedOrganizations } });
		case 'Portal/Role/SetSelectedOrganizationId':
			return update(state, { selectedOrganizationId: { $set: action.payload.selectedOrganizationId } });
		case 'Portal/Role/SetRoles':
			return update(state, { roles: { $set: action.payload.roles } });
		case 'Portal/Role/SetIsAddingRole':
			return update(state, { isAddingRole: { $set: action.payload.isAddingRole } });
		case 'Portal/Role/SetSelectedRoleId':
			return update(state, { selectedRoleId: { $set: action.payload.selectedRoleId } });
		case 'Portal/Role/SetUsers':
			return update(state, { users: { $set: action.payload.users } });
		case 'Portal/Role/SetPermissions':
			return update(state, { permissions: { $set: action.payload.permissions } });
		case 'Portal/Role/SetAvailablePermissions':
			return update(state, { availablePermissions: { $set: action.payload.permissions } });
		case 'Portal/Role/SetIsAddingMembers':
			return update(state, { isAddingMembers: { $set: action.payload.isAddingMembers } });
		case 'Portal/Role/AlertMessages':
			return update(state, { messages: { $set: action.payload.messages } });
		case 'Portal/Role/RoleDocumentColumn':
			return update(state, { documentColumns: { $set: action.payload.documentColumns } });
		case 'Portal/Role/OriginalRoleDocumentColumn':
			return update(state, { originalDocumentColumns: { $set: action.payload.originalDocumentColumns } });
		case 'Portal/Role/EnableStrictPermissionManagement':
			return update(state, { enableStrictPermissionManagement: { $set: action.payload.enableStrictPermissionManagement } });
		default: return state;
	}
};