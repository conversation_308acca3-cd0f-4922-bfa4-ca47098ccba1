{
	// Settings
	"Enable": "啓用",
	"Disable": "禁用",
	"synchronizeStateTip": "同步用戶、組織、角色",
	"synchronizationDatasets!title": "選擇數據集",
	"synchronizationDatasets!tip": "請選擇用於同步用戶/組織/角色等的數據集",
	"synchronizationDatasets!user": "用戶數據集",
	"synchronizationDatasets!organization": "組織數據集",
	"synchronizationDatasets!role": "角色數據集",
	"synchronizationDatasets!userRoleRelation": "用戶-角色關係數據集",
	"synchronizationDatasets!userOrganization": "用戶-組織關係數據集",
	"searchPlaceHolder": "搜索",
	"schedulingSettings": "定時同步設置",
	"timeoutSettings": "超時設置",
	"schedulingSync": "自動定時同步",
	"failureNotification": "失敗通知",
	"noFailureNotification": "未配置任何通知，點擊跳轉到配置頁面",
	"applicationSecret!label": "應用密鑰",
	"applicationSecret!title": "應用密鑰",
	"applicationSecret!tip": "所有的同步用戶都可以使用應用密鑰生成令牌，但是不能使用它進行登錄；修改應用密鑰不會影響已經簽發的令牌的有效性；爲了確保安全性，請謹慎分享此密鑰",
	"generate": "生成",
	"copy": "複製",
	"manualSync": "手動同步",
	"saveChanges": "保存更改",
	"cancel": "取消",
	"Email": "郵件",
	"WeChat4Work": "企業微信",
	"DingTalk": "釘釘",
	"MSTeams": "MSTeams",
	"Slack": "Slack",
	"API": "API",
	"ManualSyncSuccessful": "手動觸發同步任務成功",
	"SaveSuccessful": "保存同步設置成功",
	"scheduleFailedMessage": "未成功啓動用戶同步運行計劃",
	// Views
	"syncResults": "同步結果",
	"nextRunTime": "下次同步時間",
	"NoAutoSyncTask": "沒有自動同步的任務",
	"syncedTime": "同步時間",
	"synchronizing": "正在同步...",
	"syncedMessage!success": "同步成功",
	"syncedMessage!notAllSuccess": "未能同步所有數據",
	"syncedMessage!canceled": "同步已取消",
	"syncedMessage!failed": "同步失敗",
	"syncRecords": "同步記錄",
	"entity": "實體",
	"successfulQuantity": "成功數量",
	"failedQuantity": "失敗數量",
	"warningQuantity": "警告數量",
	"noRecordTip": "無同步記錄",
	"informationNotSynced": "信息未同步",
	"download": "下載",
	"type": "類型",
	"name": "名稱",
	"reason": "原因",
	"user": "用戶",
	"organization": "組織",
	"role": "角色",
	"userRoleRelation": "用戶-角色關係",
	"userOrgRelation": "用戶-組織關係",
	"userOrganization": "用戶-組織關係",
	"downloadDescription": "點擊可下載完整錯誤信息",
	"Error": "錯誤",
	"Warning": "警告",
	// Recurrence Editor
	"recEditorHeaderRepeat": "重複週期",
	"recEditorLabelStart": "開始時間",
	"recEditorLabelEnd": "結束時間",
	"recEditorPlaceholderNoEndDate": "無結束時間",
	"recEditorDailyEditor": "天計劃",
	"recEditorWeeklyEditor": "週計劃",
	"recEditorIntervalWeeklyEditor": "週計劃",
	"recEditorMonthlyEditor": "月計劃",
	"timeout": "超時時間",
	"hour": "小時",
	"minute": "分鐘",
	// Daily Editor
	"dailyEditorTextAt": "的{{time}}執行",
	"dailyEditorTextEvery": "每隔",
	"dailyEditorHoursTextUnits": "小時",
	"dailyEditorMinutesTextUnits": "分鐘",
	"dailyEditorSecondsTextUnits": "秒",
	"dailyEditorAddExecutionTimeRange": "添加執行時間區間",
	"dailyEditorExecutionTimeRangeStartTime": "開始時間",
	"dailyEditorExecutionTimeRangeEndTime": "結束時間",
	"dailyExecutionTimeRangeDescription": "每日執行時間範圍為 {{startTime}} 到 {{endTime}}",
	"dailyExecutionTimeRangeNeedToBeRemove": "移除執行時間區間",
	"dailyTaskLessThan24HoursRemoveExecutionTimeRange": "天計劃任務開始到結束時間的間隔小於24小時，需要移除執行時間區間。",
	// Interval Weeks Weekly Editor
	"intervalWeeklyEditorTextEvery": "每隔",
	"intervalWeeklyEditorTextUnits": "週",
	"repeatWeekDes": "每{{weekNumber}}{{weekUnitStr}}",
	"repeatWeek": "週",
	"repeatWeeks": "週",
	"comma": "，",
	"and": "和",
	"onlyInYear": "僅在 {{year}}",
	"throughYears": "{{startYear}} 到 {{endYear}}",
	"intervalWeeklyRepeatDescription": "在 {{startTime}}，{{repeatWeekDes}}，{{daysOfWeekDes}}{{years}}",
	"dayOfWeek_1": "星期日",
	"dayOfWeek_2": "星期一",
	"dayOfWeek_3": "星期二",
	"dayOfWeek_4": "星期三",
	"dayOfWeek_5": "星期四",
	"dayOfWeek_6": "星期五",
	"dayOfWeek_7": "星期六",
	// Monthly Editor
	"monthlyEditorTextEvery": "每隔",
	"monthlyEditorTextUnits": "月",
	"monthlyEditorTextOnDay": "指定日期",
	"monthlyEditorTextOnDayUnits": "",
	"monthlyEditorTextOnTheLast": "最後一個",
	"monthlyEditorTextOnTheFirst": "第一個",
	"monthlyEditorTextLastDay": "自然日",
	"monthlyEditorTextLastWeekday": "工作日",
	// Timezone
	"taskExecutingTimezone": "執行基準時區",
	"timezone_Etc/GMT+12": "(UTC-12:00) 國際日期變更線西",
	"timezone_Etc/GMT+11": "(UTC-11:00) 協調世界時-11",
	"timezone_Pacific/Honolulu": "(UTC-10:00) 夏威夷",
	"timezone_America/Adak": "(UTC-10:00) 阿留申群島",
	"timezone_Pacific/Marquesas": "(UTC-09:30) 馬克薩斯群島",
	"timezone_Etc/GMT+9": "(UTC-09:00) 協調世界時-09",
	"timezone_America/Anchorage": "(UTC-09:00) 阿拉斯加",
	"timezone_America/Tijuana": "(UTC-08:00) 下加利福尼亞州",
	"timezone_Etc/GMT+8": "(UTC-08:00) 協調世界時-08",
	"timezone_America/Los_Angeles": "(UTC-08:00) 太平洋時間(美國和加拿大)",
	"timezone_America/Phoenix": "(UTC-07:00) 亞利桑那",
	"timezone_America/Chihuahua": "(UTC-07:00) 奇瓦瓦，拉巴斯，馬薩特蘭",
	"timezone_America/Denver": "(UTC-07:00) 山地時間(美國和加拿大)",
	"timezone_America/Guatemala": "(UTC-06:00) 中美洲",
	"timezone_America/Chicago": "(UTC-06:00) 中部時間(美國和加拿大)",
	"timezone_Pacific/Easter": "(UTC-06:00) 復活節島",
	"timezone_America/Mexico_City": "(UTC-06:00) 瓜達拉哈拉，墨西哥城，蒙特雷",
	"timezone_America/Regina": "(UTC-06:00) 薩斯喀徹溫",
	"timezone_America/New_York": "(UTC-05:00) 東部時間(美國和加拿大)",
	"timezone_America/Cancun": "(UTC-05:00) 切圖馬爾",
	"timezone_America/Indiana/Indianapolis": "(UTC-05:00) 印地安那州(東部)",
	"timezone_America/Havana": "(UTC-05:00) 哈瓦那",
	"timezone_America/Bogota": "(UTC-05:00) 波哥大，利馬，基多，里奧布朗庫",
	"timezone_America/Port-au-Prince": "(UTC-05:00) 海地",
	"timezone_America/Grand_Turk": "(UTC-05:00) 特克斯和凱科斯群島",
	"timezone_America/La_Paz": "(UTC-04:00) 喬治敦，拉巴斯，馬瑙斯，聖胡安",
	"timezone_America/Asuncion": "(UTC-04:00) 亞松森",
	"timezone_America/Caracas": "(UTC-04:00) 加拉加斯",
	"timezone_America/Santiago": "(UTC-04:00) 聖地亞哥",
	"timezone_America/Halifax": "(UTC-04:00) 大西洋時間(加拿大)",
	"timezone_America/Cuiaba": "(UTC-04:00) 庫亞巴",
	"timezone_America/St_Johns": "(UTC-03:30) 紐芬蘭",
	"timezone_America/Cayenne": "(UTC-03:00) 卡宴，福塔雷薩",
	"timezone_America/Miquelon": "(UTC-03:00) 聖皮埃爾和密克隆群島",
	"timezone_America/Sao_Paulo": "(UTC-03:00) 巴西利亞",
	"timezone_America/Argentina/Buenos_Aires": "(UTC-03:00) 布宜諾斯艾利斯",
	"timezone_America/Godthab": "(UTC-03:00) 格陵蘭",
	"timezone_America/Bahia": "(UTC-03:00) 薩爾瓦多",
	"timezone_America/Montevideo": "(UTC-03:00) 蒙得維的亞",
	"timezone_America/Punta_Arenas": "(UTC-03:00) 蓬塔阿雷納斯",
	"timezone_America/Araguaina": "(UTC-03:00) 阿拉瓜伊納",
	"timezone_Etc/GMT+2": "(UTC-02:00) 中大西洋 - 舊用",
	"timezone_Atlantic/Azores": "(UTC-01:00) 亞速爾群島",
	"timezone_Atlantic/Cape_Verde": "(UTC-01:00) 佛得角群島",
	"timezone_Etc/UTC": "(UTC) 協調世界時",
	"timezone_Africa/Sao_Tome": "(UTC+00:00) 聖多美",
	"timezone_Atlantic/Reykjavik": "(UTC+00:00) 蒙羅維亞，雷克雅未克",
	"timezone_Europe/London": "(UTC+00:00) 都柏林，愛丁堡，里斯本，倫敦",
	"timezone_Africa/Cas​​ablanca": "(UTC+01:00) 卡薩布蘭卡",
	"timezone_Africa/Lagos": "(UTC+01:00) 中非西部",
	"timezone_Europe/Paris": "(UTC+01:00) 布魯塞爾，哥本哈根，馬德里，巴黎",
	"timezone_Europe/Warsaw": "(UTC+01:00) 薩拉熱窩，斯科普里，華沙，薩格勒布",
	"timezone_Europe/Budapest": "(UTC+01:00) 貝爾格萊德，布拉迪斯拉發，布達佩斯，盧布爾雅那，布拉格",
	"timezone_Europe/Berlin": "(UTC+01:00) 阿姆斯特丹，柏林，伯爾尼，羅馬，斯德哥爾摩，維也納",
	"timezone_Asia/Hebron": "(UTC+02:00) 加沙，希伯倫",
	"timezone_Europe/Kaliningrad": "(UTC+02:00) 加里寧格勒",
	"timezone_Africa/Johannesburg": "(UTC+02:00) 哈拉雷，比勒陀利亞",
	"timezone_Africa/Khartoum": "(UTC+02:00) 喀土穆",
	"timezone_Europe/Chisinau": "(UTC+02:00) 基希訥烏",
	"timezone_Asia/Damascus": "(UTC+02:00) 大馬士革",
	"timezone_Asia/Amman": "(UTC+02:00) 安曼",
	"timezone_Africa/Cairo": "(UTC+02:00) 開羅",
	"timezone_Africa/Windhoek": "(UTC+02:00) 溫得和克",
	"timezone_Africa/Tripoli": "(UTC+02:00) 的黎波里",
	"timezone_Asia/Jerusalem": "(UTC+02:00) 耶路撒冷",
	"timezone_Asia/Beirut": "(UTC+02:00) 貝魯特",
	"timezone_Europe/Kiev": "(UTC+02:00) 赫爾辛基，基輔，裡加，索非亞，塔林，維爾紐斯",
	"timezone_Europe/Bucharest": "(UTC+02:00) 雅典，布加勒斯特",
	"timezone_Europe/Istanbul": "(UTC+03:00) 伊斯坦布爾",
	"timezone_Africa/Nairobi": "(UTC+03:00) 內羅畢",
	"timezone_Asia/Baghdad": "(UTC+03:00) 巴格達",
	"timezone_Europe/Minsk": "(UTC+03:00) 明斯克",
	"timezone_Asia/Riyadh": "(UTC+03:00) 科威特，利雅得",
	"timezone_Europe/Moscow": "(UTC+03:00) 莫斯科，聖彼得堡",
	"timezone_Asia/Tehran": "(UTC+03:30) 德黑蘭",
	"timezone_Europe/Samara": "(UTC+04:00) 伊熱夫斯克，薩馬拉",
	"timezone_Europe/Volgograd": "(UTC+04:00) 伏爾加格勒",
	"timezone_Asia/Yerevan": "(UTC+04:00) 埃里溫",
	"timezone_Asia/Baku": "(UTC+04:00) 巴庫",
	"timezone_Asia/Tbilisi": "(UTC+04:00) 第比利斯",
	"timezone_Europe/Saratov": "(UTC+04:00) 薩拉托夫",
	"timezone_Indian/Mauritius": "(UTC+04:00) 路易港",
	"timezone_Asia/Dubai": "(UTC+04:00) 阿布扎比，馬斯喀特",
	"timezone_Europe/Astrakhan": "(UTC+04:00) 阿斯特拉罕，烏里揚諾夫斯克",
	"timezone_Asia/Kabul": "(UTC+04:30) 喀布爾",
	"timezone_Asia/Karachi": "(UTC+05:00) 伊斯蘭堡，卡拉奇",
	"timezone_Asia/Qyzylorda": "(UTC+05:00) 克孜洛爾達",
	"timezone_Asia/Yekaterinburg": "(UTC+05:00) 葉卡捷琳堡",
	"timezone_Asia/Tashkent": "(UTC+05:00) 阿什哈巴德，塔什幹",
	"timezone_Asia/Colombo": "(UTC+05:30) 斯里加亞渥登普拉",
	"timezone_Asia/Kolkata": "(UTC+05:30) 欽奈，加爾各答，孟買，新德里",
	"timezone_Asia/Kathmandu": "(UTC+05:45) 加德滿都",
	"timezone_Asia/Dhaka": "(UTC+06:00) 達卡",
	"timezone_Asia/Omsk": "(UTC+06:00) 鄂木斯克",
	"timezone_Asia/Almaty": "(UTC+06:00) 阿斯塔納",
	"timezone_Asia/Yangon": "(UTC+06:30) 仰光",
	"timezone_Asia/Krasnoyarsk": "(UTC+07:00) 克拉斯諾亞爾斯克",
	"timezone_Asia/Barnaul": "(UTC+07:00) 巴爾瑙爾，戈爾諾-阿爾泰斯克",
	"timezone_Asia/Tomsk": "(UTC+07:00) 托木斯克",
	"timezone_Asia/Novosibirsk": "(UTC+07:00) 新西伯利亞",
	"timezone_Asia/Bangkok": "(UTC+07:00) 曼谷，河內，雅加達",
	"timezone_Asia/Hovd": "(UTC+07:00) 科布多",
	"timezone_Asia/Ulaanbaatar": "(UTC+08:00) 烏蘭巴托",
	"timezone_Asia/Irkutsk": "(UTC+08:00) 伊爾庫茨克",
	"timezone_Asia/Shanghai": "(UTC+08:00) 北京，重慶，香港特別行政區，烏魯木齊",
	"timezone_Asia/Taipei": "(UTC+08:00) 台北",
	"timezone_Asia/Singapore": "(UTC+08:00) 吉隆坡，新加坡",
	"timezone_Australia/Perth": "(UTC+08:00) 珀斯",
	"timezone_Australia/Eucla": "(UTC+08:45) 尤克拉",
	"timezone_Asia/Tokyo": "(UTC+09:00) 大阪，札幌，東京",
	"timezone_Asia/Pyongyang": "(UTC+09:00) 平壤",
	"timezone_Asia/Chita": "(UTC+09:00) 赤塔市",
	"timezone_Asia/Yakutsk": "(UTC+09:00) 雅庫茨克",
	"timezone_Asia/Seoul": "(UTC+09:00) 首爾",
	"timezone_Australia/Darwin": "(UTC+09:30) 達爾文",
	"timezone_Australia/Adelaide": "(UTC+09:30) 阿德萊德",
	"timezone_Pacific/Port_Moresby": "(UTC+10:00) 關島，莫爾茲比港",
	"timezone_Australia/Sydney": "(UTC+10:00) 堪培拉，墨爾本，悉尼",
	"timezone_Australia/Brisbane": "(UTC+10:00) 布里斯班",
	"timezone_Asia/Vladivostok": "(UTC+10:00) 符拉迪沃斯托克",
	"timezone_Australia/Hobart": "(UTC+10:00) 霍巴特",
	"timezone_Australia/Lord_Howe": "(UTC+10:30) 豪勳爵島",
	"timezone_Asia/Srednekolymsk": "(UTC+11:00) 喬庫爾達赫",
	"timezone_Pacific/Bougainville": "(UTC+11:00) 布干維爾島",
	"timezone_Pacific/Guadalcanal": "(UTC+11:00) 所羅門群島，新喀裡多尼亞",
	"timezone_Asia/Sakhalin": "(UTC+11:00) 薩哈林",
	"timezone_Pacific/Norfolk": "(UTC+11:00) 諾福克島",
	"timezone_Asia/Magadan": "(UTC+11:00) 馬加丹",
	"timezone_Etc/GMT-12": "(UTC+12:00) 協調世界時+12",
	"timezone_Pacific/Auckland": "(UTC+12:00) 奧克蘭，惠靈頓",
	"timezone_Asia/Kamchatka": "(UTC+12:00) 彼得羅巴甫洛夫斯克-堪察加 - 舊用",
	"timezone_Pacific/Fiji": "(UTC+12:00) 斐濟",
	"timezone_Pacific/Chatham": "(UTC+12:45) 查塔姆群島",
	"timezone_Pacific/Tongatapu": "(UTC+13:00) 努庫阿洛法",
	"timezone_Etc/GMT-13": "(UTC+13:00) 協調世界時+13",
	"timezone_Pacific/Apia": "(UTC+13:00) 薩摩亞群島",
	"timezone_Pacific/Kiritimati": "(UTC+14:00) 聖誕島",
	"timezone_America/Mazatlan": "(UTC-06:00) 馬薩特蘭",
	"timezone_America/Whitehorse": "(UTC-07:00) 白馬市",
	"timezone_America/Nuuk": "(UTC-02:00) 努克",
	"timezone_Africa/Abidjan": "(UTC+00:00) 阿比讓",
	"timezone_Africa/Juba": "(UTC+03:00) 朱巴",
	"timezone_Europe/Kyiv": "(UTC+03:00) 基輔",
	//ErrorCode
	"error_V2_000_019_0001": "無效的用戶同步設置，請檢查'{{configItem}}設置。",
	"error_V2_000_019_0002": "請至少選擇一個數據集。",
	"error_V2_000_019_0003": "用戶同步任務失敗。",
	"error_V2_000_019_0004": "未通過數據集字段驗證，缺失必需字段如下：",
	"error_0001": "Id 不能為空。",
	"error_0002": "名稱不能為空。實體ID：{{id}}。",
	"error_0003": "檢測到重複的 id。實體ID：{{id}}，重複的實體：{{names}}",
	"error_0004": "檢測到重複的名稱。",
	"error_0005": "檢測到無效的名稱。",
	"error_0006": "id只能包含數字、字母、_、-、. 和 @，長度不能超過128。實體ID：{{id}}。",
	"error_0007": "檢測到重複的電子郵件。電子郵件：{{email}}，重複的用戶：{{names}}。",
	"error_0011": "實體不存在。實體ID：{{id}}。",
	"error_0011!extension": "$t(synchronization:error_0011) 實體類型：{{type}}。",
	"error_0021": "屬性({{name}})不存在。",
	"error_0022": "無效的屬性({{propName}})值({{propValue}})。",
	"error_0023": "無效的電子郵件值：{{email}}。",
	"error_0024": "無效的頭像值，頭像值只支援URL。",
	"error_0031": "組織樹中存在循環。",
	"error_0032": "組織未鏈接到組織樹。",
	"error_0041_org": "權限衝突。衝突的實體：(組織){{orgId}} 和 (父組織){{parentId}}。",
	"error_0041_org_role": "權限衝突。衝突的實體：(組織){{orgId}} 和 (組織內角色){{roleId}}。",
	"error_0098": "同步逾時，或者被異常取消。",
	"error_0099": "發生意外異常。",
	"error_1001": "無效的用戶同步設置，請檢查'{{configItem}}設置。",
	"error_1002": "至少分配一個數據集。",
	"error_1003": "用戶同步任務失敗，請檢查運行計劃設置。",
	"error_1004": "未通過數據集字段驗證。缺失必需字段：{{errorMessage}}。",
	"error_1005": "用戶同步超時。",
	"error_1006": "獲取數據集結果集失敗，失敗的數據集類型：{{types}}。",
	"errorGetAppContacts": "獲取聯係人失敗。",
	"fallback_unknown_error": "係統內部錯誤。",
}