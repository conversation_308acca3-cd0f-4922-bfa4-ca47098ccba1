﻿import ActionTypes from '../actions/action-types';
import update from 'immutability-helper';

export default (state = { settings: null, busy: false, users: null, saved: null }, action) => {
	switch (action.type) {
		case ActionTypes.SetInactiveSessionSettings:
			return update(state, { settings: { $set: action.inactiveSessionSettings } });
		case ActionTypes.SetInactiveSessionSettingsBusy:
			return update(state, { busy: { $set: action.busy } });
		case ActionTypes.SetUsers:
			return update(state, { users: { $set: action.users } });
		case ActionTypes.SetSaveInactiveSessionSettingsState:
			return update(state, { saved: { $set: action.saved } });
		default:
			return state;
	}
}