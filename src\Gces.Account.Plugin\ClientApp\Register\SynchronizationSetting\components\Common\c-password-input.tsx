import { AriaIcon } from 'gces-ui';
import { InputEditor } from 'gces-ui/lib/components/editors';
import * as React from 'react';

export interface CPasswordInputProps {
	value: string;
	onEveryChange?: (value: string) => void;
	inputDisabled?: boolean;
	buttonDisabled?: boolean;
	aid?: string;
}

interface LocalState {
	showPassword: boolean;
}

export class CPasswordInput extends React.Component<CPasswordInputProps, LocalState> {
	constructor(props: CPasswordInputProps, context) {
		super(props, context);
		this.state = {
			showPassword: false
		};
	}

	togglePasswordEye = () => {
		this.setState({
			showPassword: !this.state.showPassword
		});
	}

	render() {
		const type = this.state.showPassword ? 'text' : 'password';
		const eyeIcon = this.state.showPassword ? 'mdi-eye-off' : 'mdi-eye';
		const iconClass = `mdi ${eyeIcon} ef-abs-icon ef-pos-top ef-pos-lt`;
		const { value, inputDisabled, buttonDisabled, onEveryChange, aid } = this.props;

		return (
			<div className='flexAlignMiddle c-password-input'>
				<InputEditor
					aid={aid}
					type={type}
					value={value}
					disabled={inputDisabled}
					onEveryChange={onEveryChange}
				/>
				<button data-aid={aid && `${aid}-btn`} className='btn ef-block ef-btn ef-size-sm' onClick={this.togglePasswordEye} disabled={buttonDisabled || !value}>
					<AriaIcon className={iconClass} />
				</button>
			</div>
		);
	}
}