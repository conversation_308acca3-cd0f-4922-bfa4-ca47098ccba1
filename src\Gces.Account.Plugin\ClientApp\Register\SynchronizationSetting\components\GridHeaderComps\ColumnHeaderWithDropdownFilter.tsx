import { Dropdown, DropdownItemProps } from 'gces-ui';
import * as React from 'react';

export interface TypeFilter {
	name: string;
	value: string;
	selected: boolean;
}

export interface ColumnHeaderWithDropdownFilterProps {
	title: string;
	types: TypeFilter[];
	onUnSelect: (selectedTypes: string[]) => void;
}

export class ColumnHeaderWithDropdownFilter extends React.PureComponent<ColumnHeaderWithDropdownFilterProps, {}> {
	checkedIcon = 'mdi mdi-check-circle filter-icon';
	unCheckedIcon = 'mdi mdi-checkbox-blank-circle-outline filter-icon';

	onSelectChanged = (value) => {
		const { types, onUnSelect } = this.props;
		const unSelectedTypes = types.filter(t => !t.selected).map(t => t.value);
		const isCurSelected = types.find(t => t.value === value)?.selected;
		if (isCurSelected) {
			unSelectedTypes.push(value);
		}
		else {
			unSelectedTypes.splice(unSelectedTypes.indexOf(value), 1);
		}
		onUnSelect && onUnSelect(unSelectedTypes);
	}

	render() {
		const { types, title } = this.props;
		const typeItems: DropdownItemProps[] = types.map(t => ({
			text: t.name,
			title: t.name,
			value: t.value,
			icon: t.selected ? this.checkedIcon : this.unCheckedIcon,
		}));

		return (
			<div className='column-header-with-dropdown'>
				<div className='column-header-title' title={title}>
					{title}
				</div>
				<div className='column-header-filter'>
					<Dropdown
						style='transparent'
						className='column-header-filter-dropdown'
						title={title}
						icon={types.filter(t => t.selected).length === 0 ? 'mdi mdi-filter-remove-outline' : types.some(t => !t.selected) ? 'mdi mdi-filter-outline' : 'mdi mdi-filter'}
						size='small'
						disabled={types.length === 0}
						items={typeItems}
						onSelect={this.onSelectChanged}
						useVirtualList={false}
						rounded
						noCloseOnSelect
						hiddenChevron
						offset
					/>
				</div>
			</div>
		);
	}
}