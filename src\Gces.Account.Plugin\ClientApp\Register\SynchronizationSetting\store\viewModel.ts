export interface UserSyncTaskResponseViewModel {
	dataset: SyncDatasets;
	triggerInfo: Trigger;
	enableAutoSync: boolean;
	failInformProviders: FailedInformProvider[];
	timeout: number;
	enabled: boolean;
}

export interface UserSyncTaskRequestViewModel {
	dataset: SyncDatasetsRequestViewModel;
	triggerInfo: Trigger;
	enableAutoSync: boolean;
	failInformProviders: FailedInformProvider[];
	timeout: number;
	enabled: boolean;
}

export interface SyncDatasets {
	user: SyncDatasetViewModel;
	role: SyncDatasetViewModel;
	organization: SyncDatasetViewModel;
	userRole: SyncDatasetViewModel;
	userOrganization: SyncDatasetViewModel;
}

export interface SyncDatasetsRequestViewModel {
	user: string;
	role: string;
	organization: string;
	userRole: string;
	userOrganization: string;
}

export interface SyncDatasetViewModel {
	id: string;
	name: string;
}

export interface Trigger {
	startDate: Date;
	endDate: Date;
	timeZoneId: string;
	cronExpression: string;
}

export interface FailedInformProvider {
	channel: string;
	contacts: Contact[];
}

export interface Contact {
	displayName: string;
	address: string;
	addressType: string;
}

export interface TaskFailInformEmailProviderDTO {
	emailAddress: string;
}

export interface TaskFailInformAppMsgProviderDTO {
	appName: string;
	message: AppMessageDTO;
}

export interface AppMessageDTO {
	recipients: AppMessageRecipientDTO[];
}

export interface AppMessageRecipientDTO {
	address: string;
	addressType: string;
	displayName: string;
}

export interface SecretViewModel {
	enabled: boolean;
	value: string;
}

export interface HistoryViewModel {
	cancelled: boolean;
	runTime: string;
	syncResult: SyncResultViewModel;
	nextRunTime: string;
	errors: SyncError[];
	omit: boolean;
}

export interface SyncError {
	context: Record<string, object>;
	errorCode: number;
	errorKey: string;
	exception: string;
	message: string;
}

export interface SyncResultViewModel {
	success: boolean;
	detail: SyncResultDetail;
	error: SyncInnerError;
}

export interface SyncResultDetail {
	user: EntitySyncSummary;
	org: EntitySyncSummary;
	role: EntitySyncSummary;
	userRoleRelation: EntitySyncSummary;
	userOrgRelation: EntitySyncSummary;
	failureItems: ErrorEntity[];
}

export interface EntitySyncSummary {
	success: number;
	failure: number;
	warning: number;
}

export interface ErrorEntity {
	entity: SyncEntity;
	error: SyncInnerError;
}

export interface SyncEntity {
	type: EntityType;
	id: string;
	name: string;
	relations: string[];
}

export enum EntityType {
	User = 'User',
	Organization = 'Organization',
	Role = 'Role',
	UserRole = 'UserRole',
	UserOrg = 'UserOrg',
}

export interface SyncInnerError {
	entityType: EntityType;
	errorCode: string;
	level: ErrorLevel;
	context: { [key: string]: string };
}

export enum ErrorLevel {
	Warning = 'Warning',
	Error = 'Error',
	Critical = 'Critical'
}

export interface TaskStatusViewModel {
	process: UserSyncProcessViewModel;
	isScheduled: boolean;
	nextRunTime: string;
	executionId: string;
}

export enum UserSyncProcessViewModel {
	None = 'None',
	Validating = 'Validating',
	FetchingData = 'FetchingData',
	Synchronizing = 'Synchronizing',
	Completed = 'Completed',
}

export interface UserSyncUpdateResponseModel {
	scheduleFailed: boolean;
	errorCode: string;
	errorMessage: string;
}