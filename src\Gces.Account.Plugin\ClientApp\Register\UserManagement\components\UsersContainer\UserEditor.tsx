import * as React from 'react';
import * as update from 'immutability-helper';
import { translate } from 'react-i18next';
import { connect } from 'react-redux';
import { Scrollbars } from 'gces-react-custom-scrollbars';
import { Label, Button, DropdownItemProps, Checkbox } from 'gces-ui';
import Drawer from '../../../../components/Drawer';
import * as util from '../../../../util';
import { getManagerOrgId, getRoleDisplayName } from '../../../../utils';
import { UserInfo, RoleItem, Error, UserState, UserProperty } from '../../store/interfaces';
import { userActionCreators } from '../../store';
import { RoleConsts } from '../../../../Register/Common';
import { HomePageSelector } from '../../../Common/HomePageSelector';
import { CMultiValueInput } from '../../../../components/c-multivalue-input';
import { CNumberPicker } from '../../../../components/c-numberPicker';
import { CDropdownEditor } from '../../../../components/c-dropdown';
import { CDateTimePicker } from '../../../../components/c-datetimePicker';
import { CCheckList } from '../../../../components/c-checkList';
import { CInputEditor } from '../../../../components/c-input';
import { CDropdownCheckList } from '../../../../components/c-dropdownCheckList';
import * as moment from 'moment';

interface UserEditorOwnState {
	newUser: UserInfo & { managerId: string };
	errors: Error;
}

interface ConnectedProps {
	curUser: User;
	isAddingUser: boolean;
	editingUser: UserInfo;
	selectedOrganizationId: string;
	users: UserInfo[];
	roles: RoleItem[];
	orgs: Organization[];
	properties: UserProperty[];
	passwordPolicy: number;
	dispatch?: any;
	t?: any;
}

@translate('user', { wait: true })
class UserEditorInner extends React.Component<ConnectedProps, UserEditorOwnState> {
	_saving = false;
	_adminUserName = 'admin';
	_hasSensitiveProps = false;
	state = {
		newUser: {
			username: (this.props.editingUser && this.props.editingUser.username) || '',
			email: (this.props.editingUser && this.props.editingUser.email) || '',
			mobile: (this.props.editingUser && this.props.editingUser.mobile) || '',
			firstName: (this.props.editingUser && this.props.editingUser.firstName) || '',
			lastName: (this.props.editingUser && this.props.editingUser.lastName) || '',
			fullName: (this.props.editingUser && this.props.editingUser.fullName) || '',
			roles: (this.props.editingUser && this.props.editingUser.roles) || [],
			password: (this.props.editingUser && this.props.editingUser.password) || '',
			confirmPassword: (this.props.editingUser && this.props.editingUser.confirmPassword) || '',
			passwordExpirationPolicy: (this.props.editingUser && this.props.editingUser.passwordExpirationPolicy) || 0,
			customProperties: (this.props.editingUser && this.props.editingUser.customizeProperties) || {},
			organizationIdPath: this.props.editingUser && this.props.editingUser.organizationIdPath,
			managerId: this.props.isAddingUser ?
				this.props.selectedOrganizationId :
				getManagerOrgId(this.props.editingUser && this.props.editingUser.organizationIdPath),
			homePageId: (this.props.editingUser && this.props.editingUser.homePageId) || '',
			provider: (this.props.editingUser && this.props.editingUser.provider) || 'local',
		},
		errors: {
			username: null,
			email: null,
			mobile: null,
			password: null,
			confirmPassword: null,
		}
	};

	componentDidMount() {
		const { dispatch, selectedOrganizationId } = this.props;
		dispatch(userActionCreators.getRolesOfCurOrganization(selectedOrganizationId));
	}

	closeEditor = () => {
		this.setState({ newUser: null });
		if (this.props.isAddingUser)
			this.props.dispatch(userActionCreators.setIsAddingUser(false));
		else
			this.props.dispatch(userActionCreators.setEditingUser(null));
	}

	updateUserName = (value: string) => {
		value = value.trim();
		const newUser = update(this.state.newUser, { username: { $set: value } });
		const errors = update(this.state.errors, { username: { $set: this.validateNewUser('username', value) } });
		this.setState({ newUser, errors });
	}
	updateUserEmail = (value: string) => {
		value = value.trim();
		const newUser = update(this.state.newUser, { email: { $set: value } });
		const errors = update(this.state.errors, { email: { $set: this.validateNewUser('email', value) } });
		this.setState({ newUser, errors });
	}
	updateUserMobile = (value: string) => {
		value = value.trim();
		const newUser = update(this.state.newUser, { mobile: { $set: value } });
		const errors = update(this.state.errors, { mobile: { $set: this.validateNewUser('mobile', value) } });
		this.setState({ newUser, errors });
	}
	updateUserFirstName = (value: string) => {
		const newUser = update(this.state.newUser, { firstName: { $set: value.trim() } });
		this.setState({ newUser });
	}
	updateUserLastName = (value: string) => {
		const newUser = update(this.state.newUser, { lastName: { $set: value.trim() } });
		this.setState({ newUser });
	}
	updateUserFullName = (value: string) => {
		const newUser = update(this.state.newUser, { fullName: { $set: value.trim() } });
		this.setState({ newUser });
	}
	updateUserPassword = (value: string) => {
		const newUser = update(this.state.newUser, { password: { $set: value } });
		let errors = update(this.state.errors, { password: { $set: this.validateNewUser('password', value) } });
		if (newUser.confirmPassword) {
			const confirmPasswordError = ((newUser.confirmPassword || value) && newUser.confirmPassword !== value) ? this.props.t('PasswordNotMatch') : null;
			errors = update(errors, { confirmPassword: { $set: confirmPasswordError } });
		}
		this.setState({ newUser, errors });
	}
	updateUserConfirmPassword = (value: string) => {
		const newUser = update(this.state.newUser, { confirmPassword: { $set: value } });
		const errors = update(this.state.errors, { confirmPassword: { $set: this.validateConfirmPassword(value) } });
		this.setState({ newUser, errors });
	}
	setPasswordPolicy = (value: number) => {
		const newUser = update(this.state.newUser, { passwordExpirationPolicy: { $set: value === 0 ? 1 : 0 } });
		this.setState({ newUser });
	}

	validateNewUser = (type: string, value: string) => {
		switch (type.toLocaleLowerCase()) {
			case 'username'.toLocaleLowerCase():
				return this.validateUsername(value);

			case 'email'.toLocaleLowerCase():
				return this.validateEmail(value);

			case 'mobile'.toLocaleLowerCase():
				return this.validateMobile(value);

			case 'password'.toLocaleLowerCase():
				return this.validatePassword(value);

			default:
				return null;
		}
	};
	validateUsername = (value: string) => {
		const { users, editingUser, t } = this.props;
		if (editingUser) return null;
		if (!value || !value.trim()) {
			return t('UsernameIsRequired');
		} else if (value.trim().length > 150) {
			return t('UsernameLengthExceedLimit');
		} else {
			return (users && users.find(u => u.username && u.username.toLowerCase() === value.trim().toLowerCase() && util.isLocalOrSyncedUser(u))) ? t('UsernameAlreadyExists') : null;
		}
	};
	validateEmail = (value: string) => {
		const { isAddingUser, users, editingUser, t } = this.props;
		const email = !value ? '' : value.trim().toLowerCase();
		if (email === '') {
			if (isAddingUser || util.isLocalUser(editingUser)) return t('EmailIsRequired');
			else return null;
		}
		if (!util.isValidEmail(email)) return t('EmailIsInvalid');
		if (isAddingUser && users && users.find(u => u.email && u.email.toLowerCase() === email)) return t('EmailAlreadyExists');
		if (editingUser && util.isLocalOrSyncedUser(editingUser) && editingUser.email && editingUser.email.toLowerCase() !== email && users.find(u => u.email && u.email.toLowerCase() === email && util.isLocalOrSyncedUser(u))) return t('EmailAlreadyExists');
		return null;
	};
	validateMobile = (value: string) => {
		const { isAddingUser, users, editingUser, t } = this.props;
		if (value && value.trim()) {
			const mobile = value.trim().toLowerCase();
			if (editingUser && editingUser.mobile && editingUser.mobile.toLowerCase() !== mobile && this.props.users.find(u => u.mobile && u.mobile.toLowerCase() === mobile && util.isLocalUser(u))) return this.props.t('MobileAlreadyExists');
			if (isAddingUser && users && users.find(u => u.mobile && u.mobile.toLowerCase() === mobile)) return t('MobileAlreadyExists');
		}
		return null;
	};
	validatePassword = (value: string) => {
		const { passwordPolicy, editingUser, t } = this.props;
		if (editingUser && !value) return null;
		if (!value) {
			return passwordPolicy === util.PasswordPolicy.WeakPasswordPolicy ? t('WeakPasswordRequirement') : t('StrongPasswordRequirement');
		}
		if (!value.trim()) {
			return t('PasswordIsBlank');
		}
		return util.isValidUserPassword(value, passwordPolicy) ?
			null :
			(passwordPolicy === util.PasswordPolicy.WeakPasswordPolicy ? t('WeakPasswordRequirement') : t('StrongPasswordRequirement'));
	};
	validateConfirmPassword = (value: string) => {
		return ((this.state.newUser.password || value) && this.state.newUser.password !== value) ?
			this.props.t('PasswordNotMatch') :
			null;
	}
	validatePropertyValue = (values: any[], valueType: string) => {
		if (!values || values.length === 0 || values.every(v => !v || v.trim() === '')) {
			return true;
		}
		return util.isValidValue(values.filter(v => !!v), valueType);
	}

	handleRoleSelected = (value: string) => {
		const index = this.state.newUser.roles.indexOf(value.trim());
		const newUser = index >= 0
			? update(this.state.newUser, { roles: { $splice: [[index, 1]] } })
			: update(this.state.newUser, { roles: { $push: [value] } });

		this.setState({ newUser });
	}

	onHomePageSelectorChange = (value: string) => {
		const newUser = update(this.state.newUser, { homePageId: { $set: value } });
		this.setState({ newUser });
	}

	handleManagerSelected = (value: string) => {
		const newUser = update(this.state.newUser, { managerId: { $set: value } });
		this.setState({ newUser });
	}

	handlePropertyValueChanged = (name, value) => {
		const { properties } = this.props;
		let newUser = null;
		const property = properties.find(p => p.name === name);
		if (property.multivalued) {
			if (property.availableValues && property.availableValues.length > 0) {
				if (!value) return;
				if (!this.state.newUser.customProperties[name]) {
					newUser = update(this.state.newUser, { customProperties: { [name]: { $set: [value] } } });
				} else {
					const idx = (this.state.newUser.customProperties[name] || []).indexOf(value);
					newUser = idx >= 0 ?
						update(this.state.newUser, { customProperties: { [name]: { $splice: [[idx, 1]] } } }) :
						update(this.state.newUser, { customProperties: { [name]: { $push: [value] } } });
				}
			} else {
				newUser = update(this.state.newUser, { customProperties: { [name]: { $set: value.split(/\r?\n/) } } });
			}
		} else {
			let newValue: any;
			switch (property.valueType) {
				case 4: // date
					newValue = moment(value).isValid() ? moment(value).format(util.DateFormat) : value; break;
				case 5: // datetime
					newValue = moment(value).isValid() ? moment(value).format(util.DateTimeFormat) : value; break;
				default:
					newValue = value && value.trim();
			}
			newUser = update(this.state.newUser, { customProperties: { [name]: { $set: [newValue] } } });
		}
		this.setState({ newUser });
	}

	handleSelectAll = (propertyName: string, allSelected: boolean, values: any[]) => {
		const { properties } = this.props;
		const property = properties.find(p => p.name === propertyName);
		if (property?.multivalued) {
			const crtValues = this.state.newUser.customProperties[propertyName] || [];
			const newValues = allSelected ?
			    [...new Set([...crtValues].concat(values))] :
			    [...crtValues].filter(v => values.indexOf(v) < 0);
			const newUser = update(this.state.newUser, { customProperties: { [propertyName]: { $set: newValues } } });
			this.setState({ newUser });
		}
	}

	buildPropertiesDrawerEditor = (property: UserProperty) => {
		const { t } = this.props;
		if (property.availableValues && property.availableValues.length > 0) {
			const values = this.state.newUser.customProperties[property.name] || [];
			let propValues = property.availableValues.map((v) => {
				return {
					text: v,
					value: v,
					title: v,
					selected: values.indexOf(v) > -1
				};
			});

			if (property.multivalued) {
				return (
					<div key={property.name} className='form-item'>
						<Label text={property.name} />
						<CDropdownCheckList
							text={(this.state.newUser.customProperties[property.name] || []).join(', ')}
							items={propValues}
							onItemSelect={(value) => this.handlePropertyValueChanged(property.name, value)}
							handleSelectAll={(allSelected, values) => this.handleSelectAll(property.name, allSelected, values)}
							visibilityToggle={property.sensitive}
							noVisibilityToggle={!this._hasSensitiveProps}
							showEyeTitle={t('ShowPropertyValue')}
							hideEyeTitle={t('HidePropertyValue')}
							searchPlaceholder={t('searchPlaceHolder')}
							selectAllText={t('SelectAll')}
						/>
					</div>
				);
			} else {
				const nullValue = { text: t('NullValue'), value: null, title: t('NullValue'), selected: false };
				propValues = [nullValue, ...propValues];
				const currentValue = this.state.newUser.customProperties[property.name];
				return (
					<div key={property.name} className='form-item'>
						<Label text={property.name} />
						<CDropdownEditor
							text={currentValue || t('SelectValue')}
							items={propValues}
							onChange={(value) => this.handlePropertyValueChanged(property.name, value)}
							visibilityToggle={property.sensitive}
							noVisibilityToggle={!this._hasSensitiveProps}
							noSelectedItem={!currentValue}
							showEyeTitle={t('ShowPropertyValue')}
							hideEyeTitle={t('HidePropertyValue')}
							canSearch={true}
							searchPlaceholder={t('searchPlaceHolder')}
						/>
					</div>
				);
			}
		}
		else {
			const currentValues = this.state.newUser.customProperties[property.name] || [];
			const labelText = property.multivalued ? `${property.name}${t('OneValuePerLine')}` : property.name;
			return <div key={property.name} className='form-item user-input-property'>
				<Label text={labelText} />
				{this.buildPropertiesEditor(property, currentValues)}
			</div>;
		}
	}

	getPropertyPlaceHolder = (valueType: number, propName: string) => {
		switch (util.ContextValueTypes[valueType]) {
			case util.DataType.String: return propName;
			case util.DataType.Boolean: return 'true';
			case util.DataType.Integer: return '0';
			case util.DataType.Float: return '0.0';
			case util.DataType.Date: return '1990-01-01';
			case util.DataType.DateTime: return '1990-01-01 00:00:00';
		}
	}

	buildPropertiesEditor = (property: UserProperty, currentValues: string[]) => {
		const { t } = this.props;
		const { name, valueType, multivalued, sensitive } = property;
		const placeHolder = this.getPropertyPlaceHolder(valueType, name);
		if (multivalued) {
			return (
				<CMultiValueInput
					className='multi-valued'
					value={currentValues.join('\n')}
					valueType={util.ContextValueTypes[valueType]}
					placeHolder={placeHolder}
					onChange={(value) => this.handlePropertyValueChanged(name, value)}
					visibilityToggle={sensitive}
					noVisibilityToggle={!this._hasSensitiveProps}
					showEyeTitle={t('ShowPropertyValue')}
					hideEyeTitle={t('HidePropertyValue')}
				/>
			);
		}

		const currentValue = currentValues.join('\n');
		switch (util.ContextValueTypes[valueType]) {
			case util.DataType.String:
				return (
					<CInputEditor
						placeholder={placeHolder}
						value={currentValue}
						onEveryChange={(value: string) => this.handlePropertyValueChanged(name, value)}
						visibilityToggle={sensitive}
						noVisibilityToggle={!this._hasSensitiveProps}
						showEyeTitle={t('ShowPropertyValue')}
						hideEyeTitle={t('HidePropertyValue')}
					/>
				);
			case util.DataType.Boolean:
				{
					const items = [{ value: true, text: 'True' }, { value: false, text: 'False' }];
					const selectedItem = items.find(s => s.value.toString() === currentValue?.toLowerCase());
					return (
						<CDropdownEditor
							items={items}
							text={selectedItem && selectedItem.text}
							onChange={(value: boolean) => this.handlePropertyValueChanged(name, value.toString())}
							visibilityToggle={sensitive}
							noVisibilityToggle={!this._hasSensitiveProps}
							showEyeTitle={t('ShowPropertyValue')}
							hideEyeTitle={t('HidePropertyValue')}
						/>
					);
				}
			case util.DataType.Integer:
				return (
					<CNumberPicker
						type='integer'
						value={currentValue}
						placeholder={placeHolder}
						onChange={(value) => this.handlePropertyValueChanged(name, !!value ? value : '')}
						visibilityToggle={sensitive}
						noVisibilityToggle={!this._hasSensitiveProps}
						showEyeTitle={t('ShowPropertyValue')}
						hideEyeTitle={t('HidePropertyValue')}
					/>
				);
			case util.DataType.Float:
				return (
					<CNumberPicker
						type='float'
						value={currentValue}
						placeholder={placeHolder}
						onChange={(value) => this.handlePropertyValueChanged(name, !!value ? value : '')}
						visibilityToggle={sensitive}
						noVisibilityToggle={!this._hasSensitiveProps}
						showEyeTitle={t('ShowPropertyValue')}
						hideEyeTitle={t('HidePropertyValue')}
					/>
				);
			case util.DataType.Date:
				return (
					<CDateTimePicker
						placeholder={placeHolder}
						value={!!currentValue ? moment(currentValue) : undefined}
						mode='date'
						onChange={(value) => this.handlePropertyValueChanged(name, moment.isMoment(value) ? value.format(util.DateFormat) : value)}
						onBlur={(value) => this.handlePropertyValueChanged(name, moment.isMoment(value) ? value.format(util.DateFormat) : value)}
						dateFormat={util.DateFormat}
						visibilityToggle={sensitive}
						noVisibilityToggle={!this._hasSensitiveProps}
						showEyeTitle={t('ShowPropertyValue')}
						hideEyeTitle={t('HidePropertyValue')}
					/>
				);
			case util.DataType.DateTime:
				return (
					<CDateTimePicker
						placeholder={placeHolder}
						value={!!currentValue ? moment(currentValue) : undefined}
						mode='dateTime'
						onChange={(value) => this.handlePropertyValueChanged(name, moment.isMoment(value) ? value.format(util.DateTimeFormat) : value)}
						onBlur={(value) => this.handlePropertyValueChanged(name, moment.isMoment(value) ? value.format(util.DateTimeFormat) : value)}
						dateFormat={util.DateFormat}
						timeFormat='HH:mm:ss'
						visibilityToggle={sensitive}
						noVisibilityToggle={!this._hasSensitiveProps}
						showEyeTitle={t('ShowPropertyValue')}
						hideEyeTitle={t('HidePropertyValue')}
					/>
				);
			default: return null;
		}
	}

	handleUserDrawerSave = () => {
		if (this._saving) return;

		this._saving = true;
		const newUser: any = this.props.editingUser ? { ...this.props.editingUser } : {};
		newUser.username = this.props.editingUser ? this.state.newUser.username : (this.state.newUser.username && this.state.newUser.username.trim());
		newUser.email = this.state.newUser.email && this.state.newUser.email.trim();
		newUser.mobile = this.state.newUser.mobile && this.state.newUser.mobile.trim();
		newUser.firstName = this.state.newUser.firstName && this.state.newUser.firstName.trim();
		newUser.lastName = this.state.newUser.lastName && this.state.newUser.lastName.trim();
		newUser.fullName = this.state.newUser.fullName && this.state.newUser.fullName.trim();
		newUser.password = this.state.newUser.password;
		newUser.confirmPassword = this.state.newUser.confirmPassword;
		newUser.passwordExpirationPolicy = this.state.newUser.passwordExpirationPolicy;
		newUser.roles = this.state.newUser.roles;
		newUser.customProperties = this.state.newUser.customProperties;
		newUser.organizationId = this.props.selectedOrganizationId;
		newUser.enabled = this.props.editingUser && this.props.editingUser.status ? this.props.editingUser.status === 1 : true;
		newUser.managerId = this.state.newUser.managerId;
		newUser.homePageId = this.state.newUser.homePageId;

		const helpMsg: any = {};
		helpMsg.password = this.validateNewUser('password', newUser.password);
		helpMsg.confirmPassword = this.validateConfirmPassword(newUser.confirmPassword);
		helpMsg.email = this.validateNewUser('email', newUser.email);
		helpMsg.username = this.validateNewUser('username', newUser.username);
		helpMsg.mobile = this.validateNewUser('mobile', newUser.mobile);
		this.setState({ errors: helpMsg });

		const isValidProperties = this.props.properties.every(property => this.validatePropertyValue(newUser.customProperties[property.name], util.ContextValueTypes[property.valueType]));

		const isUserValid = !helpMsg.password && !helpMsg.confirmPassword && !helpMsg.email && !helpMsg.username && !helpMsg.mobile && isValidProperties;
		if (isUserValid) {
			const { isAddingUser } = this.props;
			if (isAddingUser)
				this.props.dispatch(userActionCreators.createUser(newUser));
			else
				this.props.dispatch(userActionCreators.updateUser(newUser));
			this._saving = false;
		} else {
			this._saving = false;
		}
	}

	render() {
		const { isAddingUser, editingUser, roles, properties, orgs, curUser, t } = this.props;
		const { newUser, errors } = this.state;

		const scrollbarProps = {
			style: { width: '100%', height: '100%' },
			renderThumbVertical: props => <div {...props} style={{ width: '4px' }} className='thumb-vertical' />,
		};

		const mappedRoles = (roles || []).map((r) => {
			return {
				text: r.displayName || getRoleDisplayName(r.name) || r.name,
				value: r.name,
				selected: newUser && newUser.roles.indexOf(r.name) > -1
			};
		}).filter(f => f.value !== RoleConsts.everyone.name && f.value !== RoleConsts.orgeveryone.name);

		const mappedOrgs: DropdownItemProps[] = (orgs || []).sort((o1, o2) => {
			const pathOrder = o1.path.localeCompare(o2.path);
			return pathOrder !== 0 ? pathOrder : o1.order - o2.order;
		}).map(o => ({
			text: o.path === '/' ? t('common:globalOrgName') : o.path,
			value: o.id,
		}));
		if (!curUser.orgId) mappedOrgs.unshift({ text: t('common:globalOrgName'), value: 'global' });
		const selectedManagedOrg = mappedOrgs.find(o => newUser.managerId === o.value);
		this._hasSensitiveProps = properties?.some(p => p.sensitive);

		return (
			<div className='user-editor'>
				<Drawer
					className='account-management-panel'
					open={true}
					title={isAddingUser ? t('AddUser') : t('EditUser')}
					closeButtonTitle={t('Close')}
					onDismiss={this.closeEditor}
					bodyRender={() => {
						return (
							<Scrollbars {...scrollbarProps}>
								<div className='form-item'>
									<label className='required' title={t('Username')}>{t('Username')} </label>
									<CInputEditor
										value={newUser.username || ''}
										disabled={isAddingUser ? false : true}
										placeholder={t('Username')}
										onEveryChange={this.updateUserName}
										onChange={this.updateUserName}
										noVisibilityToggle={!this._hasSensitiveProps}
									/>
									{errors.username && <span className='error-text'>{errors.username}</span>}
								</div>
								<div className='form-item'>
									<label className={(this.props.isAddingUser || util.isLocalUser(newUser)) ? 'required' : ''}>{t('Email')} </label>
									<CInputEditor value={newUser.email} type='email' placeholder={t('Email')} onEveryChange={this.updateUserEmail} onChange={this.updateUserEmail} noVisibilityToggle={!this._hasSensitiveProps} />
									{errors.email && <span className='error-text'>{errors.email}</span>}
								</div>
								<div className='form-item'>
									<Label text={t('Mobile')} />
									<CInputEditor value={newUser.mobile} placeholder={t('Mobile')} onEveryChange={this.updateUserMobile} onChange={this.updateUserMobile} noVisibilityToggle={!this._hasSensitiveProps} />
									{errors.mobile && <span className='error-text'>{errors.mobile}</span>}
								</div>
								<div className='form-item'>
									<Label text={t('FirstName')} />
									<CInputEditor value={newUser.firstName} placeholder={t('FirstName')} onChange={this.updateUserFirstName} noVisibilityToggle={!this._hasSensitiveProps} />
								</div>
								<div className='form-item'>
									<Label text={t('LastName')} />
									<CInputEditor value={newUser.lastName} placeholder={t('LastName')} onChange={this.updateUserLastName} noVisibilityToggle={!this._hasSensitiveProps} />
								</div>
								<div className='form-item'>
									<Label text={t('FullName')} />
									<CInputEditor value={newUser.fullName} placeholder={t('FullName')} onChange={this.updateUserFullName} noVisibilityToggle={!this._hasSensitiveProps} />
								</div>
								{(isAddingUser || util.isLocalOrSyncedUser(editingUser)) && < div className='form-item' >
									<label className={(isAddingUser || util.isLocalUser(editingUser)) ? 'required' : ''} title={t('Password')}>{t('Password')}</label>
									<CInputEditor type='password' value={newUser.password || ''} placeholder={t('Password')} onEveryChange={this.updateUserPassword} onChange={this.updateUserPassword} noVisibilityToggle={!this._hasSensitiveProps} />
									{errors.password && <span className='error-text'>{errors.password}</span>}
								</div>}
								{(isAddingUser || util.isLocalOrSyncedUser(editingUser)) && < div className='form-item' >
									<label className={(isAddingUser || util.isLocalUser(editingUser)) ? 'required' : ''} title={t('ConfirmPassword')}>{t('ConfirmPassword')}</label>
									<CInputEditor type='password' value={newUser.confirmPassword || ''} placeholder={t('ConfirmPassword')} onEveryChange={this.updateUserConfirmPassword} onChange={this.updateUserConfirmPassword} noVisibilityToggle={!this._hasSensitiveProps} />
									{errors.confirmPassword && <span className='error-text'>{errors.confirmPassword}</span>}
								</div>}
								{window.AdminPortal.Edition === 'zh' && (isAddingUser || util.isLocalOrSyncedUser(editingUser)) && <div className='form-item'>
									<Checkbox className='password-policy-checkbox' text={t('PasswordNeverExpire')} checked={newUser.passwordExpirationPolicy === 1} value={newUser.passwordExpirationPolicy} onChange={(value) => this.setPasswordPolicy(value)} />
								</div>}
								{isAddingUser && <div className='form-item'>
									<Label text={t('Roles')} />
									{roles && <CCheckList
										text={(newUser.roles || []).map(s => getRoleDisplayName(s)).join(', ')}
										disabled={null !== editingUser && editingUser.username === this._adminUserName}
										items={mappedRoles}
										onSelect={(value) => this.handleRoleSelected(value)}
										noVisibilityToggle={!this._hasSensitiveProps}
									/>}
								</div>}
								{isAddingUser && window.AdminPortal.Edition !== 'en' && <div className='form-item'>
									<Label text={t('HomePage')} />
									<HomePageSelector onHomePageSelectorChange={this.onHomePageSelectorChange} t={t} homePageId={undefined} noVisibilityToggle={!this._hasSensitiveProps} />
								</div>}
								<div className='form-item'>
									<Label text={t('ManagedBy')} />
									<CDropdownEditor
										text={selectedManagedOrg?.text}
										items={mappedOrgs}
										onChange={(value) => this.handleManagerSelected(value)}
										noVisibilityToggle={!this._hasSensitiveProps}
									/>
								</div>
								{properties && properties.map(this.buildPropertiesDrawerEditor)}
							</Scrollbars>
						);
					}}

					footerRender={() => {
						return <div>
							<Button
								style='accent'
								inline={true}
								onClick={this.handleUserDrawerSave}
								title={t('Save')}
								text={t('Save')}
								aid='user-management-save-user'
							/>
							{' '}
							<Button
								inline={true}
								onClick={this.closeEditor}
								title={t('Cancel')}
								text={t('Cancel')}
								aid='user-management-cancel-save-user'
							/>
						</div>;
					}}
				/>
			</div>

		);
	}
}

export const UserEditor = connect(
	(state: { user: UserState, navApp: { user: User } }) => ({
		isAddingUser: state.user.isAddingUser,
		editingUser: state.user.editingUser,
		users: state.user.allUsers,
		roles: state.user.roles,
		properties: state.user.properties,
		passwordPolicy: state.user.passwordPolicy,
		selectedOrganizationId: state.user.selectedOrganizationId,
		orgs: state.user.organizations,
		curUser: state.navApp.user
	})
)(UserEditorInner) as React.ComponentClass<{}>;