﻿.empty-page-container {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	background-color: inherit !important;
	width: 100%;
	height: 100%;

	.empty-page-bg {
		flex: 0 0 212px;
		width: 308px;
		margin: 5px;
		background-repeat: no-repeat;
	}

	.empty-page-tip {
		display: flex;
		justify-content: center;
		align-items: center;
		color: $ef-text;
		font-size: $ef-font-size-lg;
		padding: 10px;
		width: 100%;

		.main-tip {
			opacity: 0.62;
			display: inline-block;
			max-width: 90%;
			text-align: center;

			@include gces-truncate;
		}

		.empty-page-select-btn {
			background: none;
			border: none;
			color: $ef-accent;
			font-size: $ef-font-size-lg;

			span {
				padding: 0 5px;
				text-decoration: underline;
			}

			&:hover {
				color: $ef-accent;
				background: none;
			}
		}
	}
}
