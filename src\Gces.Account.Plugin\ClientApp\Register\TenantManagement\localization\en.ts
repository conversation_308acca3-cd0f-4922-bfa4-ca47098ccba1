export const tenantEN: LanguageKeyValueMap = {
	// section
	'account-management-organization!title': 'Organizations',
	'account-management-organization!description': 'Organization Management',

	Yes: 'Yes',
	Close: 'Close',
	tntTenantSchema: 'Organization Schema',
	tntAddTenant: 'Add Organization',
	tntEditTenant: 'Edit Organization',
	tntNoItemsTip: 'no items',
	tntAddProp: 'Add Property',
	tntAddTenantProp: 'Add Organization Property',
	tntEditTenantProp: 'Edit Organization Property',
	tntPropName: 'Property Name',
	tntPropVlue: 'Property Value',
	tntPropValueType: 'Property Value Type',
	tntPropValueType_String: 'String',
	tntPropValueType_Boolean: 'Boolean',
	tntPropValueType_Integer: 'Integer',
	tntPropValueType_Float: 'Float',
	tntPropValueType_Date: 'Date',
	tntPropValueType_DateTime: 'DateTime',
	tntRequired: 'Required',
	tntMultivalued: 'Multivalued',
	tntSensitive: 'Sensitive',
	tntShowValue: 'Show value',
	tntHideValue: 'Hide value',
	tntClose: 'Close',
	tntDelete: 'Delete Forever',
	tntEdit: 'Edit',
	tntName: 'Name',
	tntFromEmail: 'From Email',
	tntMultiLineTip: 'One value per line',
	tntAdd: 'Add',
	tntSave: 'Save',
	tntCancel: 'Cancel',
	tntTenantMembers: 'Organization Members',
	tntSelectMembers: 'Select Members',
	tntUsername: 'Username',
	tntEmail: 'Email',
	tntProvider: 'Provider',
	tntMembers: '{{count}} member(s)',
	tntDeleteTenantProp: 'Delete Organization Property',
	tntDeleteTenantPropConfirmMessage: 'Do you want to delete organization property "{{tenantPropName}}" permanently?',
	tntDeleteTenant: 'Delete Organization',
	tntDeleteTenantConfirmMessage: 'Do you want to delete organization "{{tenantName}}" permanently?',
	tntNoMemberTip: 'No Users in the organization. Please click ',
	tntAddMember: 'Add',

	tntTenantBasicInformation: 'Basic Information',
	tntTenantRoles: 'Organization Roles',
	tntSelectRoles: 'Select Roles',
	tntTenantMoveUp: 'Move Up',
	tntTenantMoveDown: 'Move Down',
	tntExpandAll: 'Expand All',
	tntCollapseAll: 'Collapse All',
	tntActions: 'Actions',
	tntGlobal: 'Global',
	tntRoleName: 'Role Name',
	tntUsersNumber: 'Users Number',
	tntNewOrganization: 'NewOrganization',
	tntTenantPermissions: 'Permissions',
	tntTenantPermissionScope: 'Permission Scope',
	tntModifyPermissionScope: 'Modify Permission Scope',
	tntExpand: 'Expand',
	tntCollapse: 'Collapse',
	tntOrganizationName: 'Organization Name',
	tntModifyPermissionScopeMessage: 'Are you sure you want to modify the permission scope of the "{{tenantName}}" organization?',
	tntModifyPermissionScopeMessageWithTenant: 'This modification will also delete the following permission scopes for the following sub-organizations:',
	tntModifyPermissionScopeMessageWithRole: 'This modification will also delete the following permissions for the following roles:',
	tntNoRolesTip: 'No Roles in the organization.',
	tntNoPermissionsTip: 'No Permissions in the organization.',
	tntPermissionsName: 'Name',
	tntPermissionsDescription: 'Description',
	tntInvisible: 'Invisible',
	tntDisableSubView: 'Disable sub-organization view value',
	tntDisableSubEdit: 'Disable Sub-organization edit value',

	tntErrorOrganizationNameNull: 'Organization name is required.',
	tntErrorOrganizationNameDuplicated: 'Organization name is duplicated.',
	tntErrorInvalidCharInName: 'Organization name could not contain characters: < > / \\ $',
	tntErrorFromPropNull: 'Property value is required.',

	tntErrorOrganizationPropNameNull: 'Organization property name is required.',
	tntErrorOrganizationPropNameDuplicated: 'Organization property name is duplicated.',

	error_5001: 'Organization name can not be empty.',
	error_5002: 'Organization with name "{{TenantName}}" already exists.',
	error_5003: 'Organization property name can not be empty.',
	error_5004: 'Organization property with name "{{TenantPropName}}" already exists.',
	error_5005: 'The tenant property name "{{TenantPropName}}" is reserved.',
	error_5006: 'The from email "{{FromEmail}}" already exists.',

	changeOrganization: 'Move Organization',
	organizationDragAbove: 'Above the current organization',
	organizationDragBelow: 'Below the current organization',
	organizationDragSub: 'Into a sub-organization of the current organization',
	moveOrganization: 'Move Organization "{{tenantName}}"',
	OK: 'OK',
	Cancel: 'Cancel',

	rt_organization: 'organization',
	'rt_organization property': 'organization property',

	error_V2_007_005_0005: 'Invalid organization property values detected.',
	error_V2_007_005_0012: 'The permissions of the child organization cannot be larger than those of the parent organization, so please modify the organization permission scope before proceeding with the operation.',

	strictPermissionOrgTip: 'Organization permission scope limits the maximum permissions of its roles and sub-organizations.',
	loosePermissionOrgTip: 'Organization permission represents the permissions of the Everyone role in the organization.',
	globalPermissionTip: 'Permission for the Global organization cannot be changed.',
};