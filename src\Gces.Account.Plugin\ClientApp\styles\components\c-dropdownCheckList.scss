.ef-dd-menu {
	.ef-dd-menu-scrollbar {
		.btn-default.toolbar-btn {
			background-color: transparent !important;
		}

		.btn.selected {
			background-color: transparent !important;
		}
	}

	.c-dropdown-check-list-toolbar-container {
		padding: 5px 1px;
		border-bottom: 1px solid var(--gces-ef-text-inv);

		.c-dropdown-check-list-toolbar {
			display: flex;
			flex-direction: row;
			justify-content: space-around;
			height: 40px;

			.c-dropdown-check-list-search {
				.tb-search-box {
					flex: 1;
					background-color: transparent;

					.sb-icon {
						flex: 0 0 24px;
					}

					.efc-textbox {
						color: var(--gces-ef-text-inv);
						font-size: var(--gces-ef-font-size-sm) !important;

						&::placeholder {
							color: var(--gces-ef-text-inv);
						}
					}
				}
			}
		}
	}

	.c-dropdown-check-list-item {
		cursor: pointer;
		margin-left: 5px;
		display: flex;
		flex-direction: row;
		align-items: center;

		.efc-checkbox {
			display: flex;
			flex-direction: row;
			align-items: center;

			span {
				color: var(--gces-ef-text-inv);
				max-width: 240px;

				@include gces-truncate;
			}
		}
	}
}