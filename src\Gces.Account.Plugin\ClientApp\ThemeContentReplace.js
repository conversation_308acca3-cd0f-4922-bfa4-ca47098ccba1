const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { NormalModule } = require('webpack');

const needRemoveRegex = new RegExp("@import[\\s]+[\"']\\./(light|ars|blue|red|forest|dark\\-forest|green|warm\\-light|minimal\\-blue)\\.scss[\"'];?", "ig");
const replaceRegex = new RegExp("@import[\\s]+[\"']\\./default\\.scss[\"'];?", "i");

const cachePath = path.resolve(__dirname, 'node_modules', '.cache', 'theme', process.env.NODE_ENV === 'development' ? 'development' : 'production');
fs.mkdirSync(cachePath, { recursive: true });
const contentCache = new Map();

function pathContent(themeContent, indexContent) {
	return indexContent.replace(needRemoveRegex, "").replace(replaceRegex, themeContent);
}

function ThemeContentReplaceLoader(content, map, meta) {
	const dir = path.dirname(this.resourcePath);
	const dirSeg = dir.split(path.sep).slice(-2);
	if (dirSeg[0] === 'styles' && dirSeg[1] === 'themes') {
		const callback = this.async();
		const indexPath = path.join(dir, 'index.scss');
		fs.readFile(indexPath, 'utf-8', function (err, data) {
			if (err) {
				callback(err);
				return;
			}
			if (process.env.NODE_ENV !== 'development') {
				const fileName = computeHashPath(path.relative(__dirname, indexPath));
				const fileContent = computeHashPath(data);
				const dest = path.resolve(cachePath, fileName);
				fs.writeFileSync(dest, fileContent);
			}
			callback(null, pathContent(content, data));
		});
	}
	else {
		this.callback(null, content, map, meta);
		return;
	}
}

class ThemeContentReplacePlugin {
	constructor(options) {
		this.options = options;
	}

	apply(compiler) {
		if (!this.options.enabled) {
			return;
		}
		compiler.hooks.compilation.tap('ThemeContentReplacePlugin', (compilation) => {

			NormalModule.getCompilationHooks(compilation).needBuild.tapPromise('ThemeContentReplacePlugin', (module, context) => {
				const themeContext = module?.resourceResolveData?.["__themeContext"];
				if (!themeContext) {
					return Promise.resolve(false);
				}
				return new Promise((resolve, reject) => {
					fs.readFile(themeContext, 'utf-8', (err, data) => {
						if (err) {
							reject(err);
							return;
						}
						try {
							const fileName = computeHashPath(path.relative(__dirname, themeContext));
							const fileContent = computeHashPath(data);

							const dest = path.resolve(cachePath, fileName);
							const cacheKey = `${dest}:${fileContent}`;

							const r = contentCache.get(cacheKey);
							if (r !== undefined) {
								return resolve(r);
							}
							if (fs.existsSync(dest)) {
								const oldContent = fs.readFileSync(dest, 'utf-8');
								if (oldContent === fileContent) {
									contentCache.set(cacheKey, false);
									resolve(false);
									return;
								}
							}
							fs.writeFileSync(dest, fileContent, 'utf-8');
							contentCache.set(cacheKey, true);
							resolve(true);
						}
						catch (e) {
							reject(e);
						}
					})
				});
			});
		});

		compiler.hooks.normalModuleFactory.tap('ThemeContentReplacePlugin', (normalModuleFactory) => {
			normalModuleFactory.hooks.module.tap('ThemeContentReplacePlugin', (createdModule, createData, resolveData) => {
				const { resource } = createData;
				const dir = path.dirname(resource);
				const dirSeg = dir.split(path.sep).slice(-2);
				if (dirSeg[0] === 'styles' && dirSeg[1] === 'themes') {
					createdModule.resourceResolveData = createdModule.resourceResolveData || {};
					createdModule.resourceResolveData["__themeContext"] = path.join(dir, "index.scss");
				}
				return createdModule;
			});
		});
	}
}

function computeHashPath(content) {
	const hash = crypto.createHash('sha256');
	const text =
		hash
			.update(content)
			.digest('hex');
	return text;
}

module.exports = ThemeContentReplaceLoader;
module.exports.ThemeContentReplacePlugin = ThemeContentReplacePlugin;