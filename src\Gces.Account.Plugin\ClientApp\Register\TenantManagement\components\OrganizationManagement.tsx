import * as React from 'react';
import { connect } from 'react-redux';
import { OrganizationState, tenantActionCreators } from '../store';
import { BlockLoader } from 'gces-ui';
import { OrganizationsPanel } from './OrganizationsPanel';
import { OrganizationDetailEditor } from './OrganizationDetailEditor';
import { Scrollbars } from 'gces-react-custom-scrollbars';

interface ConnectedProps {
	busy: boolean;
	dispatch: any;
	t: any;
}

class OrganizationManagementInner extends React.PureComponent<ConnectedProps> {
	componentWillMount() {
		this.props.dispatch(tenantActionCreators.init());
	}

	render() {
		const scrollbarsProps = {
			style: { width: '100%', height: '100%' },
			renderThumbHorizontal: props => <div {...props} style={{ height: '4px' }} className='thumb-horizontal' />,
			autoHide: true
		};
		return (
			<Scrollbars {...scrollbarsProps}>
				<div className='tenant-management'>
					<OrganizationsPanel />
					<OrganizationDetailEditor />
					{this.props.busy && <BlockLoader />}
				</div>
			</Scrollbars>
		);
	}
}

export const OrganizationManagement = connect(
	(state: { tenant: OrganizationState }) => ({
		busy: state.tenant.busy,
	})
)(OrganizationManagementInner) as React.ComponentClass<{}>;