import * as React from 'react';
import { translate } from 'react-i18next';
import { connect } from 'react-redux';
import { OrganizationItem, tenantActionCreators, OrganizationState, RolesDic } from '../store';
import EmptyPage from '../../../components/EmptyPage';
import CGrid from 'gces-react-grid';

interface ConnectedProps {
	items: OrganizationItem[];
	selected: string;
	showMembersEditor: boolean;
	showTenantEditor: boolean;
	rolesDic: RolesDic;
	dispatch: any;
	t: any;
}

@translate('organization', { wait: true })
class OrganizationRolesTabInner extends React.PureComponent<ConnectedProps> {
	componentWillMount() {
		this.props.dispatch(tenantActionCreators.getTenantRoles(this.props.selected));
	}
	componentWillReceiveProps(nextProps: ConnectedProps) {
		if (nextProps.selected !== this.props.selected) {
			this.props.dispatch(tenantActionCreators.getTenantRoles(nextProps.selected));
		}
	}

	render() {
		const { t, rolesDic, selected } = this.props;
		const roles = rolesDic[selected] || [];

		if (roles && roles.length) {
			const rowData = roles.map(role => {
				return {
					name: role.name,
					count: role.members ? role.members.length : 0,
				};
			});

			const gridProps = {
				rows: rowData,
				hideGridLine: true,
				columnResizing: false,
				columns: [
					{ key: 'name', label: t('tntRoleName') },
					{ key: 'count', label: t('tntUsersNumber') },
				]
			};
			return (<CGrid {...gridProps} />);
		}
		else {
			const emptyPageProps = {
				imageName: 'locked-user-management',
				tip: t('tntNoRolesTip'),
			};
			return (<EmptyPage {...emptyPageProps} />);
		}
	}
}

export const OrganizationRolesTab = connect(
	(state: { tenant: OrganizationState }) => ({
		items: state.tenant.items,
		selected: state.tenant.selected,
		showMembersEditor: state.tenant.showMembersEditor,
		showTenantEditor: state.tenant.showTenantEditor,
		rolesDic: state.tenant.rolesDic,
	})
)(OrganizationRolesTabInner) as React.ComponentClass<{}>;