.synchronization-settings {
	display: flex;
	height: 100%;
	font-size: 12px;

	.synchronize-settings-header {
		display: flex;
		align-items: center;

		.synchronize-settings-title {
			font-size: 12px;
			margin-right: 5px;
			margin-bottom: 2px;
			color: var(--gces-ef-accent);
		}

		.mdi {
			margin-right: 3px;

			&.mdi-information-outline {
				color: var(--gces-ef-accent);
			}
		}

		.synchronize-settings-header-divider {
			flex: auto;
			margin: 2px 5px 0 2px;
			background-color: var(--gces-ef-accent);
		}

		.synchronize-settings-header-operation {
			display: flex;
			color: var(--gces-ef-accent);
			cursor: pointer;
			align-items: center;

			.mdi {
				font-size: 18px;
			}

			span {
				margin-right: 10px;
			}
		}
	}

	@import './settings.scss';

	.synchronization-settings-divider {
		width: 1px;
		height: 98%;
	}

	@import './view.scss';
}