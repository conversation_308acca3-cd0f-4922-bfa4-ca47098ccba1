import { UserInfo } from '../store';
import * as util from '../../../util';

export function* sendRequestV2(url: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET', data: any = null) {
	return yield util.sendRequestV2(url, method, data, 'user');
}

export const isAdministrator = (roles: string[] = []) => {
	return roles.some(role => 'administrator' === role.toLowerCase());
};

export const hasManageUserPermission = (roles: string[] = []) => {
	return roles.some(role => 'manage-user' === role.toLowerCase());
};

export const hasAssignManageUserPermission = (roles: string[] = []) => {
	return roles.some(role => 'assign-manage-user' === role.toLowerCase());
};

export const updateUserInfo = (user: UserInfo, allUsers: UserInfo[] = []) => {
	let users = [...allUsers];
	user.organizations = user.tenantRoles ? Object.keys(user.tenantRoles) : [];
	const userIndex = users.findIndex(u => u.id === user.id);
	if (userIndex > -1)
		users[userIndex] = user;
	else
		users = users.concat([user]);
	return users;
};

export const removeOrgOfUser = (userId: string, orgId: string, allUsers: UserInfo[] = [], allOrganizations) => {
	const users = [...allUsers];
	const userIndex = users.findIndex(u => u.id === userId);
	const orgIndex = allOrganizations.findIndex(o => o.id === orgId);
	const orgPath = allOrganizations[orgIndex].path;
	if (userIndex > -1 && orgIndex > -1) {
		users[userIndex].organizations = users[userIndex].organizations.length ? users[userIndex].organizations.filter(o => o !== orgPath) : [];
		const tenantRoles = users[userIndex].tenantRoles ? Object.keys(users[userIndex].tenantRoles) : [];
		const newTenantRoles = {};
		tenantRoles.forEach(tr => {
			if (tr !== orgPath) {
				newTenantRoles[tr] = users[userIndex].tenantRoles[tr];
			}
		});
		users[userIndex].tenantRoles = newTenantRoles;
	}
	return users;
};

export const updateOrgUsers = (userIds: string[] = [], orgId: string, allUsers: UserInfo[], allOrganizations) => {
	const users = [...allUsers];
	const userIndexes = userIds.map(userId => users.findIndex(u => u.id === userId));
	const orgIndex = allOrganizations.findIndex(o => o.id === orgId);
	const orgPath = allOrganizations[orgIndex].path;
	if (orgIndex > -1) {
		users.forEach(u => {
			if (Object.keys(u.tenantRoles).findIndex(org => org === orgPath) > -1) {
				u.organizations = u.organizations.length ? u.organizations.filter(o => o !== orgPath) : [];
				const tenantRoles = u.tenantRoles ? Object.keys(u.tenantRoles) : [];
				const newTenantRoles = {};
				tenantRoles.forEach(tr => {
					if (tr !== orgPath) {
						newTenantRoles[tr] = u.tenantRoles[tr];
					}
				});
				u.tenantRoles = newTenantRoles;
			}
		});
		userIndexes.forEach(userIndex => {
			if (users[userIndex].organizations.findIndex(uo => uo === orgPath) === -1) {
				users[userIndex].organizations.push(orgPath);
			}
			users[userIndex].tenantRoles[orgPath] = [];
		});
	}
	return users;
};