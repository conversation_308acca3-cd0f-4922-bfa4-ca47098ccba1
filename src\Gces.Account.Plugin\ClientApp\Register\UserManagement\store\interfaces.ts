export interface UserState {
    busy: boolean;
    allUsers: UserInfo[];
    properties: UserProperty[];
    roles: RoleItem[];
    passwordPolicy: number;
    editingUser: UserInfo;
    isAddingUser: boolean;
    isImportingUser: boolean;
    isSelectingMembers: boolean;
    importResult: ImportResult;
    searchText: string;
    organizations: Organization[];
    selectedOrganizationId: string;
    detailUserId: string;
    showMembersOfSubOrg: boolean;
}

export interface UserProperty {
	id: string;
	name: string;
	valueType: number;
	showInList: boolean;
	showInProfile: boolean;
	allowUserEdit: boolean;
	multivalued: boolean;
	availableValues: string[];
	sensitive: boolean;
}

export interface UserInfo {
    id?: string;
    username: string;
    creationTime?: string;
    email: string;
    status?: 1 | 2;
    mobile?: string;
    firstName?: string;
    fullName?: string;
    lastName?: string;
    provider?: string;
    roles?: string[];
    organizations?: string[];
    tenantRoles?: any;
    password?: string;
    confirmPassword?: string;
    passwordExpirationPolicy?: number;
    customizeProperties?: any;
    customProperties?: any;
    avatar?: any;
    extraClaims?: any;
    organizationIdPath: string;
    homePageId?: string;
}

export interface Error {
    username?: string;
    email?: string;
    mobile?: null;
    password?: string;
    confirmPassword?: null;
}

export interface RoleItem {
    allowEditPermissions: boolean;
    createTime: string;
    creatorId: string;
    id: string;
    isBuiltin: boolean;
    members: any;
    name: string;
    permissions: string[];
    tenantId: string;
    displayName: string;
}

export interface ImportResult {
    importedUserCount: number;
    notImportedUserCount: number;
    errorMsg?: string;
    detailKey: string;
}

export interface Organization {
    id: string;
    name: string;
    order: number;
    parentTenantId: string;
    path: string;
}

export interface SimplifiedUserInfo {
    id?: string;
    username: string;
}