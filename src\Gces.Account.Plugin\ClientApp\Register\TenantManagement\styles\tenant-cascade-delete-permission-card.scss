@at-root {
	.cascade-delete-permission-dialog {
		.confirm-dialog {
			width: 700px;

			.text-content {
				margin: 0 !important;

				.cascade-delete-permission-title {
					padding: 5px 0 13px 0;
				}

				.cascade-delete-permission-card {
					margin-bottom: 10px;
					padding: 5px;
					background-color: var(--gces-text-contrast-dk-15);

					.cascade-delete-permission-card-header {
						height: 33px;
						display: flex;
						justify-content: space-between;
						align-items: center;

						.card-name-container {
							display: flex;
							width: 100%;
							align-items: center;
							font-weight: bold;

							span {
								display: block;
								flex: 1;
								min-width: 0;

								@include gces-truncate;
							}
						}
					}

					.cascade-delete-permission-card-body {
						height: 200px;

						.c-grid-wrapper {
							padding: 5px;

							.c-grid {
								border: none;
								background: transparent;
							}

							.cg-header {
								.cg-row {
									border: none;
									background-color: var(--gces-panels-bg);
								}
							}

							.cg-body {
								.cg-row {
									border: none;

									&.even {
										background-color: var(--gces-panels-bg);
									}

									&.odd {
										background-color: var(--gces-text-contrast-dk-15);
									}
								}
							}

							.header-cell,
							.row-cell {
								height: 100%;
								width: 100%;

								@include gces-truncate;
							}
						}
					}
				}
			}
		}
	}
}
