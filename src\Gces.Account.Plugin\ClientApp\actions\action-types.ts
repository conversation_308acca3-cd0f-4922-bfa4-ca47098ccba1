const AccountManagementModelName = 'ACCOUNTMANAGEMENT';

// Common
const StartLoading = `${AccountManagementModelName}_StartLoading`;
const EndLoading = `${AccountManagementModelName}_EndLoading`;

// Users
const SetUsers = `${AccountManagementModelName}_SetUsers`;

// Customize Properties
const SetProperties = `${AccountManagementModelName}_SetProperties`;

// Locked Users
const SetLockedUsers = `${AccountManagementModelName}_SetLockedUsers`;

// Concurrence Status
const GetConcurrenceStatus = `${AccountManagementModelName}_GetConcurrenceStatus`;
const SetConcurrenceStatus = `${AccountManagementModelName}_SetConcurrenceStatus`;
const BanUserInConcurrence = `${AccountManagementModelName}_BanUserInConcurrence`;

// System Configurations
const SysConfigModelName = 'SYSCONFIG';

// License Activation
const SetLicense = `${SysConfigModelName}_SetLicense`;
const SetCVLicense = `${SysConfigModelName}_SetCVLicense`;

// Security Setting
const SetSecuritySettings = `${SysConfigModelName}_SetSecuritySettings`;
const SetSecuritySettingsBusy = `${SysConfigModelName}_SetSecuritySettingsBusy`;

// Security Setting Saga actions
const GetSecuritySettings = `${SysConfigModelName}_GetSecuritySettings`;
const SetSecuritySettingsSaga = `${SysConfigModelName}_SetSecuritySettingsSaga`;

// Claim Mappings
const SetClaimMappings = `${AccountManagementModelName}_SetClaimMappings`;
const SetClaimMappingsViewMode = `${AccountManagementModelName}_SetClaimMappingsViewMode`;
const SetDeletingClaimMapping = `${AccountManagementModelName}_SetDeletingClaimMapping`;

// Claim Mappings Saga actions
const GetClaimMappings = `${AccountManagementModelName}_GetClaimMappings`;
const GetProperties = `${AccountManagementModelName}_GetProperties`;
const AddClaimMapping = `${AccountManagementModelName}_AddClaimMapping`;
const DeleteClaimMapping = `${AccountManagementModelName}_DeleteClaimMapping`;

// Generate Token
const GenerateTokenModelName = 'GENERATETOKEN';
const SetBusy = `${GenerateTokenModelName}_SetBusy`;
const SetTokens = `${GenerateTokenModelName}_SetTokens`;
const SetUrls = `${GenerateTokenModelName}_SetUrls`;

// Generate Token Sage actions
const GetTokens = `${GenerateTokenModelName}_GetTokens`;
const RevokeToken = `${GenerateTokenModelName}_RevokeToken`;
const GenerateUrl = `${GenerateTokenModelName}_GenerateUrl`;
const SaveToken = `${GenerateTokenModelName}_SaveToken`;

// External Login Providers
const ExternalLoginProvider = 'ExternalLoginProvider';
const SyncData = `${ExternalLoginProvider}_SyncData`;

// Two Factor Authentication
const GetTFASettings = `${SysConfigModelName}_GetTFASettings`;
const SetTFASettings = `${SysConfigModelName}_SetTFASettings`;
const SetTFASettingsBusy = `${SysConfigModelName}_SetTFASettingsBusy`;
const SetTFASettingsSaga = `${SysConfigModelName}_SetTFASettingsSaga`;
const TestTFASettings = `${SysConfigModelName}_TestTFASettings`;
const SetTFASettingsValidated = `${SysConfigModelName}_SetTFASettingsValidated`;
const SetTFASettingsSaved = `${SysConfigModelName}_SetTFASettingsSaved`;
const GetEmailSettings = `${SysConfigModelName}_GetEmailSettings`;
const SetEmailSettings = `${SysConfigModelName}_SetEmailSettings`;

// Inactive Session Settings
const GetInactiveSessionSettings = `${SysConfigModelName}_GetInactiveSessionSettings`;
const SetInactiveSessionSettings = `${SysConfigModelName}_SetInactiveSessionSettings`;
const SetInactiveSessionSettingsSaga = `${SysConfigModelName}_SetInactiveSessionSettingsSaga`;
const SetInactiveSessionSettingsBusy = `${SysConfigModelName}_SetInactiveSessionSettingsBusy`;
const SetSaveInactiveSessionSettingsState = `${SysConfigModelName}_SetSaveInactiveSessionSettingsState`;
const GetAllUsers = `${SysConfigModelName}_GetAllUsers`;

const ActionTypes = {
    // common
    StartLoading,
    EndLoading,

    // account management
    SetUsers,

    // Property management
    SetProperties,

    // Locked User
    SetLockedUsers,

    // Concurrence
    SetConcurrenceStatus,

    // Concurrence Saga actions
    GetConcurrenceStatus,
    BanUserInConcurrence,

    // License Activation
    SetLicense,
    SetCVLicense,

    // Security Setting
    SetSecuritySettings,
    SetSecuritySettingsBusy,

    // Security Setting Saga actions
    GetSecuritySettings,
    SetSecuritySettingsSaga,

    // claim mappings
    SetClaimMappings,
    SetClaimMappingsViewMode,
    SetDeletingClaimMapping,

    // claim mappings saga actions
    GetClaimMappings,
    GetProperties,
    AddClaimMapping,
    DeleteClaimMapping,

    // Generate Token
    SetBusy,
    SetTokens,
    SetUrls,

    // Generate Token Saga actions
    GetTokens,
    RevokeToken,
    GenerateUrl,
    SaveToken,

    // External Login Providers
    SyncData,

    // Two Factor Authentication
    GetTFASettings,
    SetTFASettings,
    SetTFASettingsBusy,
    SetTFASettingsSaga,
    TestTFASettings,
    SetTFASettingsValidated,
    SetTFASettingsSaved,
    GetEmailSettings,
    SetEmailSettings,

    // Inactive Session Settings
    SetInactiveSessionSettings,
    SetInactiveSessionSettingsBusy,
    SetSaveInactiveSessionSettingsState,
    GetInactiveSessionSettings,
    SetInactiveSessionSettingsSaga,
    GetAllUsers,
};

export default ActionTypes;
