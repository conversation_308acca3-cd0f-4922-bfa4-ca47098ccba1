import * as React from 'react';
import { translate } from 'react-i18next';
import { connect } from 'react-redux';
import { OrganizationState, tenantActionCreators, TenantProp, PropValueType } from '../../store';
import { Button, Label, InputEditor, Checkbox, ErrorMessage, Dropdown, DropdownItemProps } from 'gces-ui';
import { ContextValueTypes } from '../../../../util';

export interface OrganizationPropEditorProps {
	editingId: string;
}

interface ConnectedProps {
	tenantProps: TenantProp[];
	dispatch: any;
	t: any;
}

interface LocalState {
	tenantProp: TenantProp;
}

@translate('organization', { wait: true })
class OrganizationPropEditorInner extends React.PureComponent<ConnectedProps & OrganizationPropEditorProps, LocalState> {
	state: LocalState = {
		tenantProp: { id: null, name: '', required: false, multivalued: false, valueType: PropValueType.String, sensitive: false }
	};

	componentWillMount() {
		this.updateStateIfNeed(this.props);
	}
	componentWillReceiveProps(nextProps: ConnectedProps & OrganizationPropEditorProps) {
		this.updateStateIfNeed(nextProps);
	}
	updateStateIfNeed = (newProps: ConnectedProps & OrganizationPropEditorProps) => {
		const { editingId, tenantProps } = newProps;
		if (editingId !== this.state.tenantProp.id) {
			if (editingId) {
				const prop = tenantProps.find(it => it.id === editingId);
				if (prop) this.setState({ tenantProp: JSON.parse(JSON.stringify(prop)) });
			} else {
				const tenantProp: TenantProp = { id: '', name: '', required: false, multivalued: false, valueType: PropValueType.String, sensitive: false };
				this.setState({ tenantProp });
			}
		}
	}

	validPropName = (propId: string, propName: string) => {
		const { t } = this.props;
		if (!propName) return { isValidName: false, errorMessage: t('tntErrorOrganizationPropNameNull') };
		const name = propName.trim().toUpperCase();
		const invalidProperties = ['ID', 'NAME', '名称'];
		const isValidName = (-1 === invalidProperties.findIndex(p => p === name)) && (-1 === this.props.tenantProps.findIndex(p => p.id !== propId && p.name.toUpperCase() === name));
		return { isValidName, errorMessage: !isValidName && t('tntErrorOrganizationPropNameDuplicated') };
	}

	onOKClick = () => {
		const { editingId, dispatch } = this.props;
		const { id, name, required, multivalued, valueType, sensitive } = this.state.tenantProp;
		if (editingId) {
			dispatch(tenantActionCreators.editTenantProp(id, name.trim(), required, multivalued, valueType, sensitive));
		} else {
			dispatch(tenantActionCreators.addTenantProp(name.trim(), required, multivalued, valueType, sensitive));
		}
	}
	onCancelClick = () => {
		this.setState({ tenantProp: { id: null, name: '', required: false, multivalued: false, valueType: PropValueType.String, sensitive: false } });
		this.props.dispatch(tenantActionCreators.setShowTenantPropEditor(false));
	}

	onNameChange = (name: string) => {
		this.setState({ tenantProp: { ...(this.state.tenantProp), name } });
	}
	onRequireChange = () => {
		this.setState({ tenantProp: { ...(this.state.tenantProp), required: !this.state.tenantProp.required } });
	}
	onMultivaluedChange = () => {
		this.setState({ tenantProp: { ...(this.state.tenantProp), multivalued: !this.state.tenantProp.multivalued } });
	}

	onValueTypeChange = (valueType: { type: string, index: number }) => {
		this.setState({ tenantProp: { ...(this.state.tenantProp), valueType: valueType.index } });
	}
	onSensitiveChange = () => {
		this.setState({ tenantProp: { ...(this.state.tenantProp), sensitive: !this.state.tenantProp.sensitive } });
	}

	render() {
		const { t, editingId } = this.props;
		const { id, name, required, multivalued, valueType, sensitive } = this.state.tenantProp;
		const { isValidName, errorMessage } = this.validPropName(id, name);

		const propValueTypeItems: DropdownItemProps[] = ContextValueTypes.map((type, index) => {
			return {
				value: { type, index },
				text: t(`tntPropValueType_${type}`),
				title: t(`tntPropValueType_${type}`),
				selected: valueType === index,
			};
		});

		return (
			<div className='tenant-prop-editor'>
				<div className='tpe-header'>
					{t(editingId ? 'tntEditTenantProp' : 'tntAddTenantProp')}
				</div>
				<div className='tpe-body'>
					<Label labelOnTop text={t('tntPropName')}>
						<InputEditor invalid={!isValidName} value={name} onEveryChange={this.onNameChange} />
						{!isValidName && <ErrorMessage message={errorMessage} />}
					</Label>
					<Label labelOnTop text={t('tntPropValueType')}>
						<Dropdown
							offset
							width='100%'
							menuWidth='100%'
							size='small'
							textAlign='left'
							text={t(`tntPropValueType_${ContextValueTypes[valueType]}`)}
							disabled={!!editingId}
							items={propValueTypeItems}
							onSelect={this.onValueTypeChange}
						/>
					</Label>
					<Checkbox text={t('tntRequired')} title={t('tntRequired')} onChange={this.onRequireChange} checked={required} value='required' />
					{valueType !== PropValueType.Boolean && <Checkbox text={t('tntMultivalued')} title={t('tntMultivalued')} onChange={this.onMultivaluedChange} checked={multivalued} value='multivalued' />}
					<Checkbox text={t('tntSensitive')} title={t('tntSensitive')} onChange={this.onSensitiveChange} checked={sensitive} value='sensitive' />
				</div>
				<div className='tpe-footer'>
					<Button text={t('tntCancel')} title={t('tntCancel')} onClick={this.onCancelClick} size='small' />
					<Button disabled={!isValidName} text={t(editingId ? 'tntSave' : 'tntAdd')} title={t(editingId ? 'tntSave' : 'tntAdd')} onClick={this.onOKClick} size='small' style='accent' />
				</div>
			</div>
		);
	}
}

export const OrganizationPropEditor = connect(
	(state: { tenant: OrganizationState }) => ({
		tenantProps: state.tenant.tenantProps,
	})
)(OrganizationPropEditorInner) as React.ComponentClass<OrganizationPropEditorProps>;