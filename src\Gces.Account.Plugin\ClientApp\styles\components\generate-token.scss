.generate-token {
	padding: 10px;

	.required::after {
		margin-left: 4px;
		content: "*";
		font-size: $ef-font-size-sm;
		color: $danger;
	}

	textarea {
		color: $ef-text !important;
		opacity: 1 !important;
	}

	.ef-control {
		.efc-label {
			span {
				display: inline-block;
				width: 100%;

				@include gces-truncate;
			}
		}

		.efc-content {
			button {
				max-width: 100px;
			}
		}
	}
}

.generate-token-container {
	height: 100%;
	flex-direction: row;
	display: flex;

	.left-panel {
		flex: 0 0 400px;
		border-right: 1px solid $ef-bg-dk;
		height: 98%;
		padding-right: 10px;
		position: relative;
	}

	.right-panel {
		padding: 0 20px;
		flex: 1;
		min-width: 680px;

		.token-list {
			position: relative;
			width: 100%;
			height: 100%;

			.toolbar-container {
				display: flex;
				align-items: center;

				.title {
					font-size: $ef-font-size-lg;
					font-weight: bold;
					max-width: 60%;

					span {
						display: block;
						max-width: 100%;

						@include gces-truncate;
					}
				}

				.search-token-input {
					margin-left: auto;
					margin-right: 0;
				}
			}

			.token-list-container {
				width: 100%;
				height: calc(100% - 30px);
				min-width: 650px;
				display: flex;
				flex-direction: column;

				.token-list-content {
					height: 100%;
					padding: 30px 5px 5px 5px;
					flex: 1;
				}
			}
		}
	}
}

.ReactModal__Overlay--after-open {
	z-index: 9999;
	display: flex !important;
	align-items: center;
	justify-content: center;

	.gt-generateUrl-dialog {
		display: flex;
		flex-direction: column;
		box-shadow: $ef-shadow-border;
		font-size: 14px;
		width: 700px;

		.gt-generateUrl-dialog-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color: $ef-accent;
			padding: 10px 20px;

			span {
				color: $text-contrast;
				font-size: 16px;
				line-height: 30px;
			}

			button {
				color: white !important;
				background-color: $ef-accent !important;
			}
		}

		.gt-generateUrl-dialog-body {
			height: 260px;
			padding: 20px;
			background-color: $ef-body-bg;
			word-wrap: break-word;

			.item {
				display: flex;
				align-items: center;

				button {
					flex: 0 0 30px;
					margin-left: 10px;
				}
			}

			.hide-avatar-control,
			.hide-welcome-screen-control {
				display: flex;

				.efc-content {
					display: flex;
				}
			}

			.generate-url-button {
				margin-bottom: 10px;
			}
		}

		.gt-generateUrl-dialog-footer {
			display: flex;
			justify-content: flex-end;
			background-color: $ef-bg-lt;
			padding: 10px 20px;

			button {
				margin-left: 6px;
			}
		}
	}
}
