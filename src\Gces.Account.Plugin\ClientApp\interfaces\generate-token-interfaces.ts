export interface HistoryToken {
	id: string;
	user: string;
	title: string;
	description: string;
	createdTime: string;
	expiredTime: string;
	organizationPath: string;
}
export interface TokenManagementState {
	tokens: HistoryToken[];
	portalUrl: string;
	adminPortalUrl: string;
	resourcePortalUrl: string;
	busy: boolean;
}

export interface GenerateTokenResponseModelV2 {
	accessToken: string;
	expiresIn: number;
	tokenType: string;
}

export interface SaveTokenRequestModelV2 {
	title: string;
	description: string;
	user: string;
	accessToken: string;
	expiresIn: number;
	tokenType: string;
	organizationPath: string;
}

export interface ErrorResponseModelV2 {
	code: string;
	message: string;
	context: any;
	innerError: any;
}