﻿import ActionTypes from '../actions/action-types';
import update from 'immutability-helper';

export default (state = { securitySettings: {}, busy: false }, action) => {
    switch (action.type) {
        case ActionTypes.SetSecuritySettings:
            return update(state, { securitySettings: { $set: action.securitySettings } });
        case ActionTypes.SetSecuritySettingsBusy:
            return update(state, { busy: { $set: action.busy } });
        default:
            return state;
    }
}