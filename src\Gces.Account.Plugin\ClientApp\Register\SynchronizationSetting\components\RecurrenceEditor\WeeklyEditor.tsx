import { Dropdown, DropdownItemProps } from 'gces-ui/lib/components/Dropdown';
import * as update from 'immutability-helper';
import * as React from 'react';
import { translate } from 'react-i18next';
import { ScheduledInfo, WeeklyRepeatDetail } from '../../store/interface';
import { generateNumbers } from '../../utils';
import { RecSettingEditor } from './RecSettingEditor';

const weeksRepeat = generateNumbers(1, 4);

interface WeeklyEditorProps {
	scheduleInfo: ScheduledInfo;
	updateScheduleInfo: (scheduleInfo: ScheduledInfo) => void;
	t?: any;
}

@translate('dataset-scheduling', { wait: true })
export class WeeklyEditor extends React.Component<WeeklyEditorProps> {

	onWeekSelect = (week: number) => {
		const { scheduleInfo, updateScheduleInfo } = this.props;
		const newSchedule = update(scheduleInfo, { detail: { repeatInterval: { $set: week } } });
		updateScheduleInfo(newSchedule);
	}

	render() {
		const { t, scheduleInfo: { repeatType, detail } } = this.props;

		if (repeatType !== 'Weekly') return null;

		const { repeatInterval } = detail as WeeklyRepeatDetail;
		const repeatItems: DropdownItemProps[] = weeksRepeat.map(week => ({ text: `${week}`, value: week, selected: week === repeatInterval }));

		return (
			<div className='sc-rec-weekly'>
				<RecSettingEditor text={t('weeklyEditorTextEvery')} units={t('weeklyEditorTextUnits')} value='weeks-every' noRadio inverted={window.inverted}>
					<Dropdown inverted={window.inverted} text={`${repeatInterval}`} items={repeatItems} menuWidth='100%' offset size='small' textAlign='left' position='center' onSelect={this.onWeekSelect} />
				</RecSettingEditor>
			</div>
		);
	}
}