﻿import ActionTypes from './../actions/action-types'
import update from 'immutability-helper'

export default (state = { concurrence: [] }, action) => {
    switch (action.type) {
        case ActionTypes.SetUsers:
            return update(state, { users: { $set: action.users } })

        case ActionTypes.SetConcurrenceStatus:
            return update(state, { concurrenceStatus: { $set: action.concurrenceStatus } })

        default:
            return state
    }
}
