import { put, select, takeEvery, all } from 'redux-saga/effects';
import * as Actions from './actions';
import { sendRequestV2 } from '../utils';
import { OrganizationState, OrganizationItem } from './interfaces';
import { getDefaultSelectedOrgId } from '../../../utils';
import { GlobalOrganization, isGlobalOrg } from '../../../util';

const organizationUrl = '/api/v2/identity/organizations';

const updateSystemStatistics = () => window.AdminPortal && window.AdminPortal.UpdateSystemStatistics && window.AdminPortal.UpdateSystemStatistics();

function* init() {
	yield put(Actions.tenantActionCreators.setBusy(true));
	const [tenantProps, items, enableStrictPermissionManagement] = yield all([getTenantProps(), getTenantItems(), getEnableStrictPermissionManagement()]);
	yield put(Actions.tenantActionCreators.setTenantItemsSelected(null));
	yield put(Actions.tenantActionCreators.setTenantProps(tenantProps));
	if (enableStrictPermissionManagement) {
		yield put(Actions.tenantActionCreators.getAvailablePermissions(items.find(item => items.findIndex(i => i.id === item.parentTenantId) === -1).id));
	} else {
		const availablePermissions = yield getAllPermissions();
		yield put(Actions.tenantActionCreators.setAvailablePermissions(availablePermissions));
	}
	yield put(Actions.tenantActionCreators.setTenantItems(items));
	yield put(Actions.tenantActionCreators.setIsEditing(false));
	yield put(Actions.tenantActionCreators.setIsAdding(false));
	yield setDefaultItem(items);
	yield put(Actions.tenantActionCreators.setBusy(false));
	yield put(Actions.tenantActionCreators.setEnableStrictPermissionManagement(enableStrictPermissionManagement));
}
function* getTenantProps() {
	const { result } = yield sendRequestV2(`${organizationUrl}/props`);
	return result ?? [];
}
function* getTenantItems() {
	const { tenantId } = (yield select()).navApp.user;
	const isGlobal = isGlobalOrg(tenantId);
	const url = isGlobal ? `${organizationUrl}/admin?includeProps=true` : `${organizationUrl}/${tenantId}/sub-organizations`;
	const { result, error } = yield sendRequestV2(url);
	if (error) return [];
	if (isGlobal) {
		const tenants = [...result];
		for (const t of tenants) {
			if (t.parentTenantId === null && t.id !== GlobalOrganization.Id) {
				t.parentTenantId = GlobalOrganization.Id;
			}
		}
		return tenants;
	}
	else {
		const org = yield sendRequestV2(`${organizationUrl}/${tenantId}`);
		if (org.result) {
			result.push(org.result);
		}
		return result;
	}
}
function* getAllPermissions() {
	const { result } = yield sendRequestV2('/api/v2/identity/permissions?enabled=true');
	return result ?? [];
}
function* getEnableStrictPermissionManagement() {
	const { result } = yield sendRequestV2('/api/v2/identity/organizations/permission-management-mode');
	return result.enableStrictPermissionManagement;
}
function* setDefaultItem(items) {
	const { tenantId } = (yield select()).navApp.user;
	const defaultId = getDefaultSelectedOrgId(items, tenantId);
	yield put(Actions.tenantActionCreators.setTenantItemsSelected(defaultId));
}

function* getTenantUsers(action: Actions.GetTenantUsersAction) {
	const { tenantId } = action.payload;
	yield put(Actions.tenantActionCreators.setBusy(true));
	const targetUrl = tenantId ? `${organizationUrl}/${action.payload.tenantId}/users` : '/api/v2/identity/users/non-org-users';
	const { result } = yield sendRequestV2(targetUrl);
	if (result) {
		yield put(Actions.tenantActionCreators.addUsersToDic({ [tenantId]: result }));
	}
	yield put(Actions.tenantActionCreators.setBusy(false));
}
function* getTenantRoles(action: Actions.GetTenantRolesAction) {
	const { tenantId } = action.payload;
	yield put(Actions.tenantActionCreators.setBusy(true));
	const targetUrl = tenantId ? `${organizationUrl}/${action.payload.tenantId}/roles` : '/api/v2/identity/roles/non-org-roles?includeMembers=true';
	const { result } = yield sendRequestV2(targetUrl);
	if (result) {
		yield put(Actions.tenantActionCreators.addRolesToDic({ [tenantId]: result }));
	}
	yield put(Actions.tenantActionCreators.setBusy(false));
}
function* getAvailablePermissions(action: Actions.GetAvailablePermissionAction) {
	const { tenantId } = action.payload;
	const permissions = yield sendRequestV2(`/api/v2/identity/organizations/${tenantId}/available-permissions`);
	yield put(Actions.tenantActionCreators.setAvailablePermissions(permissions.result));
}
function* getTenantPermissions(action: Actions.GetTenantRolesAction) {
	const { tenantId } = action.payload;
	yield put(Actions.tenantActionCreators.setBusy(true));
	const { result } = yield sendRequestV2(`${organizationUrl}/${tenantId}/permissions`);
	if (result) {
		yield put(Actions.tenantActionCreators.addPermissionsToDic({ [tenantId]: result }));
	}
	yield put(Actions.tenantActionCreators.setBusy(false));
}
function* updateUsersInTenant(action: Actions.UpdateUsersInTenantAction) {
	const { tenantId, userIds } = action.payload;
	yield put(Actions.tenantActionCreators.setBusy(true));
	const { usersDic, noTenantUsers } = (yield select()).tenant as OrganizationState;
	const { result } = yield sendRequestV2(`${organizationUrl}/${tenantId}/users`, 'PUT', { userIds });
	if (result) {
		const users = usersDic[tenantId] || [];
		const newUsers = users.filter(u => userIds.indexOf(u.id) !== -1);
		const checkedUsers = noTenantUsers.filter(u => userIds.indexOf(u.id) !== -1);
		const newNoTenantUses = noTenantUsers.filter(u => userIds.indexOf(u.id) === -1);
		checkedUsers.forEach(u => { if (!newUsers.find(x => x.id === u.id)) { newUsers.push(u); } });
		yield put(Actions.tenantActionCreators.addUsersToDic({ [tenantId]: newUsers }));
		yield put(Actions.tenantActionCreators.setNoTenantUsers(newNoTenantUses));
		yield put(Actions.tenantActionCreators.setShowMembersEditor(false));
	}
	yield put(Actions.tenantActionCreators.setBusy(false));
}

function* addTenantProp(action: Actions.AddTenantPropAction) {
	yield put(Actions.tenantActionCreators.setBusy(true));
	const { tenantProps } = (yield select()).tenant as OrganizationState;
	const { result } = yield sendRequestV2(`${organizationUrl}/props`, 'POST', action.payload);
	if (result) {
		const newProps = [...tenantProps, result];
		yield put(Actions.tenantActionCreators.setTenantProps(newProps));
		yield put(Actions.tenantActionCreators.setShowTenantPropEditor(false));
		const items = yield getTenantItems();
		yield put(Actions.tenantActionCreators.setTenantItems(items));
	}
	yield put(Actions.tenantActionCreators.setBusy(false));
}
function* editTenantProp(action: Actions.EditTenantPropAction) {
	const { id, name, required, multivalued, valueType, sensitive } = action.payload;
	yield put(Actions.tenantActionCreators.setBusy(true));
	const { tenantProps } = (yield select()).tenant as OrganizationState;
	const { result } = yield sendRequestV2(`${organizationUrl}/props/${id}`, 'PUT', { name, required, multivalued, valueType, sensitive });
	if (result) {
		const newProps = [...(tenantProps.filter(p => p.id !== result.id)), result];
		yield put(Actions.tenantActionCreators.setTenantProps(newProps));
		yield put(Actions.tenantActionCreators.setShowTenantPropEditor(false));
		const items = yield getTenantItems();
		yield put(Actions.tenantActionCreators.setTenantItems(items));
	}
	yield put(Actions.tenantActionCreators.setBusy(false));
}
function* updateTenantPermissions(action: Actions.UpdateTenantPermissionsAction) {
	const { tenantId, permissions } = action.payload;
	yield put(Actions.tenantActionCreators.setBusy(true));
	const { result } = yield sendRequestV2(`${organizationUrl}/${tenantId}/permissions`, 'PUT', { permissions });
	if (result) {
		yield put(Actions.tenantActionCreators.addPermissionsToDic({ [tenantId]: result }));
	}
	yield put(Actions.tenantActionCreators.setBusy(false));
}
function* deleteTenantProp(action: Actions.DeleteTenantPropAction) {
	const { id } = action.payload;
	yield put(Actions.tenantActionCreators.setBusy(true));
	const { tenantProps } = (yield select()).tenant as OrganizationState;
	const { result } = yield sendRequestV2(`${organizationUrl}/props/${id}`, 'DELETE');
	if (result) {
		const newProps = tenantProps.filter(t => t.id !== action.payload.id);
		yield put(Actions.tenantActionCreators.setTenantProps(newProps));
		const items = yield getTenantItems();
		yield put(Actions.tenantActionCreators.setTenantItems(items));
	}
	yield put(Actions.tenantActionCreators.setBusy(false));
}

function getTenantRequestMode(tenant: OrganizationItem) {
	if (tenant.props) {
		const props = tenant.props.map(s => ({ ...s, ...{ values: s.editable ? s.values : null } }));
		return { ...tenant, props };
	}
	return tenant;
}
function* addTenant(action: Actions.AddTenantAction) {
	yield put(Actions.tenantActionCreators.setShowTenantEditor(false));
	const { tenant } = action.payload;
	yield put(Actions.tenantActionCreators.setBusy(true));
	const { tenant: { items }, navApp: { user } } = (yield select());
	const requestObj = getTenantRequestMode(tenant);
	const { result } = yield sendRequestV2(organizationUrl, 'POST', requestObj);
	if (result) {
		const newItems = [...items, result];
		for (const t of newItems) {
			if (t.parentTenantId === null && t.id !== GlobalOrganization.Id) {
				t.parentTenantId = GlobalOrganization.Id;
			}
		}
		yield put(Actions.tenantActionCreators.setTenantItems(newItems));
		yield put(Actions.tenantActionCreators.setTenantItemsSelected(result.id));
		yield put(Actions.tenantActionCreators.setIsAdding(false));
		if (isGlobalOrg(user.tenantId)) {
			updateSystemStatistics();
		}
	}
	yield put(Actions.tenantActionCreators.setBusy(false));
}
function* editTenant(action: Actions.EditTenantAction) {
	const { tenant } = action.payload;
	yield put(Actions.tenantActionCreators.setBusy(true));
	const requestObj = getTenantRequestMode(tenant);
	const { result } = yield sendRequestV2(`${organizationUrl}/${tenant.id}`, 'PUT', requestObj);
	if (result) {
		const items = yield getTenantItems();
		yield put(Actions.tenantActionCreators.setTenantItems(items));
		yield put(Actions.tenantActionCreators.setIsEditing(false));
	}
	yield put(Actions.tenantActionCreators.setBusy(false));
	yield put(Actions.tenantActionCreators.setShowTenantEditor(false));
}
function* deleteTenant(action: Actions.DeleteTenantAction) {
	const { tenantId } = action.payload;
	yield put(Actions.tenantActionCreators.setBusy(true));
	const { tenant: { items }, navApp: { user } } = yield select();
	const { result } = yield sendRequestV2(`${organizationUrl}/${tenantId}`, 'DELETE');
	if (result) {
		const getSubItems = (id: string, items: OrganizationItem[]) => {
			let allSubItems: OrganizationItem[] = [];
			const directSubItems = items.filter(s => s.parentTenantId === id);
			allSubItems = allSubItems.concat(directSubItems);
			if (directSubItems.length) {
				for (const sub of directSubItems) {
					allSubItems = allSubItems.concat(getSubItems(sub.id, items));
				}
			}
			return allSubItems;
		};
		const subItems = getSubItems(action.payload.tenantId, items);
		const allDeletedItems = [action.payload.tenantId, ...subItems.map(s => s.id)];
		const newItems = items.filter(t => allDeletedItems.indexOf(t.id) === -1);
		yield put(Actions.tenantActionCreators.setTenantItems(newItems));
		yield put(Actions.tenantActionCreators.setTenantItemsSelected(newItems && newItems[0] && newItems[0].id || ''));
		if (isGlobalOrg(user.tenantId)) {
			updateSystemStatistics();
		}
	}
	yield put(Actions.tenantActionCreators.setBusy(false));
}
function* move(action: Actions.MoveAction) {
	const { tenantId, offset } = action.payload;
	yield put(Actions.tenantActionCreators.setBusy(true));
	const { result } = yield sendRequestV2(`${organizationUrl}/${tenantId}/move`, 'POST', offset);
	if (result) {
		const items = yield getTenantItems();
		yield put(Actions.tenantActionCreators.setTenantItems(items));
	}
	yield put(Actions.tenantActionCreators.setBusy(false));
}

function* migrate(action: Actions.MigrateAction) {
	yield put(Actions.tenantActionCreators.setBusy(true));

	const { id, ParentOrgId, Order } = action.payload;
	const { result } = yield sendRequestV2(`${organizationUrl}/${id}/migrate`, 'POST', { ParentOrgId, Order });
	if (result) {
		const items = yield getTenantItems();
		yield put(Actions.tenantActionCreators.setTenantItems(items));
	}

	yield put(Actions.tenantActionCreators.setBusy(false));
}
export function* watchTenant() {
	yield takeEvery(Actions.SagaActionTypes.Init, init);
	yield takeEvery(Actions.SagaActionTypes.GetTenantUsers, getTenantUsers);
	yield takeEvery(Actions.SagaActionTypes.GetTenantRoles, getTenantRoles);
	yield takeEvery(Actions.SagaActionTypes.GetAvailablePermissions, getAvailablePermissions);
	yield takeEvery(Actions.SagaActionTypes.GetTenantPermissions, getTenantPermissions);
	yield takeEvery(Actions.SagaActionTypes.AddTenantProp, addTenantProp);
	yield takeEvery(Actions.SagaActionTypes.EditTenantProp, editTenantProp);
	yield takeEvery(Actions.SagaActionTypes.DeleteTenantProp, deleteTenantProp);
	yield takeEvery(Actions.SagaActionTypes.AddTenant, addTenant);
	yield takeEvery(Actions.SagaActionTypes.EditTenant, editTenant);
	yield takeEvery(Actions.SagaActionTypes.DeleteTenant, deleteTenant);
	yield takeEvery(Actions.SagaActionTypes.UpdateUsersInTenant, updateUsersInTenant);
	yield takeEvery(Actions.SagaActionTypes.UpdateTenantPermissions, updateTenantPermissions);
	yield takeEvery(Actions.SagaActionTypes.Move, move);
	yield takeEvery(Actions.SagaActionTypes.Migrate, migrate);
}