import * as React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';
import { RoleState, roleActionCreators, UserInfo, Role } from '../../store';
import { MemberEditorUserInfo, MembersEditorCommon, MembersEditorCommonProps } from '../../../Common/MembersEditorCommon';

interface ConnectedProps {
	user: User;
	allUsers: UserInfo[];
	users: UserInfo[];
	roles: Role[];
	selectedOrganizationId: string;
	selectedOrganizationPath: string;
	selectedRoleId: string;
	enableStrictPermissionManagement: boolean;
	dispatch: any;
	t: any;
}

interface LocalState {
	users: MemberEditorUserInfo[];
}

@translate('role', { wait: true })
class MembersEditorInner extends React.PureComponent<ConnectedProps, LocalState> {
	state: LocalState = {
		users: [],
	};

	componentWillMount() {
		this.initUserIds(this.props);
	}

	componentWillReceiveProps(nextProps: ConnectedProps) {
		this.initUserIds(nextProps);
	}

	initUserIds = (props: ConnectedProps) => {
		const { allUsers, users } = props;
		const memberEditorUsers = allUsers.map(u => ({ ...u, ...{ isInRight: users.findIndex(v => v.id === u.id) !== -1 } }));
		this.setState({ users: memberEditorUsers });
	}

	render() {
		const { dispatch, selectedOrganizationId, selectedRoleId, roles, enableStrictPermissionManagement, t } = this.props;
		const { users } = this.state;
		const role = roles.find(r => r.id === selectedRoleId);
		const editorCommonProps: MembersEditorCommonProps = {
			allUsers: users,
			onSaveClick: (rightUserIds) => dispatch(roleActionCreators.updateRoleUsers(selectedOrganizationId, selectedRoleId, rightUserIds, enableStrictPermissionManagement)),
			onCancelClick: () => dispatch(roleActionCreators.setIsAddingMembers(false)),
			getSummaryText: (count) => role.displayName + t('ecMembers', { count }),
		};

		return (<MembersEditorCommon {...editorCommonProps} />);
	}
}

export const MembersEditor = connect(
	(state: { role: RoleState, navApp: { user: User } }) => ({
		user: state.navApp.user,
		selectedRoleId: state.role.selectedRoleId,
		selectedOrganizationId: state.role.selectedOrganizationId,
		selectedOrganizationPath: state.role.organizations.find(o => o.id === state.role.selectedOrganizationId).path,
		allUsers: state.role.allUsers,
		users: state.role.users,
		roles: state.role.roles,
		enableStrictPermissionManagement: state.role.enableStrictPermissionManagement,
	})
)(MembersEditorInner) as React.ComponentClass<{}>;