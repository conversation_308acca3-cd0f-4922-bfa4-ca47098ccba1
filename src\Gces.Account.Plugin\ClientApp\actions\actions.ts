import ActionTypes from './action-types';

export default {
	// Reducer actions
	// common
	StartLoading: () => { return { type: ActionTypes.StartLoading }; },
	EndLoading: () => { return { type: ActionTypes.EndLoading }; },

	// account management
	SetUsers: (users) => { return { type: ActionTypes.SetUsers, users }; },

	// Property management
	SetProperties: (properties) => { return { type: ActionTypes.SetProperties, properties }; },

	// Locked User
	SetLockedUsers: (lockedUsers) => { return { type: ActionTypes.SetLockedUsers, lockedUsers }; },

	// Concurrence
	SetConcurrenceStatus: (concurrenceStatus) => { return { type: ActionTypes.SetConcurrenceStatus, concurrenceStatus }; },

	// License Activation
	SetLicense: (license) => { return { type: ActionTypes.SetLicense, license }; },
	SetCVLicense: (cvlicense) => { return { type: ActionTypes.SetCVLicense, cvlicense }; },

	// Security Setting
	SetSecuritySettings: (securitySettings) => { return { type: ActionTypes.SetSecuritySettings, securitySettings }; },
	SetSecuritySettingsBusy: (busy) => { return { type: ActionTypes.SetSecuritySettingsBusy, busy }; },

	// claim mappings
	SetClaimMappings: (claimMappings) => { return { type: ActionTypes.SetClaimMappings, claimMappings }; },
	SetClaimMappingsViewMode: (claimMappingsViewMode) => { return { type: ActionTypes.SetClaimMappingsViewMode, claimMappingsViewMode }; },
	SetDeletingClaimMapping: (deletingClaimMapping) => { return { type: ActionTypes.SetDeletingClaimMapping, deletingClaimMapping }; },

	// Generate Token
	SetBusy: (busy) => { return { type: ActionTypes.SetBusy, data: { busy } }; },
	SetTokens: (tokens) => { return { type: ActionTypes.SetTokens, data: { tokens } }; },
	SetUrls: (portalUrl, adminPortalUrl, resourcePortalUrl) => { return { type: ActionTypes.SetUrls, data: { portalUrl, adminPortalUrl, resourcePortalUrl } }; },

	// Two Factor Authentication
	SetTFASettings: (tfaSettings) => { return { type: ActionTypes.SetTFASettings, tfaSettings }; },
	SetTFASettingsBusy: (busy) => { return { type: ActionTypes.SetTFASettingsBusy, busy }; },
	SetTFASettingsValidated: (validated) => { return { type: ActionTypes.SetTFASettingsValidated, validated }; },
	SetTFASettingsSaved: (saved) => { return { type: ActionTypes.SetTFASettingsSaved, saved }; },
	SetEmailSettings: (hasEmailSettings) => { return { type: ActionTypes.SetEmailSettings, hasEmailSettings }; },

	// Inactive Session Settings
	SetInactiveSessionSettings: (inactiveSessionSettings) => { return { type: ActionTypes.SetInactiveSessionSettings, inactiveSessionSettings }; },
	SetInactiveSessionSettingsBusy: (busy) => { return { type: ActionTypes.SetInactiveSessionSettingsBusy, busy }; },
	SetSaveInactiveSessionSettingsState: (saved) => { return { type: ActionTypes.SetSaveInactiveSessionSettingsState, saved }; },

	// Saga actions
	// Concurrence Saga actions
	GetConcurrenceStatus: () => { return { type: ActionTypes.GetConcurrenceStatus }; },
	BanUserInConcurrence: (userId, ipRaw) => { return { type: ActionTypes.BanUserInConcurrence, payload: { userId, ipRaw } }; },

	// claim mappings saga actions
	GetClaimMappings: () => { return { type: ActionTypes.GetClaimMappings }; },
	GetProperties: () => { return { type: ActionTypes.GetProperties }; },
	AddClaimMapping: (Name, PropName) => { return { type: ActionTypes.AddClaimMapping, data: { Name, PropName } }; },
	DeleteClaimMapping: (id) => { return { type: ActionTypes.DeleteClaimMapping, data: { id } }; },

	// Security Setting Saga actions
	GetSecuritySettings: () => { return { type: ActionTypes.GetSecuritySettings }; },
	SetSecuritySettingsSaga: (securitySettings) => { return { type: ActionTypes.SetSecuritySettingsSaga, data: { securitySettings } }; },

	// Generate Token Saga actions
	GetTokens: (id) => { return { type: ActionTypes.GetTokens, data: { id } }; },
	RevokeToken: (id) => { return { type: ActionTypes.RevokeToken, data: { id } }; },
	GenerateUrl: (id, edition, hideAvatarMenu, hideWelcomeScreen) => { return { type: ActionTypes.GenerateUrl, data: { id, edition, hideAvatarMenu, hideWelcomeScreen } }; },
	SaveToken: (title, description, user, accessToken, expiresIn, tokenType, organizationPath) => { return { type: ActionTypes.SaveToken, data: { title, description, user, accessToken, expiresIn, tokenType, organizationPath } }; },

	// External Login Providers
	SyncData: (provider) => { return { type: ActionTypes.SyncData, provider }; },

	// Two Factor Authentication Saga actions
	GetTFASettings: () => { return { type: ActionTypes.GetTFASettings }; },
	SetTFASettingsSaga: (tfaSettings) => { return { type: ActionTypes.SetTFASettingsSaga, data: { tfaSettings } }; },
	TestTFASettings: (tfaSettings) => { return { type: ActionTypes.TestTFASettings, data: { tfaSettings } }; },
	GetEmailSettings: () => { return { type: ActionTypes.GetEmailSettings }; },

	// Inactive Session Settings Saga actions
	GetInactiveSessionSettings: () => { return { type: ActionTypes.GetInactiveSessionSettings }; },
	SetInactiveSessionSettingsSaga: (inactiveSessionSettings) => { return { type: ActionTypes.SetInactiveSessionSettingsSaga, inactiveSessionSettings }; },
	GetAllUsers: () => { return { type: ActionTypes.GetAllUsers }; },
};
