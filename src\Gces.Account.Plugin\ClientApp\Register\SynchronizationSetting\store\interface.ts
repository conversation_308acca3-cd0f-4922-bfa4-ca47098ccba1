import * as moment from 'moment';
import { ErrorLevel } from './viewModel';

export interface SynchronizationSettingsState {
	settingsEditor: SettingsEditor;
	syncView: SynchronizationView;
	manualSyncing: boolean;
	manualExecutionId: string;
	busy: boolean;
}

export interface Dataset {
	id: string;
	name: string;
}

// #region SettingsEditor
export interface SettingsEditor {
	syncInfo: SyncInfo;
	secretInfo: SecretInfo;
}

export interface SyncInfo {
	synchronizationEnable: boolean;
	syncDatasets: Record<DatasetSynchronizationType, SyncDataset>;
	synchronizationScheduling: SynchronizationScheduling;
	failureNotifications: Record<string, NotificationProvider>;
}

export interface SecretInfo {
	applicationSecretEnable: boolean;
	applicationSecret: string;
}

export interface SyncDataset {
	selected: boolean;
	dataset?: Dataset;
}

export interface SynchronizationScheduling {
	enabled: boolean;
	scheduleInfo: ScheduledInfo;
	timeout: Timeout;
}

export interface Timeout {
	value: number;
	unit: TimeoutUnit;
}

export enum TimeoutUnit {
	Minutes,
	Hours,
}
export interface NotificationProvider {
	selected: boolean;
	disabled: boolean;
	notifications: NotificationInfo[] | string;
}

export interface NotificationInfo {
	id: string;
	label: string;
}

export type DatasetSynchronizationType = 'user' | 'organization' | 'role' | 'userRoleRelation' | 'userOrganization';

export interface ScheduledInfo {
	repeatType: 'Daily' | 'Weekly' | 'Monthly' | 'IntervalWeekly';
	startDate: moment.Moment;
	endDate: moment.Moment;
	detail: DailyRepeatDetail | WeeklyRepeatDetail | MonthlyRepeatDetail | IntervalWeeklyRepeatDetail;
	timeZoneId?: string;
}

export interface DailyRepeatDetail {
	repeatType: 'Once' | 'Seconds' | 'Hours' | 'Minutes';
	secondsRepeatInterval: number;
	minutesRepeatInterval: number;
	hoursRepeatInterval: number;
	days: number[];
	dailyExecutionTimeRange: ExecutionTimeRange;
}

export interface ExecutionTimeRange {
	startTime: moment.Moment | string;
	endTime: moment.Moment | string;
}

export interface WeeklyRepeatDetail {
	repeatInterval: number;
}

export interface MonthlyRepeatDetail {
	repeatInterval: number;
	type: 'OnDay' | 'OnTheLast' | 'OnTheFirst';
	onDay: number;
	onTheLast: string;
	onTheFirst: string;
}

export interface IntervalWeeklyRepeatDetail {
	weeksRepeatInterval: number;
	days: number[];
}
// #endregion

// #region SynchronizationView
export interface SynchronizationView {
	taskStatus: TaskStatus;
	history: SyncHistory;
	gettingHistory: boolean;
}

export interface TaskStatus {
	process: UserSyncProcess;
	isScheduled: boolean;
	nextRunTime: moment.Moment;
}

export interface SyncHistory {
	info: SyncHistoryInfo;
	records: Record<DatasetType, SyncRecord>;
	errorDetails: SyncError[];
	errors: string[];
	omit: boolean;
}

export interface SyncHistoryInfo {
	state: HistoryTaskState;
	runTime: moment.Moment;
}

export enum HistoryTaskState {
	Success,
	NotAllSuccess,
	Canceled,
	Failed,
}

export type DatasetType = 'user' | 'organization' | 'role' | 'userRoleRelation' | 'userOrganization';

export interface SyncRecord {
	success: number;
	failure: number;
	warning: number;
}

export interface SyncError {
	type: string;
	name: string;
	reason: string;
	level: ErrorLevel;
}
// #endregion

export enum UserSyncProcess {
	None,
	Validating,
	FetchingData,
	Synchronizing,
	Completed,
}