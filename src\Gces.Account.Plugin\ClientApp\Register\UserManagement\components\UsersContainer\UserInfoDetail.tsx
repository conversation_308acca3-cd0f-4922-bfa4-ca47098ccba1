import * as React from 'react';
import { connect } from 'react-redux';
import { translate } from 'react-i18next';
import { Scrollbars } from 'gces-react-custom-scrollbars';
import { InputEditor, Label, Button, AriaIcon } from 'gces-ui';
import * as classnames from 'classnames';

import Drawer from '../../../../components/Drawer';
import { userActionCreators, UserState, UserInfo, Organization, UserProperty } from '../../store';
import { hasManageUserPermission, isAdministrator } from '../../utils';
import { formatOrgPath, getRoleDisplayName } from '../../../../utils';
import { RoleConsts } from '../../../../Register/Common';
import { getNamePathByIdPath } from '../../../../util';
import { CInputEditor } from '../../../../components/c-input';

interface ConnectedProps {
	user: User;
	detailUserId: string;
	allUsers: UserInfo[];
	properties: UserProperty[];
	organizations: Organization[];
	dispatch?: any;
	t?: any;
}

@translate('user', { wait: true })
class UserInfoDetailInner extends React.Component<ConnectedProps> {

	closeDetail = () => {
		this.props.dispatch(userActionCreators.setDetailUserId(''));
	}

	buildPropertiesDrawerEditor = (user: UserInfo, property) => {
		const { t } = this.props;

		const currentValue = (user.customizeProperties && user.customizeProperties[property.name] || []).join('\r\n');
		const labelText = property.multivalued ? `${property.name}${t('OneValuePerLine')}` : property.name;
		return <div key={property.name} className='form-item'>
			<Label text={labelText} />
			<InputEditor value={currentValue} multiline={property.multivalued} placeholder={property.name} disabled />
		</div>;
	}

	renderBasicInfo = (displayUser: UserInfo) => {
		const { detailUserId, t } = this.props;
		const avatarUrl = `api/v2/identity/users/${detailUserId}/avatar${window.token ? `?${window.grapecity?.wyn?.integrationTokenName || 'token'}=${window.token}` : ''}`;

		return (
			<div className='basic-info info-item'>
				<div className='info-area-title' title={t('udBasicInfo')}>{t('udBasicInfo')}</div>
				<div className='basic-info-area info-area'>
					<div className='avatar-name-phone'>
						<img className='user-avatar' src={avatarUrl} />
						<div className='info-detail'>
							<h3 className='user-name'>{displayUser.username}</h3>
							<div className='email'><AriaIcon className='mdi mdi-email-outline' /><span>{displayUser.email}</span></div>
							<div className='phone-number'><AriaIcon className='mdi mdi-cellphone' /><span>{displayUser.mobile || t('udEmpty')}</span></div>
						</div>
					</div>
					<div className='builtin-properties'>
						<div className='first-name property-item'>
							<span className='property-title' title={t('FirstName')}>{t('FirstName')}</span>
							<span className='property-value' title={displayUser.firstName || t('udEmpty')}>{displayUser.firstName || t('udEmpty')}</span>
						</div>
						<div className='last-name property-item'>
							<span className='property-title' title={t('LastName')}>{t('LastName')}</span>
							<span className='property-value' title={displayUser.lastName || t('udEmpty')}>{displayUser.lastName || t('udEmpty')}</span>
						</div>
						<div className='full-name property-item'>
							<span className='property-title' title={t('FullName')}>{t('FullName')}</span>
							<span className='property-value' title={displayUser.fullName || t('udEmpty')}>{displayUser.fullName || t('udEmpty')}</span>
						</div>
					</div>
					{this.renderCustomProperties(displayUser)}
				</div>
			</div>

		);
	}

	renderCustomPropertyValue = (displayUser: UserInfo, hasSensitiveProperty: boolean, property: UserProperty) => {
		const { t } = this.props;

		const sensitive = property.sensitive && displayUser[property.name];
		if (hasSensitiveProperty) {
			return <CInputEditor
				key={property.name}
				className={classnames({ 'efc-no-sensitive-wrapper': !sensitive })}
				readOnly
				value={displayUser[property.name] || t('udEmpty')}
				visibilityToggle={sensitive}
				noVisibilityToggle={!sensitive}
				showEyeTitle={this.props.t('ShowPropertyValue')}
				hideEyeTitle={this.props.t('HidePropertyValue')}
			/>;
		} else {
			return <span className='property-value'>{displayUser[property.name] || t('udEmpty')}</span>;
		}
	}

	renderCustomProperties = (displayUser: UserInfo) => {
		const { properties } = this.props;
		const sorter = (p1, p2) => p1.name.toUpperCase().localeCompare(p2.name.toUpperCase());
		const multivaluedProperties = properties && properties.length > 0 ? properties.filter(p => p.multivalued).sort(sorter) : [];
		const unMultivaluedProperties = properties && properties.length > 0 ? properties.filter(p => !p.multivalued).sort(sorter) : [];

		const hasSensitivePropForUnMultivalued = unMultivaluedProperties.some(p => p.sensitive && displayUser[p.name]);
		const hasSensitivePropForMultivalued = multivaluedProperties.some(p => p.sensitive && displayUser[p.name]);
		return (
			<div className='custom-property'>
				{unMultivaluedProperties.length > 0 && <div className='divider' />}
				<div className='unmultivalued-custom-properties'>
					{
						unMultivaluedProperties.map((p) => {
							return <div className='property-item' key={p.name}>
								<span className='property-title'>{p.name}</span>
								{this.renderCustomPropertyValue(displayUser, hasSensitivePropForUnMultivalued, p)}
							</div>;
						})
					}
				</div>
				{multivaluedProperties.length > 0 && <div className='divider' />}
				<div className='multivalued-custom-properties'>
					{
						multivaluedProperties.map((p) => {
							return <div className='property-item' key={p.name}>
								<span className='property-title'>{p.name}</span>
								{this.renderCustomPropertyValue(displayUser, hasSensitivePropForMultivalued, p)}
							</div>;
						})
					}
				</div>
			</div>
		);
	}

	renderManager = (displayUser: UserInfo) => {
		const { organizations, t } = this.props;
		const orgIdPath = displayUser.organizationIdPath;
		const globalOrg = {
			name: t('common:globalOrgName'),
		};
		const managerOrg = orgIdPath === '' ? globalOrg : organizations.find(o => orgIdPath.endsWith(o.id));
		if (!managerOrg) return null;
		const orgPath = formatOrgPath(getNamePathByIdPath(organizations, orgIdPath)) || t('common:globalOrgName');
		const orgName = managerOrg ? managerOrg.name : t('common:globalOrgName');
		return (
			<div className='organization-info info-item'>
				<div className='info-area-title' title={t('ManagedBy')}>{t('ManagedBy')}</div>
				<div className='organization-info-area info-area'>
					<div className='organization-info-item'>
						<span className='organization-name' title={orgName}>{orgName}</span>
						<span className='organization-path' title={orgPath}>{orgPath}</span>
					</div>
				</div>
			</div >
		);
	}

	renderOrganizations = (displayUser: UserInfo) => {
		const { t } = this.props;
		const finalOrganizations = [...(displayUser.organizations || [])];
		if (-1 === finalOrganizations.indexOf('/')) {
			finalOrganizations.unshift(t('common:globalOrgName'));
		}
		return (
			<div className='organization-info info-item'>
				<div className='info-area-title' title={t('Organizations')}>{t('Organizations')}</div>
				<div className='organization-info-area info-area'>
					{finalOrganizations && finalOrganizations.map(o => {
						return <div className='organization-info-item' key={o}>
							<span className='organization-name'>{o && o.split('/').pop()}</span>
							<span className='organization-path'>{formatOrgPath(o)}</span>
						</div>;
					})}
				</div>
			</div >
		);
	}

	renderRoles = (displayUser: UserInfo) => {
		const { t } = this.props;
		const { roles, tenantRoles } = displayUser;
		const orgPathList = tenantRoles ? Object.keys(tenantRoles) : [];
		const finalRoles = roles && roles.map(r => { return getRoleDisplayName(r); });
		if (-1 === finalRoles.indexOf(RoleConsts.everyone.name)) {
			finalRoles.unshift(t('common:rn_Everyone'));
		}
		return (
			<div className='role-info info-item'>
				<div className='info-area-title' title={t('Roles')}>{t('Roles')}</div>
				<div className='role-info-area info-area'>
					{finalRoles &&
						<div className='role-info-item'>
							<div>
								<i className='role-item-icon mdi mdi-account-group-outline' />
								<span className='role-name'>{finalRoles.join(', ')}</span>
							</div>
							<div>
								<i className='organization-item-icon mdi mdi-lan' />
								<span className='organization-path' title={t('common:globalOrgName')}>{t('common:globalOrgName')}</span>
							</div>
						</div>

					}
					{orgPathList.length > 0 &&
						orgPathList.map(orgPath => {
							const finalTenantRoles = tenantRoles[orgPath].map(r => { return getRoleDisplayName(r); });
							return (
								tenantRoles[orgPath].length > 0 &&
								<div className='role-info-item' key={orgPath}>
									<div>
										<i className='role-item-icon mdi mdi-account-group-outline' />
										<span className='role-name'>{finalTenantRoles.join(', ')}</span>
									</div>
									<div>
										<i className='organization-item-icon mdi mdi-lan' />
										<span className='organization-path'>{formatOrgPath(orgPath)}</span>
									</div>
								</div>
							);
						})
					}
				</div>
			</div >
		);
	}
	onEditUser = () => {
		const { allUsers, detailUserId, dispatch } = this.props;
		dispatch(userActionCreators.setEditingUser(allUsers.find(u => u.id === detailUserId)));
		dispatch(userActionCreators.setDetailUserId(''));
	}

	renderBody = () => {
		const { user, properties, detailUserId, allUsers, organizations } = this.props;

		const displayUserIndex = allUsers && allUsers.findIndex(ou => ou.id === detailUserId);
		const displayUser = displayUserIndex > -1 && { ...allUsers[displayUserIndex] };
		if (!displayUser) return null;

		if (properties.length) {
			properties.forEach(p => {
				let propValue = '';
				if (p.multivalued) {
					propValue = displayUser.customizeProperties
						? (displayUser.customizeProperties[p.name] ? displayUser.customizeProperties[p.name].sort((v1, v2) => v1.toUpperCase().localeCompare(v2.toUpperCase())) : []).join(', ')
						: '';
				} else {
					propValue = displayUser.customizeProperties ? displayUser.customizeProperties[p.name] || '' : '';
				}
				displayUser[p.name] = propValue;
			});
		}

		if (!isAdministrator(user.roles)) {
			const orgIndex = organizations.findIndex(v => v.id === user.orgId);
			const curRootOrg = orgIndex > -1 && organizations[orgIndex];
			const tenantRolesKeys = displayUser.tenantRoles && Object.keys(displayUser.tenantRoles);
			displayUser.organizations = displayUser.organizations.filter(o => o.startsWith(curRootOrg.path));
			const newTenantRoles = {};
			displayUser.roles = [];
			if (tenantRolesKeys && tenantRolesKeys.length) {
				tenantRolesKeys.forEach(orgPath => {
					if (orgPath.startsWith(curRootOrg.path)) {
						newTenantRoles[orgPath] = displayUser.tenantRoles[orgPath];
					}
				});
				displayUser.tenantRoles = newTenantRoles;
			}
		}

		const scrollbarProps = {
			style: { width: '100%', height: '100%' },
			renderThumbVertical: props => <div {...props} style={{ width: '4px' }} className='thumb-vertical' />,
		};
		return (
			<Scrollbars {...scrollbarProps}>
				{this.renderBasicInfo(displayUser)}
				{this.renderManager(displayUser)}
				{this.renderOrganizations(displayUser)}
				{this.renderRoles(displayUser)}
			</Scrollbars>
		);
	}

	renderFooter = () => {
		const { user, allUsers, detailUserId } = this.props;

		const displayUserIndex = allUsers && allUsers.findIndex(ou => ou.id === detailUserId);
		const displayUser = displayUserIndex > -1 && { ...allUsers[displayUserIndex] };

		if ((hasManageUserPermission(user.roles) && displayUser.organizationIdPath.includes(user.orgId)) || isAdministrator(user.roles))
			return (
				<React.Fragment>
					<Button
						style='accent'
						size='small'
						className='edit-user-button'
						text={this.props.t('Edit')}
						onClick={this.onEditUser}
						aid='edit-user'
					/>
					<Button
						style='accent'
						size='small'
						className='close-detail-button'
						text={this.props.t('Close')}
						onClick={this.closeDetail}
						aid='close-user-detail'
					/>
				</React.Fragment>
			);
		else
			return null;
	}

	render() {
		const { detailUserId, t } = this.props;
		if (!detailUserId) return null;

		return (
			<div className='user-info-detail-panel'>
				<Drawer
					className='user-detail'
					open={true}
					title={t('UserDetail')}
					closeButtonTitle={t('Close')}
					onDismiss={this.closeDetail}
					bodyRender={this.renderBody}
					footerRender={this.renderFooter}
					noMaskLayer={true}
				/>
			</div>
		);
	}
}

export const UserInfoDetail = connect(
	(state: { user: UserState, navApp: { user: User } }) => ({
		user: state.navApp.user,
		detailUserId: state.user.detailUserId,
		allUsers: state.user.allUsers,
		properties: state.user.properties,
		organizations: state.user.organizations,
	})
)(UserInfoDetailInner) as React.ComponentClass<{}>;