import ActionTypes from './../actions/action-types';
import * as update from 'immutability-helper';
import { TokenManagementState } from '../interfaces/generate-token-interfaces';

const defaultGenerateTokenState: TokenManagementState = {
	tokens: [],
	portalUrl: '',
	adminPortalUrl: '',
	resourcePortalUrl: '',
	busy: false,
};

export default (state: TokenManagementState = defaultGenerateTokenState, action) => {
	switch (action.type) {
		case ActionTypes.SetBusy:
			return update(state, { busy: { $set: action.data.busy } });

		case ActionTypes.SetTokens:
			return update(state, { tokens: { $set: action.data.tokens } });

		case ActionTypes.SetUrls:
			return update(state, { portalUrl: { $set: action.data.portalUrl }, adminPortalUrl: { $set: action.data.adminPortalUrl }, resourcePortalUrl: { $set: action.data.resourcePortalUrl } });

		default:
			return state;
	}
};
