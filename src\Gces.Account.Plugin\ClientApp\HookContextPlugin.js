const _ = require('lodash');
const PluginName = 'HookContextPlugin';
const fs = require('fs');
const path = require('path');

class HookContextPlugin {
    constructor(options) {
        this.options = options;
    }

    apply(compiler) {
        const { webpack: { Compilation, sources: { ConcatSource }, javascript: { JavascriptModulesPlugin } } } = compiler;
        const { modules, globalVar } = this.options;
        compiler.hooks.compilation.tap(PluginName, (compilation) => {
            compilation.hooks.processAssets.tap({
                name: PluginName,
                stage: Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,
            }, (assets) => {
                Object.keys(assets).forEach((assetName) => {
                    for (const { filename, plugins, component } of modules) {
                        if (filename.includes(assetName)) {
                            const wrapper = this.getRegisterWrapperCode(filename.replace(/\.js$/, ''), plugins, component, globalVar);
								compilation.assets[assetName] = new ConcatSource(wrapper.before, '\n', compilation.assets[assetName], '\n', wrapper.after);
								// eslint-disable-next-line no-console
								console.log(`\n-------------------Wrap the Reg code with HookContextPlugin: ${assetName}-----------------------`);
								break;
                        }
                    }
                });
            });
        });
        compiler.hooks.thisCompilation.tap(PluginName, (compilation) => {
			const hooks = JavascriptModulesPlugin.getCompilationHooks(compilation);
			hooks.renderStartup.tap(
				PluginName,
				(source, module, renderContext) => {
					const concatSource = new ConcatSource(source);
					return concatSource;
				});
		});
	}
    getRegisterWrapperCode(id, plugins, component, globalVar) {
		if (!this.jsRegContent) {
			this.jsRegContent = fs.readFileSync(path.join(__dirname, 'HookRegWrapCode.js')).toString();
		}
		const compiled = _.template(this.jsRegContent, { interpolate: /___([\s\S]+?)___/g });
		const compiledText = compiled({
			id,
			component,
			plugins: JSON.stringify(plugins),
			globalVar
		});
		const splitMarker = '// code split marker';
		const [before, after] = compiledText.split(splitMarker);
		return {
			before, after,
		};
	}
}

module.exports = HookContextPlugin;