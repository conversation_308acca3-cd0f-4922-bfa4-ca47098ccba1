﻿import ActionTypes from './../actions/action-types';
import update from 'immutability-helper';

export default (state = { claimMappings: [] }, action) => {
    switch (action.type) {
        case ActionTypes.SetClaimMappings:
            return update(state, { claimMappings: { $set: action.claimMappings } });

        case ActionTypes.SetProperties:
            return update(state, { properties: { $set: action.properties } });

        case ActionTypes.SetClaimMappingsViewMode:
            return update(state, { claimMappingsViewMode: { $set: action.claimMappingsViewMode } });

        case ActionTypes.SetDeletingClaimMapping:
            return update(state, { deletingClaimMapping: { $set: action.deletingClaimMapping } });

        default:
            return state;
    }
}