import * as update from 'immutability-helper';
import moment from 'moment';
import { Reducer } from 'redux';
import { beautifyMinutes } from '../utils';
import { getDefaultTimeZoneId } from '../utils/timeZoneUtils';
import { SynchronizationSettingsAction } from './action';
import { SettingsEditor, SynchronizationScheduling, SynchronizationSettingsState, SynchronizationView, TimeoutUnit, UserSyncProcess } from './interface';

const startDate = moment();
startDate.minute(beautifyMinutes(startDate.minute()));
startDate.second(0);

export const defaultSynchronizationScheduling: SynchronizationScheduling = {
	enabled: false,
	scheduleInfo: {
		startDate,
		endDate: null,
		repeatType: 'Daily',
		detail: {
			repeatType: 'Once',
			days: [2, 3, 4, 5, 6],
			hoursRepeatInterval: 1,
			minutesRepeatInterval: 5,
			secondsRepeatInterval: 5,
			dailyExecutionTimeRange: null,
		},
		timeZoneId: getDefaultTimeZoneId(),
	},
	timeout: { value: 1, unit: TimeoutUnit.Minutes },
};

const defaultSettingsEditorState: SettingsEditor = {
	syncInfo: {
		synchronizationEnable: false,
		syncDatasets: {
			'user': { selected: false },
			'organization': { selected: false },
			'role': { selected: false },
			'userRoleRelation': { selected: false },
			'userOrganization': { selected: false },
		},
		synchronizationScheduling: defaultSynchronizationScheduling,
		failureNotifications: {},
	},
	secretInfo: {
		applicationSecretEnable: false,
		applicationSecret: '',
	}
};

const defaultViewState: SynchronizationView = {
	taskStatus: {
		process: UserSyncProcess.None,
		isScheduled: false,
		nextRunTime: null,
	},
	history: null,
	gettingHistory: false,
};

const defaultState: SynchronizationSettingsState = {
	settingsEditor: defaultSettingsEditorState,
	syncView: defaultViewState,
	manualSyncing: false,
	manualExecutionId: '',
	busy: false,
};

export const synchronizationReducer: Reducer<SynchronizationSettingsState> = (state: SynchronizationSettingsState = defaultState, action: SynchronizationSettingsAction) => {
	switch (action.type) {
		case 'SYNCHRONIZATION-SETTINGS/SS/SetSyncInfo':
			const { syncInfo } = action.payload;
			return update(state, { settingsEditor: { syncInfo: { $set: syncInfo } } });
		case 'SYNCHRONIZATION-SETTINGS/SS/SetFailureNotifications':
			const { failureNotifications } = action.payload;
			return update(state, { settingsEditor: { syncInfo: { failureNotifications: { $set: failureNotifications } } } });
		case 'SYNCHRONIZATION-SETTINGS/SS/SetSecret':
			const { enable, secret } = action.payload;
			return update(state, { settingsEditor: { secretInfo: { applicationSecretEnable: { $set: enable }, applicationSecret: { $set: secret } } } });
		case 'SYNCHRONIZATION-SETTINGS/SV/SetView':
			const { view } = action.payload;
			return update(state, { syncView: { $set: view } });
		case 'SYNCHRONIZATION-SETTINGS/SV/SetNextRuntime':
			const { nextRuntime } = action.payload;
			return update(state, { syncView: { taskStatus: { nextRunTime: { $set: nextRuntime } } } });
		case 'SYNCHRONIZATION-SETTINGS/SV/SetTaskStatus':
			const { status } = action.payload;
			return update(state, { syncView: { taskStatus: { $set: status } } });
		case 'SYNCHRONIZATION-SETTINGS/SV/SetManualSyncing':
			const { status: manualSyncing } = action.payload;
			return update(state, { manualSyncing: { $set: manualSyncing } });
		case 'SYNCHRONIZATION-SETTINGS/SV/SetExecutionId':
			const { id } = action.payload;
			return update(state, { manualExecutionId: { $set: id } });
		case 'SYNCHRONIZATION-SETTINGS/SS/SetBusy':
			const { busy } = action.payload;
			return update(state, { busy: { $set: busy } });
		case 'SYNCHRONIZATION-SETTINGS/SS/SetGettingHistory':
			const { getting } = action.payload;
			return update(state, { syncView: { gettingHistory: { $set: getting } } });
		default: return state;
	}
};